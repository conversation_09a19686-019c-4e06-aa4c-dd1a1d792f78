<?php
/**
 * اختبار إضافة التصنيفات
 * Test Adding Categories
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>اختبار إضافة التصنيفات</h2>";

try {
    // تضمين ملفات التكوين
    require_once 'config/config.php';
    
    echo "<p style='color: green;'>✅ تم تضمين ملفات التكوين بنجاح</p>";
    
    // محاكاة جلسة المدير
    $_SESSION[ADMIN_SESSION_NAME] = true;
    $_SESSION['admin_id'] = 1;
    
    // معالجة إضافة تصنيف
    if (isset($_POST['add_category'])) {
        $name = sanitizeInput($_POST['name']);
        $description = sanitizeInput($_POST['description']);
        $status = sanitizeInput($_POST['status']);
        
        echo "<h3>محاولة إضافة التصنيف:</h3>";
        echo "<ul>";
        echo "<li><strong>الاسم:</strong> " . htmlspecialchars($name) . "</li>";
        echo "<li><strong>الوصف:</strong> " . htmlspecialchars($description) . "</li>";
        echo "<li><strong>الحالة:</strong> " . htmlspecialchars($status) . "</li>";
        echo "</ul>";
        
        $errors = [];
        
        if (empty($name)) {
            $errors[] = 'اسم التصنيف مطلوب';
        }
        
        // التحقق من عدم تكرار الاسم
        $existingCategory = fetchOne("SELECT id FROM categories WHERE name = ?", [$name]);
        if ($existingCategory) {
            $errors[] = 'اسم التصنيف موجود مسبقاً';
        }
        
        if (empty($errors)) {
            $categoryData = [
                'name' => $name,
                'description' => $description,
                'status' => $status
            ];
            
            echo "<p>البيانات المرسلة:</p>";
            echo "<pre>" . print_r($categoryData, true) . "</pre>";
            
            // محاولة الإضافة
            $result = insertData('categories', $categoryData);
            
            if ($result) {
                echo "<p style='color: green;'>✅ تم إضافة التصنيف بنجاح! ID: $result</p>";
                
                // التحقق من الإضافة
                $addedCategory = fetchOne("SELECT * FROM categories WHERE id = ?", [$result]);
                if ($addedCategory) {
                    echo "<p style='color: green;'>✅ تم التحقق من إضافة التصنيف في قاعدة البيانات</p>";
                    echo "<pre>" . print_r($addedCategory, true) . "</pre>";
                } else {
                    echo "<p style='color: red;'>❌ لم يتم العثور على التصنيف في قاعدة البيانات</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ فشل في إضافة التصنيف</p>";
                
                // محاولة الإضافة المباشرة للتشخيص
                echo "<p>محاولة الإضافة المباشرة...</p>";
                
                $database = new Database();
                $pdo = $database->getConnection();
                
                if ($pdo) {
                    try {
                        $stmt = $pdo->prepare("INSERT INTO categories (name, description, status) VALUES (?, ?, ?)");
                        $directResult = $stmt->execute([$name . ' (مباشر)', $description, $status]);
                        
                        if ($directResult) {
                            $directId = $pdo->lastInsertId();
                            echo "<p style='color: green;'>✅ الإضافة المباشرة نجحت! ID: $directId</p>";
                        } else {
                            echo "<p style='color: red;'>❌ الإضافة المباشرة فشلت أيضاً</p>";
                            $errorInfo = $stmt->errorInfo();
                            echo "<p>تفاصيل الخطأ: " . print_r($errorInfo, true) . "</p>";
                        }
                    } catch (PDOException $e) {
                        echo "<p style='color: red;'>❌ خطأ PDO: " . $e->getMessage() . "</p>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ لا يمكن الحصول على اتصال قاعدة البيانات</p>";
                }
            }
        } else {
            echo "<p style='color: red;'>❌ أخطاء في البيانات:</p>";
            echo "<ul>";
            foreach ($errors as $error) {
                echo "<li>$error</li>";
            }
            echo "</ul>";
        }
    }
    
    // عرض التصنيفات الحالية
    echo "<h3>التصنيفات الحالية:</h3>";
    $categories = fetchAll("SELECT * FROM categories ORDER BY created_at DESC");
    
    if (!empty($categories)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>ID</th>";
        echo "<th style='padding: 10px;'>الاسم</th>";
        echo "<th style='padding: 10px;'>الوصف</th>";
        echo "<th style='padding: 10px;'>الحالة</th>";
        echo "<th style='padding: 10px;'>تاريخ الإنشاء</th>";
        echo "</tr>";
        
        foreach ($categories as $category) {
            echo "<tr>";
            echo "<td style='padding: 10px;'>" . $category['id'] . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($category['name']) . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($category['description'] ?? '') . "</td>";
            echo "<td style='padding: 10px;'>" . $category['status'] . "</td>";
            echo "<td style='padding: 10px;'>" . $category['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد تصنيفات في قاعدة البيانات</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ في الاختبار</h4>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>

<hr>

<h3>إضافة تصنيف جديد:</h3>
<div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <form method="POST">
        <div style="margin-bottom: 15px;">
            <label for="name"><strong>اسم التصنيف:</strong></label><br>
            <input type="text" id="name" name="name" required 
                   style="padding: 10px; width: 300px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        
        <div style="margin-bottom: 15px;">
            <label for="description"><strong>الوصف:</strong></label><br>
            <textarea id="description" name="description" rows="3" 
                      style="padding: 10px; width: 300px; border: 1px solid #ddd; border-radius: 4px;"></textarea>
        </div>
        
        <div style="margin-bottom: 15px;">
            <label for="status"><strong>الحالة:</strong></label><br>
            <select id="status" name="status" 
                    style="padding: 10px; width: 150px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="active">نشط</option>
                <option value="inactive">غير نشط</option>
            </select>
        </div>
        
        <button type="submit" name="add_category" 
                style="background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px;">
            إضافة التصنيف
        </button>
    </form>
</div>

<hr>

<div style="background: #e7f3ff; padding: 20px; border-radius: 8px;">
    <h4>🔧 خطوات التشخيص:</h4>
    <ol>
        <li>املأ النموذج أعلاه واضغط "إضافة التصنيف"</li>
        <li>راقب الرسائل المعروضة</li>
        <li>تحقق من ظهور التصنيف في الجدول</li>
        <li>إذا فشل، ستظهر تفاصيل الخطأ</li>
    </ol>
    
    <h4>🎯 الحلول المحتملة:</h4>
    <ul>
        <li>إذا نجح هنا وفشل في صفحة التصنيفات، فالمشكلة في صفحة التصنيفات</li>
        <li>إذا فشل هنا، فالمشكلة في دالة insertData أو قاعدة البيانات</li>
        <li>تحقق من رسائل الخطأ المفصلة</li>
    </ul>
</div>

<div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h4>📋 بعد حل المشكلة:</h4>
    <ol>
        <li>احذف ملفات التشخيص (debug_categories.php, test_add_category.php)</li>
        <li>أزل أسطر تفعيل الأخطاء من admin/categories.php</li>
        <li>اختبر صفحة التصنيفات الأصلية</li>
    </ol>
</div>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h2, h3, h4 {
    color: #333;
}

table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 15px 0;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    overflow-x: auto;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
