<?php
/**
 * Website Repair Script
 * Comprehensive repair script for common website issues
 */

// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح الموقع - Website Repair</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background: #f8f9fa; }";
echo "h1, h2, h3 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".repair-section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }";
echo ".btn { display: inline-block; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 3px; }";
echo ".progress { background: #e9ecef; border-radius: 4px; height: 20px; margin: 10px 0; }";
echo ".progress-bar { background: #007bff; height: 100%; border-radius: 4px; transition: width 0.3s; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 إصلاح الموقع الإلكتروني</h1>";
echo "<p class='info'>سكريبت شامل لإصلاح المشاكل الشائعة في الموقع</p>";

$repairs = [];
$totalSteps = 8;
$currentStep = 0;

function updateProgress($step, $total) {
    $percentage = round(($step / $total) * 100);
    echo "<div class='progress'>";
    echo "<div class='progress-bar' style='width: {$percentage}%'></div>";
    echo "</div>";
    echo "<p>التقدم: $step من $total خطوة ($percentage%)</p>";
}

function logRepair($message, $type = 'success') {
    global $repairs;
    $repairs[] = ['message' => $message, 'type' => $type];
    $class = $type === 'success' ? 'success' : ($type === 'error' ? 'error' : 'warning');
    echo "<div class='$class'>$message</div>";
}

// Step 1: Create missing directories
echo "<div class='repair-section'>";
echo "<h2>الخطوة 1: إنشاء المجلدات المفقودة</h2>";
$currentStep++;
updateProgress($currentStep, $totalSteps);

$requiredDirs = [
    'uploads',
    'uploads/products',
    'uploads/categories',
    'assets',
    'assets/css',
    'assets/js',
    'assets/images'
];

foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            logRepair("✅ تم إنشاء المجلد: $dir");
        } else {
            logRepair("❌ فشل في إنشاء المجلد: $dir", 'error');
        }
    } else {
        logRepair("✅ المجلد موجود: $dir");
    }
    
    // Create index.html to prevent directory listing
    $indexFile = $dir . '/index.html';
    if (!file_exists($indexFile)) {
        file_put_contents($indexFile, '<!DOCTYPE html><html><head><title>403 Forbidden</title></head><body><h1>Directory access is forbidden.</h1></body></html>');
    }
}

echo "</div>";

// Step 2: Fix database connection
echo "<div class='repair-section'>";
echo "<h2>الخطوة 2: إصلاح اتصال قاعدة البيانات</h2>";
$currentStep++;
updateProgress($currentStep, $totalSteps);

try {
    require_once 'config/config.php';
    
    if (isset($pdo) && $pdo) {
        logRepair("✅ اتصال قاعدة البيانات يعمل بشكل صحيح");
        
        // Check required tables
        $requiredTables = ['admins', 'categories', 'products', 'orders', 'site_settings'];
        $existingTables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        $missingTables = array_diff($requiredTables, $existingTables);
        
        if (!empty($missingTables)) {
            logRepair("⚠️ جداول مفقودة: " . implode(', ', $missingTables), 'warning');
            
            // Try to create missing tables from database.sql
            if (file_exists('database.sql')) {
                $sql = file_get_contents('database.sql');
                $statements = array_filter(array_map('trim', explode(';', $sql)));
                
                foreach ($statements as $statement) {
                    if (!empty($statement) && !preg_match('/^(--|\/\*|SET|START|COMMIT)/i', $statement)) {
                        try {
                            $pdo->exec($statement);
                        } catch (PDOException $e) {
                            // Ignore table exists errors
                            if ($e->getCode() != '42S01') {
                                logRepair("⚠️ خطأ في إنشاء جدول: " . $e->getMessage(), 'warning');
                            }
                        }
                    }
                }
                
                logRepair("✅ تم محاولة إنشاء الجداول المفقودة");
            }
        } else {
            logRepair("✅ جميع الجداول المطلوبة موجودة");
        }
        
    } else {
        logRepair("❌ مشكلة في اتصال قاعدة البيانات", 'error');
    }
    
} catch (Exception $e) {
    logRepair("❌ خطأ في فحص قاعدة البيانات: " . $e->getMessage(), 'error');
}

echo "</div>";

// Step 3: Fix file permissions
echo "<div class='repair-section'>";
echo "<h2>الخطوة 3: إصلاح صلاحيات الملفات</h2>";
$currentStep++;
updateProgress($currentStep, $totalSteps);

$writableDirs = ['uploads', 'uploads/products', 'uploads/categories'];

foreach ($writableDirs as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            logRepair("✅ المجلد قابل للكتابة: $dir");
        } else {
            if (chmod($dir, 0755)) {
                logRepair("✅ تم إصلاح صلاحيات المجلد: $dir");
            } else {
                logRepair("❌ فشل في إصلاح صلاحيات المجلد: $dir", 'error');
            }
        }
    }
}

echo "</div>";

// Step 4: Check and fix configuration files
echo "<div class='repair-section'>";
echo "<h2>الخطوة 4: فحص ملفات التكوين</h2>";
$currentStep++;
updateProgress($currentStep, $totalSteps);

$configFiles = [
    'config/config.php' => 'ملف الإعدادات الرئيسي',
    'config/database.php' => 'ملف قاعدة البيانات',
    'config/functions.php' => 'ملف الدوال المساعدة'
];

foreach ($configFiles as $file => $desc) {
    if (file_exists($file)) {
        logRepair("✅ $desc موجود: $file");
        
        // Test if file can be included without errors
        try {
            ob_start();
            include_once $file;
            ob_end_clean();
            logRepair("✅ $desc يعمل بدون أخطاء");
        } catch (Exception $e) {
            logRepair("❌ خطأ في $desc: " . $e->getMessage(), 'error');
        }
    } else {
        logRepair("❌ $desc مفقود: $file", 'error');
    }
}

echo "</div>";

// Step 5: Check main pages
echo "<div class='repair-section'>";
echo "<h2>الخطوة 5: فحص الصفحات الرئيسية</h2>";
$currentStep++;
updateProgress($currentStep, $totalSteps);

$mainPages = [
    'index.php' => 'الصفحة الرئيسية',
    'products.php' => 'صفحة المنتجات',
    'cart.php' => 'سلة التسوق',
    'checkout.php' => 'إتمام الطلب',
    'contact.php' => 'اتصل بنا'
];

foreach ($mainPages as $file => $desc) {
    if (file_exists($file)) {
        logRepair("✅ $desc موجودة: $file");
        
        // Test if page loads without fatal errors
        try {
            ob_start();
            $error = false;
            
            set_error_handler(function($severity, $message, $file, $line) use (&$error) {
                if ($severity & (E_ERROR | E_PARSE | E_CORE_ERROR | E_COMPILE_ERROR)) {
                    $error = $message;
                }
                return true;
            });
            
            include $file;
            restore_error_handler();
            
            $output = ob_get_clean();
            
            if ($error) {
                logRepair("❌ خطأ في $desc: $error", 'error');
            } else {
                logRepair("✅ $desc تعمل بدون أخطاء فادحة");
            }
            
        } catch (Exception $e) {
            logRepair("❌ خطأ في $desc: " . $e->getMessage(), 'error');
        }
    } else {
        logRepair("❌ $desc مفقودة: $file", 'error');
    }
}

echo "</div>";

// Step 6: Check admin pages
echo "<div class='repair-section'>";
echo "<h2>الخطوة 6: فحص صفحات الإدارة</h2>";
$currentStep++;
updateProgress($currentStep, $totalSteps);

$adminPages = [
    'admin/login.php' => 'تسجيل دخول الإدارة',
    'admin/dashboard.php' => 'لوحة القيادة',
    'admin/products.php' => 'إدارة المنتجات',
    'admin/categories.php' => 'إدارة التصنيفات',
    'admin/orders.php' => 'إدارة الطلبات'
];

foreach ($adminPages as $file => $desc) {
    if (file_exists($file)) {
        logRepair("✅ $desc موجودة: $file");
    } else {
        logRepair("❌ $desc مفقودة: $file", 'error');
    }
}

echo "</div>";

// Step 7: Check AJAX endpoints
echo "<div class='repair-section'>";
echo "<h2>الخطوة 7: فحص نقاط AJAX</h2>";
$currentStep++;
updateProgress($currentStep, $totalSteps);

$ajaxFiles = [
    'ajax/cart.php' => 'عمليات سلة التسوق',
    'ajax/search_suggestions.php' => 'اقتراحات البحث',
    'ajax/newsletter.php' => 'النشرة البريدية',
    'ajax/wishlist.php' => 'قائمة الأمنيات'
];

foreach ($ajaxFiles as $file => $desc) {
    if (file_exists($file)) {
        logRepair("✅ $desc موجود: $file");
    } else {
        logRepair("❌ $desc مفقود: $file", 'error');
    }
}

echo "</div>";

// Step 8: Final verification
echo "<div class='repair-section'>";
echo "<h2>الخطوة 8: التحقق النهائي</h2>";
$currentStep++;
updateProgress($currentStep, $totalSteps);

// Test database connection one more time
try {
    if (isset($pdo) && $pdo) {
        $testQuery = $pdo->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = 'shop_db'")->fetch();
        logRepair("✅ قاعدة البيانات تحتوي على " . $testQuery['count'] . " جدول");
    }
} catch (Exception $e) {
    logRepair("❌ خطأ في التحقق النهائي من قاعدة البيانات: " . $e->getMessage(), 'error');
}

// Check if homepage loads
try {
    $homepageUrl = 'http://localhost/shop/index.php';
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET'
        ]
    ]);
    
    $homepage = @file_get_contents($homepageUrl, false, $context);
    if ($homepage && strlen($homepage) > 1000) {
        logRepair("✅ الصفحة الرئيسية تحمل بنجاح");
    } else {
        logRepair("⚠️ قد تكون هناك مشكلة في تحميل الصفحة الرئيسية", 'warning');
    }
} catch (Exception $e) {
    logRepair("⚠️ لا يمكن اختبار تحميل الصفحة الرئيسية: " . $e->getMessage(), 'warning');
}

echo "</div>";

// Summary
echo "<div class='repair-section'>";
echo "<h2>📋 ملخص الإصلاحات</h2>";

$successCount = 0;
$errorCount = 0;
$warningCount = 0;

foreach ($repairs as $repair) {
    if ($repair['type'] === 'success') $successCount++;
    elseif ($repair['type'] === 'error') $errorCount++;
    else $warningCount++;
}

echo "<div class='info'>";
echo "<h3>إحصائيات الإصلاح:</h3>";
echo "<ul>";
echo "<li><strong>نجح:</strong> $successCount عملية</li>";
echo "<li><strong>تحذيرات:</strong> $warningCount عملية</li>";
echo "<li><strong>أخطاء:</strong> $errorCount عملية</li>";
echo "</ul>";
echo "</div>";

if ($errorCount === 0) {
    echo "<div class='success'>";
    echo "<h3>🎉 تم الإصلاح بنجاح!</h3>";
    echo "<p>جميع المشاكل تم حلها. الموقع جاهز للاستخدام.</p>";
    echo "</div>";
} elseif ($errorCount <= 2) {
    echo "<div class='warning'>";
    echo "<h3>⚠️ إصلاح جزئي</h3>";
    echo "<p>تم حل معظم المشاكل، لكن هناك بعض المشاكل التي تحتاج إلى تدخل يدوي.</p>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h3>❌ يحتاج إلى مزيد من الإصلاح</h3>";
    echo "<p>هناك عدة مشاكل تحتاج إلى حل. يرجى مراجعة التفاصيل أعلاه.</p>";
    echo "</div>";
}

echo "<h3>الخطوات التالية:</h3>";
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='test_website_functionality.php' class='btn' style='background: #28a745;'>🧪 اختبار الوظائف</a>";
echo "<a href='index.php' class='btn' style='background: #17a2b8;'>🏠 زيارة الموقع</a>";
echo "<a href='admin/login.php' class='btn' style='background: #ffc107; color: #212529;'>🔐 لوحة التحكم</a>";
echo "<a href='setup_database.php' class='btn'>🗄️ إعداد قاعدة البيانات</a>";
echo "</div>";

echo "</div>";

echo "</body>";
echo "</html>";
?>
