<?php
$pageTitle = 'اختبار نظام إدارة المحتوى';
require_once 'includes/header.php';

// إحصائيات النظام
$stats = [
    'influencers' => [
        'total' => fetchOne("SELECT COUNT(*) as count FROM influencers_content")['count'] ?? 0,
        'published' => fetchOne("SELECT COUNT(*) as count FROM influencers_content WHERE status = 'published'")['count'] ?? 0,
        'videos' => fetchOne("SELECT COUNT(*) as count FROM influencers_content WHERE content_type = 'video'")['count'] ?? 0,
        'reviews' => fetchOne("SELECT COUNT(*) as count FROM influencers_content WHERE content_type = 'review'")['count'] ?? 0
    ],
    'guidelines' => [
        'total' => fetchOne("SELECT COUNT(*) as count FROM guidelines")['count'] ?? 0,
        'published' => fetchOne("SELECT COUNT(*) as count FROM guidelines WHERE status = 'published'")['count'] ?? 0,
        'critical' => fetchOne("SELECT COUNT(*) as count FROM guidelines WHERE importance_level = 'critical'")['count'] ?? 0,
        'with_video' => fetchOne("SELECT COUNT(*) as count FROM guidelines WHERE video_url IS NOT NULL AND video_url != ''")['count'] ?? 0
    ],
    'categories' => [
        'total' => fetchOne("SELECT COUNT(*) as count FROM content_categories")['count'] ?? 0,
        'active' => fetchOne("SELECT COUNT(*) as count FROM content_categories WHERE status = 'active'")['count'] ?? 0
    ],
    'views' => [
        'total' => fetchOne("SELECT COUNT(*) as count FROM content_views")['count'] ?? 0,
        'today' => fetchOne("SELECT COUNT(*) as count FROM content_views WHERE DATE(created_at) = CURDATE()")['count'] ?? 0
    ]
];

// اختبار الاتصال بقاعدة البيانات
$dbTest = [
    'connection' => false,
    'tables' => [],
    'sample_data' => []
];

try {
    // اختبار الاتصال
    $testQuery = fetchOne("SELECT 1 as test");
    $dbTest['connection'] = $testQuery['test'] == 1;
    
    // اختبار الجداول
    $tables = ['influencers_content', 'guidelines', 'content_categories', 'content_views', 'content_change_log'];
    foreach ($tables as $table) {
        try {
            $result = fetchOne("SELECT COUNT(*) as count FROM {$table}");
            $dbTest['tables'][$table] = [
                'exists' => true,
                'count' => $result['count']
            ];
        } catch (Exception $e) {
            $dbTest['tables'][$table] = [
                'exists' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    // بيانات عينة
    $dbTest['sample_data']['influencer'] = fetchOne("SELECT * FROM influencers_content LIMIT 1");
    $dbTest['sample_data']['guideline'] = fetchOne("SELECT * FROM guidelines LIMIT 1");
    $dbTest['sample_data']['category'] = fetchOne("SELECT * FROM content_categories LIMIT 1");
    
} catch (Exception $e) {
    $dbTest['connection'] = false;
    $dbTest['error'] = $e->getMessage();
}

// اختبار الملفات
$fileTests = [
    'pages' => [
        'influencers.php' => file_exists('influencers.php'),
        'guidelines.php' => file_exists('guidelines.php'),
        'admin/influencers.php' => file_exists('admin/influencers.php'),
        'admin/guidelines.php' => file_exists('admin/guidelines.php')
    ],
    'ajax' => [
        'ajax/get_guideline.php' => file_exists('ajax/get_guideline.php'),
        'ajax/track_view.php' => file_exists('ajax/track_view.php'),
        'admin/ajax/get_influencer_content.php' => file_exists('admin/ajax/get_influencer_content.php'),
        'admin/ajax/get_guideline.php' => file_exists('admin/ajax/get_guideline.php')
    ],
    'assets' => [
        'assets/css/content-management.css' => file_exists('assets/css/content-management.css'),
        'assets/js/content-management.js' => file_exists('assets/js/content-management.js')
    ],
    'database' => [
        'database/content_management_system.sql' => file_exists('database/content_management_system.sql')
    ]
];

// اختبار الدوال المساعدة
$functionTests = [
    'fetchOne' => function_exists('fetchOne'),
    'fetchAll' => function_exists('fetchAll'),
    'executeQuery' => function_exists('executeQuery'),
    'sanitizeInput' => function_exists('sanitizeInput'),
    'uploadImage' => function_exists('uploadImage')
];
?>

<link rel="stylesheet" href="assets/css/content-management.css">

<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="text-center mb-5">
        <h1 class="display-4 text-primary mb-3">
            <i class="bi bi-gear-fill"></i> اختبار نظام إدارة المحتوى
        </h1>
        <p class="lead text-muted">فحص شامل لجميع مكونات النظام والتأكد من عملها بشكل صحيح</p>
    </div>

    <!-- System Status Overview -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-gradient text-white text-center py-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <h3 class="mb-0"><i class="bi bi-speedometer2"></i> حالة النظام العامة</h3>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="p-3">
                                <i class="bi bi-database-check display-4 <?php echo $dbTest['connection'] ? 'text-success' : 'text-danger'; ?>"></i>
                                <h5 class="mt-2">قاعدة البيانات</h5>
                                <span class="badge <?php echo $dbTest['connection'] ? 'bg-success' : 'bg-danger'; ?>">
                                    <?php echo $dbTest['connection'] ? 'متصلة' : 'غير متصلة'; ?>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="p-3">
                                <i class="bi bi-files display-4 text-info"></i>
                                <h5 class="mt-2">الملفات</h5>
                                <span class="badge bg-info">
                                    <?php 
                                    $totalFiles = 0;
                                    $existingFiles = 0;
                                    foreach ($fileTests as $category) {
                                        foreach ($category as $file => $exists) {
                                            $totalFiles++;
                                            if ($exists) $existingFiles++;
                                        }
                                    }
                                    echo "{$existingFiles}/{$totalFiles}";
                                    ?>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="p-3">
                                <i class="bi bi-code-square display-4 text-warning"></i>
                                <h5 class="mt-2">الدوال</h5>
                                <span class="badge bg-warning">
                                    <?php 
                                    $totalFunctions = count($functionTests);
                                    $existingFunctions = array_sum($functionTests);
                                    echo "{$existingFunctions}/{$totalFunctions}";
                                    ?>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="p-3">
                                <i class="bi bi-eye display-4 text-primary"></i>
                                <h5 class="mt-2">المشاهدات اليوم</h5>
                                <span class="badge bg-primary"><?php echo number_format($stats['views']['today']); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-5">
        <div class="col-lg-6">
            <div class="card stats-card stats-card-primary text-white h-100">
                <div class="card-header">
                    <h4><i class="bi bi-people-fill"></i> إحصائيات المؤثرين</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <div class="display-6 fw-bold"><?php echo $stats['influencers']['total']; ?></div>
                                <div>إجمالي المحتوى</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="display-6 fw-bold"><?php echo $stats['influencers']['published']; ?></div>
                                <div>منشور</div>
                            </div>
                        </div>
                    </div>
                    <hr class="my-3">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 fw-bold"><?php echo $stats['influencers']['videos']; ?></div>
                                <div>فيديوهات</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 fw-bold"><?php echo $stats['influencers']['reviews']; ?></div>
                                <div>مراجعات</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card stats-card stats-card-success text-white h-100">
                <div class="card-header">
                    <h4><i class="bi bi-book-fill"></i> إحصائيات الإرشادات</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <div class="display-6 fw-bold"><?php echo $stats['guidelines']['total']; ?></div>
                                <div>إجمالي الإرشادات</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="display-6 fw-bold"><?php echo $stats['guidelines']['published']; ?></div>
                                <div>منشور</div>
                            </div>
                        </div>
                    </div>
                    <hr class="my-3">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 fw-bold"><?php echo $stats['guidelines']['critical']; ?></div>
                                <div>حرجة</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 fw-bold"><?php echo $stats['guidelines']['with_video']; ?></div>
                                <div>مع فيديو</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Database Tests -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0"><i class="bi bi-database"></i> اختبار قاعدة البيانات</h4>
                </div>
                <div class="card-body">
                    <?php if ($dbTest['connection']): ?>
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle-fill"></i> الاتصال بقاعدة البيانات يعمل بشكل صحيح
                        </div>
                        
                        <h5>حالة الجداول:</h5>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>اسم الجدول</th>
                                        <th>الحالة</th>
                                        <th>عدد السجلات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($dbTest['tables'] as $table => $info): ?>
                                        <tr>
                                            <td><code><?php echo $table; ?></code></td>
                                            <td>
                                                <?php if ($info['exists']): ?>
                                                    <span class="badge bg-success">موجود</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">غير موجود</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($info['exists']): ?>
                                                    <?php echo number_format($info['count']); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle-fill"></i> فشل الاتصال بقاعدة البيانات
                            <?php if (isset($dbTest['error'])): ?>
                                <br><small><?php echo htmlspecialchars($dbTest['error']); ?></small>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- File Tests -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0"><i class="bi bi-files"></i> اختبار الملفات</h4>
                </div>
                <div class="card-body">
                    <?php foreach ($fileTests as $category => $files): ?>
                        <h5 class="text-capitalize"><?php echo $category; ?>:</h5>
                        <div class="row mb-3">
                            <?php foreach ($files as $file => $exists): ?>
                                <div class="col-md-6 mb-2">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-<?php echo $exists ? 'check-circle-fill text-success' : 'x-circle-fill text-danger'; ?> me-2"></i>
                                        <code class="<?php echo $exists ? 'text-success' : 'text-danger'; ?>"><?php echo $file; ?></code>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Function Tests -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h4 class="mb-0"><i class="bi bi-code-square"></i> اختبار الدوال</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($functionTests as $function => $exists): ?>
                            <div class="col-md-4 mb-2">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-<?php echo $exists ? 'check-circle-fill text-success' : 'x-circle-fill text-danger'; ?> me-2"></i>
                                    <code class="<?php echo $exists ? 'text-success' : 'text-danger'; ?>"><?php echo $function; ?>()</code>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Links -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="bi bi-link-45deg"></i> روابط سريعة للاختبار</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>الصفحات العامة:</h5>
                            <div class="list-group">
                                <a href="influencers.php" class="list-group-item list-group-item-action" target="_blank">
                                    <i class="bi bi-people"></i> صفحة المؤثرين والمراجعات
                                </a>
                                <a href="guidelines.php" class="list-group-item list-group-item-action" target="_blank">
                                    <i class="bi bi-book"></i> صفحة الإرشادات والتعليمات
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>لوحات التحكم الإدارية:</h5>
                            <div class="list-group">
                                <a href="admin/influencers.php" class="list-group-item list-group-item-action" target="_blank">
                                    <i class="bi bi-gear"></i> إدارة المؤثرين والمراجعات
                                </a>
                                <a href="admin/guidelines.php" class="list-group-item list-group-item-action" target="_blank">
                                    <i class="bi bi-gear"></i> إدارة الإرشادات والتعليمات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sample Data Preview -->
    <?php if ($dbTest['connection'] && !empty($dbTest['sample_data'])): ?>
        <div class="row mb-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0"><i class="bi bi-eye"></i> معاينة البيانات العينة</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($dbTest['sample_data']['influencer']): ?>
                            <h5>عينة من محتوى المؤثرين:</h5>
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h6><?php echo htmlspecialchars($dbTest['sample_data']['influencer']['influencer_name']); ?></h6>
                                    <p class="text-muted"><?php echo htmlspecialchars(mb_substr($dbTest['sample_data']['influencer']['content_text'], 0, 100)) . '...'; ?></p>
                                    <small class="text-muted">النوع: <?php echo $dbTest['sample_data']['influencer']['content_type']; ?></small>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if ($dbTest['sample_data']['guideline']): ?>
                            <h5>عينة من الإرشادات:</h5>
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h6><?php echo htmlspecialchars($dbTest['sample_data']['guideline']['title']); ?></h6>
                                    <p class="text-muted"><?php echo htmlspecialchars(mb_substr($dbTest['sample_data']['guideline']['content'], 0, 100)) . '...'; ?></p>
                                    <small class="text-muted">الأهمية: <?php echo $dbTest['sample_data']['guideline']['importance_level']; ?></small>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Installation Instructions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h4 class="mb-0"><i class="bi bi-info-circle"></i> تعليمات التثبيت والاستخدام</h4>
                </div>
                <div class="card-body">
                    <div class="accordion" id="instructionsAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingOne">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                                    1. تثبيت قاعدة البيانات
                                </button>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#instructionsAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>قم بتشغيل ملف <code>database/content_management_system.sql</code> في قاعدة البيانات</li>
                                        <li>تأكد من إنشاء جميع الجداول بنجاح</li>
                                        <li>تحقق من إدراج البيانات الافتراضية للتصنيفات</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingTwo">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                                    2. إعداد الملفات
                                </button>
                            </h2>
                            <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#instructionsAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>تأكد من وجود جميع الملفات في المواقع الصحيحة</li>
                                        <li>قم بإنشاء مجلد <code>uploads/influencers/</code> وتعيين الصلاحيات المناسبة</li>
                                        <li>تأكد من تضمين ملفات CSS و JavaScript في الصفحات</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingThree">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree">
                                    3. الاستخدام
                                </button>
                            </h2>
                            <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#instructionsAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>ادخل إلى لوحة التحكم الإدارية لإضافة المحتوى</li>
                                        <li>قم بإنشاء التصنيفات المناسبة</li>
                                        <li>أضف محتوى المؤثرين والإرشادات</li>
                                        <li>اختبر الصفحات العامة للتأكد من العرض الصحيح</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="assets/js/content-management.js"></script>

<?php require_once 'includes/footer.php'; ?>
