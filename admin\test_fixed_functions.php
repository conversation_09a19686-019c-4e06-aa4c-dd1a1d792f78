<?php
require_once '../config/config.php';
requireAdminLogin();

echo "<h2>اختبار الدوال المُصلحة</h2>";

$allTestsPassed = true;
$testResults = [];

// Test 1: Basic database connection
echo "<h3>1. اختبار الاتصال بقاعدة البيانات</h3>";
try {
    $test = fetchOne("SELECT 1 as test");
    if ($test) {
        echo "<p style='color: green;'>✅ الاتصال يعمل بشكل صحيح</p>";
        $testResults['connection'] = true;
    } else {
        echo "<p style='color: red;'>❌ مشكلة في الاتصال</p>";
        $testResults['connection'] = false;
        $allTestsPassed = false;
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</p>";
    $testResults['connection'] = false;
    $allTestsPassed = false;
}

// Test 2: Insert function
echo "<h3>2. اختبار دالة الإدراج</h3>";
$testData = [
    'name' => 'منتج اختبار الإصلاح ' . date('Y-m-d H:i:s'),
    'description' => 'منتج لاختبار الدوال المُصلحة',
    'price' => 15.50,
    'stock' => 10,
    'status' => 'active'
];

try {
    $insertId = insertData('products', $testData);
    if ($insertId && $insertId > 0) {
        echo "<p style='color: green;'>✅ الإدراج نجح (ID: $insertId)</p>";
        $testResults['insert'] = $insertId;
    } else {
        echo "<p style='color: red;'>❌ الإدراج فشل</p>";
        $testResults['insert'] = false;
        $allTestsPassed = false;
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الإدراج: " . $e->getMessage() . "</p>";
    $testResults['insert'] = false;
    $allTestsPassed = false;
}

// Test 3: Update function (basic fields)
if ($testResults['insert']) {
    echo "<h3>3. اختبار دالة التحديث (الحقول الأساسية)</h3>";
    $testId = $testResults['insert'];
    
    try {
        $updateData = ['status' => 'inactive', 'stock' => 5];
        $updateResult = updateData('products', $updateData, 'id = ?', [$testId]);
        
        if ($updateResult !== false && $updateResult > 0) {
            echo "<p style='color: green;'>✅ التحديث نجح (صفوف محدثة: $updateResult)</p>";
            
            // Verify the update
            $updatedProduct = fetchOne("SELECT status, stock FROM products WHERE id = ?", [$testId]);
            if ($updatedProduct && $updatedProduct['status'] == 'inactive' && $updatedProduct['stock'] == 5) {
                echo "<p style='color: green;'>✅ التحقق من التحديث نجح</p>";
                $testResults['update_basic'] = true;
            } else {
                echo "<p style='color: red;'>❌ التحقق من التحديث فشل</p>";
                $testResults['update_basic'] = false;
                $allTestsPassed = false;
            }
        } else {
            echo "<p style='color: red;'>❌ التحديث فشل (النتيجة: " . var_export($updateResult, true) . ")</p>";
            $testResults['update_basic'] = false;
            $allTestsPassed = false;
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في التحديث: " . $e->getMessage() . "</p>";
        $testResults['update_basic'] = false;
        $allTestsPassed = false;
    }
}

// Test 4: Update function (new fields) - only if columns exist
echo "<h3>4. اختبار دالة التحديث (الحقول الجديدة)</h3>";
$columns = fetchAll("SHOW COLUMNS FROM products");
$columnNames = array_column($columns, 'Field');
$hasNewColumns = in_array('ingredients', $columnNames) && in_array('usage_instructions', $columnNames);

if ($hasNewColumns && $testResults['insert']) {
    $testId = $testResults['insert'];
    
    try {
        $updateDataNew = [
            'ingredients' => 'مكونات محدثة للاختبار',
            'usage_instructions' => 'تعليمات استخدام محدثة',
            'image_url_1' => 'https://via.placeholder.com/300x300',
            'video_url' => 'https://www.youtube.com/watch?v=test'
        ];
        
        $updateResult = updateData('products', $updateDataNew, 'id = ?', [$testId]);
        
        if ($updateResult !== false && $updateResult > 0) {
            echo "<p style='color: green;'>✅ تحديث الحقول الجديدة نجح (صفوف محدثة: $updateResult)</p>";
            
            // Verify the update
            $updatedProduct = fetchOne("SELECT ingredients, usage_instructions, image_url_1, video_url FROM products WHERE id = ?", [$testId]);
            if ($updatedProduct && $updatedProduct['ingredients'] == 'مكونات محدثة للاختبار') {
                echo "<p style='color: green;'>✅ التحقق من تحديث الحقول الجديدة نجح</p>";
                $testResults['update_new'] = true;
            } else {
                echo "<p style='color: red;'>❌ التحقق من تحديث الحقول الجديدة فشل</p>";
                $testResults['update_new'] = false;
                $allTestsPassed = false;
            }
        } else {
            echo "<p style='color: red;'>❌ تحديث الحقول الجديدة فشل (النتيجة: " . var_export($updateResult, true) . ")</p>";
            $testResults['update_new'] = false;
            $allTestsPassed = false;
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في تحديث الحقول الجديدة: " . $e->getMessage() . "</p>";
        $testResults['update_new'] = false;
        $allTestsPassed = false;
    }
} else {
    if (!$hasNewColumns) {
        echo "<p style='color: orange;'>⚠️ الحقول الجديدة غير موجودة - تخطي الاختبار</p>";
        echo "<p><a href='migrate_products_table.php'>إضافة الحقول الجديدة</a></p>";
    } else {
        echo "<p style='color: orange;'>⚠️ تخطي اختبار الحقول الجديدة (لا يوجد منتج اختبار)</p>";
    }
    $testResults['update_new'] = 'skipped';
}

// Test 5: Delete function
if ($testResults['insert']) {
    echo "<h3>5. اختبار دالة الحذف</h3>";
    $testId = $testResults['insert'];
    
    try {
        $deleteResult = deleteData('products', 'id = ?', [$testId]);
        
        if ($deleteResult !== false && $deleteResult > 0) {
            echo "<p style='color: green;'>✅ الحذف نجح (صفوف محذوفة: $deleteResult)</p>";
            
            // Verify the deletion
            $deletedProduct = fetchOne("SELECT id FROM products WHERE id = ?", [$testId]);
            if (!$deletedProduct) {
                echo "<p style='color: green;'>✅ التحقق من الحذف نجح</p>";
                $testResults['delete'] = true;
            } else {
                echo "<p style='color: red;'>❌ التحقق من الحذف فشل - المنتج ما زال موجود</p>";
                $testResults['delete'] = false;
                $allTestsPassed = false;
            }
        } else {
            echo "<p style='color: red;'>❌ الحذف فشل (النتيجة: " . var_export($deleteResult, true) . ")</p>";
            $testResults['delete'] = false;
            $allTestsPassed = false;
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في الحذف: " . $e->getMessage() . "</p>";
        $testResults['delete'] = false;
        $allTestsPassed = false;
    }
}

// Test 6: Real-world scenario test
echo "<h3>6. اختبار سيناريو حقيقي</h3>";
echo "<p>اختبار العمليات كما تحدث في إدارة المنتجات...</p>";

try {
    // Create a product like in add_product.php
    $realTestData = [
        'name' => 'منتج سيناريو حقيقي',
        'description' => 'وصف المنتج',
        'short_description' => 'وصف مختصر',
        'price' => 25.00,
        'discount' => 10.00,
        'stock' => 15,
        'category_id' => 1, // Assuming category 1 exists
        'is_featured' => 1,
        'status' => 'active'
    ];
    
    $realId = insertData('products', $realTestData);
    if ($realId) {
        echo "<p style='color: green;'>✅ إنشاء منتج حقيقي نجح (ID: $realId)</p>";
        
        // Test status toggle like in products.php
        $product = fetchOne("SELECT status FROM products WHERE id = ?", [$realId]);
        if ($product) {
            $newStatus = $product['status'] == 'active' ? 'inactive' : 'active';
            $toggleResult = updateData('products', ['status' => $newStatus], 'id = ?', [$realId]);
            
            if ($toggleResult !== false && $toggleResult > 0) {
                echo "<p style='color: green;'>✅ تبديل الحالة نجح</p>";
            } else {
                echo "<p style='color: red;'>❌ تبديل الحالة فشل</p>";
                $allTestsPassed = false;
            }
        }
        
        // Clean up
        deleteData('products', 'id = ?', [$realId]);
        echo "<p style='color: green;'>✅ تنظيف البيانات التجريبية</p>";
        
    } else {
        echo "<p style='color: red;'>❌ إنشاء منتج حقيقي فشل</p>";
        $allTestsPassed = false;
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في السيناريو الحقيقي: " . $e->getMessage() . "</p>";
    $allTestsPassed = false;
}

// Final summary
echo "<hr>";
echo "<h3>الملخص النهائي</h3>";

if ($allTestsPassed) {
    echo "<div style='background: #d4edda; padding: 20px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h4 style='color: #155724;'>🎉 جميع الاختبارات نجحت!</h4>";
    echo "<p><strong>النتيجة:</strong> دوال قاعدة البيانات تعمل بشكل صحيح الآن.</p>";
    echo "<ul>";
    echo "<li>✅ تحديث حالة المنتجات</li>";
    echo "<li>✅ تعديل المنتجات</li>";
    echo "<li>✅ حذف المنتجات</li>";
    echo "</ul>";
    echo "<p><strong>يمكنك الآن استخدام نظام إدارة المنتجات بشكل طبيعي.</strong></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h4 style='color: #721c24;'>❌ بعض الاختبارات فشلت</h4>";
    echo "<p>يرجى مراجعة الأخطاء أعلاه والتواصل للحصول على مساعدة إضافية.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='products.php' class='btn btn-primary'>العودة إلى إدارة المنتجات</a></p>";
echo "<p><a href='debug_database_operations.php'>تشغيل التشخيص المفصل</a></p>";
?>
