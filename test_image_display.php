<?php
require_once 'config/config.php';

$pageTitle = 'اختبار عرض صور المنتجات';

echo "<h1>🖼️ اختبار عرض صور المنتجات</h1>";
echo "<p>اختبار عرض الصور في جميع صفحات الموقع.</p>";

// جلب منتج للاختبار
$testProduct = fetchOne("SELECT * FROM products WHERE status = 'active' LIMIT 1");

if (!$testProduct) {
    echo "<div class='alert alert-warning'>لا توجد منتجات للاختبار. يرجى إضافة منتج أولاً.</div>";
    exit;
}

echo "<h2>1. بيانات المنتج للاختبار</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>المنتج: " . htmlspecialchars($testProduct['name']) . " (ID: {$testProduct['id']})</h4>";
echo "<ul>";
echo "<li><strong>الصورة المرفوعة (image):</strong> " . ($testProduct['image'] ? $testProduct['image'] : 'غير موجودة') . "</li>";

for ($i = 1; $i <= 5; $i++) {
    $imageField = 'image_url_' . $i;
    echo "<li><strong>الصورة الخارجية {$i} (image_url_{$i}):</strong> " . ($testProduct[$imageField] ? $testProduct[$imageField] : 'غير موجودة') . "</li>";
}
echo "</ul>";
echo "</div>";

// اختبار منطق اختيار الصورة
echo "<h2>2. اختبار منطق اختيار الصورة</h2>";

function getProductImageUrl($product) {
    // البحث عن أول صورة متاحة من الصور الخمس
    $imageUrl = 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج');
    for ($i = 1; $i <= 5; $i++) {
        if (!empty($product['image_url_' . $i])) {
            $imageUrl = $product['image_url_' . $i];
            break;
        }
    }
    // إذا لم توجد صور خارجية، استخدم الصورة المرفوعة
    if ($imageUrl === 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج') && !empty($product['image'])) {
        $imageUrl = UPLOAD_URL . '/' . $product['image'];
    }
    return $imageUrl;
}

$selectedImageUrl = getProductImageUrl($testProduct);

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>الصورة المختارة:</h4>";
echo "<p><strong>URL:</strong> " . htmlspecialchars($selectedImageUrl) . "</p>";

// عرض الصورة
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<img src='{$selectedImageUrl}' alt='صورة المنتج' style='max-width: 300px; height: 200px; object-fit: cover; border: 1px solid #ddd; border-radius: 5px;'>";
echo "</div>";
echo "</div>";

echo "<h2>3. اختبار الصفحات المختلفة</h2>";

$pages = [
    'index.php' => 'الصفحة الرئيسية',
    'products.php' => 'صفحة المنتجات',
    'offers.php' => 'صفحة العروض',
    'cart.php' => 'سلة التسوق'
];

echo "<div class='row' style='margin: 20px 0;'>";
foreach ($pages as $page => $title) {
    echo "<div class='col-md-6 mb-3'>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
    echo "<h5>📄 {$title}</h5>";
    echo "<p>اختبر عرض الصور في هذه الصفحة:</p>";
    echo "<a href='{$page}' target='_blank' class='btn btn-primary btn-sm'>فتح الصفحة</a>";
    echo "</div>";
    echo "</div>";
}
echo "</div>";

echo "<h2>4. التحسينات المطبقة</h2>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
echo "<h4>✅ الإصلاحات المطبقة:</h4>";
echo "<ul>";
echo "<li>✅ <strong>الصفحة الرئيسية (index.php):</strong> تم إصلاح عرض الصور للمنتجات المميزة والمخفضة</li>";
echo "<li>✅ <strong>صفحة العروض (offers.php):</strong> تم إصلاح عرض الصور للمنتجات المخفضة</li>";
echo "<li>✅ <strong>سلة التسوق (cart.php):</strong> تم إصلاح استعلام جلب البيانات وعرض الصور</li>";
echo "<li>✅ <strong>منطق اختيار الصورة:</strong> يتم البحث في الصور الخارجية أولاً ثم الصورة المرفوعة</li>";
echo "</ul>";
echo "</div>";

echo "<h2>5. منطق عرض الصور الجديد</h2>";
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
echo "<h4>🔄 ترتيب البحث عن الصور:</h4>";
echo "<ol>";
echo "<li><strong>الصور الخارجية:</strong> البحث في image_url_1 إلى image_url_5</li>";
echo "<li><strong>الصورة المرفوعة:</strong> استخدام حقل image إذا لم توجد صور خارجية</li>";
echo "<li><strong>صورة افتراضية:</strong> عرض placeholder إذا لم توجد أي صور</li>";
echo "</ol>";

echo "<h4>📝 الكود المطبق:</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;'>";
echo htmlspecialchars('<?php
// البحث عن أول صورة متاحة من الصور الخمس
$imageUrl = \'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=\' . urlencode(\'صورة المنتج\');
for ($i = 1; $i <= 5; $i++) {
    if (!empty($product[\'image_url_\' . $i])) {
        $imageUrl = $product[\'image_url_\' . $i];
        break;
    }
}
// إذا لم توجد صور خارجية، استخدم الصورة المرفوعة
if ($imageUrl === \'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=\' . urlencode(\'صورة المنتج\') && !empty($product[\'image\'])) {
    $imageUrl = UPLOAD_URL . \'/\' . $product[\'image\'];
}
?>
<img src="<?php echo $imageUrl; ?>" alt="صورة المنتج">');
echo "</pre>";
echo "</div>";

echo "<h2>6. اختبار قاعدة البيانات</h2>";

// اختبار وجود الحقول في قاعدة البيانات
$tableInfo = fetchAll("DESCRIBE products");
$imageFields = [];
foreach ($tableInfo as $field) {
    if (strpos($field['Field'], 'image') !== false) {
        $imageFields[] = $field['Field'];
    }
}

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
echo "<h4>🗃️ حقول الصور في جدول products:</h4>";
echo "<ul>";
foreach ($imageFields as $field) {
    echo "<li><strong>{$field}</strong></li>";
}
echo "</ul>";
echo "</div>";

// إحصائيات الصور
$stats = [
    'products_with_uploaded_image' => fetchOne("SELECT COUNT(*) as count FROM products WHERE image IS NOT NULL AND image != ''")['count'],
    'products_with_external_images' => fetchOne("SELECT COUNT(*) as count FROM products WHERE image_url_1 IS NOT NULL AND image_url_1 != ''")['count'],
    'total_products' => fetchOne("SELECT COUNT(*) as count FROM products")['count']
];

echo "<h2>7. إحصائيات الصور</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h4>📊 إحصائيات:</h4>";
echo "<ul>";
echo "<li><strong>إجمالي المنتجات:</strong> {$stats['total_products']}</li>";
echo "<li><strong>منتجات بصور مرفوعة:</strong> {$stats['products_with_uploaded_image']}</li>";
echo "<li><strong>منتجات بصور خارجية:</strong> {$stats['products_with_external_images']}</li>";
echo "</ul>";
echo "</div>";

echo "<h2>8. خطوات الاختبار</h2>";
echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px;'>";
echo "<h4>🧪 كيفية اختبار الإصلاحات:</h4>";
echo "<ol>";
echo "<li><strong>اختبار الصفحة الرئيسية:</strong>";
echo "<ul>";
echo "<li>انتقل إلى <a href='index.php' target='_blank'>الصفحة الرئيسية</a></li>";
echo "<li>تحقق من ظهور صور المنتجات المميزة</li>";
echo "<li>تحقق من ظهور صور المنتجات المخفضة</li>";
echo "</ul>";
echo "</li>";

echo "<li><strong>اختبار صفحة العروض:</strong>";
echo "<ul>";
echo "<li>انتقل إلى <a href='offers.php' target='_blank'>صفحة العروض</a></li>";
echo "<li>تحقق من ظهور صور جميع المنتجات المخفضة</li>";
echo "</ul>";
echo "</li>";

echo "<li><strong>اختبار سلة التسوق:</strong>";
echo "<ul>";
echo "<li>أضف منتجات إلى السلة من أي صفحة</li>";
echo "<li>انتقل إلى <a href='cart.php' target='_blank'>سلة التسوق</a></li>";
echo "<li>تحقق من ظهور صور المنتجات في السلة</li>";
echo "</ul>";
echo "</li>";

echo "<li><strong>مقارنة مع صفحة المنتجات:</strong>";
echo "<ul>";
echo "<li>انتقل إلى <a href='products.php' target='_blank'>صفحة المنتجات</a></li>";
echo "<li>تأكد من أن الصور تظهر بنفس الطريقة في جميع الصفحات</li>";
echo "</ul>";
echo "</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🎉 ملخص الإصلاح</h2>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>✅ تم إصلاح مشكلة عرض صور المنتجات بنجاح!</h3>";
echo "<h4>المشكلة:</h4>";
echo "<p>كانت الصور تظهر فقط في صفحة المنتجات ولا تظهر في الصفحة الرئيسية والعروض وسلة التسوق.</p>";

echo "<h4>السبب:</h4>";
echo "<p>الصفحات الأخرى كانت تستخدم حقل <code>image</code> فقط، بينما صفحة المنتجات تبحث في الصور الخارجية أولاً.</p>";

echo "<h4>الحل:</h4>";
echo "<ul>";
echo "<li>تطبيق نفس منطق البحث عن الصور في جميع الصفحات</li>";
echo "<li>البحث في الصور الخارجية (image_url_1 إلى image_url_5) أولاً</li>";
echo "<li>استخدام الصورة المرفوعة (image) كبديل</li>";
echo "<li>عرض صورة افتراضية إذا لم توجد أي صور</li>";
echo "</ul>";

echo "<h4>الصفحات المُصلحة:</h4>";
echo "<ul>";
echo "<li>✅ الصفحة الرئيسية (index.php)</li>";
echo "<li>✅ صفحة العروض (offers.php)</li>";
echo "<li>✅ سلة التسوق (cart.php)</li>";
echo "</ul>";

echo "<p><strong>الآن جميع صور المنتجات ستظهر بشكل متسق في جميع صفحات الموقع!</strong></p>";
echo "</div>";

echo "<p><em>اختبار مكتمل في " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    margin: 20px;
    background-color: #f8f9fa;
}

h1, h2, h3, h4 {
    color: #333;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
}

.btn:hover {
    background-color: #0056b3;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
}

.col-md-6 {
    flex: 0 0 50%;
    padding: 10px;
}

@media (max-width: 768px) {
    .col-md-6 {
        flex: 0 0 100%;
    }
}
</style>
