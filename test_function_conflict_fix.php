<?php
/**
 * Test that function conflicts have been resolved
 */

require_once 'config/config.php';

echo "<h2>Function Conflict Fix Test</h2>";

// Test that we only have one insertData function
echo "<h3>1. Function Existence Test</h3>";
if (function_exists('insertData')) {
    echo "✅ insertData function exists<br>";
} else {
    echo "❌ insertData function missing<br>";
    die("Critical error: insertData function not found");
}

if (function_exists('fetchOne')) {
    echo "✅ fetchOne function exists<br>";
} else {
    echo "❌ fetchOne function missing<br>";
}

if (function_exists('fetchAll')) {
    echo "✅ fetchAll function exists<br>";
} else {
    echo "❌ fetchAll function missing<br>";
}

// Test database connection
echo "<h3>2. Database Connection Test</h3>";
if ($pdo) {
    try {
        $pdo->query("SELECT 1");
        echo "✅ Database connection working<br>";
    } catch (Exception $e) {
        echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
        die();
    }
} else {
    echo "❌ PDO connection is null<br>";
    die();
}

// Test insertData function
echo "<h3>3. InsertData Function Test</h3>";
$testData = [
    'customer_name' => 'Function Conflict Test Customer',
    'customer_phone' => '07123456789',
    'address' => 'Test Address, Baghdad',
    'province' => 'بغداد',
    'subtotal' => 50000.00,
    'delivery_price' => 5000.00,
    'discount_amount' => 0.00,
    'total_price' => 55000.00,
    'payment_method' => 'cash_on_delivery',
    'status' => 'pending'
];

echo "Testing insertData with: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "<br>";

$orderId = insertData('orders', $testData);

if ($orderId) {
    echo "✅ insertData function working correctly - Order ID: {$orderId}<br>";
    
    // Test fetchOne
    $verifyOrder = fetchOne("SELECT * FROM orders WHERE id = ?", [$orderId]);
    if ($verifyOrder) {
        echo "✅ fetchOne function working - Order verified<br>";
        echo "Customer: " . $verifyOrder['customer_name'] . "<br>";
        echo "Total: " . number_format($verifyOrder['total_price']) . " IQD<br>";
    } else {
        echo "❌ fetchOne function failed<br>";
    }
    
    // Test order item insertion
    $itemData = [
        'order_id' => $orderId,
        'product_id' => 1,
        'product_name' => 'Test Product',
        'quantity' => 2,
        'price' => 25000.00,
        'total' => 50000.00
    ];
    
    $itemId = insertData('order_items', $itemData);
    if ($itemId) {
        echo "✅ Order item insertion working - Item ID: {$itemId}<br>";
    } else {
        echo "❌ Order item insertion failed<br>";
    }
    
    // Test fetchAll
    $orderItems = fetchAll("SELECT * FROM order_items WHERE order_id = ?", [$orderId]);
    if (count($orderItems) > 0) {
        echo "✅ fetchAll function working - Found " . count($orderItems) . " order items<br>";
    } else {
        echo "❌ fetchAll function failed or no items found<br>";
    }
    
    // Clean up test data
    $pdo->prepare("DELETE FROM order_items WHERE order_id = ?")->execute([$orderId]);
    $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$orderId]);
    echo "✅ Test data cleaned up<br>";
    
} else {
    echo "❌ insertData function failed<br>";
    echo "This indicates the function conflict has not been resolved.<br>";
}

echo "<h3>4. Summary</h3>";
if ($orderId) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h4>✅ Function Conflicts Resolved!</h4>";
    echo "<p>All database functions are now working correctly. The checkout process should work.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Issues Still Exist</h4>";
    echo "<p>Function conflicts may still be present. Check the error logs for more details.</p>";
    echo "</div>";
}

echo "<h3>5. Next Steps</h3>";
echo "<p>Now test the checkout process:</p>";
echo "<ul>";
echo "<li><a href='verify_checkout_fix.php' target='_blank'>Run Checkout Verification</a></li>";
echo "<li><a href='checkout.php' target='_blank'>Test Checkout Page</a></li>";
echo "<li><a href='test_checkout_form.html' target='_blank'>Manual Test Form</a></li>";
echo "</ul>";
?>
