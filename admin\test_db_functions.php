<?php
require_once '../config/config.php';
requireAdminLogin();

echo "<h2>اختبار دوال قاعدة البيانات</h2>";

// Test 1: Basic connection
echo "<h3>1. اختبار الاتصال الأساسي</h3>";
try {
    $test = fetchOne("SELECT 1 as test");
    echo "<p style='color: green;'>✅ الاتصال يعمل</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</p>";
    exit;
}

// Test 2: Check if new columns exist
echo "<h3>2. فحص الأعمدة الجديدة</h3>";
$columns = fetchAll("SHOW COLUMNS FROM products");
$columnNames = array_column($columns, 'Field');

$newColumns = ['ingredients', 'usage_instructions', 'image_url_1', 'image_url_2', 'image_url_3', 'video_url'];
$missingColumns = [];

foreach ($newColumns as $col) {
    if (in_array($col, $columnNames)) {
        echo "<p style='color: green;'>✅ العمود '$col' موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ العمود '$col' غير موجود</p>";
        $missingColumns[] = $col;
    }
}

if (!empty($missingColumns)) {
    echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
    echo "<strong>تحذير:</strong> يجب تشغيل تحديث قاعدة البيانات أولاً<br>";
    echo "<a href='migrate_products_table.php' style='color: #856404;'>انقر هنا لتشغيل التحديث</a>";
    echo "</div>";
}

// Test 3: Insert test
echo "<h3>3. اختبار الإدراج</h3>";
$testData = [
    'name' => 'منتج اختبار ' . date('Y-m-d H:i:s'),
    'description' => 'وصف منتج الاختبار',
    'price' => 10.50,
    'stock' => 5,
    'status' => 'inactive'
];

try {
    $insertId = insertData('products', $testData);
    if ($insertId) {
        echo "<p style='color: green;'>✅ تم إدراج منتج اختبار بنجاح (ID: $insertId)</p>";
        
        // Test 4: Update test
        echo "<h3>4. اختبار التحديث</h3>";
        $updateData = ['name' => 'منتج اختبار محدث ' . date('H:i:s')];
        $updateResult = updateData('products', $updateData, 'id = ?', [$insertId]);
        
        if ($updateResult !== false && $updateResult > 0) {
            echo "<p style='color: green;'>✅ تم تحديث المنتج بنجاح (صفوف محدثة: $updateResult)</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في تحديث المنتج (النتيجة: " . var_export($updateResult, true) . ")</p>";
        }
        
        // Test 5: Delete test
        echo "<h3>5. اختبار الحذف</h3>";
        $deleteResult = deleteData('products', 'id = ?', [$insertId]);
        
        if ($deleteResult !== false && $deleteResult > 0) {
            echo "<p style='color: green;'>✅ تم حذف المنتج بنجاح (صفوف محذوفة: $deleteResult)</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في حذف المنتج (النتيجة: " . var_export($deleteResult, true) . ")</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ فشل في إدراج منتج الاختبار</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاختبار: " . $e->getMessage() . "</p>";
}

// Test 6: Test with new columns (if they exist)
if (empty($missingColumns)) {
    echo "<h3>6. اختبار الأعمدة الجديدة</h3>";
    $testDataWithNewColumns = [
        'name' => 'منتج اختبار مع أعمدة جديدة ' . date('Y-m-d H:i:s'),
        'description' => 'وصف منتج الاختبار',
        'ingredients' => 'مكونات تجريبية',
        'usage_instructions' => 'تعليمات استخدام تجريبية',
        'image_url_1' => 'https://via.placeholder.com/300x300',
        'image_url_2' => 'https://via.placeholder.com/300x300',
        'image_url_3' => 'https://via.placeholder.com/300x300',
        'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        'price' => 15.75,
        'stock' => 10,
        'status' => 'inactive'
    ];
    
    try {
        $insertId = insertData('products', $testDataWithNewColumns);
        if ($insertId) {
            echo "<p style='color: green;'>✅ تم إدراج منتج مع الأعمدة الجديدة بنجاح (ID: $insertId)</p>";
            
            // Update test with new columns
            $updateDataNew = [
                'ingredients' => 'مكونات محدثة',
                'usage_instructions' => 'تعليمات محدثة',
                'video_url' => 'https://www.youtube.com/watch?v=updated'
            ];
            
            $updateResult = updateData('products', $updateDataNew, 'id = ?', [$insertId]);
            
            if ($updateResult !== false && $updateResult > 0) {
                echo "<p style='color: green;'>✅ تم تحديث الأعمدة الجديدة بنجاح</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في تحديث الأعمدة الجديدة</p>";
            }
            
            // Clean up
            deleteData('products', 'id = ?', [$insertId]);
            
        } else {
            echo "<p style='color: red;'>❌ فشل في إدراج منتج مع الأعمدة الجديدة</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار الأعمدة الجديدة: " . $e->getMessage() . "</p>";
    }
}

echo "<hr>";
echo "<p><a href='products.php'>العودة إلى إدارة المنتجات</a></p>";
echo "<p><a href='debug_products.php'>تشغيل التشخيص المفصل</a></p>";
?>
