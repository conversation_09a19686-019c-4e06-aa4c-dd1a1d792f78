<?php
// Handle AJAX requests and redirects BEFORE any output
require_once 'config/config.php';

// Handle AJAX cart operations
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    $response = ['success' => false, 'message' => '', 'data' => []];
    
    try {
        switch ($_POST['action']) {
            case 'update_quantity':
                $productId = (int)$_POST['product_id'];
                $quantity = (int)$_POST['quantity'];
                
                if ($quantity <= 0) {
                    removeFromCart($productId);
                    $response['message'] = 'تم حذف المنتج من السلة';
                } else {
                    updateCartQuantity($productId, $quantity);
                    $response['message'] = 'تم تحديث الكمية';
                }
                
                // Return updated cart totals
                $cart = getCart();
                $response['data']['cart_count'] = getCartItemCount();
                $response['data']['cart_total'] = calculateCartTotal();
                $response['success'] = true;
                break;
                
            case 'remove_item':
                $productId = (int)$_POST['product_id'];
                removeFromCart($productId);
                $response['success'] = true;
                $response['message'] = 'تم حذف المنتج من السلة';
                $response['data']['cart_count'] = getCartItemCount();
                $response['data']['cart_total'] = calculateCartTotal();
                break;
                
            case 'clear_cart':
                clearCart();
                $response['success'] = true;
                $response['message'] = 'تم إفراغ السلة';
                $response['data']['cart_count'] = 0;
                $response['data']['cart_total'] = 0;
                break;
                
            case 'apply_discount':
                $discountCode = sanitizeInput($_POST['discount_code']);
                if (!empty($discountCode)) {
                    $discount = fetchOne("
                        SELECT * FROM discount_codes 
                        WHERE code = ? AND status = 'active' 
                        AND (expiration_date IS NULL OR expiration_date >= CURDATE())
                        AND (usage_limit IS NULL OR used_count < usage_limit)
                    ", [$discountCode]);
                    
                    if ($discount) {
                        $subtotal = calculateCartSubtotal();
                        if ($subtotal >= $discount['min_order_amount']) {
                            $discountAmount = 0;
                            if ($discount['type'] == 'percentage') {
                                $discountAmount = $subtotal * $discount['amount'] / 100;
                            } else {
                                $discountAmount = $discount['amount'];
                            }
                            
                            $discountAmount = min($discountAmount, $subtotal);
                            
                            $_SESSION['applied_discount'] = [
                                'code' => $discountCode,
                                'amount' => $discountAmount,
                                'type' => $discount['type'],
                                'discount_value' => $discount['amount']
                            ];
                            
                            $response['success'] = true;
                            $response['message'] = 'تم تطبيق كود الخصم بنجاح!';
                            $response['data']['discount_amount'] = $discountAmount;
                        } else {
                            $response['message'] = 'الحد الأدنى للطلب لاستخدام هذا الكود هو ' . number_format($discount['min_order_amount']) . ' دينار';
                        }
                    } else {
                        $response['message'] = 'كود الخصم غير صحيح أو منتهي الصلاحية';
                    }
                } else {
                    $response['message'] = 'يرجى إدخال كود الخصم';
                }
                break;
                
            case 'remove_discount':
                unset($_SESSION['applied_discount']);
                $response['success'] = true;
                $response['message'] = 'تم إلغاء كود الخصم';
                break;
        }
    } catch (Exception $e) {
        $response['message'] = 'حدث خطأ: ' . $e->getMessage();
        error_log("Cart AJAX Error: " . $e->getMessage());
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit();
}

// جلب محتويات السلة
$cart = getCart();
$cartItems = [];
$subtotal = 0;

if (!empty($cart)) {
    $productIds = array_keys($cart);
    $placeholders = str_repeat('?,', count($productIds) - 1) . '?';
    
    $products = fetchAll("
        SELECT id, name, price, discount, image, stock, category_id,
               image_url_1, image_url_2, image_url_3, image_url_4, image_url_5
        FROM products
        WHERE id IN ($placeholders) AND status = 'active'
    ", $productIds);
    
    foreach ($products as $product) {
        $quantity = $cart[$product['id']];
        $originalPrice = $product['price'];
        $price = $originalPrice;
        
        // تطبيق الخصم إن وجد
        if ($product['discount'] > 0) {
            $price = $originalPrice - ($originalPrice * $product['discount'] / 100);
        }
        
        $total = $price * $quantity;
        $subtotal += $total;
        
        $cartItems[] = [
            'product' => $product,
            'quantity' => $quantity,
            'original_price' => $originalPrice,
            'price' => $price,
            'total' => $total,
            'discount_amount' => $originalPrice - $price,
            'has_discount' => $product['discount'] > 0
        ];
    }
}

// معالجة كود الخصم المطبق
$appliedDiscount = $_SESSION['applied_discount'] ?? null;
$discountAmount = $appliedDiscount['amount'] ?? 0;

// حساب تكلفة التوصيل
$deliveryPrice = 5000; // Default delivery price
$freeDeliveryThreshold = 100000; // Free delivery threshold

if ($subtotal >= $freeDeliveryThreshold) {
    $deliveryPrice = 0;
}

$finalTotal = $subtotal - $discountAmount + $deliveryPrice;

// Now include header after all processing
$pageTitle = 'سلة التسوق';
require_once 'includes/header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="bg-light py-3">
    <div class="container">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="<?= SITE_URL ?>">الرئيسية</a></li>
            <li class="breadcrumb-item active" aria-current="page">سلة التسوق</li>
        </ol>
    </div>
</nav>

<!-- Success/Error Messages -->
<div id="cartMessages" class="container mt-3" style="display: none;">
    <div id="cartAlert" class="alert alert-dismissible fade show" role="alert">
        <span id="cartAlertMessage"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
</div>

<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="bi bi-cart3"></i>
                    سلة التسوق
                    <?php if (!empty($cartItems)): ?>
                        <span class="badge bg-primary" id="cartItemCount"><?= getCartItemCount() ?></span>
                    <?php endif; ?>
                </h2>
                <?php if (!empty($cartItems)): ?>
                    <button type="button" class="btn btn-outline-danger" onclick="clearEntireCart()">
                        <i class="bi bi-trash"></i>
                        إفراغ السلة
                    </button>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if (empty($cartItems)): ?>
        <!-- Empty Cart -->
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="bi bi-cart-x display-1 text-muted mb-3"></i>
                    <h3 class="text-muted">سلة التسوق فارغة</h3>
                    <p class="text-muted mb-4">لم تقم بإضافة أي منتجات إلى سلة التسوق بعد</p>
                    <a href="<?= SITE_URL ?>/products.php" class="btn btn-primary btn-lg">
                        <i class="bi bi-shop"></i>
                        تصفح المنتجات
                    </a>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Cart Items -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul"></i>
                            المنتجات في السلة
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div id="cartItemsContainer">
                            <?php foreach ($cartItems as $index => $item): ?>
                                <div class="cart-item border-bottom p-3" data-product-id="<?= $item['product']['id'] ?>">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <?php
                                            // البحث عن أول صورة متاحة من الصور الخمس
                                            $imageUrl = '';
                                            for ($i = 1; $i <= 5; $i++) {
                                                if (!empty($item['product']['image_url_' . $i])) {
                                                    $imageUrl = $item['product']['image_url_' . $i];
                                                    break;
                                                }
                                            }
                                            // إذا لم توجد صور خارجية، استخدم الصورة المرفوعة
                                            if (empty($imageUrl) && !empty($item['product']['image'])) {
                                                $imageUrl = SITE_URL . '/uploads/products/' . $item['product']['image'];
                                            }
                                            ?>
                                            <?php if (!empty($imageUrl)): ?>
                                                <img src="<?= $imageUrl ?>"
                                                     alt="<?= htmlspecialchars($item['product']['name']) ?>"
                                                     class="img-fluid rounded">
                                            <?php else: ?>
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 80px;">
                                                    <i class="bi bi-image text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <h6 class="mb-1"><?= htmlspecialchars($item['product']['name']) ?></h6>
                                            <div class="text-muted small">
                                                <?php if ($item['has_discount']): ?>
                                                    <span class="text-decoration-line-through"><?= number_format($item['original_price']) ?> دينار</span>
                                                    <span class="text-success fw-bold"><?= number_format($item['price']) ?> دينار</span>
                                                    <span class="badge bg-success">خصم <?= $item['product']['discount'] ?>%</span>
                                                <?php else: ?>
                                                    <span class="fw-bold"><?= number_format($item['price']) ?> دينار</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-3">
                                            <div class="input-group">
                                                <button class="btn btn-outline-secondary" type="button" 
                                                        onclick="updateQuantity(<?= $item['product']['id'] ?>, <?= $item['quantity'] - 1 ?>)">
                                                    <i class="bi bi-dash"></i>
                                                </button>
                                                <input type="number" class="form-control text-center quantity-input" 
                                                       value="<?= $item['quantity'] ?>" min="1" max="<?= $item['product']['stock'] ?>"
                                                       onchange="updateQuantity(<?= $item['product']['id'] ?>, this.value)">
                                                <button class="btn btn-outline-secondary" type="button" 
                                                        onclick="updateQuantity(<?= $item['product']['id'] ?>, <?= $item['quantity'] + 1 ?>)">
                                                    <i class="bi bi-plus"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">متوفر: <?= $item['product']['stock'] ?></small>
                                        </div>
                                        
                                        <div class="col-md-2">
                                            <div class="text-end">
                                                <div class="fw-bold item-total"><?= number_format($item['total']) ?> دينار</div>
                                                <button class="btn btn-sm btn-outline-danger mt-1" 
                                                        onclick="removeItem(<?= $item['product']['id'] ?>)">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Continue Shopping -->
                <div class="mt-3">
                    <a href="<?= SITE_URL ?>/products.php" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-right"></i>
                        متابعة التسوق
                    </a>
                </div>

            <!-- Cart Summary -->
            <div class="col-lg-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-calculator"></i>
                            ملخص الطلب
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Discount Code -->
                        <div class="mb-3">
                            <label class="form-label">كود الخصم</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="discountCode"
                                       placeholder="أدخل كود الخصم"
                                       value="<?= $appliedDiscount['code'] ?? '' ?>">
                                <button class="btn btn-outline-primary" type="button" onclick="applyDiscount()">
                                    تطبيق
                                </button>
                            </div>
                            <?php if ($appliedDiscount): ?>
                                <div class="mt-2">
                                    <span class="badge bg-success">
                                        تم تطبيق كود: <?= $appliedDiscount['code'] ?>
                                        <button type="button" class="btn-close btn-close-white ms-1"
                                                onclick="removeDiscount()" style="font-size: 0.7em;"></button>
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <hr>

                        <!-- Order Summary -->
                        <div class="d-flex justify-content-between mb-2">
                            <span>المجموع الفرعي:</span>
                            <span id="subtotalAmount"><?= number_format($subtotal) ?> دينار</span>
                        </div>

                        <?php if ($discountAmount > 0): ?>
                            <div class="d-flex justify-content-between mb-2 text-success">
                                <span>الخصم:</span>
                                <span id="discountAmount">-<?= number_format($discountAmount) ?> دينار</span>
                            </div>
                        <?php endif; ?>

                        <div class="d-flex justify-content-between mb-2">
                            <span>تكلفة التوصيل:</span>
                            <span id="deliveryAmount">
                                <?php if ($deliveryPrice == 0): ?>
                                    <span class="text-success">مجاني</span>
                                <?php else: ?>
                                    <?= number_format($deliveryPrice) ?> دينار
                                <?php endif; ?>
                            </span>
                        </div>

                        <?php if ($subtotal < $freeDeliveryThreshold && $deliveryPrice > 0): ?>
                            <div class="alert alert-info small">
                                <i class="bi bi-info-circle"></i>
                                أضف <?= number_format($freeDeliveryThreshold - $subtotal) ?> دينار للحصول على توصيل مجاني
                            </div>
                        <?php endif; ?>

                        <hr>

                        <div class="d-flex justify-content-between mb-3">
                            <strong>المجموع الكلي:</strong>
                            <strong class="text-primary" id="totalAmount"><?= number_format($finalTotal) ?> دينار</strong>
                        </div>

                        <!-- Checkout Button -->
                        <a href="<?= SITE_URL ?>/checkout.php" class="btn btn-primary btn-lg w-100">
                            <i class="bi bi-credit-card"></i>
                            إتمام الطلب
                        </a>

                        <!-- Payment Methods -->
                        <div class="mt-3 text-center">
                            <small class="text-muted">طرق الدفع المتاحة:</small>
                            <div class="mt-2">
                                <span class="badge bg-light text-dark me-1">
                                    <i class="bi bi-cash"></i>
                                    الدفع عند الاستلام
                                </span>
                                <span class="badge bg-light text-dark">
                                    <i class="bi bi-bank"></i>
                                    تحويل بنكي
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="card mt-3">
                    <div class="card-body text-center">
                        <i class="bi bi-shield-check text-success fs-4"></i>
                        <h6 class="mt-2">تسوق آمن</h6>
                        <small class="text-muted">معلوماتك محمية بأعلى معايير الأمان</small>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// Cart management functions
function updateQuantity(productId, quantity) {
    quantity = parseInt(quantity);

    if (quantity < 0) {
        quantity = 0;
    }

    // Show loading state
    const cartItem = document.querySelector(`[data-product-id="${productId}"]`);
    if (cartItem) {
        cartItem.style.opacity = '0.6';
    }

    fetch('cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=update_quantity&product_id=${productId}&quantity=${quantity}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (quantity === 0) {
                // Remove item from DOM
                cartItem.remove();

                // Check if cart is empty
                const remainingItems = document.querySelectorAll('.cart-item');
                if (remainingItems.length === 0) {
                    location.reload(); // Reload to show empty cart message
                }
            } else {
                // Update quantity input
                const quantityInput = cartItem.querySelector('.quantity-input');
                quantityInput.value = quantity;

                // Update item total (will be calculated by server)
                // For now, just restore opacity
                cartItem.style.opacity = '1';
            }

            // Update cart totals
            updateCartTotals(data.data);
            showMessage(data.message, 'success');
        } else {
            showMessage(data.message, 'error');
            cartItem.style.opacity = '1';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('حدث خطأ في الاتصال', 'error');
        cartItem.style.opacity = '1';
    });
}

function removeItem(productId) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج من السلة؟')) {
        const cartItem = document.querySelector(`[data-product-id="${productId}"]`);
        if (cartItem) {
            cartItem.style.opacity = '0.6';
        }

        fetch('cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=remove_item&product_id=${productId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                cartItem.remove();

                // Check if cart is empty
                const remainingItems = document.querySelectorAll('.cart-item');
                if (remainingItems.length === 0) {
                    location.reload(); // Reload to show empty cart message
                }

                updateCartTotals(data.data);
                showMessage(data.message, 'success');
            } else {
                showMessage(data.message, 'error');
                cartItem.style.opacity = '1';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('حدث خطأ في الاتصال', 'error');
            cartItem.style.opacity = '1';
        });
    }
}

function clearEntireCart() {
    if (confirm('هل أنت متأكد من إفراغ السلة بالكامل؟')) {
        fetch('cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=clear_cart'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload(); // Reload to show empty cart message
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('حدث خطأ في الاتصال', 'error');
        });
    }
}

function applyDiscount() {
    const discountCode = document.getElementById('discountCode').value.trim();

    if (!discountCode) {
        showMessage('يرجى إدخال كود الخصم', 'error');
        return;
    }

    fetch('cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=apply_discount&discount_code=${encodeURIComponent(discountCode)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload(); // Reload to show applied discount
        } else {
            showMessage(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('حدث خطأ في الاتصال', 'error');
    });
}

function removeDiscount() {
    fetch('cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=remove_discount'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload(); // Reload to remove discount display
        } else {
            showMessage(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('حدث خطأ في الاتصال', 'error');
    });
}

function updateCartTotals(data) {
    // Update cart item count in header and badge
    const cartCountElements = document.querySelectorAll('#cartItemCount, .cart-count');
    cartCountElements.forEach(element => {
        element.textContent = data.cart_count || 0;
    });

    // Update totals if available
    if (data.cart_total !== undefined) {
        const totalElement = document.getElementById('totalAmount');
        if (totalElement) {
            totalElement.textContent = new Intl.NumberFormat('ar-IQ').format(data.cart_total) + ' دينار';
        }
    }
}

function showMessage(message, type) {
    const messagesContainer = document.getElementById('cartMessages');
    const alertElement = document.getElementById('cartAlert');
    const messageElement = document.getElementById('cartAlertMessage');

    // Set message and type
    messageElement.textContent = message;
    alertElement.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;

    // Show message
    messagesContainer.style.display = 'block';

    // Auto hide after 5 seconds
    setTimeout(() => {
        messagesContainer.style.display = 'none';
    }, 5000);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Cart page initialized');

    // Handle quantity input changes
    document.querySelectorAll('.quantity-input').forEach(input => {
        input.addEventListener('blur', function() {
            const productId = this.closest('.cart-item').dataset.productId;
            const quantity = parseInt(this.value) || 1;
            updateQuantity(productId, quantity);
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
            </div>
