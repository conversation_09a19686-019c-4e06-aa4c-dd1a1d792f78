<?php
// Prevent any output before JSON
ob_start();

require_once '../../config/config.php';
requireAdminLogin();

// Clean any output buffer and set JSON header
ob_clean();
header('Content-Type: application/json; charset=utf-8');

try {
    // Create the notification reads table if it doesn't exist
    $createTable = "
        CREATE TABLE IF NOT EXISTS admin_notification_reads (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT NOT NULL DEFAULT 1,
            notification_type ENUM('order', 'review') NOT NULL,
            reference_id INT NOT NULL,
            read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_read (admin_id, notification_type, reference_id),
            INDEX idx_admin_type (admin_id, notification_type),
            INDEX idx_reference (reference_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $pdo->exec($createTable);

    // جلب عدد الطلبات غير المقروءة
    $unreadOrdersResult = fetchOne("
        SELECT COUNT(*) as count
        FROM orders o
        LEFT JOIN admin_notification_reads anr ON (anr.notification_type = 'order' AND anr.reference_id = o.id)
        WHERE o.status = 'pending' AND anr.id IS NULL
    ");
    $unreadOrders = ($unreadOrdersResult && isset($unreadOrdersResult['count'])) ? $unreadOrdersResult['count'] : 0;

    // جلب عدد التقييمات غير المقروءة
    $unreadReviewsResult = fetchOne("
        SELECT COUNT(*) as count
        FROM reviews r
        LEFT JOIN admin_notification_reads anr ON (anr.notification_type = 'review' AND anr.reference_id = r.id)
        WHERE r.status = 'pending' AND anr.id IS NULL
    ");
    $unreadReviews = ($unreadReviewsResult && isset($unreadReviewsResult['count'])) ? $unreadReviewsResult['count'] : 0;

    echo json_encode([
        'pending_orders' => (int)$unreadOrders,
        'pending_reviews' => (int)$unreadReviews,
        'total' => (int)($unreadOrders + $unreadReviews)
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'message' => 'خطأ في جلب الإشعارات: ' . $e->getMessage(),
        'pending_orders' => 0,
        'pending_reviews' => 0,
        'total' => 0
    ], JSON_UNESCAPED_UNICODE);
}

exit;
?>
