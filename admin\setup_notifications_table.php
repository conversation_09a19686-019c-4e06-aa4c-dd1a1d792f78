<?php
require_once '../config/config.php';
requireAdminLogin();

echo "<h2>إعداد جدول الإشعارات</h2>";

try {
    // Create admin_notification_reads table to track what has been read
    $createTable = "
        CREATE TABLE IF NOT EXISTS admin_notification_reads (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT NOT NULL DEFAULT 1,
            notification_type ENUM('order', 'review') NOT NULL,
            reference_id INT NOT NULL,
            read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_read (admin_id, notification_type, reference_id),
            INDEX idx_admin_type (admin_id, notification_type),
            INDEX idx_reference (reference_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTable);
    echo "<p style='color: green;'>✅ تم إنشاء جدول admin_notification_reads بنجاح</p>";
    
    // Create a view for unread notifications
    $createView = "
        CREATE OR REPLACE VIEW unread_notifications AS
        SELECT 
            'order' as type,
            o.id as reference_id,
            o.customer_name as title,
            CONCAT('طلب جديد من ', o.customer_name, ' بقيمة ', o.total_price, ' دينار عراقي') as message,
            o.created_at,
            o.total_price as amount
        FROM orders o
        LEFT JOIN admin_notification_reads anr ON (anr.notification_type = 'order' AND anr.reference_id = o.id)
        WHERE o.status = 'pending' AND anr.id IS NULL
        
        UNION ALL
        
        SELECT 
            'review' as type,
            r.id as reference_id,
            p.name as title,
            CONCAT('تقييم جديد للمنتج ', p.name, ' من ', r.customer_name) as message,
            r.created_at,
            r.rating as amount
        FROM reviews r
        JOIN products p ON r.product_id = p.id
        LEFT JOIN admin_notification_reads anr ON (anr.notification_type = 'review' AND anr.reference_id = r.id)
        WHERE r.status = 'pending' AND anr.id IS NULL
        
        ORDER BY created_at DESC
    ";
    
    $pdo->exec($createView);
    echo "<p style='color: green;'>✅ تم إنشاء عرض unread_notifications بنجاح</p>";
    
    echo "<h3>اختبار النظام:</h3>";
    
    // Test the view
    $unreadNotifications = fetchAll("SELECT * FROM unread_notifications LIMIT 5");
    echo "<p><strong>الإشعارات غير المقروءة:</strong></p>";
    if ($unreadNotifications) {
        echo "<ul>";
        foreach ($unreadNotifications as $notification) {
            echo "<li>" . htmlspecialchars($notification['message']) . " (" . $notification['created_at'] . ")</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>لا توجد إشعارات غير مقروءة</p>";
    }
    
    // Test marking as read
    $testOrder = fetchOne("SELECT id FROM orders WHERE status = 'pending' LIMIT 1");
    if ($testOrder) {
        $orderId = $testOrder['id'];
        
        // Mark as read
        $insertRead = "INSERT IGNORE INTO admin_notification_reads (admin_id, notification_type, reference_id) VALUES (1, 'order', ?)";
        $stmt = $pdo->prepare($insertRead);
        $stmt->execute([$orderId]);
        
        echo "<p style='color: blue;'>🧪 تم تجربة تحديد الطلب #$orderId كمقروء</p>";
        
        // Check if it's now hidden
        $stillVisible = fetchOne("SELECT * FROM unread_notifications WHERE type = 'order' AND reference_id = ?", [$orderId]);
        if (!$stillVisible) {
            echo "<p style='color: green;'>✅ الطلب لم يعد يظهر في الإشعارات غير المقروءة</p>";
        } else {
            echo "<p style='color: red;'>❌ الطلب ما زال يظهر في الإشعارات</p>";
        }
    }
    
    echo "<p><a href='dashboard.php' class='btn btn-primary'>العودة إلى لوحة التحكم</a></p>";
    echo "<p><a href='test_dashboard_improvements.php' class='btn btn-secondary'>صفحة الاختبار</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد جدول الإشعارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; padding: 20px; }
    </style>
</head>
<body>
</body>
</html>
