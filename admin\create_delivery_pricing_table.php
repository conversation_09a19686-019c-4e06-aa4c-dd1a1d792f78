<?php
/**
 * إنشاء جدول أسعار التوصيل للمحافظات العراقية
 * Create delivery pricing table for Iraqi provinces
 */

require_once '../config/config.php';
require_once '../config/database.php';

// التحقق من الجلسة
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$message = '';
$messageType = '';

// إنشاء الجدول
if (isset($_POST['create_table'])) {
    try {
        $sql = "CREATE TABLE IF NOT EXISTS `delivery_pricing` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `province` varchar(100) NOT NULL,
            `price` decimal(10,2) NOT NULL DEFAULT 0,
            `is_active` tinyint(1) DEFAULT 1,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `province` (`province`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($sql);
        
        // إدراج المحافظات العراقية مع أسعار افتراضية
        $iraqiProvinces = [
            'بغداد' => 5000,
            'البصرة' => 8000,
            'نينوى' => 7000,
            'أربيل' => 7500,
            'النجف' => 6000,
            'كربلاء' => 6000,
            'الأنبار' => 8500,
            'ذي قار' => 7500,
            'بابل' => 6500,
            'كركوك' => 7000,
            'ديالى' => 6500,
            'المثنى' => 8000,
            'القادسية' => 7000,
            'ميسان' => 7500,
            'واسط' => 6500,
            'دهوك' => 8000,
            'السليمانية' => 7500,
            'صلاح الدين' => 7000
        ];
        
        foreach ($iraqiProvinces as $province => $price) {
            $checkSql = "SELECT COUNT(*) FROM delivery_pricing WHERE province = ?";
            $stmt = $pdo->prepare($checkSql);
            $stmt->execute([$province]);
            
            if ($stmt->fetchColumn() == 0) {
                $insertSql = "INSERT INTO delivery_pricing (province, price) VALUES (?, ?)";
                $insertStmt = $pdo->prepare($insertSql);
                $insertStmt->execute([$province, $price]);
            }
        }
        
        $message = 'تم إنشاء جدول أسعار التوصيل وإدراج المحافظات العراقية بنجاح!';
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = 'حدث خطأ: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// التحقق من وجود الجدول
$tableExists = false;
try {
    $result = $pdo->query("SHOW TABLES LIKE 'delivery_pricing'");
    $tableExists = $result->rowCount() > 0;
} catch (Exception $e) {
    // الجدول غير موجود
}

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-professional fade-in-professional">
                <div class="card-header">
                    <h5 class="mb-0 text-professional-dark">
                        <i class="bi bi-truck text-professional-primary"></i> 
                        إعداد نظام أسعار التوصيل للمحافظات العراقية
                    </h5>
                </div>
                
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert-professional alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?> slide-up-professional">
                            <i class="bi bi-<?php echo $messageType == 'success' ? 'check-circle-fill' : 'exclamation-triangle-fill'; ?> alert-icon"></i>
                            <div class="alert-content">
                                <strong><?php echo $messageType == 'success' ? 'نجح!' : 'خطأ!'; ?></strong><br>
                                <?php echo $message; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!$tableExists): ?>
                        <div class="alert-professional alert-info slide-up-professional">
                            <i class="bi bi-info-circle-fill alert-icon"></i>
                            <div class="alert-content">
                                <strong>معلومات مهمة:</strong><br>
                                يجب إنشاء جدول أسعار التوصيل أولاً لتفعيل نظام التسعير الديناميكي للمحافظات العراقية.
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <form method="POST">
                                <button type="submit" name="create_table" class="btn-professional btn-primary btn-lg hover-lift-professional">
                                    <i class="bi bi-plus-circle"></i> إنشاء جدول أسعار التوصيل
                                </button>
                            </form>
                        </div>
                        
                        <hr class="border-professional">
                        
                        <h6 class="text-professional-dark mb-3">المحافظات العراقية التي سيتم إضافتها:</h6>
                        <div class="row">
                            <?php 
                            $provinces = [
                                'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 
                                'الأنبار', 'ذي قار', 'بابل', 'كركوك', 'ديالى', 'المثنى', 
                                'القادسية', 'ميسان', 'واسط', 'دهوك', 'السليمانية', 'صلاح الدين'
                            ];
                            foreach ($provinces as $province): 
                            ?>
                                <div class="col-md-3 mb-2">
                                    <span class="badge-professional badge-info"><?php echo $province; ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                    <?php else: ?>
                        <div class="alert-professional alert-success slide-up-professional">
                            <i class="bi bi-check-circle-fill alert-icon"></i>
                            <div class="alert-content">
                                <strong>تم بنجاح!</strong><br>
                                جدول أسعار التوصيل موجود ومُعد بشكل صحيح.
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <div class="btn-group-professional">
                                <a href="delivery_pricing.php" class="btn-professional btn-primary hover-lift-professional">
                                    <i class="bi bi-gear"></i> إدارة أسعار التوصيل
                                </a>
                                <a href="checkout.php" class="btn-professional btn-outline-primary hover-lift-professional">
                                    <i class="bi bi-cart-check"></i> اختبار صفحة الدفع
                                </a>
                                <a href="dashboard.php" class="btn-professional btn-outline-secondary hover-lift-professional">
                                    <i class="bi bi-speedometer2"></i> لوحة التحكم
                                </a>
                            </div>
                        </div>
                        
                        <?php
                        // عرض المحافظات الموجودة
                        try {
                            $provinces = fetchAll("SELECT * FROM delivery_pricing ORDER BY province");
                            if (!empty($provinces)):
                        ?>
                            <hr class="border-professional">
                            <h6 class="text-professional-dark mb-3">المحافظات المُعدة حالياً:</h6>
                            <div class="table-responsive">
                                <table class="table-professional">
                                    <thead>
                                        <tr>
                                            <th>المحافظة</th>
                                            <th>سعر التوصيل</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($provinces as $province): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($province['province']); ?></td>
                                                <td><?php echo formatPrice($province['price']); ?></td>
                                                <td>
                                                    <div class="status-indicator-professional <?php echo $province['is_active'] ? 'active' : 'inactive'; ?>">
                                                        <?php echo $province['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php 
                            endif;
                        } catch (Exception $e) {
                            // تجاهل الأخطاء
                        }
                        ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations
    const cards = document.querySelectorAll('.card-professional');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.3s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
