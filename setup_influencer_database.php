<?php
require_once 'config/database.php';

echo "=== إعداد قاعدة البيانات للمؤثرين ===\n";

try {
    echo "\n=== 1. التحقق من الاتصال ===\n";
    if (!$pdo) {
        echo "✗ فشل الاتصال بقاعدة البيانات\n";
        exit;
    }
    echo "✓ الاتصال بقاعدة البيانات ناجح\n";

    echo "\n=== 2. إنشاء جدول content_categories ===\n";
    
    $createCategoriesTable = "
    CREATE TABLE IF NOT EXISTS content_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name_ar VARCHAR(255) NOT NULL,
        name_en VARCHAR(255),
        description TEXT,
        type ENUM('guideline', 'influencer', 'both') NOT NULL DEFAULT 'both',
        status ENUM('active', 'inactive') DEFAULT 'active',
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_type (type),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    try {
        $pdo->exec($createCategoriesTable);
        echo "✅ تم إنشاء/التحقق من جدول content_categories\n";
    } catch (Exception $e) {
        echo "✗ خطأ في إنشاء جدول content_categories: " . $e->getMessage() . "\n";
    }

    echo "\n=== 3. إنشاء جدول influencers_content ===\n";
    
    $createInfluencersTable = "
    CREATE TABLE IF NOT EXISTS influencers_content (
        id INT AUTO_INCREMENT PRIMARY KEY,
        influencer_name VARCHAR(255) NOT NULL,
        influencer_image VARCHAR(500),
        influencer_image_type ENUM('upload', 'url') DEFAULT 'upload',
        content_type ENUM('video', 'post', 'review') NOT NULL,
        content_title VARCHAR(500),
        content_text TEXT NOT NULL,
        rating TINYINT(1) CHECK (rating >= 1 AND rating <= 5),
        product_id INT,
        video_url VARCHAR(1000),
        video_platform ENUM('youtube', 'instagram', 'tiktok', 'other'),
        category_id INT,
        status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
        is_featured BOOLEAN DEFAULT FALSE,
        sort_order INT DEFAULT 0,
        views_count INT DEFAULT 0,
        likes_count INT DEFAULT 0,
        shares_count INT DEFAULT 0,
        published_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by INT,
        updated_by INT,
        
        INDEX idx_status (status),
        INDEX idx_content_type (content_type),
        INDEX idx_rating (rating),
        INDEX idx_featured (is_featured),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    try {
        $pdo->exec($createInfluencersTable);
        echo "✅ تم إنشاء/التحقق من جدول influencers_content\n";
    } catch (Exception $e) {
        echo "✗ خطأ في إنشاء جدول influencers_content: " . $e->getMessage() . "\n";
    }

    echo "\n=== 4. إضافة التصنيفات الافتراضية ===\n";
    
    // التحقق من وجود تصنيفات
    $existingCategories = $pdo->query("SELECT COUNT(*) FROM content_categories WHERE type IN ('influencer', 'both')")->fetchColumn();
    
    if ($existingCategories == 0) {
        echo "إضافة التصنيفات الافتراضية...\n";
        
        $defaultCategories = [
            [
                'name_ar' => 'مراجعات المنتجات',
                'name_en' => 'Product Reviews',
                'description' => 'مراجعات وتقييمات المؤثرين للمنتجات',
                'type' => 'influencer',
                'sort_order' => 1
            ],
            [
                'name_ar' => 'فيديوهات تعليمية',
                'name_en' => 'Tutorial Videos',
                'description' => 'فيديوهات تعليمية وإرشادية',
                'type' => 'both',
                'sort_order' => 2
            ],
            [
                'name_ar' => 'منشورات ترويجية',
                'name_en' => 'Promotional Posts',
                'description' => 'منشورات ترويجية للمنتجات والخدمات',
                'type' => 'influencer',
                'sort_order' => 3
            ],
            [
                'name_ar' => 'نصائح الاستخدام',
                'name_en' => 'Usage Tips',
                'description' => 'نصائح وإرشادات الاستخدام',
                'type' => 'both',
                'sort_order' => 4
            ],
            [
                'name_ar' => 'تجارب شخصية',
                'name_en' => 'Personal Experiences',
                'description' => 'تجارب شخصية مع المنتجات',
                'type' => 'influencer',
                'sort_order' => 5
            ]
        ];
        
        $insertCategory = $pdo->prepare("
            INSERT INTO content_categories (name_ar, name_en, description, type, status, sort_order) 
            VALUES (?, ?, ?, ?, 'active', ?)
        ");
        
        foreach ($defaultCategories as $category) {
            try {
                $insertCategory->execute([
                    $category['name_ar'],
                    $category['name_en'],
                    $category['description'],
                    $category['type'],
                    $category['sort_order']
                ]);
                echo "✓ تم إضافة تصنيف: " . $category['name_ar'] . "\n";
            } catch (Exception $e) {
                echo "✗ فشل في إضافة تصنيف " . $category['name_ar'] . ": " . $e->getMessage() . "\n";
            }
        }
        
    } else {
        echo "✓ التصنيفات موجودة بالفعل ({$existingCategories} تصنيف)\n";
    }

    echo "\n=== 5. إضافة بيانات تجريبية للمؤثرين ===\n";
    
    // التحقق من وجود بيانات
    $existingInfluencers = $pdo->query("SELECT COUNT(*) FROM influencers_content")->fetchColumn();
    
    if ($existingInfluencers == 0) {
        echo "إضافة بيانات تجريبية...\n";
        
        // الحصول على تصنيف للمؤثرين
        $category = $pdo->query("SELECT id FROM content_categories WHERE type IN ('influencer', 'both') LIMIT 1")->fetch();
        $categoryId = $category ? $category['id'] : null;
        
        $sampleInfluencers = [
            [
                'influencer_name' => 'سارة أحمد',
                'influencer_image' => 'https://via.placeholder.com/150x150/ff6b6b/ffffff?text=سارة',
                'content_type' => 'review',
                'content_title' => 'مراجعة منتج العناية بالبشرة',
                'content_text' => 'جربت هذا المنتج لمدة شهر كامل والنتائج رائعة! أنصح الجميع بتجربته.',
                'rating' => 5,
                'status' => 'published'
            ],
            [
                'influencer_name' => 'محمد علي',
                'influencer_image' => 'https://via.placeholder.com/150x150/4ecdc4/ffffff?text=محمد',
                'content_type' => 'video',
                'content_title' => 'فيديو تعليمي لاستخدام المنتج',
                'content_text' => 'في هذا الفيديو أشرح طريقة الاستخدام الصحيحة للحصول على أفضل النتائج.',
                'rating' => 4,
                'video_url' => 'https://youtube.com/watch?v=example',
                'video_platform' => 'youtube',
                'status' => 'published'
            ],
            [
                'influencer_name' => 'فاطمة خالد',
                'influencer_image' => 'https://via.placeholder.com/150x150/45b7d1/ffffff?text=فاطمة',
                'content_type' => 'post',
                'content_title' => 'تجربتي الشخصية',
                'content_text' => 'بعد استخدام المنتج لفترة، لاحظت تحسن كبير. أشارككم تجربتي الكاملة.',
                'rating' => 5,
                'status' => 'published'
            ]
        ];
        
        $insertInfluencer = $pdo->prepare("
            INSERT INTO influencers_content (
                influencer_name, influencer_image, influencer_image_type, content_type,
                content_title, content_text, rating, video_url, video_platform,
                category_id, status, is_featured, sort_order, created_by, published_at
            ) VALUES (?, ?, 'url', ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, 1, NOW())
        ");
        
        foreach ($sampleInfluencers as $index => $influencer) {
            try {
                $insertInfluencer->execute([
                    $influencer['influencer_name'],
                    $influencer['influencer_image'],
                    $influencer['content_type'],
                    $influencer['content_title'],
                    $influencer['content_text'],
                    $influencer['rating'],
                    $influencer['video_url'] ?? null,
                    $influencer['video_platform'] ?? null,
                    $categoryId,
                    $influencer['status'],
                    $index + 1
                ]);
                echo "✓ تم إضافة مؤثر: " . $influencer['influencer_name'] . "\n";
            } catch (Exception $e) {
                echo "✗ فشل في إضافة مؤثر " . $influencer['influencer_name'] . ": " . $e->getMessage() . "\n";
            }
        }
        
    } else {
        echo "✓ بيانات المؤثرين موجودة بالفعل ({$existingInfluencers} مؤثر)\n";
    }

    echo "\n=== 6. اختبار نهائي للإدراج ===\n";
    
    session_start();
    $_SESSION['admin_id'] = 1;
    
    $testData = [
        'influencer_name' => 'مؤثر اختبار - ' . date('H:i:s'),
        'influencer_image' => 'https://via.placeholder.com/150x150/007bff/ffffff?text=اختبار',
        'influencer_image_type' => 'url',
        'content_type' => 'review',
        'content_title' => 'اختبار الإدراج',
        'content_text' => 'هذا اختبار للتأكد من عمل الإدراج بشكل صحيح',
        'rating' => 5,
        'product_id' => null,
        'video_url' => null,
        'video_platform' => null,
        'category_id' => $categoryId,
        'status' => 'published',
        'is_featured' => 0,
        'sort_order' => 999,
        'created_by' => 1,
        'published_at' => date('Y-m-d H:i:s')
    ];

    $sql = "INSERT INTO influencers_content (
        influencer_name, influencer_image, influencer_image_type, content_type,
        content_title, content_text, rating, product_id, video_url, video_platform,
        category_id, status, is_featured, sort_order, created_by, published_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $params = array_values($testData);
    
    try {
        $result = executeQuery($sql, $params);
        if ($result) {
            $insertedId = $pdo->lastInsertId();
            echo "✅ اختبار الإدراج نجح - ID: {$insertedId}\n";
            
            // حذف البيانات التجريبية
            executeQuery("DELETE FROM influencers_content WHERE id = ?", [$insertedId]);
            echo "✓ تم حذف بيانات الاختبار\n";
        } else {
            echo "✗ فشل اختبار الإدراج\n";
        }
    } catch (Exception $e) {
        echo "✗ خطأ في اختبار الإدراج: " . $e->getMessage() . "\n";
    }

    echo "\n=== 7. ملخص الإعداد ===\n";
    
    $categoriesCount = $pdo->query("SELECT COUNT(*) FROM content_categories")->fetchColumn();
    $influencerCategoriesCount = $pdo->query("SELECT COUNT(*) FROM content_categories WHERE type IN ('influencer', 'both')")->fetchColumn();
    $influencersCount = $pdo->query("SELECT COUNT(*) FROM influencers_content")->fetchColumn();
    
    echo "✅ إجمالي التصنيفات: {$categoriesCount}\n";
    echo "✅ تصنيفات المؤثرين: {$influencerCategoriesCount}\n";
    echo "✅ إجمالي المؤثرين: {$influencersCount}\n";
    
    echo "\n🎉 تم إعداد قاعدة البيانات بنجاح!\n";
    echo "يمكنك الآن استخدام نظام إدارة المؤثرين بشكل طبيعي.\n";

} catch (Exception $e) {
    echo "✗ خطأ في الإعداد: " . $e->getMessage() . "\n";
}

echo "\n=== انتهاء الإعداد ===\n";
?>
