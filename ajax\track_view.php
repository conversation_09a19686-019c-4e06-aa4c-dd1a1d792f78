<?php
require_once 'config/config.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    exit();
}

$type = $_POST['type'] ?? '';
$id = isset($_POST['id']) ? (int)$_POST['id'] : 0;

try {
    if ($type === 'influencer' && $id > 0) {
        // تسجيل مشاهدة محتوى المؤثر
        $userIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        // التحقق من عدم تسجيل نفس المشاهدة خلال الساعة الماضية
        $existingView = fetchOne(
            "SELECT id FROM content_views WHERE content_type = 'influencer' AND content_id = ? AND ip_address = ? AND viewed_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)",
            [$id, $userIp]
        );

        if (!$existingView) {
            executeQuery(
                "INSERT INTO content_views (content_type, content_id, ip_address, user_agent, viewed_at) VALUES (?, ?, ?, ?, NOW())",
                ['influencer', $id, $userIp, $userAgent]
            );
        }
        
        echo json_encode(['success' => true]);
        
    } elseif ($type === 'guideline' && $id > 0) {
        // تسجيل مشاهدة الإرشاد
        $userIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        // التحقق من عدم تسجيل نفس المشاهدة خلال الساعة الماضية
        $existingView = fetchOne(
            "SELECT id FROM content_views WHERE content_type = 'guideline' AND content_id = ? AND ip_address = ? AND viewed_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)",
            [$id, $userIp]
        );

        if (!$existingView) {
            executeQuery(
                "INSERT INTO content_views (content_type, content_id, ip_address, user_agent, viewed_at) VALUES (?, ?, ?, ?, NOW())",
                ['guideline', $id, $userIp, $userAgent]
            );
        }
        
        echo json_encode(['success' => true]);
        
    } elseif ($type === 'influencers_page') {
        // تسجيل مشاهدة صفحة المؤثرين
        $userIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $existingView = fetchOne(
            "SELECT id FROM content_views WHERE content_type = 'page' AND content_id = 0 AND page_name = 'influencers' AND ip_address = ? AND viewed_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)",
            [$userIp]
        );

        if (!$existingView) {
            executeQuery(
                "INSERT INTO content_views (content_type, content_id, page_name, ip_address, user_agent, viewed_at) VALUES (?, ?, ?, ?, ?, NOW())",
                ['page', 0, 'influencers', $userIp, $userAgent]
            );
        }
        
        echo json_encode(['success' => true]);
        
    } elseif ($type === 'guidelines_page') {
        // تسجيل مشاهدة صفحة الإرشادات
        $userIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $existingView = fetchOne(
            "SELECT id FROM content_views WHERE content_type = 'page' AND content_id = 0 AND page_name = 'guidelines' AND ip_address = ? AND viewed_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)",
            [$userIp]
        );

        if (!$existingView) {
            executeQuery(
                "INSERT INTO content_views (content_type, content_id, page_name, ip_address, user_agent, viewed_at) VALUES (?, ?, ?, ?, ?, NOW())",
                ['page', 0, 'guidelines', $userIp, $userAgent]
            );
        }
        
        echo json_encode(['success' => true]);
        
    } else {
        echo json_encode(['success' => false, 'message' => 'معاملات غير صحيحة']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في تسجيل المشاهدة']);
}
?>
