<?php
/**
 * Debug file for checkout issues
 */

require_once 'config/config.php';

echo "<h2>Checkout Debug Information</h2>";

// Test database connection
echo "<h3>1. Database Connection Test</h3>";
if (isset($pdo) && $pdo) {
    echo "✅ PDO connection exists<br>";
    
    try {
        $result = $pdo->query("SELECT 1");
        echo "✅ Database connection is working<br>";
    } catch (Exception $e) {
        echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ PDO connection is null or not set<br>";
}

// Test orders table
echo "<h3>2. Orders Table Test</h3>";
try {
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'orders'");
    if ($tableCheck->rowCount() > 0) {
        echo "✅ Orders table exists<br>";
        
        // Check table structure
        $structure = $pdo->query("DESCRIBE orders");
        echo "<strong>Orders table structure:</strong><br>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while ($row = $structure->fetch()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
        
    } else {
        echo "❌ Orders table does not exist<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking orders table: " . $e->getMessage() . "<br>";
}

// Test order_items table
echo "<h3>3. Order Items Table Test</h3>";
try {
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'order_items'");
    if ($tableCheck->rowCount() > 0) {
        echo "✅ Order items table exists<br>";
    } else {
        echo "❌ Order items table does not exist<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking order_items table: " . $e->getMessage() . "<br>";
}

// Test insertData function
echo "<h3>4. InsertData Function Test</h3>";
if (function_exists('insertData')) {
    echo "✅ insertData function exists<br>";
    
    // Test with a simple insert (we'll delete it immediately)
    $testData = [
        'customer_name' => 'Test Customer',
        'customer_phone' => '07123456789',
        'address' => 'Test Address',
        'province' => 'بغداد',
        'subtotal' => 100.00,
        'delivery_price' => 5000,
        'discount_amount' => 0,
        'total_price' => 105000,
        'payment_method' => 'cash_on_delivery',
        'status' => 'pending'
    ];
    
    try {
        $testOrderId = insertData('orders', $testData);
        if ($testOrderId) {
            echo "✅ insertData function works - Test order ID: " . $testOrderId . "<br>";
            
            // Delete the test order
            $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$testOrderId]);
            echo "✅ Test order deleted<br>";
        } else {
            echo "❌ insertData function returned false<br>";
        }
    } catch (Exception $e) {
        echo "❌ insertData function failed: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ insertData function does not exist<br>";
}

// Test cart functions
echo "<h3>5. Cart Functions Test</h3>";
if (function_exists('getCart')) {
    echo "✅ getCart function exists<br>";
    $cart = getCart();
    echo "Current cart contents: " . json_encode($cart) . "<br>";
} else {
    echo "❌ getCart function does not exist<br>";
}

// Test settings functions
echo "<h3>6. Settings Functions Test</h3>";
if (function_exists('getSetting')) {
    echo "✅ getSetting function exists<br>";
    $deliveryPrice = getSetting('delivery_price');
    echo "Default delivery price: " . $deliveryPrice . "<br>";
    $freeThreshold = getSetting('free_delivery_threshold');
    echo "Free delivery threshold: " . $freeThreshold . "<br>";
} else {
    echo "❌ getSetting function does not exist<br>";
}

echo "<h3>7. Recent Orders Check</h3>";
try {
    $recentOrders = $pdo->query("SELECT * FROM orders ORDER BY created_at DESC LIMIT 5");
    $orders = $recentOrders->fetchAll();
    
    if (count($orders) > 0) {
        echo "Recent orders found: " . count($orders) . "<br>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Customer</th><th>Phone</th><th>Total</th><th>Status</th><th>Created</th></tr>";
        foreach ($orders as $order) {
            echo "<tr>";
            echo "<td>" . $order['id'] . "</td>";
            echo "<td>" . $order['customer_name'] . "</td>";
            echo "<td>" . $order['customer_phone'] . "</td>";
            echo "<td>" . $order['total_price'] . "</td>";
            echo "<td>" . $order['status'] . "</td>";
            echo "<td>" . $order['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No orders found in database<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking recent orders: " . $e->getMessage() . "<br>";
}

echo "<p><strong>Debug completed. Check the results above to identify any issues.</strong></p>";
?>
