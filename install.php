<?php
// ملف التثبيت للمتجر الإلكتروني
// يجب حذف هذا الملف بعد التثبيت

// التحقق من وجود ملف التكوين
if (file_exists('config/config.php')) {
    die('تم تثبيت الموقع مسبقاً. يرجى حذف ملف install.php');
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

// معالجة خطوات التثبيت
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    switch ($step) {
        case 1:
            // التحقق من المتطلبات
            $step = 2;
            break;
            
        case 2:
            // إعداد قاعدة البيانات
            $host = $_POST['db_host'];
            $name = $_POST['db_name'];
            $user = $_POST['db_user'];
            $pass = $_POST['db_pass'];
            
            try {
                $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $user, $pass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // إنشاء قاعدة البيانات
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `$name`");
                
                // تنفيذ ملف SQL
                $sql = file_get_contents('database.sql');
                $pdo->exec($sql);
                
                // إنشاء ملف التكوين
                $configContent = "<?php
// إعدادات قاعدة البيانات
define('DB_HOST', '$host');
define('DB_NAME', '$name');
define('DB_USER', '$user');
define('DB_PASS', '$pass');

// إعدادات الموقع
define('SITE_URL', 'http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "');
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('UPLOAD_URL', SITE_URL . '/uploads');

// إعدادات الجلسة
define('SESSION_NAME', 'ecommerce_session');
define('ADMIN_SESSION_NAME', 'admin_session');

// مفتاح التشفير (يجب تغييره)
define('ENCRYPTION_KEY', '" . bin2hex(random_bytes(32)) . "');

// تضمين ملف قاعدة البيانات
require_once 'database.php';
require_once 'functions.php';
?>";

                if (!is_dir('config')) {
                    mkdir('config', 0755, true);
                }
                
                file_put_contents('config/config.php', $configContent);
                
                $step = 3;
                $success = 'تم إعداد قاعدة البيانات بنجاح!';
                
            } catch (Exception $e) {
                $error = 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage();
            }
            break;
            
        case 3:
            // إعداد المدير
            $username = $_POST['admin_username'];
            $password = $_POST['admin_password'];
            $email = $_POST['admin_email'];
            
            if (strlen($password) < 6) {
                $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
            } else {
                require_once 'config/config.php';
                
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                
                $stmt = $pdo->prepare("UPDATE admins SET username = ?, password = ?, email = ? WHERE id = 1");
                $stmt->execute([$username, $hashedPassword, $email]);
                
                $step = 4;
                $success = 'تم إعداد حساب المدير بنجاح!';
            }
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت المتجر الإلكتروني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .install-container { max-width: 600px; margin: 50px auto; }
        .install-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .install-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px 15px 0 0; text-align: center; }
        .step-indicator { display: flex; justify-content: center; margin: 20px 0; }
        .step { width: 40px; height: 40px; border-radius: 50%; background: #e9ecef; display: flex; align-items: center; justify-content: center; margin: 0 10px; font-weight: bold; }
        .step.active { background: #667eea; color: white; }
        .step.completed { background: #28a745; color: white; }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-card">
            <div class="install-header">
                <h1>تثبيت المتجر الإلكتروني</h1>
                <p class="mb-0">مرحباً بك في معالج التثبيت</p>
            </div>
            
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : ''; ?>">3</div>
                <div class="step <?php echo $step >= 4 ? 'active' : ''; ?>">4</div>
            </div>
            
            <div class="p-4">
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success"><?php echo $success; ?></div>
                <?php endif; ?>
                
                <?php if ($step == 1): ?>
                    <h3>التحقق من المتطلبات</h3>
                    <div class="requirements">
                        <div class="requirement d-flex justify-content-between align-items-center mb-2">
                            <span>PHP 7.4+</span>
                            <span class="badge <?php echo version_compare(PHP_VERSION, '7.4.0') >= 0 ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo PHP_VERSION; ?>
                            </span>
                        </div>
                        <div class="requirement d-flex justify-content-between align-items-center mb-2">
                            <span>PDO Extension</span>
                            <span class="badge <?php echo extension_loaded('pdo') ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo extension_loaded('pdo') ? 'متوفر' : 'غير متوفر'; ?>
                            </span>
                        </div>
                        <div class="requirement d-flex justify-content-between align-items-center mb-2">
                            <span>MySQL PDO</span>
                            <span class="badge <?php echo extension_loaded('pdo_mysql') ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo extension_loaded('pdo_mysql') ? 'متوفر' : 'غير متوفر'; ?>
                            </span>
                        </div>
                        <div class="requirement d-flex justify-content-between align-items-center mb-2">
                            <span>مجلد uploads قابل للكتابة</span>
                            <span class="badge <?php echo is_writable('uploads') ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo is_writable('uploads') ? 'قابل للكتابة' : 'غير قابل للكتابة'; ?>
                            </span>
                        </div>
                    </div>
                    
                    <form method="POST" action="?step=1">
                        <button type="submit" class="btn btn-primary w-100 mt-3">التالي</button>
                    </form>
                    
                <?php elseif ($step == 2): ?>
                    <h3>إعداد قاعدة البيانات</h3>
                    <form method="POST" action="?step=2">
                        <div class="mb-3">
                            <label class="form-label">خادم قاعدة البيانات</label>
                            <input type="text" class="form-control" name="db_host" value="localhost" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">اسم قاعدة البيانات</label>
                            <input type="text" class="form-control" name="db_name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" name="db_user" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" name="db_pass">
                        </div>
                        <button type="submit" class="btn btn-primary w-100">إنشاء قاعدة البيانات</button>
                    </form>
                    
                <?php elseif ($step == 3): ?>
                    <h3>إعداد حساب المدير</h3>
                    <form method="POST" action="?step=3">
                        <div class="mb-3">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" name="admin_username" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" name="admin_password" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="admin_email" required>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">إنشاء الحساب</button>
                    </form>
                    
                <?php elseif ($step == 4): ?>
                    <div class="text-center">
                        <div class="mb-4">
                            <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                        </div>
                        <h3 class="text-success">تم التثبيت بنجاح!</h3>
                        <p class="text-muted">تم تثبيت المتجر الإلكتروني بنجاح. يمكنك الآن البدء في استخدامه.</p>
                        
                        <div class="alert alert-warning">
                            <strong>مهم:</strong> يرجى حذف ملف install.php لأسباب أمنية
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="index.php" class="btn btn-primary">زيارة الموقع</a>
                            <a href="admin/login.php" class="btn btn-outline-primary">لوحة التحكم</a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
