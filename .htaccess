# حماية وتحسين الأداء للمتجر الإلكتروني

# تفعيل إعادة الكتابة
RewriteEngine On

# منع الوصول إلى ملفات التكوين الحساسة
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "database.php">
    Order Allow,Deny
    Deny from all
</Files>

# منع الوصول إلى ملفات النسخ الاحتياطية
<FilesMatch "\.(bak|backup|old|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول إلى ملفات Git
<FilesMatch "^\.git">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول إلى ملف قاعدة البيانات
<Files "database.sql">
    Order Allow,Deny
    Deny from all
</Files>

# حماية من الهجمات الشائعة
<IfModule mod_rewrite.c>
    # منع SQL Injection
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تحسين التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# إعدادات الأمان الإضافية
<IfModule mod_headers.c>
    # منع XSS
    Header always set X-XSS-Protection "1; mode=block"
    
    # منع MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # منع Clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # تحسين الأمان
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # إزالة معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# تحديد حجم الملفات المرفوعة
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300

# منع عرض محتويات المجلدات
Options -Indexes

# صفحات الأخطاء المخصصة
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# إعادة توجيه URL نظيفة (اختياري)
<IfModule mod_rewrite.c>
    # إعادة توجيه المنتج
    RewriteRule ^product/([0-9]+)/?$ product.php?id=$1 [L,QSA]
    
    # إعادة توجيه التصنيف
    RewriteRule ^category/([0-9]+)/?$ products.php?category=$1 [L,QSA]
    
    # إعادة توجيه البحث
    RewriteRule ^search/(.+)/?$ products.php?search=$1 [L,QSA]
</IfModule>

# حماية مجلد uploads
<IfModule mod_rewrite.c>
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteCond %{REQUEST_URI} ^/uploads/.*\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$ [NC]
    RewriteRule .* - [F]
</IfModule>
