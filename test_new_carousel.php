<?php
/**
 * Professional Hero Carousel Test Page
 * Tests the completely rebuilt carousel implementation
 */

$pageTitle = 'اختبار الكاروسيل الجديد المهني';
require_once 'config/config.php';

// Test scenarios
$testScenarios = [
    'empty' => [
        'name' => 'حالة فارغة - بدون صور',
        'description' => 'اختبار الحالة الافتراضية عندما لا توجد صور مضافة للكاروسيل',
        'settings' => [
            'slide_1_image' => '',
            'slide_1_title' => 'مرحباً بك في متجرنا الإلكتروني',
            'slide_1_subtitle' => 'اكتشف مجموعة واسعة من المنتجات عالية الجودة بأفضل الأسعار',
            'slide_1_button_text' => 'تصفح المنتجات',
            'slide_1_button_url' => 'products.php',
            'slide_2_image' => '',
            'slide_3_image' => '',
            'slide_4_image' => '',
            'auto_advance_time' => '6',
            'show_indicators' => '1',
            'show_controls' => '1'
        ]
    ],
    'single' => [
        'name' => 'شريحة واحدة فقط',
        'description' => 'اختبار عرض شريحة واحدة مع النص والأزرار',
        'settings' => [
            'slide_1_image' => 'https://picsum.photos/1200/600?random=1',
            'slide_1_title' => 'عرض خاص محدود الوقت',
            'slide_1_subtitle' => 'خصومات تصل إلى 50% على جميع المنتجات المختارة',
            'slide_1_button_text' => 'تسوق الآن',
            'slide_1_button_url' => 'products.php',
            'slide_2_image' => '',
            'slide_3_image' => '',
            'slide_4_image' => '',
            'auto_advance_time' => '6',
            'show_indicators' => '1',
            'show_controls' => '1'
        ]
    ],
    'multiple' => [
        'name' => 'شرائح متعددة مع محتوى',
        'description' => 'اختبار عرض شرائح متعددة مع نصوص وأزرار مختلفة',
        'settings' => [
            'slide_1_image' => 'https://picsum.photos/1200/600?random=2',
            'slide_1_title' => 'مرحباً بك في متجرنا',
            'slide_1_subtitle' => 'اكتشف أفضل المنتجات بأسعار لا تقاوم',
            'slide_1_button_text' => 'تصفح المنتجات',
            'slide_1_button_url' => 'products.php',
            'slide_2_image' => 'https://picsum.photos/1200/600?random=3',
            'slide_2_title' => 'عروض حصرية',
            'slide_2_subtitle' => 'خصومات مذهلة لفترة محدودة فقط',
            'slide_3_image' => 'https://picsum.photos/1200/600?random=4',
            'slide_3_title' => 'منتجات جديدة',
            'slide_3_subtitle' => 'اكتشف أحدث إضافاتنا المميزة',
            'slide_4_image' => 'https://picsum.photos/1200/600?random=5',
            'slide_4_title' => 'جودة عالية',
            'slide_4_subtitle' => 'منتجات مضمونة الجودة بأفضل الأسعار',
            'auto_advance_time' => '4',
            'show_indicators' => '1',
            'show_controls' => '1'
        ]
    ],
    'mixed' => [
        'name' => 'شرائح مختلطة',
        'description' => 'اختبار مزيج من الشرائح - بعضها مع نص وبعضها بدون',
        'settings' => [
            'slide_1_image' => 'https://picsum.photos/1200/600?random=6',
            'slide_1_title' => 'شريحة مع نص كامل',
            'slide_1_subtitle' => 'هذه شريحة تحتوي على عنوان ووصف وزر تفاعلي',
            'slide_1_button_text' => 'اضغط هنا',
            'slide_1_button_url' => 'products.php',
            'slide_2_image' => 'https://picsum.photos/1200/600?random=7',
            'slide_2_title' => '',
            'slide_2_subtitle' => '',
            'slide_3_image' => 'https://picsum.photos/1200/600?random=8',
            'slide_3_title' => 'شريحة مع عنوان فقط',
            'slide_3_subtitle' => '',
            'slide_4_image' => '',
            'auto_advance_time' => '5',
            'show_indicators' => '1',
            'show_controls' => '1'
        ]
    ]
];

$currentTest = $_GET['test'] ?? 'empty';
$currentScenario = $testScenarios[$currentTest] ?? $testScenarios['empty'];

// Override the getHomepageSectionSettings function for testing
function getHomepageSectionSettings($section) {
    global $currentScenario;
    if ($section === 'carousel') {
        return $currentScenario['settings'];
    }
    return [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/hero-carousel.css" rel="stylesheet">
    
    <style>
        body {
            direction: rtl;
            text-align: right;
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .test-controls {
            background: white;
            padding: 2rem;
            margin: 2rem 0;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .test-info {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            padding: 1.5rem;
            margin: 2rem 0;
            border-radius: 12px;
            border-right: 5px solid #2196f3;
        }
        
        .feature-list {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin: 2rem 0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-item:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            color: #28a745;
            margin-left: 1rem;
            font-size: 1.2rem;
        }
        
        .btn-test {
            margin: 0.25rem;
            border-radius: 25px;
            padding: 0.5rem 1.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="test-controls">
                    <h1 class="mb-4">
                        <i class="bi bi-gear-fill text-primary"></i> 
                        اختبار الكاروسيل الجديد المهني
                    </h1>
                    <p class="lead">اختر سيناريو الاختبار لمشاهدة الكاروسيل في حالات مختلفة:</p>
                    
                    <div class="d-flex flex-wrap gap-2 mb-4">
                        <?php foreach ($testScenarios as $key => $scenario): ?>
                            <a href="?test=<?php echo $key; ?>" 
                               class="btn btn-test <?php echo $currentTest === $key ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                <?php echo $scenario['name']; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <div class="test-info">
                    <h4>
                        <i class="bi bi-info-circle text-primary"></i> 
                        السيناريو الحالي: <?php echo $currentScenario['name']; ?>
                    </h4>
                    <p class="mb-0"><?php echo $currentScenario['description']; ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Include the new professional carousel -->
    <?php include 'includes/homepage_carousel.php'; ?>

    <div class="container mt-5">
        <div class="row">
            <div class="col-lg-8">
                <div class="feature-list">
                    <h4 class="mb-4">
                        <i class="bi bi-check-circle text-success"></i> 
                        الميزات الجديدة المطبقة
                    </h4>
                    
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill feature-icon"></i>
                        <div>
                            <strong>تصميم مهني جديد:</strong> 
                            تصميم حديث ونظيف مع تأثيرات بصرية متقدمة
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill feature-icon"></i>
                        <div>
                            <strong>حالة افتراضية محسنة:</strong> 
                            عرض احترافي عندما لا توجد صور مضافة
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill feature-icon"></i>
                        <div>
                            <strong>تحميل ذكي:</strong> 
                            حالات تحميل متقدمة مع معالجة الأخطاء
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill feature-icon"></i>
                        <div>
                            <strong>تصميم متجاوب:</strong> 
                            يعمل بشكل مثالي على جميع الأجهزة والشاشات
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill feature-icon"></i>
                        <div>
                            <strong>دعم اللمس:</strong> 
                            تنقل بالسحب والإفلات على الأجهزة اللوحية والهواتف
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill feature-icon"></i>
                        <div>
                            <strong>إمكانية الوصول:</strong> 
                            دعم كامل لقارئات الشاشة والتنقل بلوحة المفاتيح
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <i class="bi bi-check-circle-fill feature-icon"></i>
                        <div>
                            <strong>أداء محسن:</strong> 
                            كود نظيف ومحسن للسرعة والأداء
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="feature-list">
                    <h5 class="mb-3">
                        <i class="bi bi-gear"></i> 
                        الإعدادات الحالية
                    </h5>
                    
                    <div class="mb-2">
                        <strong>وقت التقدم التلقائي:</strong> 
                        <?php echo $currentScenario['settings']['auto_advance_time']; ?> ثواني
                    </div>
                    
                    <div class="mb-2">
                        <strong>المؤشرات:</strong> 
                        <?php echo $currentScenario['settings']['show_indicators'] ? 'مفعلة' : 'معطلة'; ?>
                    </div>
                    
                    <div class="mb-2">
                        <strong>أزرار التحكم:</strong> 
                        <?php echo $currentScenario['settings']['show_controls'] ? 'مفعلة' : 'معطلة'; ?>
                    </div>
                    
                    <div class="mb-2">
                        <strong>عدد الشرائح:</strong> 
                        <?php 
                        $slideCount = 0;
                        for ($i = 1; $i <= 4; $i++) {
                            if (!empty($currentScenario['settings']["slide_{$i}_image"])) {
                                $slideCount++;
                            }
                        }
                        echo $slideCount > 0 ? $slideCount : 'لا توجد صور (الحالة الافتراضية)';
                        ?>
                    </div>
                </div>
                
                <div class="feature-list">
                    <h5 class="mb-3">
                        <i class="bi bi-keyboard"></i> 
                        اختصارات لوحة المفاتيح
                    </h5>
                    
                    <div class="mb-2"><kbd>←</kbd> الشريحة التالية</div>
                    <div class="mb-2"><kbd>→</kbd> الشريحة السابقة</div>
                    <div class="mb-2"><kbd>Home</kbd> الشريحة الأولى</div>
                    <div class="mb-2"><kbd>End</kbd> الشريحة الأخيرة</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Hero Carousel JS -->
    <script src="assets/js/hero-carousel.js"></script>
</body>
</html>
