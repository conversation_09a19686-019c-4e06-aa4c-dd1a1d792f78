<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'إدارة النشرة البريدية';

// معالجة الحذف
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $subscriberId = (int)$_GET['delete'];
    deleteData('newsletter', 'id = ?', [$subscriberId]);
    $_SESSION['success'] = 'تم حذف المشترك بنجاح';
    header('Location: newsletter.php');
    exit();
}

// معالجة تغيير الحالة
if (isset($_GET['toggle_status']) && is_numeric($_GET['toggle_status'])) {
    $subscriberId = (int)$_GET['toggle_status'];
    $subscriber = fetchOne("SELECT status FROM newsletter WHERE id = ?", [$subscriberId]);
    
    if ($subscriber) {
        $newStatus = $subscriber['status'] == 'active' ? 'inactive' : 'active';
        updateData('newsletter', ['status' => $newStatus], 'id = ?', ['id' => $subscriberId]);
        $_SESSION['success'] = 'تم تحديث حالة المشترك بنجاح';
    }
    
    header('Location: newsletter.php');
    exit();
}

// معالجة إضافة مشترك جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_subscriber'])) {
    $email = sanitizeInput($_POST['email']);
    $status = sanitizeInput($_POST['status']);
    
    $errors = [];
    
    if (empty($email)) {
        $errors[] = 'البريد الإلكتروني مطلوب';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }
    
    // التحقق من عدم تكرار البريد الإلكتروني
    $existingSubscriber = fetchOne("SELECT id FROM newsletter WHERE email = ?", [$email]);
    if ($existingSubscriber) {
        $errors[] = 'البريد الإلكتروني موجود مسبقاً';
    }
    
    if (empty($errors)) {
        $subscriberData = [
            'email' => $email,
            'status' => $status
        ];
        
        insertData('newsletter', $subscriberData);
        $_SESSION['success'] = 'تم إضافة المشترك بنجاح';
        header('Location: newsletter.php');
        exit();
    }
}

// معالجة تصدير القائمة
if (isset($_GET['export'])) {
    $subscribers = fetchAll("SELECT email FROM newsletter WHERE status = 'active' ORDER BY created_at DESC");
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="newsletter_subscribers_' . date('Y-m-d') . '.csv"');
    
    $output = fopen('php://output', 'w');
    fputcsv($output, ['البريد الإلكتروني']);
    
    foreach ($subscribers as $subscriber) {
        fputcsv($output, [$subscriber['email']]);
    }
    
    fclose($output);
    exit();
}

// الفلترة والبحث
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$status = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';

$whereClause = "1=1";
$params = [];

if (!empty($search)) {
    $whereClause .= " AND email LIKE ?";
    $params[] = "%$search%";
}

if (!empty($status)) {
    $whereClause .= " AND status = ?";
    $params[] = $status;
}

// الترقيم
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 50;
$offset = ($page - 1) * $perPage;

// عدد المشتركين الإجمالي
$countResult = fetchOne("SELECT COUNT(*) as total FROM newsletter WHERE $whereClause", $params);
$totalSubscribers = ($countResult && isset($countResult['total'])) ? $countResult['total'] : 0;
$totalPages = ceil($totalSubscribers / $perPage);

// جلب المشتركين
$subscribers = fetchAll("
    SELECT * FROM newsletter 
    WHERE $whereClause 
    ORDER BY created_at DESC 
    LIMIT $perPage OFFSET $offset
", $params);

// إحصائيات سريعة
$stats = [
    'active' => fetchOne("SELECT COUNT(*) as count FROM newsletter WHERE status = 'active'"),
    'inactive' => fetchOne("SELECT COUNT(*) as count FROM newsletter WHERE status = 'inactive'"),
    'total' => fetchOne("SELECT COUNT(*) as count FROM newsletter")
];

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card text-center border-success">
                <div class="card-body">
                    <h5 class="text-success"><?php echo ($stats['active'] && isset($stats['active']['count'])) ? $stats['active']['count'] : 0; ?></h5>
                    <small>مشتركين نشطين</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center border-secondary">
                <div class="card-body">
                    <h5 class="text-secondary"><?php echo ($stats['inactive'] && isset($stats['inactive']['count'])) ? $stats['inactive']['count'] : 0; ?></h5>
                    <small>مشتركين غير نشطين</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center border-primary">
                <div class="card-body">
                    <h5 class="text-primary"><?php echo ($stats['total'] && isset($stats['total']['count'])) ? $stats['total']['count'] : 0; ?></h5>
                    <small>إجمالي المشتركين</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- نموذج إضافة مشترك جديد -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">إضافة مشترك جديد</h5>
                </div>
                
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني *</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" <?php echo ($_POST['status'] ?? 'active') == 'active' ? 'selected' : ''; ?>>نشط</option>
                                <option value="inactive" <?php echo ($_POST['status'] ?? '') == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                        
                        <button type="submit" name="add_subscriber" class="btn btn-primary w-100">
                            <i class="bi bi-plus-circle"></i> إضافة المشترك
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- أدوات إضافية -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">أدوات إضافية</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="?export=1" class="btn btn-outline-success">
                            <i class="bi bi-download"></i> تصدير قائمة المشتركين
                        </a>
                        <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#importModal">
                            <i class="bi bi-upload"></i> استيراد قائمة
                        </button>
                        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#sendNewsletterModal">
                            <i class="bi bi-envelope"></i> إرسال نشرة
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- قائمة المشتركين -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">قائمة المشتركين</h5>
                </div>
                
                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <form method="GET" class="row g-3 mb-4">
                        <div class="col-md-6">
                            <input type="text" class="form-control" name="search" 
                                   placeholder="البحث في البريد الإلكتروني..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="active" <?php echo $status == 'active' ? 'selected' : ''; ?>>نشط</option>
                                <option value="inactive" <?php echo $status == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-outline-primary w-100">بحث</button>
                        </div>
                    </form>
                    
                    <!-- جدول المشتركين -->
                    <?php if (!empty($subscribers)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>البريد الإلكتروني</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الاشتراك</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($subscribers as $subscriber): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($subscriber['email']); ?></td>
                                            <td>
                                                <span class="badge <?php echo $subscriber['status'] == 'active' ? 'bg-success' : 'bg-secondary'; ?>">
                                                    <?php echo $subscriber['status'] == 'active' ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($subscriber['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="?toggle_status=<?php echo $subscriber['id']; ?>" 
                                                       class="btn btn-sm btn-outline-warning" 
                                                       title="<?php echo $subscriber['status'] == 'active' ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                        <i class="bi bi-toggle-<?php echo $subscriber['status'] == 'active' ? 'on' : 'off'; ?>"></i>
                                                    </a>
                                                    <a href="?delete=<?php echo $subscriber['id']; ?>" 
                                                       class="btn btn-sm btn-outline-danger" 
                                                       title="حذف"
                                                       onclick="return confirm('هل أنت متأكد من حذف هذا المشترك؟')">
                                                        <i class="bi bi-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- الترقيم -->
                        <?php if ($totalPages > 1): ?>
                            <nav aria-label="ترقيم الصفحات">
                                <ul class="pagination justify-content-center">
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                        
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-envelope display-1 text-muted"></i>
                            <h4 class="mt-3">لا توجد مشتركين</h4>
                            <p class="text-muted">لم يتم العثور على مشتركين تطابق معايير البحث</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal استيراد قائمة -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد قائمة مشتركين</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="csv_file" class="form-label">ملف CSV</label>
                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                        <div class="form-text">يجب أن يحتوي الملف على عمود واحد للبريد الإلكتروني</div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" name="import_subscribers" class="btn btn-primary">استيراد</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal إرسال نشرة -->
<div class="modal fade" id="sendNewsletterModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إرسال نشرة بريدية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    هذه الميزة تتطلب إعداد خدمة البريد الإلكتروني (SMTP) في الإعدادات
                </div>
                <form method="POST">
                    <div class="mb-3">
                        <label for="subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="subject" name="subject" required>
                    </div>
                    <div class="mb-3">
                        <label for="message" class="form-label">محتوى الرسالة</label>
                        <textarea class="form-control" id="message" name="message" rows="8" required></textarea>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" name="send_newsletter" class="btn btn-primary">إرسال النشرة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
