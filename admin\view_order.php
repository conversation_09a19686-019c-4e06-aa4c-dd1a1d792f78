<?php
require_once '../config/config.php';
requireAdminLogin();

// التحقق من وجود معرف الطلب
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = 'معرف الطلب غير صحيح';
    header('Location: orders.php');
    exit();
}

$orderId = (int)$_GET['id'];

// جلب تفاصيل الطلب
$order = fetchOne("SELECT * FROM orders WHERE id = ?", [$orderId]);

if (!$order) {
    $_SESSION['error'] = 'الطلب غير موجود';
    header('Location: orders.php');
    exit();
}

// جلب عناصر الطلب
$orderItems = fetchAll("
    SELECT oi.*, p.image, p.category_id 
    FROM order_items oi 
    LEFT JOIN products p ON oi.product_id = p.id 
    WHERE oi.order_id = ?
    ORDER BY oi.id
", [$orderId]);

// معالجة تحديث حالة الطلب
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'update_status') {
        $newStatus = sanitizeInput($_POST['status']);
        $validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];
        
        if (in_array($newStatus, $validStatuses)) {
            $updateResult = updateData('orders', ['status' => $newStatus], 'id = ?', [$orderId]);
            
            if ($updateResult) {
                // تسجيل تغيير الحالة في سجل النشاط
                $logData = [
                    'order_id' => $orderId,
                    'old_status' => $order['status'],
                    'new_status' => $newStatus,
                    'admin_id' => $_SESSION['admin_id'] ?? 0,
                    'changed_at' => date('Y-m-d H:i:s'),
                    'notes' => 'تم تغيير الحالة من ' . getStatusText($order['status']) . ' إلى ' . getStatusText($newStatus)
                ];
                
                // إنشاء جدول سجل التغييرات إذا لم يكن موجوداً
                try {
                    $pdo->exec("CREATE TABLE IF NOT EXISTS order_status_log (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        order_id INT NOT NULL,
                        old_status VARCHAR(50),
                        new_status VARCHAR(50),
                        admin_id INT,
                        changed_at DATETIME,
                        notes TEXT,
                        INDEX(order_id)
                    )");
                    
                    insertData('order_status_log', $logData);
                } catch (Exception $e) {
                    error_log("Failed to log status change: " . $e->getMessage());
                }
                
                $order['status'] = $newStatus; // تحديث الحالة في المتغير المحلي
                $_SESSION['success'] = 'تم تحديث حالة الطلب بنجاح';
            } else {
                $_SESSION['error'] = 'فشل في تحديث حالة الطلب';
            }
        } else {
            $_SESSION['error'] = 'حالة الطلب غير صحيحة';
        }
    }
    
    // إعادة توجيه لتجنب إعادة الإرسال
    header('Location: view_order.php?id=' . $orderId);
    exit();
}

// دالة للحصول على نص الحالة
function getStatusText($status) {
    $statuses = [
        'pending' => 'معلق',
        'confirmed' => 'مؤكد',
        'processing' => 'قيد التحضير',
        'shipped' => 'مشحون',
        'delivered' => 'مكتمل',
        'cancelled' => 'ملغي'
    ];
    return $statuses[$status] ?? $status;
}

// دالة للحصول على لون الحالة
function getStatusColor($status) {
    $colors = [
        'pending' => 'warning',
        'confirmed' => 'info',
        'processing' => 'primary',
        'shipped' => 'secondary',
        'delivered' => 'success',
        'cancelled' => 'danger'
    ];
    return $colors[$status] ?? 'secondary';
}

// دالة للحصول على أيقونة الحالة
function getStatusIcon($status) {
    $icons = [
        'pending' => 'clock',
        'confirmed' => 'check-circle',
        'processing' => 'gear',
        'shipped' => 'truck',
        'delivered' => 'check-circle-fill',
        'cancelled' => 'x-circle'
    ];
    return $icons[$status] ?? 'circle';
}

$pageTitle = 'تفاصيل الطلب #' . $orderId;
require_once 'includes/header.php';
?>

<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="dashboard.php">لوحة التحكم</a></li>
            <li class="breadcrumb-item"><a href="orders.php">إدارة الطلبات</a></li>
            <li class="breadcrumb-item active">تفاصيل الطلب #<?= $orderId ?></li>
        </ol>
    </nav>

    <!-- Success/Error Messages -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i>
            <?= $_SESSION['success'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle"></i>
            <?= $_SESSION['error'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="row">
        <!-- Order Header -->
        <div class="col-12 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="bi bi-receipt"></i>
                            تفاصيل الطلب #<?= $orderId ?>
                        </h4>
                        <div class="d-flex gap-2">
                            <span class="badge bg-<?= getStatusColor($order['status']) ?> fs-6">
                                <i class="bi bi-<?= getStatusIcon($order['status']) ?>"></i>
                                <?= getStatusText($order['status']) ?>
                            </span>
                            <a href="orders.php" class="btn btn-light btn-sm">
                                <i class="bi bi-arrow-right"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">معلومات الطلب</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>رقم الطلب:</strong></td>
                                    <td>#<?= $order['id'] ?></td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الطلب:</strong></td>
                                    <td><?= date('Y-m-d H:i', strtotime($order['created_at'])) ?></td>
                                </tr>
                                <tr>
                                    <td><strong>طريقة الدفع:</strong></td>
                                    <td>
                                        <?php if ($order['payment_method'] === 'cash_on_delivery'): ?>
                                            <i class="bi bi-cash"></i> الدفع عند الاستلام
                                        <?php elseif ($order['payment_method'] === 'bank_transfer'): ?>
                                            <i class="bi bi-bank"></i> تحويل بنكي
                                        <?php else: ?>
                                            <?= htmlspecialchars($order['payment_method']) ?>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">ملخص المبالغ</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>المجموع الفرعي:</strong></td>
                                    <td><?= number_format($order['subtotal']) ?> دينار</td>
                                </tr>
                                <?php if ($order['discount_amount'] > 0): ?>
                                <tr class="text-success">
                                    <td><strong>الخصم:</strong></td>
                                    <td>-<?= number_format($order['discount_amount']) ?> دينار</td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td><strong>تكلفة التوصيل:</strong></td>
                                    <td>
                                        <?php if ($order['delivery_price'] == 0): ?>
                                            <span class="text-success">مجاني</span>
                                        <?php else: ?>
                                            <?= number_format($order['delivery_price']) ?> دينار
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr class="table-primary">
                                    <td><strong>المجموع الكلي:</strong></td>
                                    <td><strong><?= number_format($order['total_price']) ?> دينار</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-person"></i>
                        معلومات العميل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">اسم العميل</label>
                        <div class="fw-bold"><?= htmlspecialchars($order['customer_name']) ?></div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">رقم الهاتف</label>
                        <div class="fw-bold">
                            <i class="bi bi-telephone"></i>
                            <a href="tel:<?= htmlspecialchars($order['customer_phone']) ?>" class="text-decoration-none">
                                <?= htmlspecialchars($order['customer_phone']) ?>
                            </a>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">العنوان</label>
                        <div class="fw-bold"><?= nl2br(htmlspecialchars($order['address'])) ?></div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">المحافظة</label>
                        <div class="fw-bold">
                            <i class="bi bi-geo-alt"></i>
                            <?= htmlspecialchars($order['province']) ?>
                        </div>
                    </div>
                    
                    <?php if (!empty($order['notes'])): ?>
                    <div class="mb-3">
                        <label class="form-label text-muted">ملاحظات العميل</label>
                        <div class="alert alert-light">
                            <i class="bi bi-chat-left-text"></i>
                            <?= nl2br(htmlspecialchars($order['notes'])) ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Order Status Management -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="bi bi-gear"></i>
                        إدارة حالة الطلب
                    </h5>
                </div>

    <!-- Order Items -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-list-ul"></i>
                        عناصر الطلب (<?= count($orderItems) ?> منتج)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>المجموع</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orderItems as $item): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <?php if (!empty($item['image'])): ?>
                                                    <img src="<?= SITE_URL ?>/uploads/products/<?= $item['image'] ?>"
                                                         alt="<?= htmlspecialchars($item['product_name']) ?>"
                                                         class="rounded" style="width: 50px; height: 50px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                         style="width: 50px; height: 50px;">
                                                        <i class="bi bi-image text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?= htmlspecialchars($item['product_name']) ?></div>
                                                <?php if ($item['product_id']): ?>
                                                    <small class="text-muted">معرف المنتج: #<?= $item['product_id'] ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-bold"><?= number_format($item['price']) ?> دينار</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?= $item['quantity'] ?></span>
                                    </td>
                                    <td>
                                        <span class="fw-bold text-success"><?= number_format($item['total']) ?> دينار</span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <td colspan="3" class="text-end"><strong>المجموع الفرعي:</strong></td>
                                    <td><strong><?= number_format($order['subtotal']) ?> دينار</strong></td>
                                </tr>
                                <?php if ($order['discount_amount'] > 0): ?>
                                <tr class="text-success">
                                    <td colspan="3" class="text-end"><strong>الخصم:</strong></td>
                                    <td><strong>-<?= number_format($order['discount_amount']) ?> دينار</strong></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td colspan="3" class="text-end"><strong>تكلفة التوصيل:</strong></td>
                                    <td>
                                        <strong>
                                            <?php if ($order['delivery_price'] == 0): ?>
                                                <span class="text-success">مجاني</span>
                                            <?php else: ?>
                                                <?= number_format($order['delivery_price']) ?> دينار
                                            <?php endif; ?>
                                        </strong>
                                    </td>
                                </tr>
                                <tr class="table-primary">
                                    <td colspan="3" class="text-end"><strong>المجموع الكلي:</strong></td>
                                    <td><strong class="fs-5"><?= number_format($order['total_price']) ?> دينار</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-tools"></i>
                        إجراءات الطلب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>إجراءات سريعة</h6>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-primary" onclick="printOrder()">
                                    <i class="bi bi-printer"></i>
                                    طباعة الطلب
                                </button>

                                <button type="button" class="btn btn-outline-info" onclick="exportOrder()">
                                    <i class="bi bi-download"></i>
                                    تصدير تفاصيل الطلب
                                </button>

                                <a href="tel:<?= htmlspecialchars($order['customer_phone']) ?>" class="btn btn-outline-success">
                                    <i class="bi bi-telephone"></i>
                                    اتصال بالعميل
                                </a>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6>معلومات إضافية</h6>
                            <div class="small text-muted">
                                <div class="mb-2">
                                    <strong>تاريخ الإنشاء:</strong>
                                    <?= date('d/m/Y H:i:s', strtotime($order['created_at'])) ?>
                                </div>

                                <?php if ($order['updated_at'] && $order['updated_at'] !== $order['created_at']): ?>
                                <div class="mb-2">
                                    <strong>آخر تحديث:</strong>
                                    <?= date('d/m/Y H:i:s', strtotime($order['updated_at'])) ?>
                                </div>
                                <?php endif; ?>

                                <div class="mb-2">
                                    <strong>عدد المنتجات:</strong>
                                    <?= count($orderItems) ?> منتج
                                </div>

                                <div class="mb-2">
                                    <strong>إجمالي الكمية:</strong>
                                    <?= array_sum(array_column($orderItems, 'quantity')) ?> قطعة
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تأكيد تغيير الحالة - Professional Modal
function confirmStatusChange() {
    const statusSelect = document.getElementById('status');
    const selectedStatus = statusSelect.value;
    const statusText = statusSelect.options[statusSelect.selectedIndex].text;

    if (!selectedStatus) {
        showErrorToast('يرجى اختيار الحالة الجديدة');
        return false;
    }

    // Show professional confirmation modal
    showStatusChangeConfirmation(selectedStatus, statusText);
    return false; // Prevent form submission until confirmed
}

// تأكيد حذف الطلب - Professional Modal
function confirmDeleteOrder(orderId) {
    showDeleteConfirmation(orderId);
}

// Show status change confirmation modal
function showStatusChangeConfirmation(newStatus, statusText) {
    // Create modal if it doesn't exist
    if (!document.getElementById('statusChangeModal')) {
        const modalHTML = `
        <div class="modal fade" id="statusChangeModal" tabindex="-1" aria-labelledby="statusChangeModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-0 shadow-lg">
                    <div class="modal-header bg-primary text-white border-0">
                        <h5 class="modal-title" id="statusChangeModalLabel">
                            <i class="bi bi-arrow-repeat me-2"></i>
                            تأكيد تحديث حالة الطلب
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                    </div>
                    <div class="modal-body text-center py-4">
                        <div class="mb-3">
                            <i class="bi bi-arrow-up-circle text-primary" style="font-size: 3rem;"></i>
                        </div>
                        <h6 class="mb-3" id="statusChangeMessage">هل أنت متأكد من تحديث حالة الطلب؟</h6>
                        <div class="bg-light rounded p-3">
                            <div class="text-center">
                                <small class="text-muted">الحالة الجديدة:</small>
                                <div><strong id="newStatusText">-</strong></div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer border-0 justify-content-center">
                        <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-1"></i>
                            إلغاء
                        </button>
                        <button type="button" class="btn btn-primary px-4" id="confirmStatusChangeBtn">
                            <i class="bi bi-check-circle me-1"></i>
                            <span class="btn-text">تحديث الحالة</span>
                            <span class="spinner-border spinner-border-sm d-none me-1" role="status"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>`;
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    document.getElementById('statusChangeMessage').textContent = `هل أنت متأكد من تحديث حالة الطلب إلى "${statusText}"؟`;
    document.getElementById('newStatusText').textContent = statusText;

    const modal = new bootstrap.Modal(document.getElementById('statusChangeModal'));
    modal.show();

    // Handle confirmation
    document.getElementById('confirmStatusChangeBtn').onclick = function() {
        showButtonLoading(this);
        document.getElementById('statusUpdateForm').submit();
    };
}

// Show delete confirmation modal
function showDeleteConfirmation(orderId) {
    // Create modal if it doesn't exist
    if (!document.getElementById('deleteOrderModal')) {
        const modalHTML = `
        <div class="modal fade" id="deleteOrderModal" tabindex="-1" aria-labelledby="deleteOrderModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-0 shadow-lg">
                    <div class="modal-header bg-danger text-white border-0">
                        <h5 class="modal-title" id="deleteOrderModalLabel">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            تأكيد حذف الطلب
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                    </div>
                    <div class="modal-body text-center py-4">
                        <div class="mb-3">
                            <i class="bi bi-trash3 text-danger" style="font-size: 3rem;"></i>
                        </div>
                        <h6 class="mb-3">هل أنت متأكد من حذف هذا الطلب؟</h6>
                        <div class="alert alert-warning border-0 bg-warning bg-opacity-10">
                            <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع بيانات الطلب نهائياً
                        </div>
                    </div>
                    <div class="modal-footer border-0 justify-content-center">
                        <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-1"></i>
                            إلغاء
                        </button>
                        <button type="button" class="btn btn-danger px-4" id="confirmOrderDeleteBtn">
                            <i class="bi bi-trash me-1"></i>
                            <span class="btn-text">حذف الطلب</span>
                            <span class="spinner-border spinner-border-sm d-none me-1" role="status"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>`;
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    const modal = new bootstrap.Modal(document.getElementById('deleteOrderModal'));
    modal.show();

    // Handle confirmation
    document.getElementById('confirmOrderDeleteBtn').onclick = function() {
        showButtonLoading(this);

        // إرسال طلب الحذف
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'orders.php';

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'delete_order';

        const orderIdInput = document.createElement('input');
        orderIdInput.type = 'hidden';
        orderIdInput.name = 'order_id';
        orderIdInput.value = orderId;

        form.appendChild(actionInput);
        form.appendChild(orderIdInput);
        document.body.appendChild(form);
        form.submit();
    };
}

// Utility Functions for Professional UI

// Show button loading state
function showButtonLoading(button) {
    button.classList.add('loading');
    button.disabled = true;
    const spinner = button.querySelector('.spinner-border');
    const text = button.querySelector('.btn-text');

    if (spinner) spinner.classList.remove('d-none');
    if (text) text.style.opacity = '0.7';
}

// Hide button loading state
function hideButtonLoading(button) {
    button.classList.remove('loading');
    button.disabled = false;
    const spinner = button.querySelector('.spinner-border');
    const text = button.querySelector('.btn-text');

    if (spinner) spinner.classList.add('d-none');
    if (text) text.style.opacity = '1';
}

// Show success toast
function showSuccessToast(message) {
    // Create toast container if it doesn't exist
    if (!document.getElementById('successToast')) {
        const toastHTML = `
        <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
            <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <span id="successMessage">تم تنفيذ العملية بنجاح</span>
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="إغلاق"></button>
                </div>
            </div>
        </div>`;
        document.body.insertAdjacentHTML('beforeend', toastHTML);
    }

    document.getElementById('successMessage').textContent = message;
    const toast = new bootstrap.Toast(document.getElementById('successToast'), {
        autohide: true,
        delay: 5000
    });
    toast.show();
}

// Show error toast
function showErrorToast(message) {
    // Create toast container if it doesn't exist
    if (!document.getElementById('errorToast')) {
        const toastHTML = `
        <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999; margin-top: 80px;">
            <div id="errorToast" class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <span id="errorMessage">حدث خطأ أثناء تنفيذ العملية</span>
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="إغلاق"></button>
                </div>
            </div>
        </div>`;
        document.body.insertAdjacentHTML('beforeend', toastHTML);
    }

    document.getElementById('errorMessage').textContent = message;
    const toast = new bootstrap.Toast(document.getElementById('errorToast'), {
        autohide: true,
        delay: 5000
    });
    toast.show();
}

// WhatsApp Integration
function openWhatsApp(phone, orderId, customerName) {
    // تنظيف رقم الهاتف
    let cleanPhone = phone.replace(/[^\d]/g, '');

    // إضافة رمز العراق إذا لم يكن موجوداً
    if (!cleanPhone.startsWith('964')) {
        if (cleanPhone.startsWith('0')) {
            cleanPhone = '964' + cleanPhone.substring(1);
        } else {
            cleanPhone = '964' + cleanPhone;
        }
    }

    // إنشاء رسالة WhatsApp
    const message = `السلام عليكم ${customerName}،

نتواصل معكم بخصوص طلبكم رقم #${orderId}

شكراً لثقتكم بنا
فريق خدمة العملاء`;

    // ترميز الرسالة للـ URL
    const encodedMessage = encodeURIComponent(message);

    // فتح WhatsApp
    const whatsappUrl = `https://wa.me/${cleanPhone}?text=${encodedMessage}`;
    window.open(whatsappUrl, '_blank');
}

// Print Order Function - Enhanced
function printOrder() {
    // إخفاء العناصر غير المرغوب فيها في الطباعة
    const elementsToHide = [
        '.navbar',
        '.sidebar',
        '.btn',
        '.breadcrumb',
        '.card-footer',
        'button',
        '.no-print'
    ];

    elementsToHide.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => el.style.display = 'none');
    });

    // إضافة عنوان للطباعة
    const printHeader = document.createElement('div');
    printHeader.className = 'print-header';
    printHeader.innerHTML = `
        <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px;">
            <h1 style="color: #333; margin: 0;">تفاصيل الطلب #${<?= $orderId ?>}</h1>
            <p style="margin: 5px 0; color: #666;">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
        </div>
    `;

    document.body.insertBefore(printHeader, document.body.firstChild);

    // طباعة الصفحة
    window.print();

    // إعادة إظهار العناصر بعد الطباعة
    setTimeout(() => {
        elementsToHide.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => el.style.display = '');
        });

        // إزالة عنوان الطباعة
        const printHeaderEl = document.querySelector('.print-header');
        if (printHeaderEl) {
            printHeaderEl.remove();
        }
    }, 1000);
}

// Export Order to Excel
function exportOrderToExcel(orderId) {
    // إنشاء نموذج مخفي للتصدير
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = 'export_order.php';
    form.style.display = 'none';

    const orderIdInput = document.createElement('input');
    orderIdInput.type = 'hidden';
    orderIdInput.name = 'order_id';
    orderIdInput.value = orderId;

    form.appendChild(orderIdInput);
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية للأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });

    // تحسين عرض الجداول على الأجهزة المحمولة
    const tables = document.querySelectorAll('.table-responsive');
    tables.forEach(table => {
        if (window.innerWidth < 768) {
            table.style.fontSize = '0.9em';
        }
    });
});

// إضافة أنماط CSS محسنة للطباعة
const printStyles = `
    @media print {
        /* إخفاء العناصر غير المرغوب فيها */
        .navbar,
        .sidebar,
        .btn,
        .breadcrumb,
        .card-footer,
        button,
        .no-print {
            display: none !important;
        }

        /* تحسين التخطيط للطباعة */
        body {
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: white;
            margin: 0;
            padding: 20px;
        }

        .container-fluid {
            padding: 0;
            margin: 0;
            max-width: 100%;
        }

        .card {
            border: 1px solid #ddd;
            box-shadow: none;
            margin-bottom: 15px;
            page-break-inside: avoid;
        }

        .card-header {
            background: #f8f9fa !important;
            border-bottom: 1px solid #ddd;
            padding: 10px 15px;
            color: #000 !important;
        }

        .card-body {
            padding: 15px;
        }

        .table {
            font-size: 11px;
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 8px;
            border: 1px solid #ddd;
            text-align: right;
        }

        .table thead th {
            background: #f8f9fa !important;
            font-weight: bold;
            color: #000 !important;
        }

        /* تحسين الألوان للطباعة */
        .text-primary,
        .text-success,
        .text-warning,
        .text-danger,
        .text-info {
            color: #333 !important;
        }

        .badge {
            border: 1px solid #333;
            color: #333 !important;
            background: white !important;
            padding: 2px 6px;
        }

        /* تحسين التباعد */
        .row {
            margin: 0;
        }

        .col-md-8,
        .col-md-4 {
            padding: 0 10px;
        }

        /* منع تقسيم العناصر المهمة */
        .order-summary,
        .customer-info,
        .order-items {
            page-break-inside: avoid;
        }

        /* تحسين الصور للطباعة */
        img {
            max-width: 50px;
            height: auto;
        }

        /* إضافة حدود للجداول */
        .table-bordered {
            border: 2px solid #333;
        }

        .table-bordered th,
        .table-bordered td {
            border: 1px solid #333;
        }

        /* تحسين عرض المعلومات المهمة */
        .alert {
            border: 1px solid #333 !important;
            background: white !important;
            color: #333 !important;
        }

        /* تحسين الخطوط */
        h1, h2, h3, h4, h5, h6 {
            color: #000 !important;
        }

        /* إخفاء الروابط */
        a[href]:after {
            content: none !important;
        }
    }
`;

const styleSheet = document.createElement('style');
styleSheet.textContent = printStyles;
document.head.appendChild(styleSheet);
</script>

<!-- Additional CSS for WhatsApp and enhanced styling -->
<style>
/* تحسين عرض WhatsApp button */
.btn-success {
    background-color: #25D366;
    border-color: #25D366;
}

.btn-success:hover {
    background-color: #128C7E;
    border-color: #128C7E;
}

.btn-success:focus {
    box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25);
}

/* WhatsApp icon */
.bi-whatsapp:before {
    content: "\f7e7";
}

/* تحسين أزرار الإجراءات */
.d-grid .btn {
    margin-bottom: 0.5rem;
}

.d-grid .btn:last-child {
    margin-bottom: 0;
}

/* تحسين الطباعة */
@media print {
    .no-print,
    .btn,
    .breadcrumb,
    .card-footer,
    button,
    .navbar,
    .sidebar {
        display: none !important;
    }

    .print-header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #333;
        padding-bottom: 20px;
    }

    .print-header h1 {
        color: #333;
        margin: 0;
        font-size: 24px;
    }

    .print-header p {
        margin: 5px 0;
        color: #666;
        font-size: 14px;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
