<?php
/**
 * إصلاح مشكلة تسجيل دخول المدير
 * Fix Admin Login Issue
 */

echo "<h2>إصلاح مشكلة تسجيل دخول المدير</h2>";

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'shop_db';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // التحقق من وجود جدول المديرين
    $tableExists = $pdo->query("SHOW TABLES LIKE 'admins'")->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<p style='color: red;'>❌ جدول المديرين غير موجود</p>";
        
        // إنشاء جدول المديرين
        $createTable = "
        CREATE TABLE `admins` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `username` varchar(50) NOT NULL,
          `password` varchar(255) NOT NULL,
          `email` varchar(100) DEFAULT NULL,
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          UNIQUE KEY `username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($createTable);
        echo "<p style='color: green;'>✅ تم إنشاء جدول المديرين</p>";
    } else {
        echo "<p style='color: green;'>✅ جدول المديرين موجود</p>";
    }
    
    // التحقق من وجود المدير الافتراضي
    $adminExists = $pdo->query("SELECT COUNT(*) FROM admins WHERE username = 'admin'")->fetchColumn();
    
    if ($adminExists == 0) {
        echo "<p style='color: orange;'>⚠️ المدير الافتراضي غير موجود - سيتم إنشاؤه</p>";
        
        // إنشاء المدير الافتراضي
        $newPassword = password_hash('password', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO admins (username, password, email) VALUES (?, ?, ?)");
        $stmt->execute(['admin', $newPassword, '<EMAIL>']);
        
        echo "<p style='color: green;'>✅ تم إنشاء المدير الافتراضي</p>";
    } else {
        echo "<p style='color: green;'>✅ المدير الافتراضي موجود</p>";
        
        // عرض معلومات المدير الحالي
        $admin = $pdo->query("SELECT * FROM admins WHERE username = 'admin'")->fetch();
        echo "<p><strong>معلومات المدير الحالي:</strong></p>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> " . $admin['id'] . "</li>";
        echo "<li><strong>اسم المستخدم:</strong> " . $admin['username'] . "</li>";
        echo "<li><strong>البريد الإلكتروني:</strong> " . $admin['email'] . "</li>";
        echo "<li><strong>تاريخ الإنشاء:</strong> " . $admin['created_at'] . "</li>";
        echo "</ul>";
    }
    
    // إعادة تعيين كلمة المرور
    if (isset($_POST['reset_password'])) {
        $newPassword = password_hash('password', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE admins SET password = ? WHERE username = 'admin'");
        $stmt->execute([$newPassword]);
        
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>✅ تم إعادة تعيين كلمة المرور بنجاح!</h4>";
        echo "<p><strong>بيانات تسجيل الدخول الجديدة:</strong></p>";
        echo "<ul>";
        echo "<li><strong>اسم المستخدم:</strong> admin</li>";
        echo "<li><strong>كلمة المرور:</strong> password</li>";
        echo "</ul>";
        echo "<p><a href='admin/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول الآن</a></p>";
        echo "</div>";
    }
    
    // اختبار تسجيل الدخول
    if (isset($_POST['test_login'])) {
        $testUsername = $_POST['test_username'];
        $testPassword = $_POST['test_password'];
        
        $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ?");
        $stmt->execute([$testUsername]);
        $admin = $stmt->fetch();
        
        if ($admin && password_verify($testPassword, $admin['password'])) {
            echo "<p style='color: green;'>✅ اختبار تسجيل الدخول نجح!</p>";
        } else {
            echo "<p style='color: red;'>❌ اختبار تسجيل الدخول فشل!</p>";
            if (!$admin) {
                echo "<p>السبب: اسم المستخدم غير موجود</p>";
            } else {
                echo "<p>السبب: كلمة المرور غير صحيحة</p>";
            }
        }
    }
    
    // عرض جميع المديرين
    $allAdmins = $pdo->query("SELECT id, username, email, created_at FROM admins")->fetchAll();
    echo "<h3>جميع المديرين في النظام:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>ID</th>";
    echo "<th style='padding: 10px;'>اسم المستخدم</th>";
    echo "<th style='padding: 10px;'>البريد الإلكتروني</th>";
    echo "<th style='padding: 10px;'>تاريخ الإنشاء</th>";
    echo "</tr>";
    
    foreach ($allAdmins as $admin) {
        echo "<tr>";
        echo "<td style='padding: 10px;'>" . $admin['id'] . "</td>";
        echo "<td style='padding: 10px;'>" . $admin['username'] . "</td>";
        echo "<td style='padding: 10px;'>" . $admin['email'] . "</td>";
        echo "<td style='padding: 10px;'>" . $admin['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل MySQL في XAMPP</li>";
    echo "<li>وجود قاعدة البيانات shop_db</li>";
    echo "<li>صحة بيانات الاتصال</li>";
    echo "</ul>";
}
?>

<hr>

<h3>إجراءات الإصلاح:</h3>

<!-- إعادة تعيين كلمة المرور -->
<div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h4>🔑 إعادة تعيين كلمة المرور</h4>
    <p>إذا كنت تواجه مشكلة في تسجيل الدخول، اضغط على الزر أدناه لإعادة تعيين كلمة المرور إلى "password"</p>
    <form method="POST">
        <button type="submit" name="reset_password" style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
            إعادة تعيين كلمة المرور
        </button>
    </form>
</div>

<!-- اختبار تسجيل الدخول -->
<div style="background: #d1ecf1; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h4>🧪 اختبار تسجيل الدخول</h4>
    <p>اختبر بيانات تسجيل الدخول هنا قبل المحاولة في لوحة التحكم:</p>
    <form method="POST">
        <div style="margin-bottom: 15px;">
            <label>اسم المستخدم:</label><br>
            <input type="text" name="test_username" value="admin" style="padding: 8px; width: 200px;">
        </div>
        <div style="margin-bottom: 15px;">
            <label>كلمة المرور:</label><br>
            <input type="password" name="test_password" value="password" style="padding: 8px; width: 200px;">
        </div>
        <button type="submit" name="test_login" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
            اختبار تسجيل الدخول
        </button>
    </form>
</div>

<div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h4>✅ الخطوات التالية:</h4>
    <ol>
        <li>استخدم "إعادة تعيين كلمة المرور" إذا كنت تواجه مشاكل</li>
        <li>اختبر تسجيل الدخول باستخدام النموذج أعلاه</li>
        <li>إذا نجح الاختبار، اذهب إلى <a href="admin/login.php">لوحة التحكم</a></li>
        <li>استخدم: <strong>admin</strong> / <strong>password</strong></li>
        <li>احذف هذا الملف بعد حل المشكلة</li>
    </ol>
</div>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h2, h3, h4 {
    color: #333;
}

table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

input, button {
    font-family: inherit;
}

button:hover {
    opacity: 0.9;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
