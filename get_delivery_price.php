<?php
/**
 * AJAX endpoint للحصول على سعر التوصيل للمحافظة المحددة
 * AJAX endpoint to get delivery price for selected province
 */

require_once 'config/config.php';
require_once 'config/database.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

$province = isset($_POST['province']) ? sanitizeInput($_POST['province']) : '';
$subtotal = isset($_POST['subtotal']) ? (float)$_POST['subtotal'] : 0;
$discountAmount = isset($_POST['discount_amount']) ? (float)$_POST['discount_amount'] : 0;

if (empty($province)) {
    echo json_encode(['success' => false, 'error' => 'Province is required']);
    exit();
}

try {
    // جلب سعر التوصيل للمحافظة من قاعدة البيانات
    $deliveryData = fetchOne("SELECT price FROM delivery_pricing WHERE province = ? AND is_active = 1", [$province]);
    
    $deliveryPrice = 0;
    
    if ($deliveryData) {
        $deliveryPrice = (float)$deliveryData['price'];
    } else {
        // المحافظات الافتراضية في حالة عدم وجود البيانات في قاعدة البيانات
        $defaultPrices = [
            'بغداد' => 5000,
            'البصرة' => 8000,
            'نينوى' => 7000,
            'أربيل' => 7500,
            'النجف' => 6000,
            'كربلاء' => 6000,
            'الأنبار' => 8500,
            'ذي قار' => 7500,
            'بابل' => 6500,
            'كركوك' => 7000,
            'ديالى' => 6500,
            'المثنى' => 8000,
            'القادسية' => 7000,
            'ميسان' => 7500,
            'واسط' => 6500,
            'دهوك' => 8000,
            'السليمانية' => 7500,
            'صلاح الدين' => 7000
        ];
        
        $deliveryPrice = isset($defaultPrices[$province]) ? $defaultPrices[$province] : (float)getSetting('delivery_price');
    }
    
    // التحقق من الشحن المجاني
    $freeDeliveryThreshold = (float)getSetting('free_delivery_threshold');
    $isFreeDelivery = false;
    
    if ($subtotal >= $freeDeliveryThreshold) {
        $deliveryPrice = 0;
        $isFreeDelivery = true;
    }
    
    // حساب المجموع النهائي مع الخصم
    $finalTotal = $subtotal - $discountAmount + $deliveryPrice;

    // التأكد من أن المجموع النهائي لا يقل عن صفر
    if ($finalTotal < 0) {
        $finalTotal = 0;
    }
    
    echo json_encode([
        'success' => true,
        'delivery_price' => $deliveryPrice,
        'delivery_price_formatted' => formatPrice($deliveryPrice),
        'is_free_delivery' => $isFreeDelivery,
        'final_total' => $finalTotal,
        'final_total_formatted' => formatPrice($finalTotal),
        'province' => $province
    ]);
    
} catch (Exception $e) {
    error_log("Delivery price error: " . $e->getMessage());
    echo json_encode(['error' => 'حدث خطأ في حساب سعر التوصيل']);
}
?>
