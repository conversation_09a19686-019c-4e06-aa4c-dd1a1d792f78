<?php
/**
 * Test order submission workflow
 */

require_once 'config/config.php';

echo "<h2>Order Submission Test</h2>";

// Test database connection
if (!$pdo) {
    die("❌ Database connection failed");
}
echo "✅ Database connection OK<br>";

// Check if orders table exists
try {
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'orders'");
    if ($tableCheck->rowCount() > 0) {
        echo "✅ Orders table exists<br>";
    } else {
        echo "❌ Orders table does not exist<br>";
        die("Please run setup_orders_tables.php first");
    }
} catch (Exception $e) {
    echo "❌ Error checking orders table: " . $e->getMessage() . "<br>";
    die();
}

// Check if order_items table exists
try {
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'order_items'");
    if ($tableCheck->rowCount() > 0) {
        echo "✅ Order_items table exists<br>";
    } else {
        echo "❌ Order_items table does not exist<br>";
        die("Please run setup_orders_tables.php first");
    }
} catch (Exception $e) {
    echo "❌ Error checking order_items table: " . $e->getMessage() . "<br>";
    die();
}

// Test order insertion
echo "<h3>Testing Order Insertion</h3>";

$testOrderData = [
    'customer_name' => 'Test Customer',
    'customer_phone' => '07123456789',
    'address' => 'Test Address, Baghdad',
    'province' => 'بغداد',
    'subtotal' => 50000.00,
    'delivery_price' => 5000.00,
    'discount_amount' => 0.00,
    'total_price' => 55000.00,
    'payment_method' => 'cash_on_delivery',
    'status' => 'pending'
];

echo "Test order data: " . json_encode($testOrderData, JSON_UNESCAPED_UNICODE) . "<br>";

$orderId = insertData('orders', $testOrderData);

if ($orderId) {
    echo "✅ Order inserted successfully - ID: {$orderId}<br>";
    
    // Test order item insertion
    $testItemData = [
        'order_id' => $orderId,
        'product_id' => 1,
        'product_name' => 'Test Product',
        'quantity' => 2,
        'price' => 25000.00,
        'total' => 50000.00
    ];
    
    $itemId = insertData('order_items', $testItemData);
    
    if ($itemId) {
        echo "✅ Order item inserted successfully - ID: {$itemId}<br>";
    } else {
        echo "❌ Order item insertion failed<br>";
    }
    
    // Verify order in database
    $verifyOrder = fetchOne("SELECT * FROM orders WHERE id = ?", [$orderId]);
    if ($verifyOrder) {
        echo "✅ Order verified in database<br>";
        echo "Customer: " . $verifyOrder['customer_name'] . "<br>";
        echo "Phone: " . $verifyOrder['customer_phone'] . "<br>";
        echo "Total: " . number_format($verifyOrder['total_price']) . " IQD<br>";
        echo "Status: " . $verifyOrder['status'] . "<br>";
        echo "Created: " . $verifyOrder['created_at'] . "<br>";
    } else {
        echo "❌ Order not found in database<br>";
    }
    
    // Check if order appears in admin query
    $adminOrders = fetchAll("SELECT * FROM orders ORDER BY created_at DESC LIMIT 5");
    echo "<h3>Recent Orders (as admin would see):</h3>";
    if (count($adminOrders) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Customer</th><th>Phone</th><th>Total</th><th>Status</th><th>Created</th></tr>";
        foreach ($adminOrders as $order) {
            $highlight = ($order['id'] == $orderId) ? 'style="background-color: #ffffcc;"' : '';
            echo "<tr {$highlight}>";
            echo "<td>" . $order['id'] . "</td>";
            echo "<td>" . htmlspecialchars($order['customer_name']) . "</td>";
            echo "<td>" . htmlspecialchars($order['customer_phone']) . "</td>";
            echo "<td>" . number_format($order['total_price']) . " IQD</td>";
            echo "<td>" . $order['status'] . "</td>";
            echo "<td>" . $order['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "✅ Orders are visible in admin query<br>";
    } else {
        echo "❌ No orders found in admin query<br>";
    }
    
    // Clean up test data
    $pdo->prepare("DELETE FROM order_items WHERE order_id = ?")->execute([$orderId]);
    $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$orderId]);
    echo "✅ Test data cleaned up<br>";
    
} else {
    echo "❌ Order insertion failed<br>";
}

echo "<h3>Test Summary</h3>";
echo "<p>If all tests passed, the order submission workflow should work correctly.</p>";
echo "<p><a href='checkout.php'>Test Checkout Page</a> | <a href='admin/orders.php'>Check Admin Orders</a></p>";
?>
