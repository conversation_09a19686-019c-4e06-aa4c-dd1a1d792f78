<?php
/**
 * ملف إعداد قاعدة البيانات السريع
 * Quick Database Setup Script
 * 
 * استخدم هذا الملف لإعداد قاعدة البيانات بسرعة
 * Use this file to quickly setup the database
 */

// إعدادات قاعدة البيانات لـ XAMPP
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'shop_db';

echo "<h2>إعداد قاعدة البيانات للمتجر الإلكتروني</h2>";
echo "<p>جاري إعداد قاعدة البيانات...</p>";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة البيانات
    echo "<p>1. الاتصال بخادم MySQL...</p>";
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ تم الاتصال بخادم MySQL بنجاح</p>";
    
    // إنشاء قاعدة البيانات
    echo "<p>2. إنشاء قاعدة البيانات '$database'...</p>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✓ تم إنشاء قاعدة البيانات بنجاح</p>";
    
    // الاتصال بقاعدة البيانات
    echo "<p>3. الاتصال بقاعدة البيانات...</p>";
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // قراءة ملف SQL
    echo "<p>4. قراءة ملف database.sql...</p>";
    $sqlFile = __DIR__ . '/database.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("ملف database.sql غير موجود في المجلد الجذر");
    }
    
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception("لا يمكن قراءة ملف database.sql");
    }
    echo "<p style='color: green;'>✓ تم قراءة ملف SQL بنجاح</p>";
    
    // تنفيذ الاستعلامات
    echo "<p>5. إنشاء الجداول...</p>";
    
    // تقسيم الاستعلامات
    $statements = explode(';', $sql);
    $executedCount = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        // تجاهل الأسطر الفارغة والتعليقات
        if (empty($statement) || 
            $statement === 'COMMIT' || 
            $statement === 'START TRANSACTION' ||
            preg_match('/^(SET|--)/i', $statement)) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $executedCount++;
        } catch (PDOException $e) {
            // تجاهل أخطاء الجداول الموجودة مسبقاً
            if ($e->getCode() != '42S01') { // Table already exists
                echo "<p style='color: orange;'>تحذير: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<p style='color: green;'>✓ تم تنفيذ $executedCount استعلام بنجاح</p>";
    
    // التحقق من الجداول المنشأة
    echo "<p>6. التحقق من الجداول المنشأة...</p>";
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li style='color: green;'>✓ $table</li>";
    }
    echo "</ul>";
    
    // التحقق من البيانات الافتراضية
    echo "<p>7. التحقق من البيانات الافتراضية...</p>";
    
    // تحقق من المدير الافتراضي
    $adminCount = $pdo->query("SELECT COUNT(*) FROM admins")->fetchColumn();
    echo "<p style='color: green;'>✓ عدد المديرين: $adminCount</p>";
    
    // تحقق من التصنيفات
    $categoryCount = $pdo->query("SELECT COUNT(*) FROM categories")->fetchColumn();
    echo "<p style='color: green;'>✓ عدد التصنيفات: $categoryCount</p>";
    
    // تحقق من الإعدادات
    $settingsCount = $pdo->query("SELECT COUNT(*) FROM site_settings")->fetchColumn();
    echo "<p style='color: green;'>✓ عدد الإعدادات: $settingsCount</p>";
    
    echo "<hr>";
    echo "<h3 style='color: green;'>✅ تم إعداد قاعدة البيانات بنجاح!</h3>";
    echo "<p><strong>بيانات تسجيل الدخول للوحة التحكم:</strong></p>";
    echo "<ul>";
    echo "<li><strong>الرابط:</strong> <a href='admin/login.php'>admin/login.php</a></li>";
    echo "<li><strong>اسم المستخدم:</strong> admin</li>";
    echo "<li><strong>كلمة المرور:</strong> password</li>";
    echo "</ul>";
    
    echo "<p><strong>الخطوات التالية:</strong></p>";
    echo "<ol>";
    echo "<li>احذف هذا الملف (setup_database.php) لأسباب أمنية</li>";
    echo "<li>قم بزيارة <a href='index.php'>الصفحة الرئيسية</a></li>";
    echo "<li>سجل دخول إلى <a href='admin/login.php'>لوحة التحكم</a></li>";
    echo "<li>غيّر كلمة مرور المدير من الإعدادات</li>";
    echo "<li>ابدأ في إضافة المنتجات</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ خطأ في قاعدة البيانات</h3>";
    echo "<p style='color: red;'>الخطأ: " . $e->getMessage() . "</p>";
    
    echo "<h4>خطوات حل المشكلة:</h4>";
    echo "<ol>";
    echo "<li><strong>تأكد من تشغيل XAMPP:</strong>";
    echo "<ul>";
    echo "<li>افتح XAMPP Control Panel</li>";
    echo "<li>تأكد من تشغيل Apache و MySQL</li>";
    echo "<li>إذا كان MySQL لا يعمل، اضغط على 'Start'</li>";
    echo "</ul></li>";
    
    echo "<li><strong>تحقق من إعدادات قاعدة البيانات:</strong>";
    echo "<ul>";
    echo "<li>الخادم: localhost</li>";
    echo "<li>اسم المستخدم: root</li>";
    echo "<li>كلمة المرور: (فارغة عادة في XAMPP)</li>";
    echo "</ul></li>";
    
    echo "<li><strong>تحقق من المنفذ:</strong>";
    echo "<ul>";
    echo "<li>المنفذ الافتراضي لـ MySQL هو 3306</li>";
    echo "<li>إذا كان مختلفاً، عدّل الإعدادات في config/database.php</li>";
    echo "</ul></li>";
    
    echo "<li><strong>أعد تشغيل XAMPP:</strong>";
    echo "<ul>";
    echo "<li>أوقف Apache و MySQL</li>";
    echo "<li>أعد تشغيلهما</li>";
    echo "<li>حاول مرة أخرى</li>";
    echo "</ul></li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ خطأ عام</h3>";
    echo "<p style='color: red;'>الخطأ: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
}

h2, h3 {
    color: #333;
}

p, li {
    line-height: 1.6;
}

ul, ol {
    padding-left: 20px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

hr {
    margin: 30px 0;
    border: none;
    border-top: 2px solid #ddd;
}
</style>
