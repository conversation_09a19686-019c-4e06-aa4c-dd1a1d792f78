<?php
/**
 * Search Suggestions AJAX Handler
 * Professional Arabic E-commerce Search Functionality
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json; charset=utf-8');

try {
    if (!isset($_GET['q']) || empty(trim($_GET['q']))) {
        echo json_encode(['success' => false, 'message' => 'استعلام البحث مطلوب']);
        exit;
    }

    $query = trim($_GET['q']);
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 5;

    // Search in products
    $searchQuery = "
        SELECT DISTINCT 
            p.id,
            p.name,
            p.price,
            c.name as category_name,
            'product' as type
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 'active' 
        AND (
            p.name LIKE :query 
            OR p.short_description LIKE :query 
            OR p.description LIKE :query
            OR c.name LIKE :query
        )
        ORDER BY 
            CASE 
                WHEN p.name LIKE :exact_query THEN 1
                WHEN p.name LIKE :start_query THEN 2
                ELSE 3
            END,
            p.name ASC
        LIMIT :limit
    ";

    $stmt = $pdo->prepare($searchQuery);
    $searchTerm = '%' . $query . '%';
    $exactTerm = $query;
    $startTerm = $query . '%';
    
    $stmt->bindParam(':query', $searchTerm, PDO::PARAM_STR);
    $stmt->bindParam(':exact_query', $exactTerm, PDO::PARAM_STR);
    $stmt->bindParam(':start_query', $startTerm, PDO::PARAM_STR);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Search in categories
    $categoryQuery = "
        SELECT DISTINCT 
            c.id,
            c.name,
            COUNT(p.id) as product_count,
            'category' as type
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id AND p.status = 'active'
        WHERE c.status = 'active' 
        AND c.name LIKE :query
        GROUP BY c.id, c.name
        ORDER BY 
            CASE 
                WHEN c.name LIKE :exact_query THEN 1
                WHEN c.name LIKE :start_query THEN 2
                ELSE 3
            END,
            product_count DESC,
            c.name ASC
        LIMIT 3
    ";

    $stmt = $pdo->prepare($categoryQuery);
    $stmt->bindParam(':query', $searchTerm, PDO::PARAM_STR);
    $stmt->bindParam(':exact_query', $exactTerm, PDO::PARAM_STR);
    $stmt->bindParam(':start_query', $startTerm, PDO::PARAM_STR);
    
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Combine and format results
    $suggestions = [];
    
    // Add category suggestions
    foreach ($categories as $category) {
        $suggestions[] = [
            'id' => $category['id'],
            'name' => $category['name'],
            'type' => 'category',
            'subtitle' => $category['product_count'] . ' منتج',
            'url' => SITE_URL . '/products.php?category=' . $category['id']
        ];
    }
    
    // Add product suggestions
    foreach ($products as $product) {
        $suggestions[] = [
            'id' => $product['id'],
            'name' => $product['name'],
            'type' => 'product',
            'subtitle' => formatPrice($product['price']) . ' - ' . $product['category_name'],
            'url' => SITE_URL . '/product.php?id=' . $product['id']
        ];
    }

    // Limit total suggestions
    $suggestions = array_slice($suggestions, 0, $limit);

    echo json_encode([
        'success' => true,
        'suggestions' => $suggestions,
        'query' => $query,
        'total' => count($suggestions)
    ]);

} catch (Exception $e) {
    error_log("Search suggestions error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في البحث، يرجى المحاولة مرة أخرى'
    ]);
}
?>
