<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'اختبار طرق الحذف المختلفة';

echo "<h2>اختبار طرق الحذف المختلفة</h2>";

// Create test product if requested
if (isset($_POST['create_test_product'])) {
    $testProduct = [
        'name' => 'منتج اختبار الحذف ' . date('H:i:s'),
        'description' => 'منتج مؤقت لاختبار وظائف الحذف المختلفة',
        'price' => 1.00,
        'stock' => 1,
        'status' => 'inactive'
    ];
    
    $testId = insertData('products', $testProduct);
    if ($testId) {
        echo "<div class='alert alert-success'>تم إنشاء منتج اختبار جديد (ID: $testId)</div>";
    } else {
        echo "<div class='alert alert-danger'>فشل في إنشاء منتج اختبار</div>";
    }
}

// Handle POST delete
if (isset($_POST['delete_product_post']) && is_numeric($_POST['delete_product_post'])) {
    $productId = (int)$_POST['delete_product_post'];
    
    echo "<h3>اختبار الحذف بطريقة POST</h3>";
    
    $product = fetchOne("SELECT name FROM products WHERE id = ?", [$productId]);
    
    if ($product) {
        try {
            $images = fetchAll("SELECT image_path FROM product_images WHERE product_id = ?", [$productId]);
            $deleteImages = deleteData('product_images', 'product_id = ?', [$productId]);
            $deleteProduct = deleteData('products', 'id = ?', [$productId]);
            
            if ($deleteProduct !== false && $deleteProduct > 0) {
                echo "<div class='alert alert-success'>";
                echo "<h4>✅ نجح الحذف بطريقة POST!</h4>";
                echo "<p>تم حذف المنتج '" . htmlspecialchars($product['name']) . "' بنجاح.</p>";
                echo "<p>صفوف محذوفة: $deleteProduct</p>";
                echo "</div>";
            } else {
                $checkProduct = fetchOne("SELECT id FROM products WHERE id = ?", [$productId]);
                if (!$checkProduct) {
                    echo "<div class='alert alert-success'>";
                    echo "<h4>✅ نجح الحذف بطريقة POST (تأكيد إضافي)!</h4>";
                    echo "<p>المنتج لم يعد موجود في قاعدة البيانات.</p>";
                    echo "</div>";
                } else {
                    echo "<div class='alert alert-danger'>";
                    echo "<h4>❌ فشل الحذف بطريقة POST!</h4>";
                    echo "<p>المنتج ما زال موجود في قاعدة البيانات.</p>";
                    echo "</div>";
                }
            }
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h4>❌ خطأ في الحذف بطريقة POST!</h4>";
            echo "<p>رسالة الخطأ: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    } else {
        echo "<div class='alert alert-warning'>المنتج غير موجود</div>";
    }
}

// Handle GET delete
if (isset($_GET['delete_product_get']) && is_numeric($_GET['delete_product_get'])) {
    $productId = (int)$_GET['delete_product_get'];
    
    echo "<h3>اختبار الحذف بطريقة GET</h3>";
    
    $product = fetchOne("SELECT name FROM products WHERE id = ?", [$productId]);
    
    if ($product) {
        if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
            try {
                $images = fetchAll("SELECT image_path FROM product_images WHERE product_id = ?", [$productId]);
                $deleteImages = deleteData('product_images', 'product_id = ?', [$productId]);
                $deleteProduct = deleteData('products', 'id = ?', [$productId]);
                
                if ($deleteProduct !== false && $deleteProduct > 0) {
                    echo "<div class='alert alert-success'>";
                    echo "<h4>✅ نجح الحذف بطريقة GET!</h4>";
                    echo "<p>تم حذف المنتج '" . htmlspecialchars($product['name']) . "' بنجاح.</p>";
                    echo "<p>صفوف محذوفة: $deleteProduct</p>";
                    echo "</div>";
                } else {
                    $checkProduct = fetchOne("SELECT id FROM products WHERE id = ?", [$productId]);
                    if (!$checkProduct) {
                        echo "<div class='alert alert-success'>";
                        echo "<h4>✅ نجح الحذف بطريقة GET (تأكيد إضافي)!</h4>";
                        echo "<p>المنتج لم يعد موجود في قاعدة البيانات.</p>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-danger'>";
                        echo "<h4>❌ فشل الحذف بطريقة GET!</h4>";
                        echo "<p>المنتج ما زال موجود في قاعدة البيانات.</p>";
                        echo "</div>";
                    }
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>";
                echo "<h4>❌ خطأ في الحذف بطريقة GET!</h4>";
                echo "<p>رسالة الخطأ: " . $e->getMessage() . "</p>";
                echo "</div>";
            }
        } else {
            echo "<div class='alert alert-warning'>";
            echo "<h4>تأكيد الحذف بطريقة GET</h4>";
            echo "<p>هل أنت متأكد من حذف المنتج '" . htmlspecialchars($product['name']) . "'؟</p>";
            echo "<a href='?delete_product_get=$productId&confirm=yes' class='btn btn-danger'>نعم، احذف</a> ";
            echo "<a href='?' class='btn btn-secondary'>إلغاء</a>";
            echo "</div>";
        }
    } else {
        echo "<div class='alert alert-warning'>المنتج غير موجود</div>";
    }
}

// Show available products
echo "<h3>المنتجات المتاحة للاختبار:</h3>";

$products = fetchAll("SELECT id, name, status, created_at FROM products ORDER BY id DESC LIMIT 10");

if (!empty($products)) {
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped table-bordered'>";
    echo "<thead class='table-dark'>";
    echo "<tr>";
    echo "<th>ID</th>";
    echo "<th>اسم المنتج</th>";
    echo "<th>الحالة</th>";
    echo "<th>تاريخ الإنشاء</th>";
    echo "<th>اختبار GET</th>";
    echo "<th>اختبار POST</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($products as $prod) {
        echo "<tr>";
        echo "<td>" . $prod['id'] . "</td>";
        echo "<td>" . htmlspecialchars($prod['name']) . "</td>";
        echo "<td><span class='badge bg-" . ($prod['status'] == 'active' ? 'success' : 'secondary') . "'>" . $prod['status'] . "</span></td>";
        echo "<td>" . $prod['created_at'] . "</td>";
        echo "<td>";
        echo "<a href='?delete_product_get=" . $prod['id'] . "' class='btn btn-sm btn-outline-primary'>حذف GET</a>";
        echo "</td>";
        echo "<td>";
        echo "<form method='POST' style='display: inline;' onsubmit='return confirm(\"هل أنت متأكد من حذف هذا المنتج؟\")'>";
        echo "<input type='hidden' name='delete_product_post' value='" . $prod['id'] . "'>";
        echo "<button type='submit' class='btn btn-sm btn-outline-danger'>حذف POST</button>";
        echo "</form>";
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
} else {
    echo "<div class='alert alert-info'>لا توجد منتجات في قاعدة البيانات</div>";
}

// Create test product form
echo "<hr>";
echo "<div class='card'>";
echo "<div class='card-header'>";
echo "<h4>إنشاء منتج اختبار</h4>";
echo "</div>";
echo "<div class='card-body'>";
echo "<form method='POST'>";
echo "<p>انقر على الزر أدناه لإنشاء منتج اختبار جديد يمكن حذفه:</p>";
echo "<button type='submit' name='create_test_product' class='btn btn-success'>";
echo "<i class='bi bi-plus-circle'></i> إنشاء منتج اختبار";
echo "</button>";
echo "</form>";
echo "</div>";
echo "</div>";

echo "<hr>";
echo "<div class='alert alert-info'>";
echo "<h5>معلومات الاختبار:</h5>";
echo "<ul>";
echo "<li><strong>طريقة GET:</strong> تستخدم رابط URL مع معامل ?delete=ID</li>";
echo "<li><strong>طريقة POST:</strong> تستخدم نموذج مخفي لإرسال البيانات</li>";
echo "<li><strong>الهدف:</strong> معرفة أي طريقة تعمل بشكل صحيح</li>";
echo "<li><strong>النتيجة المتوقعة:</strong> حذف المنتج من قاعدة البيانات</li>";
echo "</ul>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<a href='products.php' class='btn btn-primary'>العودة إلى products.php</a> ";
echo "<a href='test_direct_delete.php' class='btn btn-info'>اختبار الحذف المباشر</a>";
echo "</div>";

// Add some CSS for better styling
echo "<style>";
echo ".table th, .table td { vertical-align: middle; }";
echo ".btn-sm { margin: 2px; }";
echo ".alert { margin: 15px 0; }";
echo "</style>";
?>
