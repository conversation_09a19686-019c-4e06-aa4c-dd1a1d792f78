<?php
$pageTitle = 'الرئيسية';
require_once 'includes/header.php';

// التحقق من وجود جدول إعدادات الصفحة الرئيسية وإنشاؤه إذا لم يكن موجوداً
try {
    $tableExists = fetchOne("SHOW TABLES LIKE 'homepage_settings'");
    if (!$tableExists) {
        // إنشاء الجدول
        $createTableQuery = "
            CREATE TABLE `homepage_settings` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `section_name` varchar(100) NOT NULL,
                `setting_key` varchar(100) NOT NULL,
                `setting_value` text,
                `setting_type` enum('text','textarea','image','url','number','boolean') DEFAULT 'text',
                `sort_order` int(11) DEFAULT 0,
                `is_active` tinyint(1) DEFAULT 1,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_section_key` (`section_name`,`setting_key`),
                KEY `idx_section` (`section_name`),
                KEY `idx_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $pdo->exec($createTableQuery);

        // إدراج البيانات الافتراضية
        $defaultSettings = [
            // Carousel Settings
            ['carousel', 'slide_1_image', 'https://via.placeholder.com/1200x600/667eea/ffffff?text=الشريحة+الأولى', 'image', 1],
            ['carousel', 'slide_1_title', 'مرحباً بك في متجرنا الإلكتروني', 'text', 2],
            ['carousel', 'slide_1_subtitle', 'اكتشف مجموعة واسعة من المنتجات عالية الجودة', 'text', 3],
            ['carousel', 'slide_1_button_text', 'تصفح المنتجات', 'text', 4],
            ['carousel', 'slide_1_button_url', '/products.php', 'url', 5],
            ['carousel', 'slide_2_image', 'https://via.placeholder.com/1200x600/764ba2/ffffff?text=الشريحة+الثانية', 'image', 6],
            ['carousel', 'slide_3_image', 'https://via.placeholder.com/1200x600/28a745/ffffff?text=الشريحة+الثالثة', 'image', 7],
            ['carousel', 'slide_4_image', 'https://via.placeholder.com/1200x600/dc3545/ffffff?text=الشريحة+الرابعة', 'image', 8],
            ['carousel', 'auto_advance_time', '10', 'number', 9],
            ['carousel', 'show_indicators', '1', 'boolean', 10],
            ['carousel', 'show_controls', '1', 'boolean', 11],

            // Featured Products Settings
            ['featured_products', 'show_section', '1', 'boolean', 1],
            ['featured_products', 'section_title', 'المنتجات المميزة', 'text', 2],
            ['featured_products', 'section_subtitle', 'اكتشف أفضل منتجاتنا المختارة بعناية', 'text', 3],
            ['featured_products', 'products_limit', '6', 'number', 4],

            // Offers Settings
            ['offers', 'show_section', '1', 'boolean', 1],
            ['offers', 'section_title', 'العروض الخاصة', 'text', 2],
            ['offers', 'section_subtitle', 'لا تفوت عروضنا المحدودة والحصرية', 'text', 3],
            ['offers', 'banner_image', 'https://via.placeholder.com/1200x300/dc3545/ffffff?text=عروض+خاصة', 'image', 4],
            ['offers', 'banner_title', 'خصومات تصل إلى 50%', 'text', 5],
            ['offers', 'banner_subtitle', 'على مجموعة مختارة من المنتجات', 'text', 6],
            ['offers', 'banner_button_text', 'تصفح العروض', 'text', 7],
            ['offers', 'banner_button_url', '/offers.php', 'url', 8]
        ];

        $insertQuery = "INSERT INTO homepage_settings (section_name, setting_key, setting_value, setting_type, sort_order) VALUES (?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($insertQuery);

        foreach ($defaultSettings as $setting) {
            $stmt->execute($setting);
        }
    }
} catch (Exception $e) {
    error_log("Homepage settings table creation error: " . $e->getMessage());
}

// دالة احتياطية لجلب إعدادات الصفحة الرئيسية
if (!function_exists('getHomepageSectionSettings')) {
    function getHomepageSectionSettings($section) {
        try {
            global $pdo;
            $query = "SELECT setting_key, setting_value FROM homepage_settings WHERE section_name = ? AND is_active = 1";
            $stmt = $pdo->prepare($query);
            $stmt->execute([$section]);
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $settings = [];
            foreach ($results as $row) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
            return $settings;
        } catch (Exception $e) {
            return [];
        }
    }
}

// جلب إعدادات الصفحة الرئيسية مع القيم الافتراضية
$featuredProductsSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'المنتجات المميزة',
    'section_subtitle' => 'اكتشف أفضل منتجاتنا المختارة بعناية',
    'products_limit' => '6'
], getHomepageSectionSettings('featured_products'));

$offersSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'العروض الخاصة',
    'section_subtitle' => 'لا تفوت عروضنا المحدودة والحصرية',
    'banner_image' => 'https://via.placeholder.com/1200x300/dc3545/ffffff?text=عروض+خاصة',
    'banner_title' => 'خصومات تصل إلى 50%',
    'banner_subtitle' => 'على مجموعة مختارة من المنتجات',
    'banner_button_text' => 'تصفح العروض',
    'banner_button_url' => '/offers.php'
], getHomepageSectionSettings('offers'));

$influencersSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'المؤثرين',
    'section_subtitle' => 'شاهد تجارب المؤثرين مع منتجاتنا',
    'content_limit' => '6'
], getHomepageSectionSettings('influencers'));

$reviewsSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'آراء العملاء',
    'section_subtitle' => 'ماذا يقول عملاؤنا عن تجربتهم معنا',
    'reviews_limit' => '6'
], getHomepageSectionSettings('customer_reviews'));

$storySettings = array_merge([
    'show_section' => '1',
    'section_title' => 'قصة نجاح متجر Care',
    'story_content' => 'منذ تأسيسنا، نسعى لتقديم أفضل المنتجات وأعلى مستويات الخدمة لعملائنا الكرام.',
    'story_image' => 'https://via.placeholder.com/600x400/17a2b8/ffffff?text=قصة+النجاح',
    'achievements_customers' => '10000+',
    'achievements_products' => '500+',
    'achievements_years' => '5+'
], getHomepageSectionSettings('success_story'));

$whyChooseUsSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'لماذا تختار متجري الإلكتروني؟',
    'section_subtitle' => 'نحن نقدم أفضل تجربة تسوق إلكتروني في المنطقة',
    'feature_1_icon' => 'bi-award',
    'feature_1_title' => 'جودة عالية',
    'feature_1_description' => 'نختار منتجاتنا بعناية فائقة لضمان أعلى مستويات الجودة والأصالة',
    'feature_2_icon' => 'bi-truck',
    'feature_2_title' => 'توصيل سريع',
    'feature_2_description' => 'نوفر خدمة توصيل سريعة وآمنة إلى جميع أنحاء العراق',
    'feature_3_icon' => 'bi-shield-check',
    'feature_3_title' => 'ضمان الجودة',
    'feature_3_description' => 'جميع منتجاتنا مضمونة مع إمكانية الإرجاع والاستبدال',
    'feature_4_icon' => 'bi-headset',
    'feature_4_title' => 'دعم 24/7',
    'feature_4_description' => 'فريق خدمة العملاء متاح على مدار الساعة لمساعدتك'
], getHomepageSectionSettings('why_choose_us'));

$newsletterSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'اشترك في النشرة البريدية',
    'section_subtitle' => 'احصل على آخر العروض والمنتجات الجديدة',
    'background_image' => 'https://via.placeholder.com/1200x300/343a40/ffffff?text=النشرة+البريدية',
    'placeholder_text' => 'أدخل بريدك الإلكتروني',
    'button_text' => 'اشترك الآن'
], getHomepageSectionSettings('newsletter'));

$ctaSettings = array_merge([
    'show_section' => '1',
    'section_title' => 'ابدأ التسوق الآن!',
    'section_subtitle' => 'اكتشف آلاف المنتجات المميزة واستمتع بتجربة تسوق لا تُنسى',
    'background_color' => '#343a40',
    'primary_button_text' => 'تصفح المنتجات',
    'primary_button_url' => '/products.php',
    'secondary_button_text' => 'تواصل معنا',
    'secondary_button_url' => '/contact.php'
], getHomepageSectionSettings('call_to_action'));

// دالة مساعدة للتحقق من وجود الجدول
function tableExists($tableName) {
    try {
        $result = fetchOne("SHOW TABLES LIKE '$tableName'");
        return !empty($result);
    } catch (Exception $e) {
        return false;
    }
}

// دالة احتياطية لتنسيق التاريخ بالعربية
if (!function_exists('formatArabicDate')) {
    function formatArabicDate($date) {
        return date('Y/m/d', strtotime($date));
    }
}

// جلب المنتجات المميزة
$featuredProductsLimit = isset($featuredProductsSettings['products_limit']) ? (int)$featuredProductsSettings['products_limit'] : 6;
$featuredProducts = fetchAll("
    SELECT p.*, c.name as category_name 
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    WHERE p.is_featured = 1 AND p.status = 'active' 
    ORDER BY p.created_at DESC 
    LIMIT $featuredProductsLimit
");

// جلب المنتجات المخفضة للعروض
$discountedProducts = fetchAll("
    SELECT p.*, c.name as category_name 
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    WHERE p.discount > 0 AND p.status = 'active' 
    ORDER BY p.discount DESC 
    LIMIT 6
");

// جلب محتوى المؤثرين
$influencersLimit = isset($influencersSettings['content_limit']) ? (int)$influencersSettings['content_limit'] : 6;
$influencersContent = [];
if (tableExists('influencers_content')) {
    $influencersContent = fetchAll("
        SELECT * FROM influencers_content 
        WHERE status = 'published' 
        ORDER BY is_featured DESC, created_at DESC 
        LIMIT $influencersLimit
    ");
}

// جلب التقييمات المعتمدة
$reviewsLimit = isset($reviewsSettings['reviews_limit']) ? (int)$reviewsSettings['reviews_limit'] : 6;
$reviews = fetchAll("
    SELECT r.*, p.name as product_name 
    FROM reviews r 
    LEFT JOIN products p ON r.product_id = p.id 
    WHERE r.status = 'approved' 
    ORDER BY r.created_at DESC 
    LIMIT $reviewsLimit
");
?>

<!-- Hero Carousel Section -->
<?php include 'includes/homepage_carousel.php'; ?>

<!-- 1. المنتجات المميزة (Featured Products) -->
<?php if (isset($featuredProductsSettings['show_section']) && $featuredProductsSettings['show_section'] == '1' && !empty($featuredProducts)): ?>
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold"><?php echo htmlspecialchars($featuredProductsSettings['section_title'] ?? 'المنتجات المميزة'); ?></h2>
            <p class="text-muted"><?php echo htmlspecialchars($featuredProductsSettings['section_subtitle'] ?? 'اكتشف أفضل منتجاتنا المختارة بعناية'); ?></p>
        </div>
        
        <div class="row">
            <?php foreach ($featuredProducts as $product): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card product-card h-100 position-relative">
                        <?php if ($product['discount'] > 0): ?>
                            <span class="discount-badge">خصم <?php echo $product['discount']; ?>%</span>
                        <?php endif; ?>
                        
                        <?php
                        // البحث عن أول صورة متاحة من الصور الخمس
                        $imageUrl = 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج');
                        for ($i = 1; $i <= 5; $i++) {
                            if (!empty($product['image_url_' . $i])) {
                                $imageUrl = $product['image_url_' . $i];
                                break;
                            }
                        }
                        // إذا لم توجد صور خارجية، استخدم الصورة المرفوعة
                        if ($imageUrl === 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج') && !empty($product['image'])) {
                            $imageUrl = UPLOAD_URL . '/' . $product['image'];
                        }
                        ?>
                        <img src="<?php echo $imageUrl; ?>"
                             class="card-img-top" alt="<?php echo htmlspecialchars($product['name']); ?>">
                        
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                            <p class="text-muted small mb-2">
                                <i class="bi bi-tag"></i> <?php echo htmlspecialchars($product['category_name']); ?>
                            </p>
                            <p class="card-text text-muted flex-grow-1">
                                <?php echo htmlspecialchars(substr($product['short_description'], 0, 100)) . '...'; ?>
                            </p>
                            
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="price-section">
                                        <?php if ($product['discount'] > 0): ?>
                                            <?php $discountedPrice = $product['price'] - ($product['price'] * $product['discount'] / 100); ?>
                                            <span class="h5 text-success mb-0"><?php echo formatPrice($discountedPrice); ?></span>
                                            <small class="text-muted text-decoration-line-through"><?php echo formatPrice($product['price']); ?></small>
                                        <?php else: ?>
                                            <span class="h5 text-primary mb-0"><?php echo formatPrice($product['price']); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>" 
                                       class="btn btn-outline-primary btn-sm flex-grow-1">
                                        <i class="bi bi-eye"></i> عرض
                                    </a>
                                    <button class="btn btn-primary btn-sm flex-grow-1" 
                                            onclick="addToCart(<?php echo $product['id']; ?>, 1)">
                                        <i class="bi bi-cart-plus"></i> أضف للسلة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary btn-lg">
                <i class="bi bi-grid"></i> عرض جميع المنتجات
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- 2. العروض (Special Offers) -->
<?php if (isset($offersSettings['show_section']) && $offersSettings['show_section'] == '1'): ?>
<section class="py-5 special-offers-section">
    <div class="container">
        <div class="offers-section-header">
            <h2 class="offers-section-title"><?php echo htmlspecialchars($offersSettings['section_title'] ?? 'العروض الخاصة'); ?></h2>
            <p class="offers-section-subtitle"><?php echo htmlspecialchars($offersSettings['section_subtitle'] ?? 'لا تفوت عروضنا المحدودة والحصرية'); ?></p>
        </div>
        
        <!-- Professional Offers Banner -->
        <?php if (!empty($offersSettings['banner_image'])): ?>
        <div class="offers-banner"
             style="background-image: url('<?php echo htmlspecialchars($offersSettings['banner_image']); ?>');">
            <div class="offers-banner-content">
                <h3 class="offers-banner-title"><?php echo htmlspecialchars($offersSettings['banner_title'] ?? 'خصومات تصل إلى 50%'); ?></h3>
                <p class="offers-banner-subtitle"><?php echo htmlspecialchars($offersSettings['banner_subtitle'] ?? 'على مجموعة مختارة من المنتجات'); ?></p>
                <a href="<?php echo SITE_URL . htmlspecialchars($offersSettings['banner_button_url'] ?? '/offers.php'); ?>"
                   class="offers-banner-btn">
                    <i class="bi bi-percent"></i>
                    <?php echo htmlspecialchars($offersSettings['banner_button_text'] ?? 'تصفح العروض'); ?>
                </a>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Discounted Products -->
        <?php if (!empty($discountedProducts)): ?>
        <div class="row">
            <?php foreach ($discountedProducts as $product): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card product-card h-100 position-relative border-danger">
                        <span class="discount-badge">خصم <?php echo $product['discount']; ?>%</span>
                        
                        <?php
                        // البحث عن أول صورة متاحة من الصور الخمس
                        $imageUrl = 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج');
                        for ($i = 1; $i <= 5; $i++) {
                            if (!empty($product['image_url_' . $i])) {
                                $imageUrl = $product['image_url_' . $i];
                                break;
                            }
                        }
                        // إذا لم توجد صور خارجية، استخدم الصورة المرفوعة
                        if ($imageUrl === 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج') && !empty($product['image'])) {
                            $imageUrl = UPLOAD_URL . '/' . $product['image'];
                        }
                        ?>
                        <img src="<?php echo $imageUrl; ?>"
                             class="card-img-top" alt="<?php echo htmlspecialchars($product['name']); ?>">
                        
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                            <p class="text-muted small mb-2">
                                <i class="bi bi-tag"></i> <?php echo htmlspecialchars($product['category_name']); ?>
                            </p>
                            <p class="card-text text-muted flex-grow-1">
                                <?php echo htmlspecialchars(substr($product['short_description'], 0, 100)) . '...'; ?>
                            </p>
                            
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="price-section">
                                        <?php $discountedPrice = $product['price'] - ($product['price'] * $product['discount'] / 100); ?>
                                        <span class="h5 text-success mb-0"><?php echo formatPrice($discountedPrice); ?></span>
                                        <small class="text-muted text-decoration-line-through"><?php echo formatPrice($product['price']); ?></small>
                                        <small class="text-danger d-block">وفر <?php echo formatPrice($product['price'] - $discountedPrice); ?></small>
                                    </div>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>" 
                                       class="btn btn-outline-primary btn-sm flex-grow-1">
                                        <i class="bi bi-eye"></i> عرض
                                    </a>
                                    <button class="btn btn-primary btn-sm flex-grow-1" 
                                            onclick="addToCart(<?php echo $product['id']; ?>, 1)">
                                        <i class="bi bi-cart-plus"></i> أضف للسلة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php else: ?>
        <!-- Empty State for No Offers -->
        <div class="no-offers-state">
            <div class="no-offers-icon">
                <i class="bi bi-tag"></i>
            </div>
            <h3 class="no-offers-title">لا توجد عروض متاحة</h3>
            <p class="no-offers-message">لا توجد عروض متاحة في الوقت الحالي. تابعونا للحصول على أحدث العروض والخصومات الحصرية.</p>
            <div class="no-offers-action">
                <a href="<?php echo SITE_URL; ?>/products.php" class="no-offers-btn">
                    <i class="bi bi-grid"></i>
                    تصفح جميع المنتجات
                </a>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>
<?php endif; ?>

<style>
.product-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.discount-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 0.6rem 1rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 700;
    z-index: 5;
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
    animation: discountPulse 3s ease-in-out infinite;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transform: rotate(-5deg);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.offers-banner {
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.card-img-top {
    height: 250px;
    object-fit: cover;
    object-position: center;
}

.price-section {
    text-align: right;
}
</style>

<script>
// Enhanced add to cart function with professional notifications
function addToCart(productId, quantity = 1) {
    // Try to get product data from the page
    const productCard = document.querySelector(`[data-product-id="${productId}"]`) ||
                       document.querySelector(`button[onclick*="addToCart(${productId}"]`).closest('.card, .product-card, .offer-card');

    let productData = {};
    if (productCard) {
        const nameElement = productCard.querySelector('.card-title, .offer-card-title, .product-title');
        const imageElement = productCard.querySelector('img');
        const priceElement = productCard.querySelector('.text-success, .current-price, .price');

        productData = {
            name: nameElement ? nameElement.textContent.trim() : null,
            image: imageElement ? imageElement.src : null,
            price: priceElement ? priceElement.textContent.trim() : null
        };
    }

    // Use the enhanced notification system if available
    if (window.addToCartWithNotification) {
        addToCartWithNotification(productId, quantity, productData);
    } else {
        // Fallback to basic implementation
        fetch('<?php echo SITE_URL; ?>/ajax/cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=add&product_id=${productId}&quantity=${quantity}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('تم إضافة المنتج إلى السلة بنجاح!', 'success');
                // Update cart count if element exists
                const cartBadge = document.querySelector('.cart-badge');
                if (cartBadge && data.cart_count) {
                    cartBadge.textContent = data.cart_count;
                    cartBadge.style.display = 'flex';
                }
            } else {
                showToast(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
        });
    }
}

// Toast notification function
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 5000);
}
</script>

<!-- 3. المؤثرين (Influencers) -->
<?php if (isset($influencersSettings['show_section']) && $influencersSettings['show_section'] == '1' && !empty($influencersContent)): ?>
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold"><?php echo htmlspecialchars($influencersSettings['section_title'] ?? 'المؤثرين'); ?></h2>
            <p class="text-muted"><?php echo htmlspecialchars($influencersSettings['section_subtitle'] ?? 'شاهد تجارب المؤثرين مع منتجاتنا'); ?></p>
        </div>

        <div class="row">
            <?php foreach ($influencersContent as $content): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card influencer-card h-100">
                        <div class="card-header bg-gradient text-white">
                            <div class="d-flex align-items-center">
                                <?php if (!empty($content['influencer_image'])): ?>
                                    <img src="<?php echo htmlspecialchars($content['influencer_image']); ?>"
                                         class="influencer-avatar me-3" alt="<?php echo htmlspecialchars($content['influencer_name']); ?>">
                                <?php endif; ?>
                                <div>
                                    <h6 class="mb-0"><?php echo htmlspecialchars($content['influencer_name']); ?></h6>
                                    <small class="opacity-75"><?php echo ucfirst($content['content_type']); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($content['content_title'])): ?>
                                <h6 class="card-title"><?php echo htmlspecialchars($content['content_title']); ?></h6>
                            <?php endif; ?>
                            <p class="card-text"><?php echo htmlspecialchars(substr($content['content_text'], 0, 150)) . '...'; ?></p>

                            <?php if ($content['content_type'] === 'review' && !empty($content['rating'])): ?>
                                <div class="rating mb-2">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="bi bi-star<?php echo $i <= $content['rating'] ? '-fill text-warning' : ' text-muted'; ?>"></i>
                                    <?php endfor; ?>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($content['video_url'])): ?>
                                <a href="<?php echo htmlspecialchars($content['video_url']); ?>"
                                   class="btn btn-primary btn-sm" target="_blank">
                                    <i class="bi bi-play-circle"></i> مشاهدة الفيديو
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-4">
            <a href="<?php echo SITE_URL; ?>/influencers.php" class="btn btn-primary btn-lg">
                <i class="bi bi-people"></i> عرض جميع المؤثرين
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- 4. آراء العملاء (Customer Reviews) -->
<?php if (isset($reviewsSettings['show_section']) && $reviewsSettings['show_section'] == '1' && !empty($reviews)): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold"><?php echo htmlspecialchars($reviewsSettings['section_title'] ?? 'آراء العملاء'); ?></h2>
            <p class="text-muted"><?php echo htmlspecialchars($reviewsSettings['section_subtitle'] ?? 'ماذا يقول عملاؤنا عن تجربتهم معنا'); ?></p>
        </div>

        <div class="row">
            <?php foreach ($reviews as $review): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card review-card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="review-avatar me-3">
                                    <i class="bi bi-person-circle fs-2 text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0"><?php echo htmlspecialchars($review['customer_name']); ?></h6>
                                    <div class="rating">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="bi bi-star<?php echo $i <= $review['rating'] ? '-fill text-warning' : ' text-muted'; ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                            </div>

                            <p class="card-text"><?php echo htmlspecialchars($review['comment']); ?></p>

                            <?php if (!empty($review['product_name'])): ?>
                                <small class="text-muted">
                                    <i class="bi bi-box"></i> <?php echo htmlspecialchars($review['product_name']); ?>
                                </small>
                            <?php endif; ?>
                        </div>
                        <div class="card-footer bg-transparent">
                            <small class="text-muted">
                                <i class="bi bi-calendar"></i> <?php echo formatArabicDate($review['created_at']); ?>
                            </small>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- 5. قصة نجاح متجر Care (Success Story) -->
<?php if (isset($storySettings['show_section']) && $storySettings['show_section'] == '1'): ?>
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold"><?php echo htmlspecialchars($storySettings['section_title'] ?? 'قصة نجاح متجر Care'); ?></h2>
        </div>

        <div class="row align-items-center">
            <div class="col-lg-6 mb-4">
                <p class="lead">
                    <?php echo htmlspecialchars($storySettings['story_content'] ?? 'منذ تأسيسنا، نسعى لتقديم أفضل المنتجات وأعلى مستويات الخدمة لعملائنا الكرام.'); ?>
                </p>

                <div class="row text-center mt-4">
                    <div class="col-4">
                        <div class="achievement-item">
                            <h3 class="text-primary fw-bold"><?php echo htmlspecialchars($storySettings['achievements_customers'] ?? '10000+'); ?></h3>
                            <p class="text-muted">عميل سعيد</p>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="achievement-item">
                            <h3 class="text-success fw-bold"><?php echo htmlspecialchars($storySettings['achievements_products'] ?? '500+'); ?></h3>
                            <p class="text-muted">منتج متنوع</p>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="achievement-item">
                            <h3 class="text-info fw-bold"><?php echo htmlspecialchars($storySettings['achievements_years'] ?? '5+'); ?></h3>
                            <p class="text-muted">سنوات خبرة</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <img src="<?php echo htmlspecialchars($storySettings['story_image'] ?? 'https://via.placeholder.com/600x400/17a2b8/ffffff?text=قصة+النجاح'); ?>"
                     class="img-fluid rounded-3 shadow" alt="قصة النجاح">
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- 6. لماذا تختار متجري الإلكتروني؟ (Why Choose Us) -->
<?php if (isset($whyChooseUsSettings['show_section']) && $whyChooseUsSettings['show_section'] == '1'): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold"><?php echo htmlspecialchars($whyChooseUsSettings['section_title'] ?? 'لماذا تختار متجري الإلكتروني؟'); ?></h2>
            <p class="text-muted"><?php echo htmlspecialchars($whyChooseUsSettings['section_subtitle'] ?? 'نحن نقدم أفضل تجربة تسوق إلكتروني في المنطقة'); ?></p>
        </div>

        <div class="row">
            <!-- Feature 1 -->
            <div class="col-lg-6 mb-4">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                             style="width: 60px; height: 60px;">
                            <i class="<?php echo htmlspecialchars($whyChooseUsSettings['feature_1_icon'] ?? 'bi-award'); ?> text-white fs-4"></i>
                        </div>
                    </div>
                    <div class="ms-4">
                        <h5><?php echo htmlspecialchars($whyChooseUsSettings['feature_1_title'] ?? 'جودة عالية'); ?></h5>
                        <p class="text-muted"><?php echo htmlspecialchars($whyChooseUsSettings['feature_1_description'] ?? 'نختار منتجاتنا بعناية فائقة لضمان أعلى مستويات الجودة والأصالة'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Feature 2 -->
            <div class="col-lg-6 mb-4">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="bg-success rounded-circle d-flex align-items-center justify-content-center"
                             style="width: 60px; height: 60px;">
                            <i class="<?php echo htmlspecialchars($whyChooseUsSettings['feature_2_icon'] ?? 'bi-truck'); ?> text-white fs-4"></i>
                        </div>
                    </div>
                    <div class="ms-4">
                        <h5><?php echo htmlspecialchars($whyChooseUsSettings['feature_2_title'] ?? 'توصيل سريع'); ?></h5>
                        <p class="text-muted"><?php echo htmlspecialchars($whyChooseUsSettings['feature_2_description'] ?? 'نوفر خدمة توصيل سريعة وآمنة إلى جميع أنحاء العراق'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Feature 3 -->
            <div class="col-lg-6 mb-4">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="bg-info rounded-circle d-flex align-items-center justify-content-center"
                             style="width: 60px; height: 60px;">
                            <i class="<?php echo htmlspecialchars($whyChooseUsSettings['feature_3_icon'] ?? 'bi-shield-check'); ?> text-white fs-4"></i>
                        </div>
                    </div>
                    <div class="ms-4">
                        <h5><?php echo htmlspecialchars($whyChooseUsSettings['feature_3_title'] ?? 'ضمان الجودة'); ?></h5>
                        <p class="text-muted"><?php echo htmlspecialchars($whyChooseUsSettings['feature_3_description'] ?? 'جميع منتجاتنا مضمونة مع إمكانية الإرجاع والاستبدال'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Feature 4 -->
            <div class="col-lg-6 mb-4">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center"
                             style="width: 60px; height: 60px;">
                            <i class="<?php echo htmlspecialchars($whyChooseUsSettings['feature_4_icon'] ?? 'bi-headset'); ?> text-white fs-4"></i>
                        </div>
                    </div>
                    <div class="ms-4">
                        <h5><?php echo htmlspecialchars($whyChooseUsSettings['feature_4_title'] ?? 'دعم 24/7'); ?></h5>
                        <p class="text-muted"><?php echo htmlspecialchars($whyChooseUsSettings['feature_4_description'] ?? 'فريق خدمة العملاء متاح على مدار الساعة لمساعدتك'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- 7. اشترك في النشرة البريدية (Newsletter) -->
<?php if (isset($newsletterSettings['show_section']) && $newsletterSettings['show_section'] == '1'): ?>
<section class="py-5 newsletter-section"
         style="background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('<?php echo htmlspecialchars($newsletterSettings['background_image'] ?? 'https://via.placeholder.com/1200x300/343a40/ffffff?text=النشرة+البريدية'); ?>') center/cover;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center text-white">
                <h2 class="fw-bold mb-3"><?php echo htmlspecialchars($newsletterSettings['section_title'] ?? 'اشترك في النشرة البريدية'); ?></h2>
                <p class="lead mb-4"><?php echo htmlspecialchars($newsletterSettings['section_subtitle'] ?? 'احصل على آخر العروض والمنتجات الجديدة'); ?></p>

                <form id="homeNewsletterForm" class="row g-3 justify-content-center">
                    <div class="col-md-6">
                        <input type="email" class="form-control form-control-lg" name="email"
                               placeholder="<?php echo htmlspecialchars($newsletterSettings['placeholder_text'] ?? 'أدخل بريدك الإلكتروني'); ?>" required>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary btn-lg w-100">
                            <i class="bi bi-envelope"></i> <?php echo htmlspecialchars($newsletterSettings['button_text'] ?? 'اشترك الآن'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- 8. ابدأ التسوق الآن! (Call to Action) -->
<?php if (isset($ctaSettings['show_section']) && $ctaSettings['show_section'] == '1'): ?>
<section class="py-5 text-white" style="background-color: <?php echo htmlspecialchars($ctaSettings['background_color'] ?? '#343a40'); ?>;">
    <div class="container text-center">
        <h2 class="fw-bold mb-3"><?php echo htmlspecialchars($ctaSettings['section_title'] ?? 'ابدأ التسوق الآن!'); ?></h2>
        <p class="lead mb-4"><?php echo htmlspecialchars($ctaSettings['section_subtitle'] ?? 'اكتشف آلاف المنتجات المميزة واستمتع بتجربة تسوق لا تُنسى'); ?></p>
        <div class="d-flex justify-content-center gap-3">
            <a href="<?php echo SITE_URL . htmlspecialchars($ctaSettings['primary_button_url'] ?? '/products.php'); ?>" class="btn btn-primary btn-lg">
                <i class="bi bi-grid"></i> <?php echo htmlspecialchars($ctaSettings['primary_button_text'] ?? 'تصفح المنتجات'); ?>
            </a>
            <a href="<?php echo SITE_URL . htmlspecialchars($ctaSettings['secondary_button_url'] ?? '/contact.php'); ?>"
               class="btn btn-success btn-lg">
                <i class="bi bi-whatsapp"></i> <?php echo htmlspecialchars($ctaSettings['secondary_button_text'] ?? 'تواصل معنا'); ?>
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<style>
.influencer-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.influencer-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.influencer-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
}

.influencer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(255,255,255,0.3);
}

.review-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.review-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.achievement-item {
    padding: 1rem;
    border-radius: 10px;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.newsletter-section {
    position: relative;
}

.newsletter-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
    z-index: 1;
}

.newsletter-section .container {
    position: relative;
    z-index: 2;
}

@media (max-width: 768px) {
    .d-flex.gap-3 {
        flex-direction: column;
        gap: 1rem !important;
    }

    .achievement-item h3 {
        font-size: 1.5rem;
    }
}
</style>

<script>
// Newsletter subscription
document.getElementById('homeNewsletterForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('<?php echo SITE_URL; ?>/ajax/newsletter.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('تم الاشتراك بنجاح في النشرة البريدية!', 'success');
            this.reset();
        } else {
            showToast(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
