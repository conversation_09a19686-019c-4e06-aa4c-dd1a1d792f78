<?php
/**
 * Deep investigation of insertData failure
 */

require_once 'config/config.php';

echo "<h1>InsertData Failure Investigation</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 1. Check actual orders table structure
echo "<h2>1. Orders Table Structure Analysis</h2>";
try {
    $columns = $pdo->query("DESCRIBE orders")->fetchAll();
    echo "<h3>Actual Orders Table Columns:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td><strong>" . $column['Field'] . "</strong></td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . ($column['Extra'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "❌ Error getting table structure: " . $e->getMessage() . "<br>";
}

// 2. Test with minimal data first
echo "<h2>2. Minimal Data Insertion Test</h2>";
$minimalData = [
    'customer_name' => 'Test Customer',
    'customer_phone' => '07123456789',
    'total_price' => 50000.00
];

echo "<h3>Testing with minimal data:</h3>";
echo "<pre>" . json_encode($minimalData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

// Capture any errors
ob_start();
$minimalResult = insertData('orders', $minimalData);
$output = ob_get_clean();

if ($output) {
    echo "<h4>Output captured:</h4><pre>$output</pre>";
}

if ($minimalResult) {
    echo "✅ Minimal insertion successful - ID: $minimalResult<br>";
    // Clean up
    $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$minimalResult]);
} else {
    echo "❌ Minimal insertion failed<br>";
}

// 3. Test with full checkout data
echo "<h2>3. Full Checkout Data Test</h2>";
$fullData = [
    'customer_name' => 'Full Test Customer',
    'customer_phone' => '07123456789',
    'address' => 'Test Address, Baghdad',
    'province' => 'بغداد',
    'subtotal' => 50000.00,
    'delivery_price' => 5000.00,
    'discount_amount' => 0.00,
    'total_price' => 55000.00,
    'payment_method' => 'cash_on_delivery',
    'status' => 'pending'
];

echo "<h3>Testing with full checkout data:</h3>";
echo "<pre>" . json_encode($fullData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

// Check which columns exist vs which we're trying to insert
$tableColumns = array_column($columns, 'Field');
$dataColumns = array_keys($fullData);

echo "<h4>Column Comparison:</h4>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Data Column</th><th>Exists in Table</th><th>Status</th></tr>";
foreach ($dataColumns as $dataCol) {
    $exists = in_array($dataCol, $tableColumns);
    $status = $exists ? '✅' : '❌';
    echo "<tr><td>$dataCol</td><td>" . ($exists ? 'Yes' : 'No') . "</td><td>$status</td></tr>";
}
echo "</table>";

echo "<h4>Missing columns in data:</h4>";
$missingInData = array_diff($tableColumns, $dataColumns);
foreach ($missingInData as $missing) {
    // Check if it's required
    $colInfo = array_filter($columns, function($col) use ($missing) {
        return $col['Field'] === $missing;
    });
    $colInfo = reset($colInfo);
    $required = ($colInfo['Null'] === 'NO' && $colInfo['Default'] === null && $colInfo['Extra'] !== 'auto_increment');
    echo "- $missing " . ($required ? '(REQUIRED)' : '(optional)') . "<br>";
}

// 4. Test insertData function directly with error capture
echo "<h2>4. Direct InsertData Function Test</h2>";

// Enable PDO error mode
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

try {
    echo "<h3>Attempting full data insertion...</h3>";
    
    // Manual SQL construction to see what's happening
    $columns = implode(',', array_keys($fullData));
    $placeholders = ':' . implode(', :', array_keys($fullData));
    $sql = "INSERT INTO orders ({$columns}) VALUES ({$placeholders})";
    
    echo "<h4>Generated SQL:</h4>";
    echo "<pre>$sql</pre>";
    
    echo "<h4>Parameters:</h4>";
    echo "<pre>" . json_encode($fullData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    
    // Try manual insertion
    $stmt = $pdo->prepare($sql);
    if (!$stmt) {
        $errorInfo = $pdo->errorInfo();
        echo "❌ Prepare failed: " . json_encode($errorInfo) . "<br>";
    } else {
        echo "✅ Statement prepared successfully<br>";
        
        $result = $stmt->execute($fullData);
        if ($result) {
            $lastId = $pdo->lastInsertId();
            echo "✅ Manual insertion successful - ID: $lastId<br>";
            // Clean up
            $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$lastId]);
        } else {
            $errorInfo = $stmt->errorInfo();
            echo "❌ Execute failed: " . json_encode($errorInfo) . "<br>";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ PDO Exception: " . $e->getMessage() . "<br>";
    echo "Error Code: " . $e->getCode() . "<br>";
    echo "SQL State: " . $e->errorInfo[0] ?? 'Unknown' . "<br>";
}

// 5. Check for function conflicts again
echo "<h2>5. Function Definition Check</h2>";
$reflection = new ReflectionFunction('insertData');
echo "insertData function defined in: " . $reflection->getFileName() . " at line " . $reflection->getStartLine() . "<br>";

// 6. Check database user permissions
echo "<h2>6. Database Permissions Check</h2>";
try {
    $grants = $pdo->query("SHOW GRANTS")->fetchAll();
    echo "<h3>Current user grants:</h3>";
    foreach ($grants as $grant) {
        echo "- " . $grant[0] . "<br>";
    }
} catch (Exception $e) {
    echo "Could not check grants: " . $e->getMessage() . "<br>";
}

echo "<h2>7. Recommendations</h2>";
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
echo "<h4>Based on the analysis above:</h4>";
echo "<ol>";
echo "<li>Check if any columns are missing from the data that are required by the table</li>";
echo "<li>Verify data types match between the data and table schema</li>";
echo "<li>Look for any foreign key constraints that might be failing</li>";
echo "<li>Check if the database user has INSERT permissions</li>";
echo "<li>Review the exact error messages from the PDO exceptions</li>";
echo "</ol>";
echo "</div>";
?>
