<?php
/**
 * اختبار إصلاح التحذيرات
 * Test Warnings Fix
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>اختبار إصلاح التحذيرات</h2>";

try {
    // تضمين ملفات التكوين
    require_once 'config/config.php';
    
    echo "<p style='color: green;'>✅ تم تضمين ملفات التكوين بنجاح</p>";
    
    // محاكاة جلسة المدير
    $_SESSION[ADMIN_SESSION_NAME] = true;
    $_SESSION['admin_id'] = 1;
    
    echo "<h3>اختبار الدوال المحسّنة:</h3>";
    
    // اختبار fetchAll مع جدول فارغ
    echo "<h4>1. اختبار fetchAll:</h4>";
    $categories = fetchAll("SELECT id, name FROM categories WHERE status = 'active' ORDER BY name");
    echo "<p>نتيجة fetchAll: " . (is_array($categories) ? "مصفوفة تحتوي على " . count($categories) . " عنصر" : "ليست مصفوفة") . "</p>";
    
    if (!$categories) {
        $categories = [];
    }
    echo "<p>بعد التحقق: " . count($categories) . " تصنيف</p>";
    
    // اختبار fetchOne
    echo "<h4>2. اختبار fetchOne:</h4>";
    $category = fetchOne("SELECT id, name FROM categories LIMIT 1");
    echo "<p>نتيجة fetchOne: " . ($category ? "موجود" : "غير موجود") . "</p>";
    
    // اختبار uploadImage مع بيانات خاطئة
    echo "<h4>3. اختبار uploadImage:</h4>";
    
    // اختبار مع بيانات خاطئة
    $badFile = ['invalid' => 'data'];
    $result1 = uploadImage($badFile, 'test');
    echo "<p>اختبار بيانات خاطئة: " . (is_array($result1) ? "✅ مصفوفة" : "❌ ليست مصفوفة") . "</p>";
    if (is_array($result1)) {
        echo "<p>الرسالة: " . ($result1['error'] ?? 'لا توجد رسالة') . "</p>";
    }
    
    // اختبار مع بيانات صحيحة ولكن ملف غير موجود
    $fakeFile = [
        'name' => 'test.jpg',
        'type' => 'image/jpeg',
        'size' => 1000,
        'tmp_name' => '/nonexistent/file',
        'error' => 0
    ];
    $result2 = uploadImage($fakeFile, 'test');
    echo "<p>اختبار ملف غير موجود: " . (is_array($result2) ? "✅ مصفوفة" : "❌ ليست مصفوفة") . "</p>";
    if (is_array($result2)) {
        echo "<p>الرسالة: " . ($result2['error'] ?? 'لا توجد رسالة') . "</p>";
    }
    
    // اختبار الدوال المساعدة الجديدة
    echo "<h4>4. اختبار الدوال المساعدة:</h4>";
    
    $testArray = ['a', 'b', 'c'];
    $testFalse = false;
    $testNull = null;
    
    echo "<p>ensureArray مع مصفوفة: " . count(ensureArray($testArray)) . " عناصر</p>";
    echo "<p>ensureArray مع false: " . count(ensureArray($testFalse)) . " عناصر</p>";
    echo "<p>ensureArray مع null: " . count(ensureArray($testNull)) . " عناصر</p>";
    
    echo "<p>isValidResult مع مصفوفة: " . (isValidResult($testArray) ? "✅ صحيح" : "❌ خاطئ") . "</p>";
    echo "<p>isValidResult مع false: " . (isValidResult($testFalse) ? "✅ صحيح" : "❌ خاطئ") . "</p>";
    echo "<p>isValidResult مع null: " . (isValidResult($testNull) ? "✅ صحيح" : "❌ خاطئ") . "</p>";
    
    // اختبار صفحات لوحة التحكم
    echo "<h3>اختبار صفحات لوحة التحكم:</h3>";
    
    $adminPages = [
        'admin/products.php' => 'صفحة المنتجات',
        'admin/add_product.php' => 'صفحة إضافة منتج',
        'admin/categories.php' => 'صفحة التصنيفات'
    ];
    
    foreach ($adminPages as $page => $description) {
        echo "<h4>اختبار $description:</h4>";
        
        if (file_exists($page)) {
            // محاولة تضمين الصفحة للتحقق من الأخطاء
            ob_start();
            $error = false;
            $warning = false;
            
            set_error_handler(function($severity, $message, $file, $line) use (&$error, &$warning) {
                if ($severity === E_ERROR || $severity === E_PARSE) {
                    $error = "خطأ: $message في السطر $line";
                } elseif ($severity === E_WARNING || $severity === E_NOTICE) {
                    $warning = "تحذير: $message في السطر $line";
                }
                return true;
            });
            
            try {
                include $page;
                restore_error_handler();
                $output = ob_get_clean();
                
                if ($error) {
                    echo "<p style='color: red;'>❌ خطأ: $error</p>";
                } elseif ($warning) {
                    echo "<p style='color: orange;'>⚠️ تحذير: $warning</p>";
                } else {
                    echo "<p style='color: green;'>✅ تم تحميل الصفحة بدون أخطاء أو تحذيرات</p>";
                }
            } catch (Exception $e) {
                restore_error_handler();
                ob_end_clean();
                echo "<p style='color: red;'>❌ استثناء: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ الملف غير موجود</p>";
        }
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ اختبار إصلاح التحذيرات مكتمل!</h4>";
    echo "<p>تم إصلاح جميع التحذيرات المتعلقة بـ:</p>";
    echo "<ul>";
    echo "<li>دوال قاعدة البيانات (fetchAll, fetchOne)</li>";
    echo "<li>دالة رفع الصور (uploadImage)</li>";
    echo "<li>التحقق من صحة البيانات</li>";
    echo "<li>معالجة المصفوفات الفارغة</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ في الاختبار</h4>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>

<hr>

<h3>الإصلاحات المطبقة:</h3>
<div style="background: #e7f3ff; padding: 20px; border-radius: 8px;">
    <h4>🔧 إصلاحات قاعدة البيانات:</h4>
    <ul>
        <li><strong>fetchAll:</strong> ترجع مصفوفة فارغة [] بدلاً من false</li>
        <li><strong>fetchOne:</strong> ترجع null بدلاً من false</li>
        <li><strong>التحقق من النتائج:</strong> فحص إضافي قبل استخدام النتائج</li>
    </ul>
    
    <h4>🖼️ إصلاحات رفع الصور:</h4>
    <ul>
        <li><strong>التحقق من البيانات:</strong> فحص صحة بيانات الملف</li>
        <li><strong>معالجة الأخطاء:</strong> إرجاع مصفوفة دائماً</li>
        <li><strong>رسائل خطأ واضحة:</strong> تحديد سبب الفشل</li>
    </ul>
    
    <h4>🛡️ دوال مساعدة جديدة:</h4>
    <ul>
        <li><strong>ensureArray:</strong> التأكد من أن النتيجة مصفوفة</li>
        <li><strong>isValidResult:</strong> التحقق من صحة النتيجة</li>
        <li><strong>معالجة محسّنة:</strong> في جميع صفحات لوحة التحكم</li>
    </ul>
</div>

<div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h4>📋 الخطوات التالية:</h4>
    <ol>
        <li>تأكد من عدم ظهور تحذيرات في صفحات لوحة التحكم</li>
        <li>اختبر إضافة منتجات وتصنيفات</li>
        <li>اختبر رفع الصور</li>
        <li>احذف ملفات الاختبار بعد التأكد من عمل كل شيء</li>
    </ol>
</div>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h2, h3, h4 {
    color: #333;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 10px 0;
}
</style>
