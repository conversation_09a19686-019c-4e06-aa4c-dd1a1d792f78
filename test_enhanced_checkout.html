<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Enhanced Checkout</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        .btn-test {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            font-weight: bold;
            border-radius: 10px;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <div class="test-header">
                <h1>🎯 Enhanced Checkout Test</h1>
                <p class="mb-0">Test the enhanced checkout process with comprehensive logging</p>
            </div>

            <div class="info-box">
                <h4>📋 Test Instructions</h4>
                <ol>
                    <li><strong>Add products to cart first:</strong> <a href="products.php" target="_blank" class="btn btn-sm btn-outline-primary">Go to Products</a></li>
                    <li><strong>Fill out the form below</strong> (pre-filled with test data)</li>
                    <li><strong>Submit the form</strong> - it will go directly to the enhanced checkout.php</li>
                    <li><strong>Check the results</strong> in the admin panel and success page</li>
                </ol>
            </div>

            <div class="warning-box">
                <h5>⚠️ Important Notes</h5>
                <ul class="mb-0">
                    <li>Make sure you have products in your cart before submitting</li>
                    <li>The enhanced checkout now has comprehensive logging</li>
                    <li>Check PHP error logs for detailed processing information</li>
                    <li>Orders should immediately appear in the admin panel</li>
                </ul>
            </div>

            <div class="form-section">
                <h3 class="mb-4">🛒 Checkout Form</h3>
                
                <form action="checkout.php" method="POST" id="enhancedCheckoutForm">
                    <input type="hidden" name="place_order" value="1">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customer_name" class="form-label">
                                <strong>اسم العميل *</strong>
                            </label>
                            <input type="text" class="form-control form-control-lg" 
                                   id="customer_name" name="customer_name" 
                                   value="Enhanced Test Customer" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="customer_phone" class="form-label">
                                <strong>رقم الهاتف *</strong>
                            </label>
                            <input type="text" class="form-control form-control-lg" 
                                   id="customer_phone" name="customer_phone" 
                                   value="07123456789" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">
                            <strong>العنوان *</strong>
                        </label>
                        <textarea class="form-control form-control-lg" 
                                  id="address" name="address" rows="3" required>Enhanced Test Address, Baghdad, Iraq - This order should appear in admin panel immediately</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="province" class="form-label">
                                <strong>المحافظة *</strong>
                            </label>
                            <select class="form-control form-control-lg" id="province" name="province" required>
                                <option value="">اختر المحافظة</option>
                                <option value="بغداد" selected>بغداد</option>
                                <option value="البصرة">البصرة</option>
                                <option value="نينوى">نينوى</option>
                                <option value="أربيل">أربيل</option>
                                <option value="النجف">النجف</option>
                                <option value="كربلاء">كربلاء</option>
                                <option value="الأنبار">الأنبار</option>
                                <option value="ذي قار">ذي قار</option>
                                <option value="بابل">بابل</option>
                                <option value="كركوك">كركوك</option>
                                <option value="ديالى">ديالى</option>
                                <option value="المثنى">المثنى</option>
                                <option value="القادسية">القادسية</option>
                                <option value="ميسان">ميسان</option>
                                <option value="واسط">واسط</option>
                                <option value="دهوك">دهوك</option>
                                <option value="السليمانية">السليمانية</option>
                                <option value="صلاح الدين">صلاح الدين</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="payment_method" class="form-label">
                                <strong>طريقة الدفع</strong>
                            </label>
                            <select class="form-control form-control-lg" id="payment_method" name="payment_method">
                                <option value="cash_on_delivery" selected>الدفع عند الاستلام</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="notes" class="form-label">
                            <strong>ملاحظات</strong>
                        </label>
                        <textarea class="form-control form-control-lg" 
                                  id="notes" name="notes" rows="2">Enhanced checkout test - comprehensive logging enabled - order should be saved to database and visible in admin panel</textarea>
                    </div>
                    
                    <button type="submit" class="btn-test">
                        🚀 تأكيد الطلب - Enhanced Test
                    </button>
                </form>
            </div>

            <div class="form-section">
                <h4>🔍 After Submitting</h4>
                <div class="row">
                    <div class="col-md-4">
                        <a href="admin/orders.php" target="_blank" class="btn btn-outline-primary w-100 mb-2">
                            📊 Check Admin Orders
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="cart.php" target="_blank" class="btn btn-outline-secondary w-100 mb-2">
                            🛒 Check Cart Status
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="products.php" target="_blank" class="btn btn-outline-success w-100 mb-2">
                            🛍️ Add Products First
                        </a>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <h4>📈 Expected Results</h4>
                <div class="row">
                    <div class="col-md-6">
                        <h5>✅ Success Indicators:</h5>
                        <ul>
                            <li>Redirect to order success page</li>
                            <li>Order appears in admin panel</li>
                            <li>Cart is emptied</li>
                            <li>Order items are linked correctly</li>
                            <li>Customer data is saved</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>🔍 Check These Logs:</h5>
                        <ul>
                            <li>PHP error logs for detailed processing</li>
                            <li>Browser console for JavaScript errors</li>
                            <li>Network tab for form submission</li>
                            <li>Database for order records</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('enhancedCheckoutForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري معالجة الطلب...';
            submitBtn.disabled = true;
            
            // Show processing message
            const processingDiv = document.createElement('div');
            processingDiv.className = 'alert alert-info mt-3';
            processingDiv.innerHTML = '<strong>🔄 جاري المعالجة...</strong><br>يتم الآن معالجة طلبك وحفظه في قاعدة البيانات. سيتم توجيهك إلى صفحة التأكيد.';
            this.appendChild(processingDiv);
        });
    </script>
</body>
</html>
