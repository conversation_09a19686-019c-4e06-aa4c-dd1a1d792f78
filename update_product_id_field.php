<?php
require_once 'config/config.php';

echo "تحديث حقل product_id في جدول influencers_content...\n";

try {
    $pdo = getConnection();
    
    // التحقق من نوع البيانات الحالي لحقل product_id
    $result = $pdo->query("DESCRIBE influencers_content product_id")->fetch();
    
    if ($result && strpos($result['Type'], 'int') !== false) {
        echo "تحديث نوع البيانات من INT إلى VARCHAR(50)...\n";
        
        $pdo->exec("ALTER TABLE influencers_content MODIFY COLUMN product_id VARCHAR(50)");
        echo "✅ تم تحديث حقل product_id بنجاح\n";
    } else {
        echo "⚠️ حقل product_id يستخدم نوع البيانات الصحيح مسبقاً\n";
    }
    
    echo "✅ تم تحديث قاعدة البيانات بنجاح\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في تحديث قاعدة البيانات: " . $e->getMessage() . "\n";
}
?>
