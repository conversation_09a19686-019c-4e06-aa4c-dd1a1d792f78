<?php
/**
 * Test cart persistence during checkout process
 */

require_once 'config/config.php';

echo "<h1>🛒 Cart Persistence Test</h1>";
echo "<p>This test checks if the cart persists during the checkout process.</p>";

// Start session
if (!isset($_SESSION)) {
    session_start();
}

echo "<h2>1. Initial Cart Setup</h2>";

// Clear any existing cart first
clearCart();
echo "✅ Cleared existing cart<br>";

// Add test products to cart
$testProducts = [1 => 2, 2 => 1]; // Product 1: qty 2, Product 2: qty 1

foreach ($testProducts as $productId => $quantity) {
    addToCart($productId, $quantity);
    echo "✅ Added product $productId (qty: $quantity) to cart<br>";
}

// Check cart contents
$cart = getCart();
echo "<strong>Cart after setup:</strong> " . json_encode($cart) . "<br>";
echo "Cart item count: " . getCartItemCount() . "<br>";

echo "<h2>2. Cart Session Test</h2>";
echo "Session ID: " . session_id() . "<br>";
echo "Cart session name: " . CART_SESSION_NAME . "<br>";
echo "Cart in session: " . json_encode($_SESSION[CART_SESSION_NAME] ?? 'NOT SET') . "<br>";

echo "<h2>3. Simulate Checkout Page Load</h2>";

// Simulate what checkout.php does at the beginning
$checkoutCart = getCart();
echo "Cart retrieved by getCart(): " . json_encode($checkoutCart) . "<br>";

if (empty($checkoutCart)) {
    echo "❌ Cart is empty - checkout would redirect to cart.php<br>";
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🚨 CRITICAL ISSUE FOUND!</h3>";
    echo "<p>The cart is empty when checkout.php tries to access it. This is why orders are not being created.</p>";
    echo "<p><strong>Possible causes:</strong></p>";
    echo "<ul>";
    echo "<li>Session is not persisting between pages</li>";
    echo "<li>Cart is being cleared somewhere unexpectedly</li>";
    echo "<li>Session configuration issues</li>";
    echo "<li>CART_SESSION_NAME constant mismatch</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "✅ Cart is available for checkout<br>";
    
    // Test product retrieval
    $productIds = array_keys($checkoutCart);
    $placeholders = str_repeat('?,', count($productIds) - 1) . '?';
    
    $products = fetchAll("
        SELECT id, name, price, discount, image, stock 
        FROM products 
        WHERE id IN ($placeholders) AND status = 'active'
    ", $productIds);
    
    echo "Products found for cart items: " . count($products) . "<br>";
    
    if (count($products) === 0) {
        echo "❌ No products found - this would cause checkout to fail<br>";
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
        echo "<h3>🚨 ANOTHER CRITICAL ISSUE!</h3>";
        echo "<p>No products were found for the cart items. This means:</p>";
        echo "<ul>";
        echo "<li>Products don't exist in the database</li>";
        echo "<li>Products are not active</li>";
        echo "<li>Product IDs in cart don't match database</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "✅ Products retrieved successfully<br>";
        foreach ($products as $product) {
            echo "- Product {$product['id']}: {$product['name']} - " . number_format($product['price']) . " IQD<br>";
        }
    }
}

echo "<h2>4. Test Form Submission Simulation</h2>";

// Simulate form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_checkout'])) {
    echo "<h3>Form Submitted!</h3>";
    
    // Check cart again after form submission
    $postCart = getCart();
    echo "Cart after form submission: " . json_encode($postCart) . "<br>";
    
    if (empty($postCart)) {
        echo "❌ Cart is empty after form submission!<br>";
    } else {
        echo "✅ Cart persists after form submission<br>";
        
        // Simulate the checkout process
        echo "<h4>Simulating checkout process...</h4>";
        
        // Get form data
        $customerName = sanitizeInput($_POST['customer_name']);
        $customerPhone = sanitizeInput($_POST['customer_phone']);
        $address = sanitizeInput($_POST['address']);
        $province = sanitizeInput($_POST['province']);
        
        echo "Customer: $customerName<br>";
        echo "Phone: $customerPhone<br>";
        echo "Province: $province<br>";
        
        // Calculate totals (simplified)
        $subtotal = 75000; // Simplified for test
        $deliveryPrice = 5000;
        $finalTotal = $subtotal + $deliveryPrice;
        
        // Prepare order data
        $orderData = [
            'customer_name' => $customerName,
            'customer_phone' => $customerPhone,
            'address' => $address,
            'province' => $province,
            'subtotal' => $subtotal,
            'delivery_price' => $deliveryPrice,
            'discount_amount' => 0,
            'total_price' => $finalTotal,
            'payment_method' => 'cash_on_delivery',
            'status' => 'pending',
            'notes' => 'Cart persistence test order'
        ];
        
        echo "<h4>Attempting order creation...</h4>";
        $orderId = insertData('orders', $orderData);
        
        if ($orderId) {
            echo "✅ Order created successfully - ID: $orderId<br>";
            
            // Test cart clearing
            clearCart();
            $clearedCart = getCart();
            if (empty($clearedCart)) {
                echo "✅ Cart cleared successfully<br>";
            } else {
                echo "❌ Cart clearing failed<br>";
            }
            
            // Clean up test order
            $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$orderId]);
            echo "✅ Test order cleaned up<br>";
            
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
            echo "<h3>🎉 Cart Persistence Test PASSED!</h3>";
            echo "<p>The cart persists correctly and orders can be created. The checkout process should work.</p>";
            echo "</div>";
            
        } else {
            echo "❌ Order creation failed<br>";
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
            echo "<h3>🚨 ORDER CREATION FAILED!</h3>";
            echo "<p>Even though the cart persists, order creation is still failing.</p>";
            echo "</div>";
        }
    }
} else {
    // Show test form
    echo "<h3>Submit Test Form</h3>";
    echo "<p>This form will test if the cart persists during form submission:</p>";
    
    echo '<form method="POST" style="max-width: 500px; border: 1px solid #ddd; padding: 20px; border-radius: 5px;">';
    echo '<input type="hidden" name="test_checkout" value="1">';
    
    echo '<div style="margin-bottom: 15px;">';
    echo '<label>اسم العميل:</label><br>';
    echo '<input type="text" name="customer_name" value="Cart Test Customer" required style="width: 100%; padding: 8px;">';
    echo '</div>';
    
    echo '<div style="margin-bottom: 15px;">';
    echo '<label>رقم الهاتف:</label><br>';
    echo '<input type="text" name="customer_phone" value="07123456789" required style="width: 100%; padding: 8px;">';
    echo '</div>';
    
    echo '<div style="margin-bottom: 15px;">';
    echo '<label>العنوان:</label><br>';
    echo '<textarea name="address" required style="width: 100%; padding: 8px; height: 60px;">Cart Test Address</textarea>';
    echo '</div>';
    
    echo '<div style="margin-bottom: 15px;">';
    echo '<label>المحافظة:</label><br>';
    echo '<select name="province" required style="width: 100%; padding: 8px;">';
    echo '<option value="بغداد" selected>بغداد</option>';
    echo '</select>';
    echo '</div>';
    
    echo '<button type="submit" style="background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px;">';
    echo 'اختبار استمرارية السلة';
    echo '</button>';
    
    echo '</form>';
}

echo "<h2>5. Debugging Information</h2>";
echo "<h3>Session Information:</h3>";
echo "<ul>";
echo "<li>Session started: " . (session_status() === PHP_SESSION_ACTIVE ? 'Yes' : 'No') . "</li>";
echo "<li>Session ID: " . session_id() . "</li>";
echo "<li>Session save path: " . session_save_path() . "</li>";
echo "<li>Session cookie params: " . json_encode(session_get_cookie_params()) . "</li>";
echo "</ul>";

echo "<h3>Constants:</h3>";
echo "<ul>";
echo "<li>CART_SESSION_NAME: " . (defined('CART_SESSION_NAME') ? CART_SESSION_NAME : 'NOT DEFINED') . "</li>";
echo "<li>SITE_URL: " . (defined('SITE_URL') ? SITE_URL : 'NOT DEFINED') . "</li>";
echo "</ul>";

echo "<h3>Current Session Contents:</h3>";
echo "<pre>" . json_encode($_SESSION, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

echo "<h2>6. Recommendations</h2>";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>";
echo "<h4>If cart is empty:</h4>";
echo "<ol>";
echo "<li>Check if products exist in database: <a href='admin/products.php' target='_blank'>Admin Products</a></li>";
echo "<li>Add products to cart manually: <a href='products.php' target='_blank'>Products Page</a></li>";
echo "<li>Test checkout with real cart: <a href='checkout.php' target='_blank'>Checkout Page</a></li>";
echo "</ol>";
echo "</div>";

echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
