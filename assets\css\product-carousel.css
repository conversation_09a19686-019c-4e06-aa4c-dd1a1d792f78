/**
 * Professional Product Carousel Styles
 * Modern, Clean, and Error-Free Implementation for Arabic E-commerce
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

/* ==========================================================================
   Product Carousel Base Styles
   ========================================================================== */

.product-carousel-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
}

.product-carousel {
    position: relative;
    width: 100%;
    height: 400px;
    overflow: hidden;
}

.product-carousel-track {
    display: flex;
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    height: 100%;
}

.product-carousel-slide {
    flex: 0 0 100%;
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.product-carousel-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.product-carousel-slide:hover img {
    transform: scale(1.05);
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.product-carousel-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 2;
    transition: opacity 0.3s ease;
}

.product-carousel-loading.hidden {
    opacity: 0;
    pointer-events: none;
}

.carousel-loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e0e0e0;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.carousel-loading-text {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Navigation Controls
   ========================================================================== */

.product-carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 3;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    color: #333;
    font-size: 1.2rem;
}

.product-carousel-nav:hover {
    background: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    transform: translateY(-50%) scale(1.1);
}

.product-carousel-nav.prev {
    left: 15px;
}

.product-carousel-nav.next {
    right: 15px;
}

/* RTL Support */
[dir="rtl"] .product-carousel-nav.prev {
    left: auto;
    right: 15px;
}

[dir="rtl"] .product-carousel-nav.next {
    right: auto;
    left: 15px;
}

/* ==========================================================================
   Indicators
   ========================================================================== */

.product-carousel-indicators {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    z-index: 3;
}

.product-carousel-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
}

.product-carousel-indicator.active {
    background: white;
    transform: scale(1.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* ==========================================================================
   Image Error States
   ========================================================================== */

.product-carousel-slide.error {
    background: #f8f9fa;
    color: #6c757d;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.product-carousel-slide.error i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.product-carousel-slide.error .error-text {
    font-size: 0.9rem;
    text-align: center;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .product-carousel {
        height: 300px;
    }
    
    .product-carousel-nav {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
    
    .product-carousel-nav.prev {
        left: 10px;
    }
    
    .product-carousel-nav.next {
        right: 10px;
    }
    
    [dir="rtl"] .product-carousel-nav.prev {
        right: 10px;
    }
    
    [dir="rtl"] .product-carousel-nav.next {
        left: 10px;
    }
    
    .product-carousel-indicator {
        width: 8px;
        height: 8px;
    }
}

@media (max-width: 576px) {
    .product-carousel {
        height: 250px;
    }
    
    .product-carousel-nav {
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
    }
    
    .product-carousel-indicators {
        bottom: 10px;
        gap: 6px;
    }
}

/* ==========================================================================
   Accessibility & Performance
   ========================================================================== */

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .product-carousel-track,
    .product-carousel-slide img,
    .product-carousel-nav,
    .product-carousel-indicator {
        transition: none !important;
        animation: none !important;
    }
}

/* Focus states for accessibility */
.product-carousel-nav:focus,
.product-carousel-indicator:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .product-carousel-nav {
        background: white;
        border: 2px solid #000;
    }
    
    .product-carousel-indicator {
        border: 2px solid #000;
    }
}
