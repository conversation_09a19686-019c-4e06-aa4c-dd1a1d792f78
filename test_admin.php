<?php
/**
 * اختبار لوحة التحكم الإدارية
 * Test Admin Dashboard
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>اختبار لوحة التحكم الإدارية</h2>";

try {
    // تضمين ملف التكوين
    require_once 'config/config.php';
    
    echo "<p style='color: green;'>✅ تم تضمين ملف التكوين بنجاح</p>";
    
    // اختبار دوال الإحصائيات
    echo "<h3>اختبار دوال الإحصائيات:</h3>";
    
    // اختبار عدد المنتجات
    $productsCount = fetchOne("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
    $products = ($productsCount && isset($productsCount['count'])) ? $productsCount['count'] : 0;
    echo "<p><strong>المنتجات النشطة:</strong> $products</p>";
    
    // اختبار عدد الطلبات
    $ordersCount = fetchOne("SELECT COUNT(*) as count FROM orders");
    $orders = ($ordersCount && isset($ordersCount['count'])) ? $ordersCount['count'] : 0;
    echo "<p><strong>إجمالي الطلبات:</strong> $orders</p>";
    
    // اختبار الطلبات المعلقة
    $pendingOrdersCount = fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'");
    $pendingOrders = ($pendingOrdersCount && isset($pendingOrdersCount['count'])) ? $pendingOrdersCount['count'] : 0;
    echo "<p><strong>الطلبات المعلقة:</strong> $pendingOrders</p>";
    
    // اختبار التقييمات
    $reviewsCount = fetchOne("SELECT COUNT(*) as count FROM reviews WHERE status = 'approved'");
    $reviews = ($reviewsCount && isset($reviewsCount['count'])) ? $reviewsCount['count'] : 0;
    echo "<p><strong>التقييمات المعتمدة:</strong> $reviews</p>";
    
    // اختبار النشرة البريدية
    $newsletterCount = fetchOne("SELECT COUNT(*) as count FROM newsletter WHERE status = 'active'");
    $newsletter = ($newsletterCount && isset($newsletterCount['count'])) ? $newsletterCount['count'] : 0;
    echo "<p><strong>مشتركي النشرة:</strong> $newsletter</p>";
    
    // اختبار إحصائيات المبيعات
    echo "<h3>اختبار إحصائيات المبيعات:</h3>";
    $salesStats = fetchOne("
        SELECT 
            SUM(total_price) as total_sales,
            AVG(total_price) as avg_order_value,
            COUNT(*) as total_orders
        FROM orders 
        WHERE status != 'cancelled'
    ");
    
    $totalSales = ($salesStats && isset($salesStats['total_sales'])) ? $salesStats['total_sales'] : 0;
    $avgOrderValue = ($salesStats && isset($salesStats['avg_order_value'])) ? $salesStats['avg_order_value'] : 0;
    $totalOrders = ($salesStats && isset($salesStats['total_orders'])) ? $salesStats['total_orders'] : 0;
    
    echo "<p><strong>إجمالي المبيعات:</strong> " . number_format($totalSales, 2) . " دينار عراقي</p>";
    echo "<p><strong>متوسط قيمة الطلب:</strong> " . number_format($avgOrderValue, 2) . " دينار عراقي</p>";
    echo "<p><strong>عدد الطلبات المكتملة:</strong> $totalOrders</p>";
    
    // اختبار الطلبات الحديثة
    echo "<h3>اختبار الطلبات الحديثة:</h3>";
    $recentOrders = fetchAll("
        SELECT id, customer_name, total_price, status, created_at 
        FROM orders 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    
    if (!empty($recentOrders)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>رقم الطلب</th>";
        echo "<th style='padding: 10px;'>اسم العميل</th>";
        echo "<th style='padding: 10px;'>المبلغ</th>";
        echo "<th style='padding: 10px;'>الحالة</th>";
        echo "<th style='padding: 10px;'>التاريخ</th>";
        echo "</tr>";
        
        foreach ($recentOrders as $order) {
            echo "<tr>";
            echo "<td style='padding: 10px;'>" . $order['id'] . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($order['customer_name']) . "</td>";
            echo "<td style='padding: 10px;'>" . number_format($order['total_price'], 2) . " دينار عراقي</td>";
            echo "<td style='padding: 10px;'>" . $order['status'] . "</td>";
            echo "<td style='padding: 10px;'>" . $order['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد طلبات في قاعدة البيانات</p>";
    }
    
    // اختبار المنتجات منخفضة المخزون
    echo "<h3>اختبار المنتجات منخفضة المخزون:</h3>";
    $lowStockProducts = fetchAll("
        SELECT id, name, stock 
        FROM products 
        WHERE stock <= 5 AND status = 'active' 
        ORDER BY stock ASC 
        LIMIT 5
    ");
    
    if (!empty($lowStockProducts)) {
        echo "<ul>";
        foreach ($lowStockProducts as $product) {
            echo "<li>" . htmlspecialchars($product['name']) . " - المخزون: " . $product['stock'] . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: green;'>✅ جميع المنتجات لديها مخزون كافي</p>";
    }
    
    // اختبار ملفات AJAX
    echo "<h3>اختبار ملفات AJAX:</h3>";
    
    // محاكاة جلسة المدير للاختبار
    $_SESSION[ADMIN_SESSION_NAME] = true;
    $_SESSION['admin_id'] = 1;
    
    // اختبار dashboard_stats.php
    ob_start();
    include 'admin/ajax/dashboard_stats.php';
    $dashboardStats = ob_get_clean();
    
    $statsData = json_decode($dashboardStats, true);
    if ($statsData) {
        echo "<p style='color: green;'>✅ dashboard_stats.php يعمل بشكل صحيح</p>";
        echo "<p>البيانات المُرجعة: " . count($statsData) . " عنصر</p>";
    } else {
        echo "<p style='color: red;'>❌ خطأ في dashboard_stats.php</p>";
    }
    
    // اختبار notifications.php
    ob_start();
    include 'admin/ajax/notifications.php';
    $notificationsData = ob_get_clean();
    
    $notifications = json_decode($notificationsData, true);
    if ($notifications) {
        echo "<p style='color: green;'>✅ notifications.php يعمل بشكل صحيح</p>";
        echo "<p>الطلبات المعلقة: " . $notifications['pending_orders'] . "</p>";
        echo "<p>التقييمات المعلقة: " . $notifications['pending_reviews'] . "</p>";
    } else {
        echo "<p style='color: red;'>❌ خطأ في notifications.php</p>";
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ جميع الاختبارات نجحت!</h4>";
    echo "<p>لوحة التحكم الإدارية جاهزة للاستخدام.</p>";
    echo "<p><a href='admin/login.php'>تسجيل الدخول إلى لوحة التحكم</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ في الاختبار</h4>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

// معلومات إضافية
echo "<hr>";
echo "<h3>ملفات لوحة التحكم:</h3>";
$adminFiles = [
    'admin/login.php' => 'صفحة تسجيل الدخول',
    'admin/dashboard.php' => 'لوحة القيادة',
    'admin/includes/header.php' => 'رأس الصفحة',
    'admin/includes/footer.php' => 'تذييل الصفحة',
    'admin/ajax/dashboard_stats.php' => 'إحصائيات AJAX',
    'admin/ajax/notifications.php' => 'إشعارات AJAX'
];

echo "<ul>";
foreach ($adminFiles as $file => $description) {
    $exists = file_exists($file);
    echo "<li><strong>$description:</strong> " . ($exists ? "✅ $file" : "❌ $file غير موجود") . "</li>";
}
echo "</ul>";

echo "<h3>بيانات تسجيل الدخول:</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>اسم المستخدم:</strong> admin</p>";
echo "<p><strong>كلمة المرور:</strong> password</p>";
echo "<p><strong>رابط لوحة التحكم:</strong> <a href='admin/login.php'>admin/login.php</a></p>";
echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h2, h3, h4 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 15px 0;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 10px 0;
}

hr {
    margin: 30px 0;
    border: none;
    border-top: 2px solid #dee2e6;
}
</style>
