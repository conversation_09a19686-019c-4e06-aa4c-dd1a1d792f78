<?php
// Prevent any output before JSON
ob_start();

require_once '../../config/config.php';
requireAdminLogin();

// Clean any output buffer and set JSON header
ob_clean();
header('Content-Type: application/json; charset=utf-8');

// جلب الإحصائيات المحدثة
$productsCount = fetchOne("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
$ordersCount = fetchOne("SELECT COUNT(*) as count FROM orders");
$pendingOrdersCount = fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'");
$reviewsCount = fetchOne("SELECT COUNT(*) as count FROM reviews WHERE status = 'approved'");
$newsletterCount = fetchOne("SELECT COUNT(*) as count FROM newsletter WHERE status = 'active'");

$stats = [
    'products' => ($productsCount && isset($productsCount['count'])) ? $productsCount['count'] : 0,
    'orders' => ($ordersCount && isset($ordersCount['count'])) ? $ordersCount['count'] : 0,
    'pending_orders' => ($pendingOrdersCount && isset($pendingOrdersCount['count'])) ? $pendingOrdersCount['count'] : 0,
    'reviews' => ($reviewsCount && isset($reviewsCount['count'])) ? $reviewsCount['count'] : 0,
    'newsletter' => ($newsletterCount && isset($newsletterCount['count'])) ? $newsletterCount['count'] : 0
];

// إحصائيات المبيعات
$salesStats = fetchOne("
    SELECT 
        SUM(total_price) as total_sales,
        AVG(total_price) as avg_order_value,
        COUNT(*) as total_orders
    FROM orders 
    WHERE status != 'cancelled'
");

try {
    echo json_encode([
        'products' => (int)$stats['products'],
        'orders' => (int)$stats['orders'],
        'pending_orders' => (int)$stats['pending_orders'],
        'reviews' => (int)$stats['reviews'],
        'newsletter' => (int)$stats['newsletter'],
        'total_sales' => (float)($salesStats['total_sales'] ?? 0),
        'avg_order_value' => (float)($salesStats['avg_order_value'] ?? 0),
        'total_orders' => (int)($salesStats['total_orders'] ?? 0)
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'message' => 'خطأ في جلب الإحصائيات: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

exit;
?>
