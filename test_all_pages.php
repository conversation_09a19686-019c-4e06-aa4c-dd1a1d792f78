<?php
/**
 * اختبار جميع صفحات الموقع
 * Test All Website Pages
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>اختبار جميع صفحات الموقع</h2>";

// قائمة الصفحات للاختبار
$pages = [
    'index.php' => 'الصفحة الرئيسية',
    'products.php' => 'صفحة المنتجات',
    'offers.php' => 'صفحة العروض',
    'cart.php' => 'سلة التسوق',
    'contact.php' => 'صفحة الاتصال'
];

echo "<p>جاري اختبار الصفحات...</p>";

foreach ($pages as $page => $title) {
    echo "<h3>اختبار: $title ($page)</h3>";
    
    try {
        // محاولة تضمين الصفحة
        ob_start();
        $error = false;
        
        // تسجيل معالج الأخطاء
        set_error_handler(function($severity, $message, $file, $line) use (&$error) {
            $error = "خطأ: $message في الملف $file على السطر $line";
            return true;
        });
        
        // محاولة تضمين الصفحة
        include $page;
        
        // استعادة معالج الأخطاء الافتراضي
        restore_error_handler();
        
        $output = ob_get_clean();
        
        if ($error) {
            echo "<p style='color: red;'>❌ فشل: $error</p>";
        } else {
            echo "<p style='color: green;'>✅ نجح: تم تحميل الصفحة بدون أخطاء</p>";
            echo "<p><a href='$page' target='_blank'>فتح الصفحة</a></p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ استثناء: " . $e->getMessage() . "</p>";
        ob_end_clean();
    } catch (Error $e) {
        echo "<p style='color: red;'>❌ خطأ فادح: " . $e->getMessage() . "</p>";
        ob_end_clean();
    }
    
    echo "<hr>";
}

// اختبار دوال قاعدة البيانات
echo "<h3>اختبار دوال قاعدة البيانات</h3>";

try {
    require_once 'config/config.php';
    
    // اختبار fetchAll مع نتائج فارغة
    $emptyResult = fetchAll("SELECT * FROM products WHERE id = -1");
    echo "<p><strong>fetchAll (فارغ):</strong> " . (is_array($emptyResult) ? "✅ مصفوفة فارغة" : "❌ ليس مصفوفة") . "</p>";
    
    // اختبار fetchOne مع نتائج فارغة
    $emptyOne = fetchOne("SELECT * FROM products WHERE id = -1");
    echo "<p><strong>fetchOne (فارغ):</strong> " . (is_null($emptyOne) ? "✅ null" : "❌ ليس null") . "</p>";
    
    // اختبار fetchAll مع نتائج
    $categories = fetchAll("SELECT id, name FROM categories LIMIT 3");
    echo "<p><strong>fetchAll (التصنيفات):</strong> " . (is_array($categories) ? "✅ " . count($categories) . " عنصر" : "❌ ليس مصفوفة") . "</p>";
    
    // اختبار fetchOne مع نتائج
    $count = fetchOne("SELECT COUNT(*) as total FROM categories");
    echo "<p><strong>fetchOne (العدد):</strong> " . (is_array($count) && isset($count['total']) ? "✅ " . $count['total'] . " تصنيف" : "❌ خطأ") . "</p>";
    
    echo "<p style='color: green;'>✅ جميع دوال قاعدة البيانات تعمل بشكل صحيح</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// اختبار الجلسات
echo "<h3>اختبار الجلسات</h3>";
if (session_status() == PHP_SESSION_ACTIVE) {
    echo "<p style='color: green;'>✅ الجلسات تعمل بشكل صحيح</p>";
} else {
    echo "<p style='color: red;'>❌ مشكلة في الجلسات</p>";
}

// معلومات النظام
echo "<h3>معلومات النظام</h3>";
echo "<ul>";
echo "<li><strong>إصدار PHP:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>الذاكرة المتاحة:</strong> " . ini_get('memory_limit') . "</li>";
echo "<li><strong>الوقت الأقصى للتنفيذ:</strong> " . ini_get('max_execution_time') . " ثانية</li>";
echo "<li><strong>حجم الملف الأقصى:</strong> " . ini_get('upload_max_filesize') . "</li>";
echo "<li><strong>المجلد الحالي:</strong> " . __DIR__ . "</li>";
echo "</ul>";

// التحقق من الإضافات
echo "<h3>الإضافات المطلوبة</h3>";
$extensions = ['pdo', 'pdo_mysql', 'gd', 'session', 'json'];
echo "<ul>";
foreach ($extensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "<li><strong>$ext:</strong> " . ($loaded ? "✅ متوفر" : "❌ غير متوفر") . "</li>";
}
echo "</ul>";

// ملخص النتائج
echo "<hr>";
echo "<h3>ملخص النتائج</h3>";
echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px;'>";
echo "<h4>الصفحات المختبرة:</h4>";
echo "<ul>";
foreach ($pages as $page => $title) {
    echo "<li><a href='$page' target='_blank'>$title</a></li>";
}
echo "</ul>";

echo "<h4>الخطوات التالية:</h4>";
echo "<ol>";
echo "<li>تحقق من أن جميع الصفحات تعمل بدون أخطاء</li>";
echo "<li>إذا كانت قاعدة البيانات فارغة، أضف بعض المنتجات من <a href='admin/login.php'>لوحة التحكم</a></li>";
echo "<li>اختبر عملية الشراء كاملة</li>";
echo "<li>احذف ملفات الاختبار بعد التأكد من عمل كل شيء</li>";
echo "</ol>";
echo "</div>";

// قائمة ملفات الاختبار للحذف
echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h4>⚠️ ملفات الاختبار للحذف:</h4>";
$testFiles = [
    'test_all_pages.php',
    'test_products.php', 
    'test_connection.php',
    'debug_login.php',
    'fix_admin_login.php',
    'setup_database.php'
];

echo "<ul>";
foreach ($testFiles as $file) {
    if (file_exists($file)) {
        echo "<li>$file ✅</li>";
    }
}
echo "</ul>";
echo "<p><strong>احذف هذه الملفات بعد التأكد من عمل الموقع لأسباب أمنية.</strong></p>";
echo "</div>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h2, h3, h4 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 10px 0;
}

hr {
    margin: 30px 0;
    border: none;
    border-top: 2px solid #dee2e6;
}

p {
    margin: 10px 0;
}
</style>
