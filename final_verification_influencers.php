<?php
require_once 'config/config.php';

echo "=== التحقق النهائي من نظام المؤثرين ===\n";

try {
    echo "\n=== 1. فحص بنية قاعدة البيانات ===\n";
    
    // فحص وجود الجداول
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredTables = ['influencers_content', 'content_categories'];
    foreach ($requiredTables as $table) {
        if (in_array($table, $tables)) {
            echo "✓ جدول {$table} موجود\n";
        } else {
            echo "✗ جدول {$table} غير موجود\n";
        }
    }
    
    echo "\n=== 2. فحص البيانات الأساسية ===\n";
    
    // فحص التصنيفات
    $categoriesCount = $pdo->query("SELECT COUNT(*) FROM content_categories WHERE type IN ('influencer', 'both')")->fetchColumn();
    echo "تصنيفات المؤثرين المتاحة: {$categoriesCount}\n";
    
    if ($categoriesCount > 0) {
        $categories = $pdo->query("SELECT id, name_ar, type FROM content_categories WHERE type IN ('influencer', 'both') ORDER BY sort_order")->fetchAll();
        foreach ($categories as $category) {
            echo "  - ID: {$category['id']}, الاسم: {$category['name_ar']}, النوع: {$category['type']}\n";
        }
    }
    
    // فحص المؤثرين
    $influencersCount = $pdo->query("SELECT COUNT(*) FROM influencers_content")->fetchColumn();
    $publishedCount = $pdo->query("SELECT COUNT(*) FROM influencers_content WHERE status = 'published'")->fetchColumn();
    
    echo "إجمالي المؤثرين: {$influencersCount}\n";
    echo "المؤثرين المنشورين: {$publishedCount}\n";
    
    if ($influencersCount > 0) {
        echo "عينة من المؤثرين:\n";
        $sampleInfluencers = $pdo->query("SELECT id, influencer_name, content_type, status FROM influencers_content ORDER BY created_at DESC LIMIT 3")->fetchAll();
        foreach ($sampleInfluencers as $influencer) {
            echo "  - ID: {$influencer['id']}, الاسم: {$influencer['influencer_name']}, النوع: {$influencer['content_type']}, الحالة: {$influencer['status']}\n";
        }
    }
    
    echo "\n=== 3. اختبار عملية الإدراج الكاملة ===\n";
    
    session_start();
    $_SESSION['admin_id'] = 1;
    $_SESSION['admin_logged_in'] = true;
    
    // الحصول على تصنيف للاختبار
    $testCategory = $pdo->query("SELECT id FROM content_categories WHERE type IN ('influencer', 'both') LIMIT 1")->fetch();
    $testCategoryId = $testCategory ? $testCategory['id'] : null;
    
    // بيانات اختبار شاملة
    $testCases = [
        [
            'name' => 'اختبار مراجعة بسيطة',
            'data' => [
                'influencer_name' => 'مؤثر اختبار 1',
                'influencer_image' => 'https://via.placeholder.com/150x150/ff6b6b/ffffff?text=اختبار1',
                'influencer_image_type' => 'url',
                'content_type' => 'review',
                'content_title' => 'مراجعة منتج ممتاز',
                'content_text' => 'هذا المنتج رائع ومفيد جداً',
                'rating' => 5,
                'product_id' => null,
                'video_url' => null,
                'video_platform' => null,
                'category_id' => $testCategoryId,
                'status' => 'published',
                'is_featured' => 1,
                'sort_order' => 1,
                'created_by' => 1,
                'published_at' => date('Y-m-d H:i:s')
            ]
        ],
        [
            'name' => 'اختبار فيديو يوتيوب',
            'data' => [
                'influencer_name' => 'مؤثر اختبار 2',
                'influencer_image' => 'https://via.placeholder.com/150x150/4ecdc4/ffffff?text=اختبار2',
                'influencer_image_type' => 'url',
                'content_type' => 'video',
                'content_title' => 'فيديو تعليمي مفيد',
                'content_text' => 'شرح مفصل لطريقة الاستخدام',
                'rating' => 4,
                'product_id' => null,
                'video_url' => 'https://youtube.com/watch?v=test123',
                'video_platform' => 'youtube',
                'category_id' => $testCategoryId,
                'status' => 'published',
                'is_featured' => 0,
                'sort_order' => 2,
                'created_by' => 1,
                'published_at' => date('Y-m-d H:i:s')
            ]
        ],
        [
            'name' => 'اختبار منشور بدون تقييم',
            'data' => [
                'influencer_name' => 'مؤثر اختبار 3',
                'influencer_image' => null,
                'influencer_image_type' => 'upload',
                'content_type' => 'post',
                'content_title' => null,
                'content_text' => 'منشور بسيط بدون عنوان أو تقييم',
                'rating' => null,
                'product_id' => null,
                'video_url' => null,
                'video_platform' => null,
                'category_id' => null,
                'status' => 'published',
                'is_featured' => 0,
                'sort_order' => 3,
                'created_by' => 1,
                'published_at' => date('Y-m-d H:i:s')
            ]
        ]
    ];
    
    $insertedIds = [];
    
    foreach ($testCases as $testCase) {
        echo "\n--- {$testCase['name']} ---\n";
        
        $sql = "INSERT INTO influencers_content (
            influencer_name, influencer_image, influencer_image_type, content_type,
            content_title, content_text, rating, product_id, video_url, video_platform,
            category_id, status, is_featured, sort_order, created_by, published_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = array_values($testCase['data']);
        
        try {
            $result = executeQuery($sql, $params);
            if ($result) {
                $insertedId = $pdo->lastInsertId();
                $insertedIds[] = $insertedId;
                echo "✅ تم الإدراج بنجاح - ID: {$insertedId}\n";
                
                // التحقق من الإدراج
                $inserted = fetchOne("SELECT * FROM influencers_content WHERE id = ?", [$insertedId]);
                if ($inserted) {
                    echo "✓ تم التحقق من البيانات\n";
                    echo "  - الاسم: " . $inserted['influencer_name'] . "\n";
                    echo "  - النوع: " . $inserted['content_type'] . "\n";
                    echo "  - الحالة: " . $inserted['status'] . "\n";
                } else {
                    echo "✗ فشل في التحقق من البيانات\n";
                }
            } else {
                echo "✗ فشل في الإدراج\n";
            }
        } catch (Exception $e) {
            echo "✗ خطأ في الإدراج: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n=== 4. اختبار استعلامات العرض ===\n";
    
    // اختبار استعلام لوحة التحكم
    echo "--- استعلام لوحة التحكم ---\n";
    try {
        $adminQuery = "SELECT ic.*, cc.name_ar as category_name 
                      FROM influencers_content ic
                      LEFT JOIN content_categories cc ON ic.category_id = cc.id
                      ORDER BY ic.created_at DESC";
        
        $adminResults = fetchAll($adminQuery);
        echo "✓ استعلام لوحة التحكم نجح - عدد النتائج: " . count($adminResults) . "\n";
        
        if (!empty($adminResults)) {
            echo "عينة من النتائج:\n";
            foreach (array_slice($adminResults, 0, 3) as $result) {
                echo "  - " . $result['influencer_name'] . " (" . $result['content_type'] . ")\n";
            }
        }
    } catch (Exception $e) {
        echo "✗ خطأ في استعلام لوحة التحكم: " . $e->getMessage() . "\n";
    }
    
    // اختبار استعلام الموقع
    echo "\n--- استعلام الموقع ---\n";
    try {
        $frontendQuery = "SELECT * FROM influencers_content 
                         WHERE status = 'published' 
                         ORDER BY is_featured DESC, sort_order ASC, created_at DESC";
        
        $frontendResults = fetchAll($frontendQuery);
        echo "✓ استعلام الموقع نجح - عدد النتائج: " . count($frontendResults) . "\n";
        
        if (!empty($frontendResults)) {
            echo "المحتوى المنشور:\n";
            foreach (array_slice($frontendResults, 0, 3) as $result) {
                $featured = $result['is_featured'] ? ' (مميز)' : '';
                echo "  - " . $result['influencer_name'] . " (" . $result['content_type'] . ")" . $featured . "\n";
            }
        }
    } catch (Exception $e) {
        echo "✗ خطأ في استعلام الموقع: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== 5. تنظيف البيانات التجريبية ===\n";
    
    if (!empty($insertedIds)) {
        foreach ($insertedIds as $id) {
            try {
                executeQuery("DELETE FROM influencers_content WHERE id = ?", [$id]);
                echo "✓ تم حذف السجل ID: {$id}\n";
            } catch (Exception $e) {
                echo "✗ فشل في حذف السجل ID: {$id} - " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n=== 6. الملخص النهائي ===\n";
    
    $finalStats = [
        'total_categories' => $pdo->query("SELECT COUNT(*) FROM content_categories")->fetchColumn(),
        'influencer_categories' => $pdo->query("SELECT COUNT(*) FROM content_categories WHERE type IN ('influencer', 'both')")->fetchColumn(),
        'total_influencers' => $pdo->query("SELECT COUNT(*) FROM influencers_content")->fetchColumn(),
        'published_influencers' => $pdo->query("SELECT COUNT(*) FROM influencers_content WHERE status = 'published'")->fetchColumn(),
        'featured_influencers' => $pdo->query("SELECT COUNT(*) FROM influencers_content WHERE is_featured = 1")->fetchColumn()
    ];
    
    echo "📊 إحصائيات النظام:\n";
    echo "- إجمالي التصنيفات: " . $finalStats['total_categories'] . "\n";
    echo "- تصنيفات المؤثرين: " . $finalStats['influencer_categories'] . "\n";
    echo "- إجمالي المؤثرين: " . $finalStats['total_influencers'] . "\n";
    echo "- المؤثرين المنشورين: " . $finalStats['published_influencers'] . "\n";
    echo "- المؤثرين المميزين: " . $finalStats['featured_influencers'] . "\n";
    
    echo "\n🎉 التحقق النهائي مكتمل!\n";
    
    if ($finalStats['influencer_categories'] > 0 && $finalStats['total_influencers'] >= 0) {
        echo "✅ النظام جاهز للاستخدام\n";
        echo "✅ يمكن إضافة محتوى المؤثرين بنجاح\n";
        echo "✅ البيانات ستظهر في لوحة التحكم والموقع\n";
    } else {
        echo "⚠ قد تحتاج إلى إعداد إضافي\n";
    }
    
    echo "\n📋 الخطوات التالية:\n";
    echo "1. اختبر لوحة التحكم: /admin/influencers.php\n";
    echo "2. اختبر صفحة المؤثرين: /influencers.php\n";
    echo "3. أضف محتوى جديد من لوحة التحكم\n";
    echo "4. تأكد من ظهور المحتوى في الموقع\n";

} catch (Exception $e) {
    echo "✗ خطأ في التحقق النهائي: " . $e->getMessage() . "\n";
}

echo "\n=== انتهاء التحقق النهائي ===\n";
?>
