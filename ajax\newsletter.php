<?php
require_once '../config/config.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit();
}

$email = isset($_POST['email']) ? sanitizeInput($_POST['email']) : '';

if (empty($email)) {
    echo json_encode(['success' => false, 'message' => 'يرجى إدخال البريد الإلكتروني']);
    exit();
}

if (!validateEmail($email)) {
    echo json_encode(['success' => false, 'message' => 'البريد الإلكتروني غير صحيح']);
    exit();
}

// التحقق من وجود البريد الإلكتروني مسبقاً
$existingEmail = fetchOne("SELECT id FROM newsletter WHERE email = ?", [$email]);

if ($existingEmail) {
    echo json_encode(['success' => false, 'message' => 'هذا البريد الإلكتروني مشترك مسبقاً']);
    exit();
}

// إضافة البريد الإلكتروني إلى النشرة البريدية
$newsletterData = [
    'email' => $email,
    'status' => 'active'
];

if (insertData('newsletter', $newsletterData)) {
    echo json_encode(['success' => true, 'message' => 'تم الاشتراك بنجاح في النشرة البريدية']);
} else {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء الاشتراك، يرجى المحاولة مرة أخرى']);
}
?>
