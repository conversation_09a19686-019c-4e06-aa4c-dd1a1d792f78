<?php
/**
 * Final verification that checkout process is working correctly
 */

require_once 'config/config.php';

echo "<h1>Checkout Process Verification</h1>";
echo "<p>This tool verifies that all checkout fixes have been applied correctly.</p>";

$allGood = true;

// 1. Check database connection
echo "<h2>1. Database Connection</h2>";
if ($pdo) {
    try {
        $pdo->query("SELECT 1");
        echo "✅ Database connection working<br>";
    } catch (Exception $e) {
        echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
        $allGood = false;
    }
} else {
    echo "❌ PDO connection is null<br>";
    $allGood = false;
}

// 2. Check required tables
echo "<h2>2. Required Tables</h2>";
$requiredTables = ['orders', 'order_items', 'products'];
foreach ($requiredTables as $table) {
    try {
        $result = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($result->rowCount() > 0) {
            echo "✅ Table '$table' exists<br>";
        } else {
            echo "❌ Table '$table' missing<br>";
            $allGood = false;
        }
    } catch (Exception $e) {
        echo "❌ Error checking table '$table': " . $e->getMessage() . "<br>";
        $allGood = false;
    }
}

// 3. Check essential functions
echo "<h2>3. Essential Functions</h2>";
$requiredFunctions = ['insertData', 'fetchOne', 'fetchAll', 'getCart', 'clearCart'];
foreach ($requiredFunctions as $func) {
    if (function_exists($func)) {
        echo "✅ Function '$func' exists<br>";
    } else {
        echo "❌ Function '$func' missing<br>";
        $allGood = false;
    }
}

// 4. Test order insertion
echo "<h2>4. Order Insertion Test</h2>";
$testOrderData = [
    'customer_name' => 'Verification Test Customer',
    'customer_phone' => '07123456789',
    'address' => 'Test Address, Baghdad',
    'province' => 'بغداد',
    'subtotal' => 50000.00,
    'delivery_price' => 5000.00,
    'discount_amount' => 0.00,
    'total_price' => 55000.00,
    'payment_method' => 'cash_on_delivery',
    'status' => 'pending'
];

$testOrderId = insertData('orders', $testOrderData);
if ($testOrderId) {
    echo "✅ Order insertion working - Test Order ID: {$testOrderId}<br>";
    
    // Test order item insertion
    $testItemData = [
        'order_id' => $testOrderId,
        'product_id' => 1,
        'product_name' => 'Test Product',
        'quantity' => 1,
        'price' => 50000.00,
        'total' => 50000.00
    ];
    
    $testItemId = insertData('order_items', $testItemData);
    if ($testItemId) {
        echo "✅ Order item insertion working - Test Item ID: {$testItemId}<br>";
    } else {
        echo "❌ Order item insertion failed<br>";
        $allGood = false;
    }
    
    // Clean up test data
    $pdo->prepare("DELETE FROM order_items WHERE order_id = ?")->execute([$testOrderId]);
    $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$testOrderId]);
    echo "✅ Test data cleaned up<br>";
    
} else {
    echo "❌ Order insertion failed<br>";
    $allGood = false;
}

// 5. Check checkout.php file for fixes
echo "<h2>5. Checkout File Verification</h2>";
$checkoutContent = file_get_contents('checkout.php');

// Check if the problematic timeout was removed
if (strpos($checkoutContent, 'setTimeout') !== false && strpos($checkoutContent, '30000') !== false) {
    echo "⚠️ Warning: Timeout code may still be present in checkout.php<br>";
} else {
    echo "✅ Problematic timeout code has been removed<br>";
}

// Check if debugging was added
if (strpos($checkoutContent, 'Checkout form submitted - POST data received') !== false) {
    echo "✅ Debugging code has been added<br>";
} else {
    echo "⚠️ Warning: Debugging code may not be present<br>";
}

// Check if form processing exists
if (strpos($checkoutContent, "if (\$_SERVER['REQUEST_METHOD'] == 'POST' && isset(\$_POST['place_order']))") !== false) {
    echo "✅ Form processing code exists<br>";
} else {
    echo "❌ Form processing code missing<br>";
    $allGood = false;
}

// 6. Check recent orders
echo "<h2>6. Recent Orders Check</h2>";
try {
    $recentOrders = fetchAll("SELECT COUNT(*) as count FROM orders WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)");
    $todayCount = $recentOrders[0]['count'] ?? 0;
    echo "Orders created in last 24 hours: {$todayCount}<br>";
    
    $allOrders = fetchAll("SELECT COUNT(*) as count FROM orders");
    $totalCount = $allOrders[0]['count'] ?? 0;
    echo "Total orders in database: {$totalCount}<br>";
    
} catch (Exception $e) {
    echo "❌ Error checking orders: " . $e->getMessage() . "<br>";
}

// 7. Final assessment
echo "<h2>7. Final Assessment</h2>";
if ($allGood) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ All Systems Ready!</h3>";
    echo "<p>The checkout process has been successfully fixed and should now work correctly.</p>";
    echo "<ul>";
    echo "<li>Database connection is working</li>";
    echo "<li>All required tables exist</li>";
    echo "<li>Essential functions are available</li>";
    echo "<li>Order insertion is working</li>";
    echo "<li>JavaScript timeout issue has been resolved</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ Issues Found</h3>";
    echo "<p>Some issues were detected that may prevent the checkout process from working correctly. Please review the errors above.</p>";
    echo "</div>";
}

// 8. Next steps
echo "<h2>8. Testing Instructions</h2>";
echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>To test the checkout process:</h4>";
echo "<ol>";
echo "<li><strong>Add products to cart:</strong> Visit <a href='products.php' target='_blank'>products.php</a> and add items to cart</li>";
echo "<li><strong>Test checkout:</strong> Go to <a href='checkout.php' target='_blank'>checkout.php</a> and complete the form</li>";
echo "<li><strong>Verify order:</strong> Check <a href='admin/orders.php' target='_blank'>admin orders page</a> to see if order appears</li>";
echo "<li><strong>Alternative test:</strong> Use <a href='test_checkout_form.html' target='_blank'>manual test form</a></li>";
echo "</ol>";
echo "</div>";

echo "<h2>9. Troubleshooting Tools</h2>";
echo "<ul>";
echo "<li><a href='test_checkout_debug.php' target='_blank'>Comprehensive Debug Test</a></li>";
echo "<li><a href='test_checkout_submission.php' target='_blank'>Order Submission Test</a></li>";
echo "<li><a href='test_checkout_form.html' target='_blank'>Manual Test Form</a></li>";
echo "<li><a href='debug_checkout.php' target='_blank'>Original Debug Tool</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><em>Checkout fix verification completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
