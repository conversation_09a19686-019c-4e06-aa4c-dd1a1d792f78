<?php
/**
 * ملف اختبار الاتصال
 * Connection Test File
 * 
 * استخدم هذا الملف لاختبار الاتصال بقاعدة البيانات
 */

echo "<h2>اختبار اتصال قاعدة البيانات</h2>";
echo "<p>جاري اختبار الاتصال...</p>";

// معلومات النظام
echo "<h3>معلومات النظام:</h3>";
echo "<ul>";
echo "<li><strong>إصدار PHP:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>نظام التشغيل:</strong> " . PHP_OS . "</li>";
echo "<li><strong>المجلد الحالي:</strong> " . __DIR__ . "</li>";
echo "<li><strong>الوقت:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "</ul>";

// التحقق من الإضافات المطلوبة
echo "<h3>الإضافات المطلوبة:</h3>";
echo "<ul>";
echo "<li><strong>PDO:</strong> " . (extension_loaded('pdo') ? '✅ متوفر' : '❌ غير متوفر') . "</li>";
echo "<li><strong>PDO MySQL:</strong> " . (extension_loaded('pdo_mysql') ? '✅ متوفر' : '❌ غير متوفر') . "</li>";
echo "<li><strong>MySQLi:</strong> " . (extension_loaded('mysqli') ? '✅ متوفر' : '❌ غير متوفر') . "</li>";
echo "<li><strong>GD:</strong> " . (extension_loaded('gd') ? '✅ متوفر' : '❌ غير متوفر') . "</li>";
echo "<li><strong>Session:</strong> " . (function_exists('session_start') ? '✅ متوفر' : '❌ غير متوفر') . "</li>";
echo "</ul>";

// التحقق من الملفات المطلوبة
echo "<h3>الملفات المطلوبة:</h3>";
$requiredFiles = [
    'config/database.php',
    'config/config.php',
    'database.sql',
    'index.php'
];

echo "<ul>";
foreach ($requiredFiles as $file) {
    $exists = file_exists($file);
    echo "<li><strong>$file:</strong> " . ($exists ? '✅ موجود' : '❌ غير موجود') . "</li>";
}
echo "</ul>";

// التحقق من المجلدات
echo "<h3>المجلدات المطلوبة:</h3>";
$requiredDirs = [
    'uploads',
    'uploads/products',
    'uploads/categories',
    'admin',
    'config'
];

echo "<ul>";
foreach ($requiredDirs as $dir) {
    $exists = is_dir($dir);
    $writable = $exists ? is_writable($dir) : false;
    echo "<li><strong>$dir:</strong> ";
    if ($exists) {
        echo "✅ موجود";
        echo $writable ? " (قابل للكتابة)" : " (غير قابل للكتابة)";
    } else {
        echo "❌ غير موجود";
    }
    echo "</li>";
}
echo "</ul>";

// اختبار الاتصال بقاعدة البيانات
echo "<h3>اختبار الاتصال بقاعدة البيانات:</h3>";

try {
    // إعدادات قاعدة البيانات
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $database = 'shop_db';
    
    echo "<p>محاولة الاتصال بـ MySQL...</p>";
    
    // اختبار الاتصال بـ MySQL فقط
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ تم الاتصال بخادم MySQL بنجاح</p>";
    
    // التحقق من وجود قاعدة البيانات
    $databases = $pdo->query("SHOW DATABASES")->fetchAll(PDO::FETCH_COLUMN);
    $dbExists = in_array($database, $databases);
    
    echo "<p><strong>قواعد البيانات الموجودة:</strong></p>";
    echo "<ul>";
    foreach ($databases as $db) {
        $highlight = ($db === $database) ? " style='color: green; font-weight: bold;'" : "";
        echo "<li$highlight>$db</li>";
    }
    echo "</ul>";
    
    if ($dbExists) {
        echo "<p style='color: green;'>✅ قاعدة البيانات '$database' موجودة</p>";
        
        // الاتصال بقاعدة البيانات
        $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
        
        // التحقق من الجداول
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($tables)) {
            echo "<p style='color: orange;'>⚠️ قاعدة البيانات فارغة - لا توجد جداول</p>";
            echo "<p><a href='setup_database.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إعداد قاعدة البيانات</a></p>";
        } else {
            echo "<p style='color: green;'>✅ الجداول الموجودة (" . count($tables) . "):</p>";
            echo "<ul>";
            foreach ($tables as $table) {
                // عدد السجلات في كل جدول
                try {
                    $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
                    echo "<li><strong>$table:</strong> $count سجل</li>";
                } catch (Exception $e) {
                    echo "<li><strong>$table:</strong> خطأ في القراءة</li>";
                }
            }
            echo "</ul>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ قاعدة البيانات '$database' غير موجودة</p>";
        echo "<p><a href='setup_database.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إنشاء قاعدة البيانات</a></p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</p>";
    
    echo "<h4>خطوات حل المشكلة:</h4>";
    echo "<ol>";
    echo "<li><strong>تأكد من تشغيل XAMPP:</strong> افتح XAMPP Control Panel وتأكد من تشغيل MySQL</li>";
    echo "<li><strong>تحقق من المنفذ:</strong> المنفذ الافتراضي هو 3306</li>";
    echo "<li><strong>تحقق من كلمة المرور:</strong> عادة تكون فارغة في XAMPP</li>";
    echo "<li><strong>أعد تشغيل XAMPP:</strong> أوقف وأعد تشغيل Apache و MySQL</li>";
    echo "</ol>";
}

// اختبار تضمين ملفات التكوين
echo "<h3>اختبار ملفات التكوين:</h3>";

try {
    if (file_exists('config/config.php')) {
        echo "<p>محاولة تضمين config.php...</p>";
        require_once 'config/config.php';
        echo "<p style='color: green;'>✅ تم تضمين config.php بنجاح</p>";
        
        // اختبار الثوابت
        echo "<p><strong>الثوابت المعرّفة:</strong></p>";
        echo "<ul>";
        if (defined('SITE_URL')) echo "<li><strong>SITE_URL:</strong> " . SITE_URL . "</li>";
        if (defined('DB_HOST')) echo "<li><strong>DB_HOST:</strong> " . DB_HOST . "</li>";
        if (defined('DB_NAME')) echo "<li><strong>DB_NAME:</strong> " . DB_NAME . "</li>";
        if (defined('DB_USER')) echo "<li><strong>DB_USER:</strong> " . DB_USER . "</li>";
        echo "</ul>";
        
    } else {
        echo "<p style='color: red;'>❌ ملف config.php غير موجود</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تضمين config.php: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>الخطوات التالية:</h3>";
echo "<ol>";
echo "<li>إذا كانت جميع الاختبارات ناجحة، قم بزيارة <a href='index.php'>الصفحة الرئيسية</a></li>";
echo "<li>إذا كانت قاعدة البيانات فارغة، استخدم <a href='setup_database.php'>إعداد قاعدة البيانات</a></li>";
echo "<li>للوصول إلى لوحة التحكم: <a href='admin/login.php'>admin/login.php</a></li>";
echo "<li>احذف هذا الملف بعد التأكد من عمل كل شيء</li>";
echo "</ol>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

ul, ol {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

li {
    margin-bottom: 8px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

hr {
    margin: 40px 0;
    border: none;
    border-top: 3px solid #dee2e6;
}

p {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin: 10px 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
</style>
