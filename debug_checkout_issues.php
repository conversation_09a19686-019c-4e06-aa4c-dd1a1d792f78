<?php
/**
 * Debug specific checkout issues
 */

require_once 'config/config.php';

echo "<h1>🐛 Checkout Issues Debugger</h1>";

// 1. Check constants and configuration
echo "<h2>1. Configuration Check</h2>";
echo "SITE_URL: " . (defined('SITE_URL') ? SITE_URL : 'NOT DEFINED') . "<br>";
echo "DB_HOST: " . (defined('DB_HOST') ? DB_HOST : 'NOT DEFINED') . "<br>";
echo "DB_NAME: " . (defined('DB_NAME') ? DB_NAME : 'NOT DEFINED') . "<br>";

// 2. Check session
echo "<h2>2. Session Check</h2>";
if (!isset($_SESSION)) {
    session_start();
}
echo "Session ID: " . session_id() . "<br>";
echo "Session status: " . session_status() . " (1=disabled, 2=active)<br>";

// 3. Check cart functions
echo "<h2>3. Cart Functions Check</h2>";
if (function_exists('getCart')) {
    echo "✅ getCart function exists<br>";
    $testCart = getCart();
    echo "Current cart: " . json_encode($testCart) . "<br>";
} else {
    echo "❌ getCart function missing<br>";
}

if (function_exists('clearCart')) {
    echo "✅ clearCart function exists<br>";
} else {
    echo "❌ clearCart function missing<br>";
}

// 4. Check sanitizeInput function
echo "<h2>4. Input Sanitization Check</h2>";
if (function_exists('sanitizeInput')) {
    echo "✅ sanitizeInput function exists<br>";
    $testInput = sanitizeInput("Test <script>alert('xss')</script> Input");
    echo "Test sanitization: '$testInput'<br>";
} else {
    echo "❌ sanitizeInput function missing<br>";
}

// 5. Test database operations
echo "<h2>5. Database Operations Test</h2>";
try {
    // Test simple query
    $testQuery = fetchOne("SELECT 1 as test");
    if ($testQuery && $testQuery['test'] == 1) {
        echo "✅ Basic database query working<br>";
    } else {
        echo "❌ Basic database query failed<br>";
    }
    
    // Test orders table access
    $ordersTest = fetchOne("SELECT COUNT(*) as count FROM orders");
    echo "✅ Orders table accessible - Count: " . ($ordersTest['count'] ?? 0) . "<br>";
    
    // Test order_items table access
    $itemsTest = fetchOne("SELECT COUNT(*) as count FROM order_items");
    echo "✅ Order_items table accessible - Count: " . ($itemsTest['count'] ?? 0) . "<br>";
    
} catch (Exception $e) {
    echo "❌ Database operation failed: " . $e->getMessage() . "<br>";
}

// 6. Test insertData with minimal data
echo "<h2>6. InsertData Function Test</h2>";
$minimalOrderData = [
    'customer_name' => 'Debug Test',
    'customer_phone' => '07123456789',
    'address' => 'Test Address',
    'province' => 'بغداد',
    'total_price' => 50000.00,
    'payment_method' => 'cash_on_delivery',
    'status' => 'pending'
];

echo "Testing insertData with minimal order data...<br>";
try {
    $testOrderId = insertData('orders', $minimalOrderData);
    if ($testOrderId) {
        echo "✅ insertData working - Test Order ID: $testOrderId<br>";
        
        // Clean up
        $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$testOrderId]);
        echo "✅ Test order cleaned up<br>";
    } else {
        echo "❌ insertData failed<br>";
        
        // Get PDO error info
        $errorInfo = $pdo->errorInfo();
        if ($errorInfo[0] !== '00000') {
            echo "PDO Error: " . json_encode($errorInfo) . "<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ insertData exception: " . $e->getMessage() . "<br>";
}

// 7. Check if checkout.php can be accessed
echo "<h2>7. Checkout File Access Test</h2>";
if (file_exists('checkout.php')) {
    echo "✅ checkout.php file exists<br>";
    echo "File size: " . filesize('checkout.php') . " bytes<br>";
    echo "Last modified: " . date('Y-m-d H:i:s', filemtime('checkout.php')) . "<br>";
} else {
    echo "❌ checkout.php file not found<br>";
}

// 8. Check order-success.php
echo "<h2>8. Order Success Page Check</h2>";
if (file_exists('order-success.php')) {
    echo "✅ order-success.php file exists<br>";
} else {
    echo "❌ order-success.php file not found<br>";
}

// 9. Test form submission simulation
echo "<h2>9. Form Submission Simulation</h2>";

// Simulate POST data
$_POST = [
    'place_order' => '1',
    'customer_name' => 'Debug Simulation Customer',
    'customer_phone' => '07123456789',
    'address' => 'Debug Test Address',
    'province' => 'بغداد',
    'payment_method' => 'cash_on_delivery',
    'notes' => 'Debug simulation test'
];

$_SERVER['REQUEST_METHOD'] = 'POST';

echo "Simulated POST data:<br>";
echo "<pre>" . json_encode($_POST, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

// Check if the condition would be met
$conditionMet = ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['place_order']));
echo "Checkout condition met: " . ($conditionMet ? 'YES' : 'NO') . "<br>";

if ($conditionMet) {
    echo "✅ Form submission condition would be satisfied<br>";
    
    // Test data extraction
    $customerName = sanitizeInput($_POST['customer_name']);
    $customerPhone = sanitizeInput($_POST['customer_phone']);
    $address = sanitizeInput($_POST['address']);
    $province = sanitizeInput($_POST['province']);
    
    echo "Extracted data:<br>";
    echo "- Customer: $customerName<br>";
    echo "- Phone: $customerPhone<br>";
    echo "- Province: $province<br>";
    
    // Test validation
    $errors = [];
    if (empty($customerName)) $errors[] = 'الاسم مطلوب';
    if (empty($customerPhone)) $errors[] = 'رقم الهاتف مطلوب';
    if (empty($address)) $errors[] = 'العنوان مطلوب';
    if (empty($province)) $errors[] = 'المحافظة مطلوبة';
    
    if (empty($errors)) {
        echo "✅ Validation would pass<br>";
    } else {
        echo "❌ Validation would fail: " . implode(', ', $errors) . "<br>";
    }
} else {
    echo "❌ Form submission condition would not be met<br>";
}

// 10. Check for any output buffering issues
echo "<h2>10. Output Buffering Check</h2>";
$obLevel = ob_get_level();
echo "Output buffering level: $obLevel<br>";
if ($obLevel > 0) {
    echo "⚠️ Output buffering is active - this might interfere with redirects<br>";
} else {
    echo "✅ No output buffering interference<br>";
}

// 11. Check headers sent
echo "<h2>11. Headers Check</h2>";
if (headers_sent($file, $line)) {
    echo "⚠️ Headers already sent from $file at line $line<br>";
    echo "This would prevent redirects from working<br>";
} else {
    echo "✅ Headers not sent yet - redirects should work<br>";
}

// Reset POST and SERVER for normal operation
unset($_POST);
$_SERVER['REQUEST_METHOD'] = 'GET';

echo "<h2>🎯 Summary & Recommendations</h2>";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>";
echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Test with real form:</strong> <a href='test_actual_checkout.php' target='_blank'>Direct Checkout Test</a></li>";
echo "<li><strong>Check browser console:</strong> Look for JavaScript errors during submission</li>";
echo "<li><strong>Monitor network tab:</strong> See if form actually submits</li>";
echo "<li><strong>Check PHP error logs:</strong> Look for server-side errors</li>";
echo "<li><strong>Test admin panel:</strong> <a href='admin/orders.php' target='_blank'>Check Orders</a></li>";
echo "</ol>";
echo "</div>";

echo "<p><em>Debug completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
