<?php
$pageTitle = 'الإرشادات والتعليمات';
require_once 'includes/header.php';

// معاملات البحث والفلترة
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$category = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$importance = isset($_GET['importance']) ? sanitizeInput($_GET['importance']) : '';
$difficulty = isset($_GET['difficulty']) ? sanitizeInput($_GET['difficulty']) : '';
$usageStage = isset($_GET['usage_stage']) ? sanitizeInput($_GET['usage_stage']) : '';
$sortBy = isset($_GET['sort']) ? sanitizeInput($_GET['sort']) : 'importance';

// بناء استعلام البحث
$whereConditions = ["g.status = 'published'"];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(g.title LIKE ? OR g.short_description LIKE ? OR g.content LIKE ?)";
    $searchTerm = "%{$search}%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if ($category > 0) {
    $whereConditions[] = "g.category_id = ?";
    $params[] = $category;
}

if (!empty($importance)) {
    $whereConditions[] = "g.importance_level = ?";
    $params[] = $importance;
}

if (!empty($difficulty)) {
    $whereConditions[] = "g.difficulty_level = ?";
    $params[] = $difficulty;
}

if (!empty($usageStage)) {
    $whereConditions[] = "g.usage_stage = ?";
    $params[] = $usageStage;
}

$whereClause = implode(' AND ', $whereConditions);

// ترتيب النتائج
$orderBy = "g.importance_level DESC, g.sort_order ASC, g.created_at DESC";
switch ($sortBy) {
    case 'title':
        $orderBy = "g.title ASC";
        break;
    case 'newest':
        $orderBy = "g.created_at DESC";
        break;
    case 'popular':
        $orderBy = "g.views_count DESC, g.created_at DESC";
        break;
    case 'difficulty':
        $orderBy = "FIELD(g.difficulty_level, 'easy', 'medium', 'hard'), g.title ASC";
        break;
}

// التصفح (Pagination)
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 12;
$offset = ($page - 1) * $perPage;

// عدد الإرشادات الإجمالي
$countSql = "SELECT COUNT(*) as total FROM guidelines g WHERE {$whereClause}";
$totalResult = fetchOne($countSql, $params);
$totalGuidelines = ($totalResult && isset($totalResult['total'])) ? $totalResult['total'] : 0;
$totalPages = ceil($totalGuidelines / $perPage);

// جلب الإرشادات
$sql = "
    SELECT g.*, cc.name_ar as category_name, cc.icon as category_icon, cc.color as category_color,
           COALESCE(cv.view_count, 0) as total_views
    FROM guidelines g
    LEFT JOIN content_categories cc ON g.category_id = cc.id
    LEFT JOIN (
        SELECT content_id, COUNT(*) as view_count 
        FROM content_views 
        WHERE content_type = 'guideline' 
        GROUP BY content_id
    ) cv ON g.id = cv.content_id
    WHERE {$whereClause}
    ORDER BY {$orderBy}
    LIMIT {$perPage} OFFSET {$offset}
";

$guidelines = fetchAll($sql, $params);

// جلب التصنيفات للفلتر
$categories = fetchAll("SELECT id, name_ar as name FROM content_categories WHERE type IN ('guideline', 'both') AND status = 'active' ORDER BY name_ar");

// إحصائيات سريعة
$stats = [
    'total' => $totalGuidelines,
    'critical' => fetchOne("SELECT COUNT(*) as count FROM guidelines WHERE importance_level = 'critical' AND status = 'published'")['count'] ?? 0,
    'important' => fetchOne("SELECT COUNT(*) as count FROM guidelines WHERE importance_level = 'important' AND status = 'published'")['count'] ?? 0,
    'categories' => count($categories)
];

// دالة مساعدة لعرض الأيقونات
function getIconClass($iconType) {
    $icons = [
        'warning' => 'bi-exclamation-triangle-fill text-warning',
        'info' => 'bi-info-circle-fill text-info',
        'tip' => 'bi-lightbulb-fill text-success',
        'steps' => 'bi-list-ol text-primary',
        'danger' => 'bi-shield-exclamation text-danger',
        'success' => 'bi-check-circle-fill text-success'
    ];
    return $icons[$iconType] ?? 'bi-info-circle-fill text-info';
}

// دالة مساعدة لعرض مستوى الأهمية
function getImportanceBadge($level) {
    $badges = [
        'critical' => 'bg-danger',
        'important' => 'bg-warning',
        'normal' => 'bg-info'
    ];
    $names = [
        'critical' => 'حرج',
        'important' => 'مهم',
        'normal' => 'عادي'
    ];
    return ['class' => $badges[$level] ?? 'bg-info', 'name' => $names[$level] ?? 'عادي'];
}

// دالة مساعدة لعرض مستوى الصعوبة
function getDifficultyBadge($level) {
    $badges = [
        'easy' => 'bg-success',
        'medium' => 'bg-warning',
        'hard' => 'bg-danger'
    ];
    $names = [
        'easy' => 'سهل',
        'medium' => 'متوسط',
        'hard' => 'صعب'
    ];
    return ['class' => $badges[$level] ?? 'bg-success', 'name' => $names[$level] ?? 'سهل'];
}
?>

<!-- Page Header -->
<div class="bg-gradient-success text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="bi bi-book-fill"></i> الإرشادات والتعليمات
                </h1>
                <p class="lead mb-4">
                    دليلك الشامل لاستخدام المنتجات بأمان وفعالية مع نصائح الخبراء
                </p>
                <div class="d-flex gap-4">
                    <div class="text-center">
                        <div class="h2 fw-bold"><?php echo $stats['total']; ?></div>
                        <div>إجمالي الإرشادات</div>
                    </div>
                    <div class="text-center">
                        <div class="h2 fw-bold"><?php echo $stats['critical']; ?></div>
                        <div>إرشادات حرجة</div>
                    </div>
                    <div class="text-center">
                        <div class="h2 fw-bold"><?php echo $stats['important']; ?></div>
                        <div>إرشادات مهمة</div>
                    </div>
                    <div class="text-center">
                        <div class="h2 fw-bold"><?php echo $stats['categories']; ?></div>
                        <div>تصنيف</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 text-center">
                <i class="bi bi-shield-check display-1 opacity-25"></i>
            </div>
        </div>
    </div>
</div>

<!-- Quick Index -->
<div class="container my-4">
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-list-ul"></i> فهرس سريع</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <h6 class="text-primary">حسب الأهمية</h6>
                    <ul class="list-unstyled">
                        <li><a href="?importance=critical" class="text-decoration-none"><i class="bi bi-exclamation-triangle-fill text-danger"></i> إرشادات حرجة</a></li>
                        <li><a href="?importance=important" class="text-decoration-none"><i class="bi bi-exclamation-circle-fill text-warning"></i> إرشادات مهمة</a></li>
                        <li><a href="?importance=normal" class="text-decoration-none"><i class="bi bi-info-circle-fill text-info"></i> إرشادات عامة</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6 class="text-primary">حسب مرحلة الاستخدام</h6>
                    <ul class="list-unstyled">
                        <li><a href="?usage_stage=pre_purchase" class="text-decoration-none"><i class="bi bi-cart-plus"></i> قبل الشراء</a></li>
                        <li><a href="?usage_stage=post_purchase" class="text-decoration-none"><i class="bi bi-box-seam"></i> بعد الشراء</a></li>
                        <li><a href="?usage_stage=maintenance" class="text-decoration-none"><i class="bi bi-tools"></i> الصيانة</a></li>
                        <li><a href="?usage_stage=general" class="text-decoration-none"><i class="bi bi-info-square"></i> عام</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6 class="text-primary">حسب الصعوبة</h6>
                    <ul class="list-unstyled">
                        <li><a href="?difficulty=easy" class="text-decoration-none"><i class="bi bi-check-circle text-success"></i> سهل</a></li>
                        <li><a href="?difficulty=medium" class="text-decoration-none"><i class="bi bi-dash-circle text-warning"></i> متوسط</a></li>
                        <li><a href="?difficulty=hard" class="text-decoration-none"><i class="bi bi-x-circle text-danger"></i> صعب</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6 class="text-primary">التصنيفات</h6>
                    <ul class="list-unstyled">
                        <?php foreach (array_slice($categories, 0, 4) as $cat): ?>
                            <li><a href="?category=<?php echo $cat['id']; ?>" class="text-decoration-none"><i class="bi bi-tag"></i> <?php echo htmlspecialchars($cat['name']); ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="container my-4">
    <div class="card shadow-sm">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <!-- البحث -->
                <div class="col-md-4">
                    <label for="search" class="form-label">البحث</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="ابحث في الإرشادات...">
                    </div>
                </div>

                <!-- التصنيف -->
                <div class="col-md-2">
                    <label for="category" class="form-label">التصنيف</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">جميع التصنيفات</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo $cat['id']; ?>" <?php echo $category === $cat['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($cat['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- الأهمية -->
                <div class="col-md-2">
                    <label for="importance" class="form-label">الأهمية</label>
                    <select class="form-select" id="importance" name="importance">
                        <option value="">جميع المستويات</option>
                        <option value="critical" <?php echo $importance === 'critical' ? 'selected' : ''; ?>>حرج</option>
                        <option value="important" <?php echo $importance === 'important' ? 'selected' : ''; ?>>مهم</option>
                        <option value="normal" <?php echo $importance === 'normal' ? 'selected' : ''; ?>>عادي</option>
                    </select>
                </div>

                <!-- الصعوبة -->
                <div class="col-md-2">
                    <label for="difficulty" class="form-label">الصعوبة</label>
                    <select class="form-select" id="difficulty" name="difficulty">
                        <option value="">جميع المستويات</option>
                        <option value="easy" <?php echo $difficulty === 'easy' ? 'selected' : ''; ?>>سهل</option>
                        <option value="medium" <?php echo $difficulty === 'medium' ? 'selected' : ''; ?>>متوسط</option>
                        <option value="hard" <?php echo $difficulty === 'hard' ? 'selected' : ''; ?>>صعب</option>
                    </select>
                </div>

                <!-- الترتيب -->
                <div class="col-md-2">
                    <label for="sort" class="form-label">ترتيب حسب</label>
                    <select class="form-select" id="sort" name="sort">
                        <option value="importance" <?php echo $sortBy === 'importance' ? 'selected' : ''; ?>>الأهمية</option>
                        <option value="title" <?php echo $sortBy === 'title' ? 'selected' : ''; ?>>العنوان</option>
                        <option value="newest" <?php echo $sortBy === 'newest' ? 'selected' : ''; ?>>الأحدث</option>
                        <option value="popular" <?php echo $sortBy === 'popular' ? 'selected' : ''; ?>>الأكثر مشاهدة</option>
                        <option value="difficulty" <?php echo $sortBy === 'difficulty' ? 'selected' : ''; ?>>الصعوبة</option>
                    </select>
                </div>

                <!-- أزرار التحكم -->
                <div class="col-12">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-funnel"></i> تطبيق الفلاتر
                        </button>
                        <a href="guidelines.php" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Guidelines Grid -->
<div class="container my-5">
    <?php if (empty($guidelines)): ?>
        <!-- No Guidelines Found -->
        <div class="text-center py-5">
            <i class="bi bi-book display-1 text-muted mb-3"></i>
            <h3 class="text-muted">لا توجد إرشادات</h3>
            <p class="text-muted mb-4">لم يتم العثور على إرشادات تطابق معايير البحث</p>
            <a href="guidelines.php" class="btn btn-primary">
                <i class="bi bi-arrow-left"></i> عرض جميع الإرشادات
            </a>
        </div>
    <?php else: ?>
        <!-- Guidelines Cards -->
        <div class="row">
            <?php foreach ($guidelines as $guideline): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm border-0 guideline-card" onclick="openGuideline(<?php echo $guideline['id']; ?>)" style="cursor: pointer;">
                        <!-- Card Header -->
                        <div class="card-header bg-white border-0 pb-2">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="d-flex align-items-center">
                                    <i class="<?php echo getIconClass($guideline['icon_type']); ?> fs-4 me-2"></i>
                                    <div>
                                        <?php $importance = getImportanceBadge($guideline['importance_level']); ?>
                                        <span class="badge <?php echo $importance['class']; ?> rounded-pill">
                                            <?php echo $importance['name']; ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <?php $difficulty = getDifficultyBadge($guideline['difficulty_level']); ?>
                                    <span class="badge <?php echo $difficulty['class']; ?> rounded-pill">
                                        <?php echo $difficulty['name']; ?>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Card Body -->
                        <div class="card-body">
                            <h5 class="card-title"><?php echo htmlspecialchars($guideline['title']); ?></h5>
                            
                            <?php if ($guideline['short_description']): ?>
                                <p class="card-text text-muted">
                                    <?php echo htmlspecialchars(mb_substr($guideline['short_description'], 0, 120) . (mb_strlen($guideline['short_description']) > 120 ? '...' : '')); ?>
                                </p>
                            <?php endif; ?>

                            <!-- Category -->
                            <?php if ($guideline['category_name']): ?>
                                <div class="mb-2">
                                    <span class="badge bg-light text-dark">
                                        <i class="<?php echo $guideline['category_icon']; ?>"></i>
                                        <?php echo htmlspecialchars($guideline['category_name']); ?>
                                    </span>
                                </div>
                            <?php endif; ?>

                            <!-- Steps Preview -->
                            <?php if ($guideline['steps']): ?>
                                <?php $steps = json_decode($guideline['steps'], true); ?>
                                <?php if (is_array($steps) && count($steps) > 0): ?>
                                    <div class="mb-3">
                                        <small class="text-muted">
                                            <i class="bi bi-list-ol"></i> <?php echo count($steps); ?> خطوة
                                        </small>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>

                            <!-- Video Indicator -->
                            <?php if ($guideline['video_url']): ?>
                                <div class="mb-2">
                                    <span class="badge bg-danger rounded-pill">
                                        <i class="bi bi-play-circle"></i> فيديو تعليمي
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Card Footer -->
                        <div class="card-footer bg-white border-0">
                            <div class="d-flex justify-content-between align-items-center text-muted small">
                                <div>
                                    <i class="bi bi-eye"></i> <?php echo number_format($guideline['total_views']); ?> مشاهدة
                                </div>
                                <div>
                                    <i class="bi bi-calendar"></i> <?php echo date('Y/m/d', strtotime($guideline['created_at'])); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <nav aria-label="تصفح الإرشادات" class="mt-5">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                <i class="bi bi-chevron-right"></i> السابق
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                التالي <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    <?php endif; ?>
</div>

<!-- Guideline Detail Modal -->
<div class="modal fade" id="guidelineModal" tabindex="-1" aria-labelledby="guidelineModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="guidelineModalLabel">تفاصيل الإرشاد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body" id="guidelineContent">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.guideline-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.guideline-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.badge {
    font-size: 0.75em;
}

.card-header {
    border-bottom: 1px solid rgba(0,0,0,0.05);
}
</style>

<script>
// فتح تفاصيل الإرشاد
function openGuideline(id) {
    const modal = new bootstrap.Modal(document.getElementById('guidelineModal'));
    const content = document.getElementById('guidelineContent');
    
    // إظهار مؤشر التحميل
    content.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    // جلب تفاصيل الإرشاد
    fetch(`ajax/get_guideline.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = data.html;
                
                // تسجيل مشاهدة
                fetch('ajax/track_view.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `type=guideline&id=${id}`
                });
            } else {
                content.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        حدث خطأ في جلب تفاصيل الإرشاد
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    حدث خطأ في الاتصال
                </div>
            `;
        });
}

// تتبع المشاهدات
document.addEventListener('DOMContentLoaded', function() {
    // تسجيل مشاهدة الصفحة
    fetch('ajax/track_view.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'type=guidelines_page'
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
