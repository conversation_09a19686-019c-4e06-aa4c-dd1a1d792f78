<?php
/**
 * تشخيص مشكلة إضافة التصنيفات
 * Debug Categories Issue
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>تشخيص مشكلة إضافة التصنيفات</h2>";

try {
    // تضمين ملفات التكوين
    require_once 'config/config.php';
    
    echo "<p style='color: green;'>✅ تم تضمين ملفات التكوين بنجاح</p>";
    
    // التحقق من اتصال قاعدة البيانات
    if ($pdo) {
        echo "<p style='color: green;'>✅ اتصال قاعدة البيانات متاح</p>";
    } else {
        echo "<p style='color: red;'>❌ اتصال قاعدة البيانات غير متاح</p>";
        exit();
    }
    
    // التحقق من وجود جدول التصنيفات
    try {
        $tableCheck = $pdo->query("SHOW TABLES LIKE 'categories'");
        if ($tableCheck->rowCount() > 0) {
            echo "<p style='color: green;'>✅ جدول التصنيفات موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ جدول التصنيفات غير موجود</p>";
            exit();
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في التحقق من الجدول: " . $e->getMessage() . "</p>";
        exit();
    }
    
    // عرض التصنيفات الحالية
    echo "<h3>التصنيفات الحالية:</h3>";
    $categories = fetchAll("SELECT * FROM categories ORDER BY created_at DESC");
    
    if (!empty($categories)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>ID</th>";
        echo "<th style='padding: 10px;'>الاسم</th>";
        echo "<th style='padding: 10px;'>الوصف</th>";
        echo "<th style='padding: 10px;'>الحالة</th>";
        echo "<th style='padding: 10px;'>تاريخ الإنشاء</th>";
        echo "</tr>";
        
        foreach ($categories as $category) {
            echo "<tr>";
            echo "<td style='padding: 10px;'>" . $category['id'] . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($category['name']) . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($category['description'] ?? '') . "</td>";
            echo "<td style='padding: 10px;'>" . $category['status'] . "</td>";
            echo "<td style='padding: 10px;'>" . $category['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد تصنيفات في قاعدة البيانات</p>";
    }
    
    // اختبار إضافة تصنيف
    if (isset($_POST['test_add'])) {
        echo "<h3>اختبار إضافة تصنيف:</h3>";
        
        $testName = 'تصنيف تجريبي ' . time();
        $testDescription = 'وصف تجريبي للتصنيف';
        
        echo "<p>محاولة إضافة تصنيف: <strong>$testName</strong></p>";
        
        try {
            // اختبار دالة insertData
            $categoryData = [
                'name' => $testName,
                'description' => $testDescription,
                'status' => 'active'
            ];
            
            $result = insertData('categories', $categoryData);
            
            if ($result) {
                echo "<p style='color: green;'>✅ تم إضافة التصنيف بنجاح! ID: $result</p>";
                echo "<p><a href='debug_categories.php'>تحديث الصفحة لرؤية التصنيف الجديد</a></p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إضافة التصنيف</p>";
                
                // محاولة الإضافة المباشرة
                echo "<p>محاولة الإضافة المباشرة...</p>";
                $stmt = $pdo->prepare("INSERT INTO categories (name, description, status) VALUES (?, ?, ?)");
                $directResult = $stmt->execute([$testName . ' (مباشر)', $testDescription, 'active']);
                
                if ($directResult) {
                    echo "<p style='color: green;'>✅ الإضافة المباشرة نجحت</p>";
                } else {
                    echo "<p style='color: red;'>❌ الإضافة المباشرة فشلت أيضاً</p>";
                    $errorInfo = $stmt->errorInfo();
                    echo "<p>تفاصيل الخطأ: " . print_r($errorInfo, true) . "</p>";
                }
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في إضافة التصنيف: " . $e->getMessage() . "</p>";
        }
    }
    
    // اختبار الدوال المساعدة
    echo "<h3>اختبار الدوال المساعدة:</h3>";
    
    // اختبار sanitizeInput
    $testInput = "<script>alert('test')</script>تجربة";
    $sanitized = sanitizeInput($testInput);
    echo "<p><strong>sanitizeInput:</strong> '$testInput' → '$sanitized'</p>";
    
    // اختبار fetchAll
    echo "<p><strong>fetchAll:</strong> ";
    $testFetch = fetchAll("SELECT COUNT(*) as count FROM categories");
    if ($testFetch) {
        echo "✅ يعمل - عدد التصنيفات: " . $testFetch[0]['count'];
    } else {
        echo "❌ لا يعمل";
    }
    echo "</p>";
    
    // اختبار fetchOne
    echo "<p><strong>fetchOne:</strong> ";
    $testFetchOne = fetchOne("SELECT COUNT(*) as count FROM categories");
    if ($testFetchOne) {
        echo "✅ يعمل - عدد التصنيفات: " . $testFetchOne['count'];
    } else {
        echo "❌ لا يعمل";
    }
    echo "</p>";
    
    // فحص هيكل الجدول
    echo "<h3>هيكل جدول التصنيفات:</h3>";
    $columns = $pdo->query("DESCRIBE categories")->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; background: white;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>العمود</th>";
    echo "<th style='padding: 10px;'>النوع</th>";
    echo "<th style='padding: 10px;'>Null</th>";
    echo "<th style='padding: 10px;'>Key</th>";
    echo "<th style='padding: 10px;'>Default</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='padding: 10px;'>" . $column['Field'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Type'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Null'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Key'] . "</td>";
        echo "<td style='padding: 10px;'>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ في التشخيص</h4>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>

<hr>

<h3>اختبار إضافة تصنيف:</h3>
<form method="POST">
    <button type="submit" name="test_add" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        إضافة تصنيف تجريبي
    </button>
</form>

<hr>

<h3>الحلول المحتملة:</h3>
<div style="background: #e7f3ff; padding: 20px; border-radius: 8px;">
    <h4>إذا كانت المشكلة في دالة insertData:</h4>
    <ol>
        <li>تأكد من أن المتغير $pdo متاح في النطاق العام</li>
        <li>تحقق من أن جميع الحقول المطلوبة موجودة</li>
        <li>تأكد من أن أسماء الأعمدة صحيحة</li>
    </ol>
    
    <h4>إذا كانت المشكلة في صفحة التصنيفات:</h4>
    <ol>
        <li>تحقق من أن النموذج يرسل البيانات بشكل صحيح</li>
        <li>تأكد من أن إعادة التوجيه تعمل</li>
        <li>تحقق من رسائل الخطأ</li>
    </ol>
</div>

<div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h4>للاختبار:</h4>
    <ol>
        <li>اضغط على "إضافة تصنيف تجريبي" أعلاه</li>
        <li>إذا نجح، فالمشكلة في صفحة التصنيفات</li>
        <li>إذا فشل، فالمشكلة في دالة insertData</li>
        <li>تحقق من الأخطاء المعروضة</li>
    </ol>
</div>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h2, h3, h4 {
    color: #333;
}

table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 15px 0;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
