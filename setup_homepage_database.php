<?php
/**
 * إعداد قاعدة بيانات الصفحة الرئيسية
 * Homepage Database Setup Script
 */

require_once 'config/config.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إعداد قاعدة بيانات الصفحة الرئيسية</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>";
echo "<style>body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }</style>";
echo "</head>";
echo "<body>";
echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";
echo "<div class='card shadow-lg'>";
echo "<div class='card-header bg-primary text-white text-center'>";
echo "<h2><i class='bi bi-database'></i> إعداد قاعدة بيانات الصفحة الرئيسية</h2>";
echo "</div>";
echo "<div class='card-body'>";

try {
    // التحقق من الاتصال بقاعدة البيانات
    $pdo = getDBConnection();
    echo "<div class='alert alert-success'><i class='bi bi-check-circle'></i> تم الاتصال بقاعدة البيانات بنجاح</div>";

    // إنشاء جدول homepage_settings
    echo "<h4 class='mt-4'><i class='bi bi-table'></i> إنشاء جدول إعدادات الصفحة الرئيسية</h4>";
    
    $createTableSQL = "
    CREATE TABLE IF NOT EXISTS `homepage_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `section_name` varchar(100) NOT NULL,
        `setting_key` varchar(100) NOT NULL,
        `setting_value` text,
        `setting_type` enum('text','textarea','image','url','number','boolean') DEFAULT 'text',
        `sort_order` int(11) DEFAULT 0,
        `is_active` tinyint(1) DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `section_setting` (`section_name`, `setting_key`),
        KEY `section_name` (`section_name`),
        KEY `is_active` (`is_active`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $pdo->exec($createTableSQL);
    echo "<div class='alert alert-success'><i class='bi bi-check'></i> تم إنشاء جدول homepage_settings بنجاح</div>";

    // التحقق من وجود البيانات
    $existingData = fetchOne("SELECT COUNT(*) as count FROM homepage_settings");
    
    if ($existingData['count'] == 0) {
        echo "<h4 class='mt-4'><i class='bi bi-plus-circle'></i> إدراج البيانات الافتراضية</h4>";
        
        // قراءة ملف البيانات الافتراضية
        $sqlFile = 'database/homepage_settings_data.sql';
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            $pdo->exec($sql);
            echo "<div class='alert alert-success'><i class='bi bi-check'></i> تم إدراج البيانات الافتراضية بنجاح</div>";
        } else {
            // إدراج البيانات يدوياً إذا لم يوجد الملف
            $defaultSettings = [
                // إعدادات الكاروسيل
                ['carousel', 'slide_1_image', 'https://via.placeholder.com/1200x600/667eea/ffffff?text=الشريحة+الأولى', 'url', 1],
                ['carousel', 'slide_1_title', 'مرحباً بك في متجرنا الإلكتروني', 'text', 2],
                ['carousel', 'slide_1_subtitle', 'اكتشف مجموعة واسعة من المنتجات عالية الجودة', 'text', 3],
                ['carousel', 'slide_1_button_text', 'تصفح المنتجات', 'text', 4],
                ['carousel', 'slide_1_button_url', '/products.php', 'text', 5],
                ['carousel', 'slide_2_image', 'https://via.placeholder.com/1200x600/764ba2/ffffff?text=الشريحة+الثانية', 'url', 6],
                ['carousel', 'slide_3_image', 'https://via.placeholder.com/1200x600/28a745/ffffff?text=الشريحة+الثالثة', 'url', 7],
                ['carousel', 'slide_4_image', 'https://via.placeholder.com/1200x600/dc3545/ffffff?text=الشريحة+الرابعة', 'url', 8],
                ['carousel', 'auto_advance_time', '10', 'number', 9],
                ['carousel', 'show_indicators', '1', 'boolean', 10],
                ['carousel', 'show_controls', '1', 'boolean', 11],
                
                // إعدادات المنتجات المميزة
                ['featured_products', 'section_title', 'المنتجات المميزة', 'text', 1],
                ['featured_products', 'section_subtitle', 'اكتشف أفضل منتجاتنا المختارة بعناية', 'text', 2],
                ['featured_products', 'products_limit', '6', 'number', 3],
                ['featured_products', 'show_section', '1', 'boolean', 4],
                
                // إعدادات العروض
                ['offers', 'section_title', 'العروض الخاصة', 'text', 1],
                ['offers', 'section_subtitle', 'لا تفوت عروضنا المحدودة والحصرية', 'text', 2],
                ['offers', 'banner_image', 'https://via.placeholder.com/1200x400/ffc107/000000?text=عروض+خاصة', 'url', 3],
                ['offers', 'banner_title', 'خصومات تصل إلى 50%', 'text', 4],
                ['offers', 'banner_subtitle', 'على مجموعة مختارة من المنتجات', 'text', 5],
                ['offers', 'banner_button_text', 'تصفح العروض', 'text', 6],
                ['offers', 'banner_button_url', '/offers.php', 'text', 7],
                ['offers', 'show_section', '1', 'boolean', 8]
            ];
            
            $stmt = $pdo->prepare("INSERT INTO homepage_settings (section_name, setting_key, setting_value, setting_type, sort_order) VALUES (?, ?, ?, ?, ?)");
            
            foreach ($defaultSettings as $setting) {
                $stmt->execute($setting);
            }
            
            echo "<div class='alert alert-success'><i class='bi bi-check'></i> تم إدراج البيانات الافتراضية بنجاح (يدوياً)</div>";
        }
    } else {
        echo "<div class='alert alert-info'><i class='bi bi-info-circle'></i> البيانات موجودة مسبقاً ({$existingData['count']} إعداد)</div>";
    }

    // عرض ملخص البيانات
    echo "<h4 class='mt-4'><i class='bi bi-list'></i> ملخص الإعدادات</h4>";
    $sections = fetchAll("SELECT section_name, COUNT(*) as count FROM homepage_settings GROUP BY section_name ORDER BY section_name");
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped'>";
    echo "<thead class='table-dark'>";
    echo "<tr><th>القسم</th><th>عدد الإعدادات</th></tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($sections as $section) {
        echo "<tr>";
        echo "<td><strong>" . htmlspecialchars($section['section_name']) . "</strong></td>";
        echo "<td><span class='badge bg-primary'>" . $section['count'] . "</span></td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";

    echo "<div class='alert alert-success mt-4'>";
    echo "<h5><i class='bi bi-check-circle'></i> تم إعداد قاعدة بيانات الصفحة الرئيسية بنجاح!</h5>";
    echo "<p class='mb-0'>يمكنك الآن الانتقال إلى لوحة التحكم لإدارة إعدادات الصفحة الرئيسية.</p>";
    echo "</div>";

    echo "<div class='text-center mt-4'>";
    echo "<a href='admin/dashboard.php' class='btn btn-primary btn-lg me-3'>";
    echo "<i class='bi bi-speedometer2'></i> لوحة التحكم";
    echo "</a>";
    echo "<a href='index.php' class='btn btn-success btn-lg'>";
    echo "<i class='bi bi-house'></i> الصفحة الرئيسية";
    echo "</a>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5><i class='bi bi-exclamation-triangle'></i> حدث خطأ!</h5>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</body>";
echo "</html>";
?>
