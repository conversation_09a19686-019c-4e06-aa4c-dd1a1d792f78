<?php
require_once '../config/config.php';
requireAdminLogin();

// التحقق من وجود معرف الطلب
if (!isset($_POST['order_id']) || !is_numeric($_POST['order_id'])) {
    $_SESSION['error'] = 'معرف الطلب غير صحيح';
    header('Location: orders.php');
    exit();
}

$orderId = (int)$_POST['order_id'];

// جلب تفاصيل الطلب
$order = fetchOne("SELECT * FROM orders WHERE id = ?", [$orderId]);

if (!$order) {
    $_SESSION['error'] = 'الطلب غير موجود';
    header('Location: orders.php');
    exit();
}

// جلب عناصر الطلب
$orderItems = fetchAll("
    SELECT oi.*, p.image, p.category_id 
    FROM order_items oi 
    LEFT JOIN products p ON oi.product_id = p.id 
    WHERE oi.order_id = ?
    ORDER BY oi.id
", [$orderId]);

// دالة تصدير الطلب إلى Excel
function exportSingleOrderToExcel($order, $orderItems) {
    $statusNames = [
        'pending' => 'معلق',
        'confirmed' => 'مؤكد',
        'processing' => 'قيد التحضير',
        'shipped' => 'مشحون',
        'delivered' => 'مكتمل',
        'cancelled' => 'ملغي'
    ];
    
    $paymentMethods = [
        'cash_on_delivery' => 'الدفع عند الاستلام',
        'bank_transfer' => 'تحويل بنكي'
    ];
    
    // إعداد اسم الملف
    $filename = 'order_' . $order['id'] . '_details_' . date('Y-m-d_H-i-s') . '.xlsx';
    
    // إعداد headers للتحميل
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    // بداية ملف Excel XML
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">' . "\n";
    
    // إعدادات المستند
    $xml .= '<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">' . "\n";
    $xml .= '<Title>تفاصيل الطلب #' . $order['id'] . '</Title>' . "\n";
    $xml .= '<Author>نظام إدارة المتجر</Author>' . "\n";
    $xml .= '<Created>' . date('Y-m-d\TH:i:s\Z') . '</Created>' . "\n";
    $xml .= '</DocumentProperties>' . "\n";
    
    // الأنماط
    $xml .= '<Styles>' . "\n";
    
    // نمط العنوان الرئيسي
    $xml .= '<Style ss:ID="Title">' . "\n";
    $xml .= '<Font ss:Bold="1" ss:Size="16" ss:Color="#FFFFFF"/>' . "\n";
    $xml .= '<Interior ss:Color="#2E75B6" ss:Pattern="Solid"/>' . "\n";
    $xml .= '<Alignment ss:Horizontal="Center" ss:Vertical="Center"/>' . "\n";
    $xml .= '<Borders>' . "\n";
    $xml .= '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="2"/>' . "\n";
    $xml .= '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="2"/>' . "\n";
    $xml .= '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="2"/>' . "\n";
    $xml .= '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="2"/>' . "\n";
    $xml .= '</Borders>' . "\n";
    $xml .= '</Style>' . "\n";
    
    // نمط العناوين الفرعية
    $xml .= '<Style ss:ID="Header">' . "\n";
    $xml .= '<Font ss:Bold="1" ss:Size="12" ss:Color="#FFFFFF"/>' . "\n";
    $xml .= '<Interior ss:Color="#4472C4" ss:Pattern="Solid"/>' . "\n";
    $xml .= '<Alignment ss:Horizontal="Center" ss:Vertical="Center"/>' . "\n";
    $xml .= '<Borders>' . "\n";
    $xml .= '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    $xml .= '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    $xml .= '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    $xml .= '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    $xml .= '</Borders>' . "\n";
    $xml .= '</Style>' . "\n";
    
    // نمط البيانات
    $xml .= '<Style ss:ID="Data">' . "\n";
    $xml .= '<Font ss:Size="11"/>' . "\n";
    $xml .= '<Alignment ss:Horizontal="Right" ss:Vertical="Center"/>' . "\n";
    $xml .= '<Borders>' . "\n";
    $xml .= '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '</Borders>' . "\n";
    $xml .= '</Style>' . "\n";
    
    // نمط العملة
    $xml .= '<Style ss:ID="Currency">' . "\n";
    $xml .= '<Font ss:Size="11" ss:Bold="1"/>' . "\n";
    $xml .= '<Alignment ss:Horizontal="Right" ss:Vertical="Center"/>' . "\n";
    $xml .= '<NumberFormat ss:Format="#,##0 &quot;دينار&quot;"/>' . "\n";
    $xml .= '<Borders>' . "\n";
    $xml .= '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '</Borders>' . "\n";
    $xml .= '</Style>' . "\n";
    
    // نمط التسمية
    $xml .= '<Style ss:ID="Label">' . "\n";
    $xml .= '<Font ss:Bold="1" ss:Size="11"/>' . "\n";
    $xml .= '<Alignment ss:Horizontal="Right" ss:Vertical="Center"/>' . "\n";
    $xml .= '<Interior ss:Color="#F2F2F2" ss:Pattern="Solid"/>' . "\n";
    $xml .= '<Borders>' . "\n";
    $xml .= '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '</Borders>' . "\n";
    $xml .= '</Style>' . "\n";
    
    $xml .= '</Styles>' . "\n";
    
    // ورقة العمل
    $xml .= '<Worksheet ss:Name="تفاصيل الطلب">' . "\n";
    $xml .= '<Table>' . "\n";
    
    // تحديد عرض الأعمدة
    $xml .= '<Column ss:Width="150"/>' . "\n"; // العنوان
    $xml .= '<Column ss:Width="200"/>' . "\n"; // القيمة
    
    // العنوان الرئيسي
    $xml .= '<Row ss:Height="30">' . "\n";
    $xml .= '<Cell ss:StyleID="Title" ss:MergeAcross="1"><Data ss:Type="String">تفاصيل الطلب #' . $order['id'] . '</Data></Cell>' . "\n";
    $xml .= '</Row>' . "\n";
    
    // صف فارغ
    $xml .= '<Row><Cell></Cell><Cell></Cell></Row>' . "\n";
    
    // معلومات الطلب الأساسية
    $xml .= '<Row>' . "\n";
    $xml .= '<Cell ss:StyleID="Label"><Data ss:Type="String">رقم الطلب</Data></Cell>' . "\n";
    $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">#' . $order['id'] . '</Data></Cell>' . "\n";
    $xml .= '</Row>' . "\n";
    
    $xml .= '<Row>' . "\n";
    $xml .= '<Cell ss:StyleID="Label"><Data ss:Type="String">تاريخ الطلب</Data></Cell>' . "\n";
    $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . date('Y-m-d H:i', strtotime($order['created_at'])) . '</Data></Cell>' . "\n";
    $xml .= '</Row>' . "\n";
    
    $xml .= '<Row>' . "\n";
    $xml .= '<Cell ss:StyleID="Label"><Data ss:Type="String">حالة الطلب</Data></Cell>' . "\n";
    $statusText = $statusNames[$order['status']] ?? $order['status'];
    $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($statusText, ENT_XML1, 'UTF-8') . '</Data></Cell>' . "\n";
    $xml .= '</Row>' . "\n";
    
    // صف فارغ
    $xml .= '<Row><Cell></Cell><Cell></Cell></Row>' . "\n";
    
    // معلومات العميل
    $xml .= '<Row>' . "\n";
    $xml .= '<Cell ss:StyleID="Header" ss:MergeAcross="1"><Data ss:Type="String">معلومات العميل</Data></Cell>' . "\n";
    $xml .= '</Row>' . "\n";
    
    $xml .= '<Row>' . "\n";
    $xml .= '<Cell ss:StyleID="Label"><Data ss:Type="String">اسم العميل</Data></Cell>' . "\n";
    $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($order['customer_name'], ENT_XML1, 'UTF-8') . '</Data></Cell>' . "\n";
    $xml .= '</Row>' . "\n";
    
    $xml .= '<Row>' . "\n";
    $xml .= '<Cell ss:StyleID="Label"><Data ss:Type="String">رقم الهاتف</Data></Cell>' . "\n";
    $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($order['customer_phone'], ENT_XML1, 'UTF-8') . '</Data></Cell>' . "\n";
    $xml .= '</Row>' . "\n";
    
    $xml .= '<Row>' . "\n";
    $xml .= '<Cell ss:StyleID="Label"><Data ss:Type="String">المحافظة</Data></Cell>' . "\n";
    $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($order['province'], ENT_XML1, 'UTF-8') . '</Data></Cell>' . "\n";
    $xml .= '</Row>' . "\n";
    
    $xml .= '<Row>' . "\n";
    $xml .= '<Cell ss:StyleID="Label"><Data ss:Type="String">العنوان</Data></Cell>' . "\n";
    $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($order['address'], ENT_XML1, 'UTF-8') . '</Data></Cell>' . "\n";
    $xml .= '</Row>' . "\n";
    
    // صف فارغ
    $xml .= '<Row><Cell></Cell><Cell></Cell></Row>' . "\n";
    
    // عناصر الطلب
    if (!empty($orderItems)) {
        $xml .= '<Row>' . "\n";
        $xml .= '<Cell ss:StyleID="Header" ss:MergeAcross="1"><Data ss:Type="String">عناصر الطلب</Data></Cell>' . "\n";
        $xml .= '</Row>' . "\n";
        
        // تغيير عرض الأعمدة لعناصر الطلب
        $xml .= '<Column ss:Width="200"/>' . "\n"; // اسم المنتج
        $xml .= '<Column ss:Width="80"/>' . "\n";  // الكمية
        $xml .= '<Column ss:Width="100"/>' . "\n"; // السعر
        $xml .= '<Column ss:Width="100"/>' . "\n"; // المجموع
        
        // عناوين عناصر الطلب
        $xml .= '<Row>' . "\n";
        $xml .= '<Cell ss:StyleID="Header"><Data ss:Type="String">اسم المنتج</Data></Cell>' . "\n";
        $xml .= '<Cell ss:StyleID="Header"><Data ss:Type="String">الكمية</Data></Cell>' . "\n";
        $xml .= '<Cell ss:StyleID="Header"><Data ss:Type="String">السعر</Data></Cell>' . "\n";
        $xml .= '<Cell ss:StyleID="Header"><Data ss:Type="String">المجموع</Data></Cell>' . "\n";
        $xml .= '</Row>' . "\n";
        
        // بيانات عناصر الطلب
        foreach ($orderItems as $item) {
            $xml .= '<Row>' . "\n";
            $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($item['product_name'], ENT_XML1, 'UTF-8') . '</Data></Cell>' . "\n";
            $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="Number">' . $item['quantity'] . '</Data></Cell>' . "\n";
            $xml .= '<Cell ss:StyleID="Currency"><Data ss:Type="Number">' . $item['price'] . '</Data></Cell>' . "\n";
            $xml .= '<Cell ss:StyleID="Currency"><Data ss:Type="Number">' . $item['total'] . '</Data></Cell>' . "\n";
            $xml .= '</Row>' . "\n";
        }
    }
    
    // صف فارغ
    $xml .= '<Row><Cell></Cell><Cell></Cell></Row>' . "\n";
    
    // ملخص المبالغ
    $xml .= '<Row>' . "\n";
    $xml .= '<Cell ss:StyleID="Header" ss:MergeAcross="1"><Data ss:Type="String">ملخص المبالغ</Data></Cell>' . "\n";
    $xml .= '</Row>' . "\n";
    
    $xml .= '<Row>' . "\n";
    $xml .= '<Cell ss:StyleID="Label"><Data ss:Type="String">المجموع الفرعي</Data></Cell>' . "\n";
    $xml .= '<Cell ss:StyleID="Currency"><Data ss:Type="Number">' . $order['subtotal'] . '</Data></Cell>' . "\n";
    $xml .= '</Row>' . "\n";
    
    if ($order['discount_amount'] > 0) {
        $xml .= '<Row>' . "\n";
        $xml .= '<Cell ss:StyleID="Label"><Data ss:Type="String">الخصم</Data></Cell>' . "\n";
        $xml .= '<Cell ss:StyleID="Currency"><Data ss:Type="Number">' . $order['discount_amount'] . '</Data></Cell>' . "\n";
        $xml .= '</Row>' . "\n";
    }
    
    $xml .= '<Row>' . "\n";
    $xml .= '<Cell ss:StyleID="Label"><Data ss:Type="String">تكلفة التوصيل</Data></Cell>' . "\n";
    $xml .= '<Cell ss:StyleID="Currency"><Data ss:Type="Number">' . $order['delivery_price'] . '</Data></Cell>' . "\n";
    $xml .= '</Row>' . "\n";
    
    $xml .= '<Row>' . "\n";
    $xml .= '<Cell ss:StyleID="Label"><Data ss:Type="String">المجموع الكلي</Data></Cell>' . "\n";
    $xml .= '<Cell ss:StyleID="Currency"><Data ss:Type="Number">' . $order['total_price'] . '</Data></Cell>' . "\n";
    $xml .= '</Row>' . "\n";
    
    // صف فارغ
    $xml .= '<Row><Cell></Cell><Cell></Cell></Row>' . "\n";
    
    // معلومات إضافية
    $xml .= '<Row>' . "\n";
    $xml .= '<Cell ss:StyleID="Label"><Data ss:Type="String">طريقة الدفع</Data></Cell>' . "\n";
    $paymentText = $paymentMethods[$order['payment_method']] ?? $order['payment_method'];
    $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($paymentText, ENT_XML1, 'UTF-8') . '</Data></Cell>' . "\n";
    $xml .= '</Row>' . "\n";
    
    if (!empty($order['notes'])) {
        $xml .= '<Row>' . "\n";
        $xml .= '<Cell ss:StyleID="Label"><Data ss:Type="String">ملاحظات</Data></Cell>' . "\n";
        $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($order['notes'], ENT_XML1, 'UTF-8') . '</Data></Cell>' . "\n";
        $xml .= '</Row>' . "\n";
    }
    
    $xml .= '</Table>' . "\n";
    $xml .= '</Worksheet>' . "\n";
    $xml .= '</Workbook>' . "\n";
    
    echo $xml;
    exit();
}

// تصدير الطلب
try {
    exportSingleOrderToExcel($order, $orderItems);
} catch (Exception $e) {
    error_log("Single order export error: " . $e->getMessage());
    $_SESSION['error'] = 'خطأ في تصدير تفاصيل الطلب: ' . $e->getMessage();
    header('Location: view_order.php?id=' . $orderId);
    exit();
}
?>
