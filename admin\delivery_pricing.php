<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'إدارة أسعار التوصيل';

// معالجة تحديث الأسعار
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_pricing'])) {
    $errors = [];
    $success = [];
    
    foreach ($_POST['provinces'] as $provinceId => $data) {
        $price = (float)$data['price'];
        $isActive = isset($data['is_active']) ? 1 : 0;
        
        if ($price < 0) {
            $errors[] = 'سعر التوصيل لا يمكن أن يكون سالباً';
            continue;
        }
        
        $result = updateData('delivery_pricing', [
            'price' => $price,
            'is_active' => $isActive
        ], 'id = ?', [(int)$provinceId]);
        
        if ($result !== false && $result > 0) {
            $success[] = 'تم تحديث الأسعار بنجاح';
        }
    }
    
    if (!empty($success)) {
        $_SESSION['success'] = 'تم تحديث أسعار التوصيل بنجاح';
    }
    
    if (!empty($errors)) {
        $_SESSION['error'] = implode('<br>', $errors);
    }
    
    header('Location: delivery_pricing.php');
    exit();
}

// جلب أسعار التوصيل
$deliveryPricing = fetchAll("SELECT * FROM delivery_pricing ORDER BY province");

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-professional fade-in-professional">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-professional-dark">
                            <i class="bi bi-truck text-professional-primary"></i> إدارة أسعار التوصيل للمحافظات العراقية
                        </h5>
                        <span class="badge-professional badge-info">إجمالي المحافظات: <?php echo count($deliveryPricing); ?></span>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- رسائل النجاح والخطأ -->
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert-professional alert-success slide-up-professional" role="alert">
                            <i class="bi bi-check-circle-fill alert-icon"></i>
                            <div class="alert-content">
                                <strong>تم بنجاح!</strong><br>
                                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert-professional alert-danger slide-up-professional" role="alert">
                            <i class="bi bi-exclamation-triangle-fill alert-icon"></i>
                            <div class="alert-content">
                                <strong>حدث خطأ!</strong><br>
                                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($deliveryPricing)): ?>
                        <form method="POST" class="form-professional">
                            <div class="alert-professional alert-info slide-up-professional mb-4">
                                <i class="bi bi-info-circle-fill alert-icon"></i>
                                <div class="alert-content">
                                    <strong>معلومات مهمة:</strong><br>
                                    يمكنك تحديث أسعار التوصيل لكل محافظة عراقية. الأسعار بالدينار العراقي.
                                </div>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table-professional">
                                    <thead>
                                        <tr>
                                            <th>المحافظة</th>
                                            <th>سعر التوصيل (دينار عراقي)</th>
                                            <th>الحالة</th>
                                            <th>آخر تحديث</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($deliveryPricing as $province): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($province['province']); ?></strong>
                                                </td>
                                                <td>
                                                    <div class="input-group">
                                                        <input type="number" 
                                                               class="form-control focus-professional" 
                                                               name="provinces[<?php echo $province['id']; ?>][price]" 
                                                               value="<?php echo $province['price']; ?>" 
                                                               min="0" 
                                                               step="100"
                                                               required>
                                                        <span class="input-group-text bg-professional-light border-professional">دينار</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input" 
                                                               type="checkbox" 
                                                               name="provinces[<?php echo $province['id']; ?>][is_active]" 
                                                               id="active_<?php echo $province['id']; ?>"
                                                               <?php echo $province['is_active'] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="active_<?php echo $province['id']; ?>">
                                                            نشط
                                                        </label>
                                                    </div>
                                                </td>
                                                <td>
                                                    <small class="text-professional-muted">
                                                        <?php echo date('Y-m-d H:i', strtotime($province['updated_at'])); ?>
                                                    </small>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="text-center mt-4">
                                <button type="submit" name="update_pricing" class="btn-professional btn-primary btn-lg hover-lift-professional">
                                    <i class="bi bi-check-circle"></i> حفظ التحديثات
                                </button>
                            </div>
                        </form>
                        
                    <?php else: ?>
                        <div class="text-center py-5 fade-in-professional">
                            <i class="bi bi-truck display-1 text-professional-muted"></i>
                            <h4 class="mt-3 text-professional-dark">لا توجد محافظات مُعدة</h4>
                            <p class="text-professional-muted">يجب إنشاء جدول أسعار التوصيل أولاً</p>
                            <a href="create_delivery_pricing_table.php" class="btn-professional btn-primary hover-lift-professional">
                                <i class="bi bi-plus-circle"></i> إعداد أسعار التوصيل
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations to form elements
    const inputs = document.querySelectorAll('input[type="number"]');
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            // Add visual feedback when price is changed
            this.style.borderColor = '#28a745';
            setTimeout(() => {
                this.style.borderColor = '';
            }, 1000);
        });
    });
    
    // Add confirmation for form submission
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const changedInputs = Array.from(inputs).filter(input => 
                input.style.borderColor === 'rgb(40, 167, 69)'
            );
            
            if (changedInputs.length > 0) {
                const confirmed = confirm('هل أنت متأكد من حفظ التحديثات على أسعار التوصيل؟');
                if (!confirmed) {
                    e.preventDefault();
                }
            }
        });
    }
    
    // Add smooth animations to cards
    const cards = document.querySelectorAll('.card-professional');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.3s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
