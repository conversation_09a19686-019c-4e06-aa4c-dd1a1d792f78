<?php
/**
 * Homepage Enhancements Validation Test
 * Professional Arabic E-commerce Homepage Testing Suite
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

// Test Results Array
$testResults = [];

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار تحسينات الصفحة الرئيسية</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css' rel='stylesheet'>
    <style>
        body { font-family: 'Cairo', sans-serif; direction: rtl; }
        .test-section { margin: 2rem 0; padding: 1.5rem; border: 1px solid #dee2e6; border-radius: 0.5rem; }
        .test-pass { color: #28a745; }
        .test-fail { color: #dc3545; }
        .test-warning { color: #ffc107; }
    </style>
</head>
<body>
<div class='container my-5'>
    <h1 class='text-center mb-5'>🧪 اختبار تحسينات الصفحة الرئيسية</h1>";

// Test 1: Visual Enhancements
echo "<div class='test-section'>
    <h3><i class='bi bi-palette'></i> اختبار التحسينات البصرية</h3>";

// Check CSS file exists and has new features
$cssFile = 'assets/css/homepage.css';
if (file_exists($cssFile)) {
    $cssContent = file_get_contents($cssFile);
    
    $visualTests = [
        'Loading Animations' => strpos($cssContent, '.page-loader') !== false,
        'Smooth Scroll' => strpos($cssContent, 'scroll-behavior: smooth') !== false,
        'Hover Effects' => strpos($cssContent, '.ripple-effect') !== false,
        'Color Consistency' => strpos($cssContent, '--primary-gradient') !== false,
        'Shadow Effects' => strpos($cssContent, '--shadow-md') !== false
    ];
    
    foreach ($visualTests as $test => $result) {
        $status = $result ? 'test-pass' : 'test-fail';
        $icon = $result ? 'bi-check-circle' : 'bi-x-circle';
        echo "<p class='$status'><i class='bi $icon'></i> $test: " . ($result ? 'نجح' : 'فشل') . "</p>";
    }
} else {
    echo "<p class='test-fail'><i class='bi bi-x-circle'></i> ملف CSS غير موجود</p>";
}

echo "</div>";

// Test 2: Content & Functionality Improvements
echo "<div class='test-section'>
    <h3><i class='bi bi-gear'></i> اختبار تحسينات المحتوى والوظائف</h3>";

// Check AJAX files exist
$ajaxFiles = [
    'ajax/search_suggestions.php' => 'وظيفة البحث المحسنة',
    'ajax/wishlist.php' => 'نظام المفضلة',
    'ajax/compare.php' => 'نظام المقارنة',
    'ajax/product_quick_view.php' => 'العرض السريع للمنتجات'
];

foreach ($ajaxFiles as $file => $description) {
    $exists = file_exists($file);
    $status = $exists ? 'test-pass' : 'test-fail';
    $icon = $exists ? 'bi-check-circle' : 'bi-x-circle';
    echo "<p class='$status'><i class='bi $icon'></i> $description: " . ($exists ? 'موجود' : 'غير موجود') . "</p>";
}

// Check JavaScript functionality in index.php
$indexContent = file_get_contents('index.php');
$jsTests = [
    'Testimonial Carousel' => strpos($indexContent, 'moveTestimonialCarousel') !== false,
    'Search Functionality' => strpos($indexContent, 'initSearchFunctionality') !== false,
    'Quick Actions' => strpos($indexContent, 'addToWishlist') !== false,
    'WhatsApp Integration' => strpos($indexContent, 'floating-whatsapp') !== false
];

foreach ($jsTests as $test => $result) {
    $status = $result ? 'test-pass' : 'test-fail';
    $icon = $result ? 'bi-check-circle' : 'bi-x-circle';
    echo "<p class='$status'><i class='bi $icon'></i> $test: " . ($result ? 'نجح' : 'فشل') . "</p>";
}

echo "</div>";

// Test 3: Performance & Technical Enhancements
echo "<div class='test-section'>
    <h3><i class='bi bi-speedometer2'></i> اختبار تحسينات الأداء والتقنية</h3>";

$performanceTests = [
    'Caching Headers' => strpos($indexContent, 'Cache-Control') !== false,
    'Compression' => strpos($indexContent, 'ob_gzhandler') !== false,
    'WebP Support' => strpos($indexContent, 'checkWebPSupport') !== false,
    'Structured Data' => strpos($indexContent, 'application/ld+json') !== false,
    'Accessibility' => strpos($indexContent, 'aria-label') !== false,
    'Performance Monitoring' => strpos($indexContent, 'initPerformanceMonitoring') !== false
];

foreach ($performanceTests as $test => $result) {
    $status = $result ? 'test-pass' : 'test-fail';
    $icon = $result ? 'bi-check-circle' : 'bi-x-circle';
    echo "<p class='$status'><i class='bi $icon'></i> $test: " . ($result ? 'نجح' : 'فشل') . "</p>";
}

echo "</div>";

// Test 4: Arabic E-commerce Specific Features
echo "<div class='test-section'>
    <h3><i class='bi bi-translate'></i> اختبار الميزات العربية المخصصة للتجارة الإلكترونية</h3>";

$arabicTests = [
    'Currency Widget' => strpos($indexContent, 'currency-widget') !== false,
    'Region Selector' => strpos($indexContent, 'regionSelector') !== false,
    'Arabic Typography' => strpos($cssContent, '.arabic-title') !== false,
    'Arabic Dates' => strpos($indexContent, 'updateArabicDates') !== false,
    'RTL Animations' => strpos($cssContent, 'slideInFromRight') !== false,
    'Regional Pricing' => strpos($indexContent, 'updateRegionPricing') !== false
];

foreach ($arabicTests as $test => $result) {
    $status = $result ? 'test-pass' : 'test-fail';
    $icon = $result ? 'bi-check-circle' : 'bi-x-circle';
    echo "<p class='$status'><i class='bi $icon'></i> $test: " . ($result ? 'نجح' : 'فشل') . "</p>";
}

echo "</div>";

// Test 5: Database Integration Test
echo "<div class='test-section'>
    <h3><i class='bi bi-database'></i> اختبار التكامل مع قاعدة البيانات</h3>";

try {
    // Test database connection
    $testQuery = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
    $productCount = $testQuery->fetch()['count'];
    echo "<p class='test-pass'><i class='bi bi-check-circle'></i> اتصال قاعدة البيانات: نجح ($productCount منتج نشط)</p>";
    
    // Test categories
    $categoryQuery = $pdo->query("SELECT COUNT(*) as count FROM categories WHERE status = 'active'");
    $categoryCount = $categoryQuery->fetch()['count'];
    echo "<p class='test-pass'><i class='bi bi-check-circle'></i> الفئات: $categoryCount فئة نشطة</p>";
    
} catch (Exception $e) {
    echo "<p class='test-fail'><i class='bi bi-x-circle'></i> خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Test Summary
$totalTests = 0;
$passedTests = 0;

// Count all tests
foreach ([$visualTests ?? [], $jsTests ?? [], $performanceTests ?? [], $arabicTests ?? []] as $testGroup) {
    foreach ($testGroup as $result) {
        $totalTests++;
        if ($result) $passedTests++;
    }
}

$successRate = $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 2) : 0;

echo "<div class='test-section bg-light'>
    <h3><i class='bi bi-clipboard-check'></i> ملخص النتائج</h3>
    <div class='row'>
        <div class='col-md-4'>
            <div class='card text-center'>
                <div class='card-body'>
                    <h4 class='text-primary'>$totalTests</h4>
                    <p>إجمالي الاختبارات</p>
                </div>
            </div>
        </div>
        <div class='col-md-4'>
            <div class='card text-center'>
                <div class='card-body'>
                    <h4 class='text-success'>$passedTests</h4>
                    <p>اختبارات ناجحة</p>
                </div>
            </div>
        </div>
        <div class='col-md-4'>
            <div class='card text-center'>
                <div class='card-body'>
                    <h4 class='text-info'>$successRate%</h4>
                    <p>معدل النجاح</p>
                </div>
            </div>
        </div>
    </div>
</div>";

echo "<div class='text-center mt-4'>
    <a href='index.php' class='btn btn-primary btn-lg'>
        <i class='bi bi-house'></i> عرض الصفحة الرئيسية المحسنة
    </a>
</div>";

echo "</div></body></html>";
?>
