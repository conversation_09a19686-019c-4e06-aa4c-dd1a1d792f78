<?php
/**
 * Professional Hero Carousel Component - Rebuilt from Scratch
 * Modern, Clean, and Error-Free Implementation
 *
 * @version 2.0
 * <AUTHOR> Development Team
 */

// جلب إعدادات الكاروسيل مع معالجة الأخطاء
try {
    $carouselSettings = getHomepageSectionSettings('carousel');
} catch (Exception $e) {
    error_log('Carousel settings error: ' . $e->getMessage());
    $carouselSettings = [];
}

// القيم الافتراضية المهنية
$defaults = [
    'slide_1_image' => '',
    'slide_1_title' => 'مرحباً بك في متجرنا الإلكتروني',
    'slide_1_subtitle' => 'اكتشف مجموعة واسعة من المنتجات عالية الجودة بأفضل الأسعار',
    'slide_1_button_text' => 'تصفح المنتجات',
    'slide_1_button_url' => 'products.php',
    'slide_2_image' => '',
    'slide_2_title' => '',
    'slide_2_subtitle' => '',
    'slide_3_image' => '',
    'slide_3_title' => '',
    'slide_3_subtitle' => '',
    'slide_4_image' => '',
    'slide_4_title' => '',
    'slide_4_subtitle' => '',
    'auto_advance_time' => '6',
    'show_indicators' => '1',
    'show_controls' => '1'
];

// دمج الإعدادات بطريقة آمنة
$carouselSettings = array_merge($defaults, $carouselSettings);

// تحليل الشرائح المتاحة
$slides = [];
for ($i = 1; $i <= 4; $i++) {
    $imageUrl = trim($carouselSettings["slide_{$i}_image"] ?? '');
    if (!empty($imageUrl)) {
        $slides[] = [
            'number' => $i,
            'image' => $imageUrl,
            'title' => trim($carouselSettings["slide_{$i}_title"] ?? ''),
            'subtitle' => trim($carouselSettings["slide_{$i}_subtitle"] ?? ''),
            'has_content' => !empty($carouselSettings["slide_{$i}_title"]) || !empty($carouselSettings["slide_{$i}_subtitle"])
        ];
    }
}

// إعدادات الكاروسيل
$hasSlides = count($slides) > 0;
$autoAdvanceTime = max(3, min(15, (int)$carouselSettings['auto_advance_time'])) * 1000;
$showIndicators = ($carouselSettings['show_indicators'] ?? '1') === '1' && count($slides) > 1;
$showControls = ($carouselSettings['show_controls'] ?? '1') === '1' && count($slides) > 1;
?>

<!-- Professional Hero Carousel Section -->
<section class="hero-section" id="heroSection" role="banner" aria-label="عرض الشرائح الرئيسية">
    <?php if ($hasSlides): ?>
        <!-- Carousel Container -->
        <div class="hero-carousel"
             id="heroCarousel"
             data-auto-advance="<?php echo $autoAdvanceTime; ?>"
             data-slides-count="<?php echo count($slides); ?>"
             role="region"
             aria-label="كاروسيل الصور الرئيسية">

            <!-- Carousel Slides -->
            <div class="carousel-slides" id="carouselSlides">
                <?php foreach ($slides as $index => $slide): ?>
                    <div class="carousel-slide <?php echo $index === 0 ? 'active' : ''; ?>"
                         data-slide="<?php echo $index; ?>"
                         role="group"
                         aria-roledescription="شريحة"
                         aria-label="شريحة <?php echo $index + 1; ?> من <?php echo count($slides); ?>">

                        <!-- Image Container -->
                        <div class="slide-image-container">
                            <!-- Loading Placeholder -->
                            <div class="slide-loading" aria-hidden="true">
                                <div class="loading-spinner"></div>
                                <span class="loading-text">جاري التحميل...</span>
                            </div>

                            <!-- Main Image -->
                            <img src="<?php echo htmlspecialchars($slide['image']); ?>"
                                 class="slide-image"
                                 alt="<?php echo htmlspecialchars($slide['title'] ?: 'صورة الشريحة ' . ($index + 1)); ?>"
                                 loading="<?php echo $index === 0 ? 'eager' : 'lazy'; ?>"
                                 onload="this.parentElement.querySelector('.slide-loading').style.display='none'"
                                 onerror="this.parentElement.classList.add('image-error')">
                        </div>

                        <!-- Content Overlay -->
                        <?php if ($slide['has_content'] || $slide['number'] === 1): ?>
                        <div class="slide-content" role="group" aria-label="محتوى الشريحة">
                            <div class="container">
                                <div class="content-wrapper">
                                    <?php if (!empty($slide['title'])): ?>
                                        <h1 class="slide-title" data-animation="slideUp">
                                            <?php echo htmlspecialchars($slide['title']); ?>
                                        </h1>
                                    <?php endif; ?>

                                    <?php if (!empty($slide['subtitle'])): ?>
                                        <p class="slide-subtitle" data-animation="slideUp" data-delay="200">
                                            <?php echo htmlspecialchars($slide['subtitle']); ?>
                                        </p>
                                    <?php endif; ?>

                                    <?php if ($slide['number'] === 1 && !empty($carouselSettings['slide_1_button_text'])): ?>
                                        <div class="slide-actions" data-animation="fadeIn" data-delay="400">
                                            <a href="<?php echo htmlspecialchars($carouselSettings['slide_1_button_url']); ?>"
                                               class="btn btn-primary btn-hero"
                                               role="button">
                                                <i class="bi bi-grid-fill" aria-hidden="true"></i>
                                                <span><?php echo htmlspecialchars($carouselSettings['slide_1_button_text']); ?></span>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Navigation Controls -->
            <?php if ($showControls): ?>
            <div class="carousel-controls" role="group" aria-label="أزرار التنقل">
                <button class="carousel-btn carousel-btn-prev"
                        type="button"
                        id="carouselPrev"
                        aria-label="الشريحة السابقة">
                    <i class="bi bi-chevron-right" aria-hidden="true"></i>
                </button>
                <button class="carousel-btn carousel-btn-next"
                        type="button"
                        id="carouselNext"
                        aria-label="الشريحة التالية">
                    <i class="bi bi-chevron-left" aria-hidden="true"></i>
                </button>
            </div>
            <?php endif; ?>

            <!-- Indicators -->
            <?php if ($showIndicators): ?>
            <div class="carousel-indicators" role="tablist" aria-label="مؤشرات الشرائح">
                <?php foreach ($slides as $index => $slide): ?>
                    <button class="indicator <?php echo $index === 0 ? 'active' : ''; ?>"
                            type="button"
                            role="tab"
                            data-slide="<?php echo $index; ?>"
                            aria-label="الذهاب إلى الشريحة <?php echo $index + 1; ?>"
                            aria-selected="<?php echo $index === 0 ? 'true' : 'false'; ?>">
                    </button>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>

    <?php else: ?>
        <!-- Default Hero Section -->
        <div class="hero-default" role="banner" aria-label="القسم الرئيسي">
            <div class="hero-background">
                <div class="background-overlay"></div>
                <div class="container">
                    <div class="default-content">
                        <h1 class="default-title" data-animation="slideUp">
                            <?php echo htmlspecialchars($carouselSettings['slide_1_title']); ?>
                        </h1>
                        <p class="default-subtitle" data-animation="slideUp" data-delay="200">
                            <?php echo htmlspecialchars($carouselSettings['slide_1_subtitle']); ?>
                        </p>
                        <div class="default-actions" data-animation="fadeIn" data-delay="400">
                            <a href="<?php echo htmlspecialchars($carouselSettings['slide_1_button_url']); ?>"
                               class="btn btn-primary btn-hero"
                               role="button">
                                <i class="bi bi-grid-fill" aria-hidden="true"></i>
                                <span><?php echo htmlspecialchars($carouselSettings['slide_1_button_text']); ?></span>
                            </a>
                            <a href="about.php"
                               class="btn btn-outline-light btn-hero"
                               role="button">
                                <i class="bi bi-info-circle" aria-hidden="true"></i>
                                <span>تعرف علينا</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</section>




