<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'إصلاح مشاكل المنتجات';

echo "<h2>إصلاح مشاكل إدارة المنتجات</h2>";

$issues = [];
$fixes = [];

// Check 1: Database connection
echo "<h3>1. فحص الاتصال بقاعدة البيانات</h3>";
try {
    $test = fetchOne("SELECT 1 as test");
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات يعمل</p>";
} catch (Exception $e) {
    $issues[] = "مشكلة في الاتصال بقاعدة البيانات: " . $e->getMessage();
    echo "<p style='color: red;'>❌ مشكلة في الاتصال: " . $e->getMessage() . "</p>";
}

// Check 2: Products table structure
echo "<h3>2. فحص هيكل جدول المنتجات</h3>";
try {
    $columns = fetchAll("SHOW COLUMNS FROM products");
    $columnNames = array_column($columns, 'Field');
    
    $requiredColumns = [
        'id', 'name', 'description', 'short_description', 'price', 'discount', 
        'stock', 'category_id', 'image', 'is_featured', 'status', 'created_at', 'updated_at'
    ];
    
    $newColumns = ['ingredients', 'usage_instructions', 'image_url_1', 'image_url_2', 'image_url_3', 'video_url'];
    
    $missingRequired = [];
    $missingNew = [];
    
    foreach ($requiredColumns as $col) {
        if (!in_array($col, $columnNames)) {
            $missingRequired[] = $col;
        }
    }
    
    foreach ($newColumns as $col) {
        if (!in_array($col, $columnNames)) {
            $missingNew[] = $col;
        }
    }
    
    if (empty($missingRequired)) {
        echo "<p style='color: green;'>✅ جميع الأعمدة الأساسية موجودة</p>";
    } else {
        $issues[] = "أعمدة أساسية مفقودة: " . implode(', ', $missingRequired);
        echo "<p style='color: red;'>❌ أعمدة أساسية مفقودة: " . implode(', ', $missingRequired) . "</p>";
    }
    
    if (empty($missingNew)) {
        echo "<p style='color: green;'>✅ جميع الأعمدة الجديدة موجودة</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ أعمدة جديدة مفقودة: " . implode(', ', $missingNew) . "</p>";
        echo "<p>يمكن إضافتها باستخدام <a href='migrate_products_table.php'>تحديث قاعدة البيانات</a></p>";
    }
    
} catch (Exception $e) {
    $issues[] = "خطأ في فحص جدول المنتجات: " . $e->getMessage();
    echo "<p style='color: red;'>❌ خطأ في فحص الجدول: " . $e->getMessage() . "</p>";
}

// Check 3: Test basic operations
echo "<h3>3. اختبار العمليات الأساسية</h3>";

// Test insert
try {
    $testData = [
        'name' => 'منتج اختبار الإصلاح',
        'description' => 'منتج مؤقت للاختبار',
        'price' => 1.00,
        'stock' => 1,
        'status' => 'inactive'
    ];
    
    $insertId = insertData('products', $testData);
    if ($insertId) {
        echo "<p style='color: green;'>✅ عملية الإدراج تعمل</p>";
        
        // Test update
        $updateResult = updateData('products', ['name' => 'منتج اختبار محدث'], 'id = ?', [$insertId]);
        if ($updateResult !== false && $updateResult > 0) {
            echo "<p style='color: green;'>✅ عملية التحديث تعمل</p>";
        } else {
            $issues[] = "عملية التحديث لا تعمل";
            echo "<p style='color: red;'>❌ عملية التحديث لا تعمل (النتيجة: " . var_export($updateResult, true) . ")</p>";
        }
        
        // Test delete
        $deleteResult = deleteData('products', 'id = ?', [$insertId]);
        if ($deleteResult !== false && $deleteResult > 0) {
            echo "<p style='color: green;'>✅ عملية الحذف تعمل</p>";
        } else {
            $issues[] = "عملية الحذف لا تعمل";
            echo "<p style='color: red;'>❌ عملية الحذف لا تعمل (النتيجة: " . var_export($deleteResult, true) . ")</p>";
        }
        
    } else {
        $issues[] = "عملية الإدراج لا تعمل";
        echo "<p style='color: red;'>❌ عملية الإدراج لا تعمل</p>";
    }
    
} catch (Exception $e) {
    $issues[] = "خطأ في اختبار العمليات: " . $e->getMessage();
    echo "<p style='color: red;'>❌ خطأ في الاختبار: " . $e->getMessage() . "</p>";
}

// Auto-fix section
echo "<h3>4. الإصلاحات التلقائية</h3>";

if (isset($_POST['auto_fix'])) {
    echo "<h4>تشغيل الإصلاحات...</h4>";
    
    // Fix 1: Add missing columns
    if (!empty($missingNew)) {
        echo "<p>إضافة الأعمدة المفقودة...</p>";
        try {
            $alterQueries = [
                "ALTER TABLE products ADD COLUMN ingredients TEXT DEFAULT NULL",
                "ALTER TABLE products ADD COLUMN usage_instructions TEXT DEFAULT NULL", 
                "ALTER TABLE products ADD COLUMN image_url_1 VARCHAR(500) DEFAULT NULL",
                "ALTER TABLE products ADD COLUMN image_url_2 VARCHAR(500) DEFAULT NULL",
                "ALTER TABLE products ADD COLUMN image_url_3 VARCHAR(500) DEFAULT NULL",
                "ALTER TABLE products ADD COLUMN video_url VARCHAR(500) DEFAULT NULL"
            ];
            
            foreach ($alterQueries as $query) {
                try {
                    executeQuery($query);
                } catch (Exception $e) {
                    // Ignore if column already exists
                    if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                        throw $e;
                    }
                }
            }
            
            echo "<p style='color: green;'>✅ تم إضافة الأعمدة المفقودة</p>";
            $fixes[] = "تم إضافة الأعمدة الجديدة";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في إضافة الأعمدة: " . $e->getMessage() . "</p>";
        }
    }
    
    // Test again after fixes
    echo "<h4>اختبار بعد الإصلاح...</h4>";
    try {
        $testData = [
            'name' => 'منتج اختبار بعد الإصلاح',
            'description' => 'اختبار',
            'ingredients' => 'مكونات تجريبية',
            'usage_instructions' => 'تعليمات تجريبية',
            'image_url_1' => 'https://via.placeholder.com/300',
            'video_url' => 'https://youtube.com/test',
            'price' => 1.00,
            'stock' => 1,
            'status' => 'inactive'
        ];
        
        $insertId = insertData('products', $testData);
        if ($insertId) {
            echo "<p style='color: green;'>✅ الإدراج مع الأعمدة الجديدة يعمل</p>";
            
            $updateData = ['ingredients' => 'مكونات محدثة', 'usage_instructions' => 'تعليمات محدثة'];
            $updateResult = updateData('products', $updateData, 'id = ?', [$insertId]);
            
            if ($updateResult !== false && $updateResult > 0) {
                echo "<p style='color: green;'>✅ التحديث مع الأعمدة الجديدة يعمل</p>";
            }
            
            deleteData('products', 'id = ?', [$insertId]);
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في الاختبار النهائي: " . $e->getMessage() . "</p>";
    }
}

// Summary
echo "<h3>5. الملخص</h3>";
if (empty($issues)) {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h4 style='color: #155724;'>✅ جميع الفحوصات نجحت!</h4>";
    echo "<p>نظام إدارة المنتجات يعمل بشكل صحيح.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h4 style='color: #721c24;'>❌ تم العثور على مشاكل:</h4>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>" . htmlspecialchars($issue) . "</li>";
    }
    echo "</ul>";
    
    if (!isset($_POST['auto_fix'])) {
        echo "<form method='POST'>";
        echo "<button type='submit' name='auto_fix' class='btn btn-warning'>تشغيل الإصلاح التلقائي</button>";
        echo "</form>";
    }
    echo "</div>";
}

if (!empty($fixes)) {
    echo "<div style='background: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px; margin-top: 10px;'>";
    echo "<h4 style='color: #0c5460;'>🔧 الإصلاحات المطبقة:</h4>";
    echo "<ul>";
    foreach ($fixes as $fix) {
        echo "<li>" . htmlspecialchars($fix) . "</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='products.php'>العودة إلى إدارة المنتجات</a></p>";
echo "<p><a href='test_db_functions.php'>تشغيل اختبار دوال قاعدة البيانات</a></p>";
?>
