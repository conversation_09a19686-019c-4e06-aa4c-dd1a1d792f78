<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Checkout Form</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>Test Checkout Form</h3>
                        <p class="mb-0">Use this form to test the checkout process directly</p>
                    </div>
                    <div class="card-body">
                        <form action="checkout.php" method="POST" id="testCheckoutForm">
                            <input type="hidden" name="place_order" value="1">
                            
                            <div class="mb-3">
                                <label for="customer_name" class="form-label">اسم العميل *</label>
                                <input type="text" class="form-control" id="customer_name" name="customer_name" 
                                       value="Test Customer" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="customer_phone" class="form-label">رقم الهاتف *</label>
                                <input type="text" class="form-control" id="customer_phone" name="customer_phone" 
                                       value="07123456789" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">العنوان *</label>
                                <textarea class="form-control" id="address" name="address" rows="3" required>Test Address, Baghdad, Iraq</textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="province" class="form-label">المحافظة *</label>
                                <select class="form-control" id="province" name="province" required>
                                    <option value="">اختر المحافظة</option>
                                    <option value="بغداد" selected>بغداد</option>
                                    <option value="البصرة">البصرة</option>
                                    <option value="نينوى">نينوى</option>
                                    <option value="أربيل">أربيل</option>
                                    <option value="النجف">النجف</option>
                                    <option value="كربلاء">كربلاء</option>
                                    <option value="الأنبار">الأنبار</option>
                                    <option value="ذي قار">ذي قار</option>
                                    <option value="بابل">بابل</option>
                                    <option value="كركوك">كركوك</option>
                                    <option value="ديالى">ديالى</option>
                                    <option value="المثنى">المثنى</option>
                                    <option value="القادسية">القادسية</option>
                                    <option value="ميسان">ميسان</option>
                                    <option value="واسط">واسط</option>
                                    <option value="دهوك">دهوك</option>
                                    <option value="السليمانية">السليمانية</option>
                                    <option value="صلاح الدين">صلاح الدين</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="payment_method" class="form-label">طريقة الدفع</label>
                                <select class="form-control" id="payment_method" name="payment_method">
                                    <option value="cash_on_delivery" selected>الدفع عند الاستلام</option>
                                    <option value="bank_transfer">تحويل بنكي</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="2">Test order from manual form</textarea>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    تأكيد الطلب
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h4>Instructions</h4>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>First, add some products to your cart by visiting the <a href="products.php" target="_blank">products page</a></li>
                            <li>Then submit this form to test the checkout process</li>
                            <li>Check if the order appears in the <a href="admin/orders.php" target="_blank">admin orders page</a></li>
                            <li>If successful, you should be redirected to the order success page</li>
                        </ol>
                        
                        <div class="alert alert-info">
                            <strong>Note:</strong> Make sure you have products in your cart before submitting this form.
                            You can add products by visiting the products page first.
                        </div>
                        
                        <div class="mt-3">
                            <a href="products.php" class="btn btn-outline-primary me-2">Add Products to Cart</a>
                            <a href="cart.php" class="btn btn-outline-secondary me-2">View Cart</a>
                            <a href="checkout.php" class="btn btn-outline-success me-2">Normal Checkout</a>
                            <a href="admin/orders.php" class="btn btn-outline-warning">Admin Orders</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('testCheckoutForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري المعالجة...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
