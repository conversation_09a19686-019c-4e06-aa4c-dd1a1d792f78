<?php
require_once 'config/database.php';

echo "=== فحص بنية قاعدة البيانات للمؤثرين ===\n";

try {
    echo "\n=== 1. فحص الاتصال بقاعدة البيانات ===\n";
    if ($pdo) {
        echo "✓ الاتصال بقاعدة البيانات ناجح\n";
        echo "نوع قاعدة البيانات: " . $pdo->getAttribute(PDO::ATTR_DRIVER_NAME) . "\n";
    } else {
        echo "✗ فشل الاتصال بقاعدة البيانات\n";
        exit;
    }

    echo "\n=== 2. فحص الجداول الموجودة ===\n";
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "الجداول الموجودة في قاعدة البيانات:\n";
    foreach ($tables as $table) {
        echo "- {$table}\n";
    }

    echo "\n=== 3. فحص جدول influencers_content ===\n";
    $influencersTableExists = in_array('influencers_content', $tables);
    
    if ($influencersTableExists) {
        echo "✓ جدول influencers_content موجود\n";
        
        // فحص بنية الجدول
        $columns = $pdo->query("DESCRIBE influencers_content")->fetchAll(PDO::FETCH_ASSOC);
        echo "أعمدة جدول influencers_content:\n";
        foreach ($columns as $column) {
            $nullable = $column['Null'] === 'YES' ? 'اختياري' : 'مطلوب';
            $default = $column['Default'] !== null ? " (افتراضي: {$column['Default']})" : '';
            echo "- " . $column['Field'] . " (" . $column['Type'] . ") - {$nullable}{$default}\n";
        }
        
        // فحص عدد السجلات
        $count = $pdo->query("SELECT COUNT(*) FROM influencers_content")->fetchColumn();
        echo "عدد السجلات الموجودة: {$count}\n";
        
    } else {
        echo "✗ جدول influencers_content غير موجود\n";
        echo "محاولة إنشاء الجدول...\n";
        
        $createInfluencersTable = "
        CREATE TABLE IF NOT EXISTS influencers_content (
            id INT AUTO_INCREMENT PRIMARY KEY,
            influencer_name VARCHAR(255) NOT NULL,
            influencer_image VARCHAR(500),
            influencer_image_type ENUM('upload', 'url') DEFAULT 'upload',
            content_type ENUM('video', 'post', 'review') NOT NULL,
            content_title VARCHAR(500),
            content_text TEXT NOT NULL,
            rating TINYINT(1) CHECK (rating >= 1 AND rating <= 5),
            product_id INT,
            video_url VARCHAR(1000),
            video_platform ENUM('youtube', 'instagram', 'tiktok', 'other'),
            category_id INT,
            status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
            is_featured BOOLEAN DEFAULT FALSE,
            sort_order INT DEFAULT 0,
            views_count INT DEFAULT 0,
            likes_count INT DEFAULT 0,
            shares_count INT DEFAULT 0,
            published_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_by INT,
            updated_by INT,
            
            INDEX idx_status (status),
            INDEX idx_content_type (content_type),
            INDEX idx_rating (rating),
            INDEX idx_featured (is_featured)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $pdo->exec($createInfluencersTable);
            echo "✅ تم إنشاء جدول influencers_content بنجاح\n";
        } catch (Exception $e) {
            echo "✗ فشل في إنشاء جدول influencers_content: " . $e->getMessage() . "\n";
        }
    }

    echo "\n=== 4. فحص جدول content_categories ===\n";
    $categoriesTableExists = in_array('content_categories', $tables);
    
    if ($categoriesTableExists) {
        echo "✓ جدول content_categories موجود\n";
        
        // فحص بنية الجدول
        $columns = $pdo->query("DESCRIBE content_categories")->fetchAll(PDO::FETCH_ASSOC);
        echo "أعمدة جدول content_categories:\n";
        foreach ($columns as $column) {
            $nullable = $column['Null'] === 'YES' ? 'اختياري' : 'مطلوب';
            $default = $column['Default'] !== null ? " (افتراضي: {$column['Default']})" : '';
            echo "- " . $column['Field'] . " (" . $column['Type'] . ") - {$nullable}{$default}\n";
        }
        
        // فحص التصنيفات الموجودة
        $categories = $pdo->query("SELECT * FROM content_categories")->fetchAll(PDO::FETCH_ASSOC);
        echo "التصنيفات الموجودة:\n";
        foreach ($categories as $category) {
            echo "- ID: {$category['id']}, الاسم: {$category['name_ar']}, النوع: {$category['type']}, الحالة: {$category['status']}\n";
        }
        
        // فحص تصنيفات المؤثرين
        $influencerCategories = $pdo->query("SELECT * FROM content_categories WHERE type IN ('influencer', 'both')")->fetchAll(PDO::FETCH_ASSOC);
        echo "تصنيفات المؤثرين:\n";
        if (empty($influencerCategories)) {
            echo "⚠ لا توجد تصنيفات للمؤثرين\n";
        } else {
            foreach ($influencerCategories as $category) {
                echo "- ID: {$category['id']}, الاسم: {$category['name_ar']}\n";
            }
        }
        
    } else {
        echo "✗ جدول content_categories غير موجود\n";
        echo "محاولة إنشاء الجدول...\n";
        
        $createCategoriesTable = "
        CREATE TABLE IF NOT EXISTS content_categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name_ar VARCHAR(255) NOT NULL,
            name_en VARCHAR(255),
            description TEXT,
            type ENUM('guideline', 'influencer', 'both') NOT NULL DEFAULT 'both',
            status ENUM('active', 'inactive') DEFAULT 'active',
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_type (type),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $pdo->exec($createCategoriesTable);
            echo "✅ تم إنشاء جدول content_categories بنجاح\n";
            
            // إضافة تصنيفات افتراضية
            $defaultCategories = [
                ['name_ar' => 'مراجعات المنتجات', 'name_en' => 'Product Reviews', 'type' => 'influencer'],
                ['name_ar' => 'فيديوهات تعليمية', 'name_en' => 'Tutorial Videos', 'type' => 'both'],
                ['name_ar' => 'منشورات ترويجية', 'name_en' => 'Promotional Posts', 'type' => 'influencer'],
                ['name_ar' => 'نصائح الاستخدام', 'name_en' => 'Usage Tips', 'type' => 'both']
            ];
            
            $insertCategory = $pdo->prepare("INSERT INTO content_categories (name_ar, name_en, type, status) VALUES (?, ?, ?, 'active')");
            
            foreach ($defaultCategories as $category) {
                $insertCategory->execute([$category['name_ar'], $category['name_en'], $category['type']]);
            }
            
            echo "✅ تم إضافة التصنيفات الافتراضية\n";
            
        } catch (Exception $e) {
            echo "✗ فشل في إنشاء جدول content_categories: " . $e->getMessage() . "\n";
        }
    }

    echo "\n=== 5. فحص جدول products ===\n";
    $productsTableExists = in_array('products', $tables);
    
    if ($productsTableExists) {
        echo "✓ جدول products موجود\n";
        $productsCount = $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn();
        echo "عدد المنتجات: {$productsCount}\n";
    } else {
        echo "⚠ جدول products غير موجود - قد يسبب مشاكل في المفاتيح الخارجية\n";
    }

    echo "\n=== 6. اختبار إدراج بيانات تجريبية ===\n";
    
    // التأكد من وجود جلسة المدير
    session_start();
    $_SESSION['admin_id'] = 1;
    
    $testData = [
        'influencer_name' => 'مؤثر تجريبي - ' . date('H:i:s'),
        'influencer_image' => 'https://via.placeholder.com/150x150/007bff/ffffff?text=مؤثر',
        'influencer_image_type' => 'url',
        'content_type' => 'review',
        'content_title' => 'مراجعة تجريبية',
        'content_text' => 'نص المراجعة التجريبية للاختبار',
        'rating' => 5,
        'product_id' => null,
        'video_url' => null,
        'video_platform' => null,
        'category_id' => null,
        'status' => 'published',
        'is_featured' => 0,
        'sort_order' => 0,
        'created_by' => 1,
        'published_at' => date('Y-m-d H:i:s')
    ];

    $sql = "INSERT INTO influencers_content (
        influencer_name, influencer_image, influencer_image_type, content_type,
        content_title, content_text, rating, product_id, video_url, video_platform,
        category_id, status, is_featured, sort_order, created_by, published_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $params = array_values($testData);
    
    echo "محاولة إدراج بيانات تجريبية...\n";
    echo "عدد المعاملات: " . count($params) . "\n";
    echo "عدد العلامات في SQL: " . substr_count($sql, '?') . "\n";
    
    try {
        $stmt = $pdo->prepare($sql);
        if ($stmt) {
            $result = $stmt->execute($params);
            if ($result) {
                $insertedId = $pdo->lastInsertId();
                echo "✅ تم إدراج البيانات التجريبية بنجاح - ID: {$insertedId}\n";
                
                // التحقق من الإدراج
                $inserted = $pdo->prepare("SELECT * FROM influencers_content WHERE id = ?");
                $inserted->execute([$insertedId]);
                $data = $inserted->fetch(PDO::FETCH_ASSOC);
                
                if ($data) {
                    echo "✓ تم التحقق من وجود البيانات:\n";
                    echo "  - الاسم: " . $data['influencer_name'] . "\n";
                    echo "  - النوع: " . $data['content_type'] . "\n";
                    echo "  - الحالة: " . $data['status'] . "\n";
                    echo "  - تاريخ الإنشاء: " . $data['created_at'] . "\n";
                }
                
                // حذف البيانات التجريبية
                $pdo->prepare("DELETE FROM influencers_content WHERE id = ?")->execute([$insertedId]);
                echo "✓ تم حذف البيانات التجريبية\n";
                
            } else {
                echo "✗ فشل في تنفيذ الإدراج\n";
                $errorInfo = $stmt->errorInfo();
                echo "معلومات الخطأ: " . json_encode($errorInfo) . "\n";
            }
        } else {
            echo "✗ فشل في تحضير الاستعلام\n";
            $errorInfo = $pdo->errorInfo();
            echo "معلومات الخطأ: " . json_encode($errorInfo) . "\n";
        }
    } catch (Exception $e) {
        echo "✗ خطأ في الإدراج: " . $e->getMessage() . "\n";
        echo "كود الخطأ: " . $e->getCode() . "\n";
    }

} catch (Exception $e) {
    echo "✗ خطأ عام: " . $e->getMessage() . "\n";
}

echo "\n=== انتهاء فحص بنية قاعدة البيانات ===\n";
?>
