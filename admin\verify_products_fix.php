<?php
require_once '../config/config.php';
requireAdminLogin();

echo "<h2>التحقق من إصلاح مشاكل المنتجات</h2>";

// Check if we have any existing products to test with
$existingProducts = fetchAll("SELECT id, name, status FROM products LIMIT 5");

if (empty($existingProducts)) {
    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
    echo "<h4>⚠️ لا توجد منتجات للاختبار</h4>";
    echo "<p>سيتم إنشاء منتج تجريبي للاختبار...</p>";
    echo "</div>";
    
    // Create a test product
    $testProduct = [
        'name' => 'منتج اختبار التحقق',
        'description' => 'منتج مؤقت للتحقق من الإصلاحات',
        'price' => 10.00,
        'stock' => 5,
        'status' => 'active'
    ];
    
    $testId = insertData('products', $testProduct);
    if ($testId) {
        $existingProducts = [['id' => $testId, 'name' => 'منتج اختبار التحقق', 'status' => 'active']];
        echo "<p style='color: green;'>✅ تم إنشاء منتج تجريبي (ID: $testId)</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء منتج تجريبي</p>";
        exit;
    }
}

echo "<h3>المنتجات المتاحة للاختبار:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
echo "<tr><th>ID</th><th>الاسم</th><th>الحالة</th><th>إجراءات الاختبار</th></tr>";

foreach ($existingProducts as $product) {
    echo "<tr>";
    echo "<td>" . $product['id'] . "</td>";
    echo "<td>" . htmlspecialchars($product['name']) . "</td>";
    echo "<td>" . $product['status'] . "</td>";
    echo "<td>";
    echo "<a href='?test_toggle=" . $product['id'] . "' style='margin-right: 10px;'>اختبار تبديل الحالة</a>";
    echo "<a href='?test_update=" . $product['id'] . "' style='margin-right: 10px;'>اختبار التحديث</a>";
    echo "</td>";
    echo "</tr>";
}
echo "</table>";

// Handle test actions
if (isset($_GET['test_toggle']) && is_numeric($_GET['test_toggle'])) {
    $productId = (int)$_GET['test_toggle'];
    echo "<h3>اختبار تبديل الحالة للمنتج ID: $productId</h3>";
    
    $product = fetchOne("SELECT status FROM products WHERE id = ?", [$productId]);
    if ($product) {
        $oldStatus = $product['status'];
        $newStatus = $product['status'] == 'active' ? 'inactive' : 'active';
        
        echo "<p><strong>الحالة الحالية:</strong> $oldStatus</p>";
        echo "<p><strong>الحالة الجديدة:</strong> $newStatus</p>";
        
        $result = updateData('products', ['status' => $newStatus], 'id = ?', [$productId]);
        
        if ($result !== false && $result > 0) {
            echo "<p style='color: green;'>✅ تم تبديل الحالة بنجاح! (صفوف محدثة: $result)</p>";
            
            // Verify the change
            $updatedProduct = fetchOne("SELECT status FROM products WHERE id = ?", [$productId]);
            if ($updatedProduct && $updatedProduct['status'] == $newStatus) {
                echo "<p style='color: green;'>✅ تم التحقق من التغيير بنجاح</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في التحقق من التغيير</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ فشل في تبديل الحالة (النتيجة: " . var_export($result, true) . ")</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ المنتج غير موجود</p>";
    }
    
    echo "<p><a href='verify_products_fix.php'>العودة للاختبارات</a></p>";
}

if (isset($_GET['test_update']) && is_numeric($_GET['test_update'])) {
    $productId = (int)$_GET['test_update'];
    echo "<h3>اختبار تحديث المنتج ID: $productId</h3>";
    
    $product = fetchOne("SELECT name, stock FROM products WHERE id = ?", [$productId]);
    if ($product) {
        $oldName = $product['name'];
        $oldStock = $product['stock'];
        $newName = $oldName . ' (محدث ' . date('H:i:s') . ')';
        $newStock = $oldStock + 1;
        
        echo "<p><strong>الاسم الحالي:</strong> $oldName</p>";
        echo "<p><strong>الاسم الجديد:</strong> $newName</p>";
        echo "<p><strong>المخزون الحالي:</strong> $oldStock</p>";
        echo "<p><strong>المخزون الجديد:</strong> $newStock</p>";
        
        $updateData = [
            'name' => $newName,
            'stock' => $newStock
        ];
        
        $result = updateData('products', $updateData, 'id = ?', [$productId]);
        
        if ($result !== false && $result > 0) {
            echo "<p style='color: green;'>✅ تم تحديث المنتج بنجاح! (صفوف محدثة: $result)</p>";
            
            // Verify the change
            $updatedProduct = fetchOne("SELECT name, stock FROM products WHERE id = ?", [$productId]);
            if ($updatedProduct && $updatedProduct['name'] == $newName && $updatedProduct['stock'] == $newStock) {
                echo "<p style='color: green;'>✅ تم التحقق من التحديث بنجاح</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في التحقق من التحديث</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ فشل في تحديث المنتج (النتيجة: " . var_export($result, true) . ")</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ المنتج غير موجود</p>";
    }
    
    echo "<p><a href='verify_products_fix.php'>العودة للاختبارات</a></p>";
}

// Test delete functionality
if (isset($_GET['test_delete'])) {
    echo "<h3>اختبار وظيفة الحذف</h3>";
    
    // Create a temporary product for deletion test
    $tempProduct = [
        'name' => 'منتج مؤقت للحذف',
        'description' => 'سيتم حذف هذا المنتج',
        'price' => 1.00,
        'stock' => 1,
        'status' => 'inactive'
    ];
    
    $tempId = insertData('products', $tempProduct);
    if ($tempId) {
        echo "<p>تم إنشاء منتج مؤقت للحذف (ID: $tempId)</p>";
        
        $deleteResult = deleteData('products', 'id = ?', [$tempId]);
        
        if ($deleteResult !== false && $deleteResult > 0) {
            echo "<p style='color: green;'>✅ تم حذف المنتج بنجاح! (صفوف محذوفة: $deleteResult)</p>";
            
            // Verify deletion
            $deletedProduct = fetchOne("SELECT id FROM products WHERE id = ?", [$tempId]);
            if (!$deletedProduct) {
                echo "<p style='color: green;'>✅ تم التحقق من الحذف بنجاح</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في التحقق من الحذف - المنتج ما زال موجود</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ فشل في حذف المنتج (النتيجة: " . var_export($deleteResult, true) . ")</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء منتج مؤقت للحذف</p>";
    }
    
    echo "<p><a href='verify_products_fix.php'>العودة للاختبارات</a></p>";
}

// Show test options if no specific test is running
if (!isset($_GET['test_toggle']) && !isset($_GET['test_update']) && !isset($_GET['test_delete'])) {
    echo "<h3>اختبارات إضافية:</h3>";
    echo "<p><a href='?test_delete=1' class='btn btn-warning'>اختبار وظيفة الحذف</a></p>";
    
    echo "<hr>";
    echo "<h3>الخطوات التالية:</h3>";
    echo "<div style='background: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px;'>";
    echo "<h4>للتأكد من أن كل شيء يعمل:</h4>";
    echo "<ol>";
    echo "<li>اختبر تبديل حالة المنتجات باستخدام الروابط أعلاه</li>";
    echo "<li>اختبر تحديث المنتجات</li>";
    echo "<li>اختبر حذف المنتجات</li>";
    echo "<li>انتقل إلى <a href='products.php'>إدارة المنتجات</a> واختبر الوظائف هناك</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<hr>";
    echo "<p><strong>أدوات إضافية:</strong></p>";
    echo "<p><a href='test_fixed_functions.php'>تشغيل الاختبار الشامل</a></p>";
    echo "<p><a href='products.php'>العودة إلى إدارة المنتجات</a></p>";
}
?>
