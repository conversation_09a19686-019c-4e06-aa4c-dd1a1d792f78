<?php
/**
 * Setup script to create orders tables if they don't exist
 */

require_once 'config/config.php';

echo "<h2>Setting up Orders Tables</h2>";

if (!$pdo) {
    die("❌ Database connection failed");
}

try {
    // Read and execute SQL file
    $sql = file_get_contents('create_orders_tables.sql');
    
    if (!$sql) {
        die("❌ Could not read SQL file");
    }
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                echo "✅ Executed: " . substr($statement, 0, 50) . "...<br>";
            } catch (PDOException $e) {
                echo "⚠️ Warning: " . $e->getMessage() . "<br>";
            }
        }
    }
    
    echo "<h3>Verification</h3>";
    
    // Check if tables exist
    $tables = ['orders', 'order_items', 'delivery_pricing'];
    foreach ($tables as $table) {
        $result = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($result->rowCount() > 0) {
            echo "✅ Table '$table' exists<br>";
            
            // Show table structure
            $structure = $pdo->query("DESCRIBE $table");
            echo "<details><summary>Show $table structure</summary>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            while ($row = $structure->fetch()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . $row['Default'] . "</td>";
                echo "</tr>";
            }
            echo "</table></details>";
        } else {
            echo "❌ Table '$table' does not exist<br>";
        }
    }
    
    // Test insert functionality
    echo "<h3>Testing Insert Functionality</h3>";
    
    $testData = [
        'customer_name' => 'Test Customer',
        'customer_phone' => '07123456789',
        'address' => 'Test Address',
        'province' => 'بغداد',
        'subtotal' => 100.00,
        'delivery_price' => 5000.00,
        'discount_amount' => 0.00,
        'total_price' => 105000.00,
        'payment_method' => 'cash_on_delivery',
        'status' => 'pending'
    ];
    
    $testOrderId = insertData('orders', $testData);
    if ($testOrderId) {
        echo "✅ Test order created with ID: $testOrderId<br>";
        
        // Test order item insert
        $testItemData = [
            'order_id' => $testOrderId,
            'product_id' => 1,
            'product_name' => 'Test Product',
            'quantity' => 2,
            'price' => 50.00,
            'total' => 100.00
        ];
        
        $testItemId = insertData('order_items', $testItemData);
        if ($testItemId) {
            echo "✅ Test order item created with ID: $testItemId<br>";
        } else {
            echo "❌ Failed to create test order item<br>";
        }
        
        // Clean up test data
        $pdo->prepare("DELETE FROM order_items WHERE order_id = ?")->execute([$testOrderId]);
        $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$testOrderId]);
        echo "✅ Test data cleaned up<br>";
        
    } else {
        echo "❌ Failed to create test order<br>";
    }
    
    echo "<h3>Setup Complete!</h3>";
    echo "<p>You can now test the checkout functionality.</p>";
    echo "<p><a href='checkout.php'>Go to Checkout</a> | <a href='debug_checkout.php'>Run Debug</a></p>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
