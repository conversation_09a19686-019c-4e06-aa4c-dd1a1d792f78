<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'اختبار حذف POST فقط';

echo "<h2>اختبار وظيفة الحذف بطريقة POST فقط</h2>";

// Create test product if requested
if (isset($_POST['create_test'])) {
    $testProduct = [
        'name' => 'منتج اختبار POST ' . date('H:i:s'),
        'description' => 'منتج مؤقت لاختبار حذف POST',
        'price' => 1.00,
        'stock' => 1,
        'status' => 'inactive'
    ];
    
    $testId = insertData('products', $testProduct);
    if ($testId) {
        echo "<div class='alert alert-success'>تم إنشاء منتج اختبار (ID: $testId)</div>";
    } else {
        echo "<div class='alert alert-danger'>فشل في إنشاء منتج اختبار</div>";
    }
}

// Handle POST delete (same logic as products.php)
if (isset($_POST['delete_product']) && is_numeric($_POST['delete_product'])) {
    $productId = (int)$_POST['delete_product'];
    
    echo "<h3>اختبار حذف المنتج ID: $productId</h3>";
    
    $product = fetchOne("SELECT name FROM products WHERE id = ?", [$productId]);
    
    if ($product) {
        $productName = $product['name'];
        
        try {
            // Same delete logic as products.php
            $images = fetchAll("SELECT image_path FROM product_images WHERE product_id = ?", [$productId]);
            
            foreach ($images as $image) {
                $imagePath = UPLOAD_PATH . '/products/' . $image['image_path'];
                if (file_exists($imagePath)) {
                    unlink($imagePath);
                }
            }
            
            $deleteImages = deleteData('product_images', 'product_id = ?', [$productId]);
            $deleteProduct = deleteData('products', 'id = ?', [$productId]);
            
            if ($deleteProduct !== false && $deleteProduct > 0) {
                echo "<div class='alert alert-success'>";
                echo "<h4>✅ نجح الحذف بطريقة POST!</h4>";
                echo "<p>تم حذف المنتج '" . htmlspecialchars($productName) . "' بنجاح.</p>";
                echo "<p><strong>تفاصيل:</strong></p>";
                echo "<ul>";
                echo "<li>صفوف محذوفة من products: $deleteProduct</li>";
                echo "<li>صفوف محذوفة من product_images: " . var_export($deleteImages, true) . "</li>";
                echo "<li>عدد الصور المحذوفة: " . count($images) . "</li>";
                echo "</ul>";
                echo "</div>";
            } else {
                $checkProduct = fetchOne("SELECT id FROM products WHERE id = ?", [$productId]);
                if (!$checkProduct) {
                    echo "<div class='alert alert-success'>";
                    echo "<h4>✅ نجح الحذف (تأكيد إضافي)!</h4>";
                    echo "<p>المنتج لم يعد موجود في قاعدة البيانات.</p>";
                    echo "</div>";
                } else {
                    echo "<div class='alert alert-danger'>";
                    echo "<h4>❌ فشل الحذف!</h4>";
                    echo "<p>المنتج ما زال موجود في قاعدة البيانات.</p>";
                    echo "<p>نتيجة deleteData: " . var_export($deleteProduct, true) . "</p>";
                    echo "</div>";
                }
            }
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h4>❌ خطأ في الحذف!</h4>";
            echo "<p>رسالة الخطأ: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    } else {
        echo "<div class='alert alert-warning'>المنتج غير موجود</div>";
    }
}

// Show available products
echo "<h3>المنتجات المتاحة للاختبار:</h3>";

$products = fetchAll("SELECT id, name, status, created_at FROM products ORDER BY id DESC LIMIT 10");

if (!empty($products)) {
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped table-bordered'>";
    echo "<thead class='table-dark'>";
    echo "<tr>";
    echo "<th>ID</th>";
    echo "<th>اسم المنتج</th>";
    echo "<th>الحالة</th>";
    echo "<th>تاريخ الإنشاء</th>";
    echo "<th>حذف POST</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($products as $prod) {
        echo "<tr>";
        echo "<td>" . $prod['id'] . "</td>";
        echo "<td>" . htmlspecialchars($prod['name']) . "</td>";
        echo "<td><span class='badge bg-" . ($prod['status'] == 'active' ? 'success' : 'secondary') . "'>" . $prod['status'] . "</span></td>";
        echo "<td>" . $prod['created_at'] . "</td>";
        echo "<td>";
        echo "<form method='POST' style='display: inline;' onsubmit='return confirm(\"هل أنت متأكد من حذف المنتج \\\"" . htmlspecialchars($prod['name'], ENT_QUOTES) . "\\\"؟\\n\\nهذا الإجراء لا يمكن التراجع عنه!\")'>";
        echo "<input type='hidden' name='delete_product' value='" . $prod['id'] . "'>";
        echo "<button type='submit' class='btn btn-sm btn-danger'>";
        echo "<i class='bi bi-trash'></i> حذف";
        echo "</button>";
        echo "</form>";
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
} else {
    echo "<div class='alert alert-info'>لا توجد منتجات في قاعدة البيانات</div>";
}

// Create test product form
echo "<hr>";
echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h4>إنشاء منتج اختبار</h4>";
echo "</div>";
echo "<div class='card-body'>";
echo "<form method='POST'>";
echo "<p>انقر على الزر أدناه لإنشاء منتج اختبار جديد:</p>";
echo "<button type='submit' name='create_test' class='btn btn-success'>";
echo "<i class='bi bi-plus-circle'></i> إنشاء منتج اختبار";
echo "</button>";
echo "</form>";
echo "</div>";
echo "</div>";

echo "<hr>";
echo "<div class='alert alert-info'>";
echo "<h5><i class='bi bi-info-circle'></i> معلومات الاختبار:</h5>";
echo "<ul>";
echo "<li><strong>الهدف:</strong> التأكد من أن حذف POST يعمل بشكل صحيح بعد إزالة طريقة GET</li>";
echo "<li><strong>الطريقة:</strong> استخدام نموذج POST مع تأكيد JavaScript</li>";
echo "<li><strong>النتيجة المتوقعة:</strong> حذف المنتج من قاعدة البيانات مع رسالة نجاح</li>";
echo "<li><strong>التحقق:</strong> المنتج يختفي من القائمة ولا يعود موجود في قاعدة البيانات</li>";
echo "</ul>";
echo "</div>";

echo "<div class='alert alert-success'>";
echo "<h5><i class='bi bi-check-circle'></i> التحسينات المطبقة:</h5>";
echo "<ul>";
echo "<li>✅ تم إزالة زر الحذف GET (الأزرق)</li>";
echo "<li>✅ تم إزالة دالة confirmDelete() JavaScript</li>";
echo "<li>✅ تم إزالة معالج GET من PHP</li>";
echo "<li>✅ تم إزالة Modal الحذف</li>";
echo "<li>✅ تم تحسين زر POST ليصبح الوحيد</li>";
echo "</ul>";
echo "</div>";

echo "<div class='mt-4'>";
echo "<a href='products.php' class='btn btn-primary btn-lg'>";
echo "<i class='bi bi-box-seam'></i> اختبار products.php الآن";
echo "</a>";
echo "</div>";

// Add some CSS for better styling
echo "<style>";
echo ".table th, .table td { vertical-align: middle; }";
echo ".btn-sm { margin: 2px; }";
echo ".alert { margin: 15px 0; }";
echo ".card { margin: 15px 0; }";
echo "</style>";
?>
