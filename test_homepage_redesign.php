<?php
/**
 * Homepage Redesign Test Script
 * Tests all homepage functionality and admin integration
 */

require_once 'config/config.php';

$testResults = [];
$errors = [];

// Test 1: Database Tables and Settings
echo "<h2>🔍 Testing Database Structure</h2>";

try {
    // Check homepage_settings table
    $settingsTable = fetchOne("SHOW TABLES LIKE 'homepage_settings'");
    if ($settingsTable) {
        $testResults['database']['homepage_settings_table'] = '✅ Homepage settings table exists';
        
        // Check settings data
        $settingsCount = fetchOne("SELECT COUNT(*) as count FROM homepage_settings")['count'];
        $testResults['database']['settings_data'] = "✅ Homepage settings data: {$settingsCount} records";
    } else {
        $errors[] = '❌ Homepage settings table missing';
    }
    
    // Check helper functions
    if (function_exists('getHomepageSettings')) {
        $testResults['functions']['homepage_functions'] = '✅ Homepage helper functions available';
    } else {
        $errors[] = '❌ Homepage helper functions missing';
    }
    
} catch (Exception $e) {
    $errors[] = '❌ Database error: ' . $e->getMessage();
}

// Test 2: Admin Interface
echo "<h2>🔧 Testing Admin Interface</h2>";

try {
    if (file_exists('admin/homepage_settings.php')) {
        $testResults['admin']['settings_page'] = '✅ Admin homepage settings page exists';
    } else {
        $errors[] = '❌ Admin homepage settings page missing';
    }
    
    // Check dashboard link
    $dashboardContent = file_get_contents('admin/dashboard.php');
    if (strpos($dashboardContent, 'homepage_settings.php') !== false) {
        $testResults['admin']['dashboard_link'] = '✅ Dashboard link to homepage settings exists';
    } else {
        $errors[] = '❌ Dashboard link to homepage settings missing';
    }
    
} catch (Exception $e) {
    $errors[] = '❌ Admin interface error: ' . $e->getMessage();
}

// Test 3: Homepage Files and Structure
echo "<h2>🏠 Testing Homepage Structure</h2>";

$requiredFiles = [
    'index.php' => 'Main homepage file',
    'includes/homepage_carousel.php' => 'Carousel component',
    'assets/css/homepage.css' => 'Homepage styles',
    'ajax/cart.php' => 'Cart AJAX handler',
    'ajax/newsletter.php' => 'Newsletter AJAX handler'
];

foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        $testResults['files'][$file] = "✅ {$description}";
    } else {
        $errors[] = "❌ Missing: {$description} ({$file})";
    }
}

// Test 4: Homepage Settings Retrieval
echo "<h2>⚙️ Testing Settings Retrieval</h2>";

try {
    $sections = [
        'carousel' => 'Carousel settings',
        'featured_products' => 'Featured products settings',
        'offers' => 'Offers settings',
        'influencers' => 'Influencers settings',
        'customer_reviews' => 'Customer reviews settings',
        'success_story' => 'Success story settings',
        'why_choose_us' => 'Why choose us settings',
        'newsletter' => 'Newsletter settings',
        'call_to_action' => 'Call to action settings'
    ];
    
    foreach ($sections as $section => $description) {
        $settings = getHomepageSectionSettings($section);
        if (!empty($settings)) {
            $testResults['settings'][$section] = "✅ {$description}: " . count($settings) . " settings";
        } else {
            $testResults['settings'][$section] = "⚠️ {$description}: No settings found (using defaults)";
        }
    }
    
} catch (Exception $e) {
    $errors[] = '❌ Settings retrieval error: ' . $e->getMessage();
}

// Test 5: Data Integration
echo "<h2>📊 Testing Data Integration</h2>";

try {
    // Test featured products
    $featuredProducts = fetchAll("
        SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.is_featured = 1 AND p.status = 'active' 
        LIMIT 6
    ");
    $testResults['data']['featured_products'] = "✅ Featured products: " . count($featuredProducts) . " found";
    
    // Test discounted products
    $discountedProducts = fetchAll("
        SELECT COUNT(*) as count FROM products 
        WHERE discount > 0 AND status = 'active'
    ");
    $testResults['data']['discounted_products'] = "✅ Discounted products: " . $discountedProducts[0]['count'] . " found";
    
    // Test reviews
    $reviews = fetchAll("
        SELECT COUNT(*) as count FROM reviews 
        WHERE status = 'approved'
    ");
    $testResults['data']['reviews'] = "✅ Approved reviews: " . $reviews[0]['count'] . " found";
    
} catch (Exception $e) {
    $errors[] = '❌ Data integration error: ' . $e->getMessage();
}

// Test 6: Header Navigation
echo "<h2>🧭 Testing Header Navigation</h2>";

try {
    $headerContent = file_get_contents('includes/header.php');
    
    $navigationItems = [
        'الرئيسية' => 'Home',
        'المنتجات' => 'Products',
        'العروض' => 'Offers',
        'الإرشادات' => 'Guidelines',
        'المؤثرين' => 'Influencers',
        'اتصل بنا' => 'Contact Us',
        'سلة التسوق' => 'Shopping Cart',
        'البحث' => 'Search'
    ];
    
    $foundItems = 0;
    foreach ($navigationItems as $arabic => $english) {
        if (strpos($headerContent, $arabic) !== false) {
            $foundItems++;
        }
    }
    
    $testResults['navigation']['header_items'] = "✅ Navigation items: {$foundItems}/" . count($navigationItems) . " found";
    
    // Check for homepage CSS inclusion
    if (strpos($headerContent, 'homepage.css') !== false) {
        $testResults['navigation']['homepage_css'] = '✅ Homepage CSS included in header';
    } else {
        $errors[] = '❌ Homepage CSS not included in header';
    }
    
} catch (Exception $e) {
    $errors[] = '❌ Header navigation error: ' . $e->getMessage();
}

// Test 7: Responsive Design Elements
echo "<h2>📱 Testing Responsive Design</h2>";

$cssContent = file_get_contents('assets/css/homepage.css');
$responsiveFeatures = [
    '@media (max-width: 768px)' => 'Tablet responsive styles',
    '@media (max-width: 576px)' => 'Mobile responsive styles',
    'carousel-image' => 'Carousel responsive images',
    'product-card' => 'Product card styles',
    'btn' => 'Button styles'
];

foreach ($responsiveFeatures as $feature => $description) {
    if (strpos($cssContent, $feature) !== false) {
        $testResults['responsive'][$feature] = "✅ {$description}";
    } else {
        $errors[] = "❌ Missing responsive feature: {$description}";
    }
}

// Display Results
echo "<h1>🧪 Homepage Redesign Test Results</h1>";

if (empty($errors)) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>✅ All Tests Passed!</h2>";
    echo "<p>The homepage redesign has been successfully implemented with all required features.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h2>❌ Some Issues Found</h2>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>{$error}</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<h2>📋 Detailed Test Results</h2>";
echo "<div style='font-family: monospace; background: #f8f9fa; padding: 20px; border-radius: 5px;'>";

foreach ($testResults as $category => $tests) {
    echo "<h3>" . ucfirst($category) . "</h3>";
    foreach ($tests as $test => $result) {
        echo "<div style='margin: 5px 0;'>{$result}</div>";
    }
    echo "<br>";
}

echo "</div>";

// Performance and Recommendations
echo "<h2>🚀 Performance & Recommendations</h2>";
echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";

$recommendations = [
    "✅ Modern Arabic RTL design implemented",
    "✅ Professional carousel with admin control",
    "✅ 8 content sections as requested",
    "✅ Mobile-first responsive design",
    "✅ Iraqi Dinar currency formatting",
    "✅ Admin panel integration complete",
    "🔧 Consider adding image optimization for better performance",
    "🔧 Consider implementing lazy loading for images",
    "🔧 Consider adding SEO meta tags for each section"
];

foreach ($recommendations as $rec) {
    echo "<div style='margin: 5px 0;'>{$rec}</div>";
}

echo "</div>";

echo "<h2>🎯 Next Steps</h2>";
echo "<ol>";
echo "<li>Visit the <a href='admin/homepage_settings.php'>Admin Homepage Settings</a> to customize content</li>";
echo "<li>Upload carousel images and configure settings</li>";
echo "<li>Test the homepage on different devices</li>";
echo "<li>Configure newsletter subscription settings</li>";
echo "<li>Add real product data and reviews</li>";
echo "</ol>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='index.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>🏠 View New Homepage</a>";
echo " ";
echo "<a href='admin/homepage_settings.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>⚙️ Manage Settings</a>";
echo "</div>";
?>
