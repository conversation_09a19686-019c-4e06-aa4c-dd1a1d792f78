/**
 * Professional Hero Carousel JavaScript
 * Modern, Clean, and Error-Free Implementation
 * 
 * @version 2.0
 * <AUTHOR> Development Team
 */

class HeroCarousel {
    constructor(element) {
        this.carousel = element;
        this.slides = this.carousel.querySelectorAll('.carousel-slide');
        this.indicators = this.carousel.querySelectorAll('.indicator');
        this.prevBtn = this.carousel.querySelector('.carousel-btn-prev');
        this.nextBtn = this.carousel.querySelector('.carousel-btn-next');
        
        this.currentSlide = 0;
        this.totalSlides = this.slides.length;
        this.autoAdvanceTime = parseInt(this.carousel.dataset.autoAdvance) || 6000;
        this.isPlaying = true;
        this.autoAdvanceTimer = null;
        
        // Touch/swipe properties
        this.touchStartX = 0;
        this.touchEndX = 0;
        this.touchThreshold = 50;
        
        this.init();
    }
    
    init() {
        if (this.totalSlides <= 1) return;
        
        this.setupEventListeners();
        this.startAutoAdvance();
        this.initializeAnimations();
        
        // Initialize accessibility
        this.updateAriaAttributes();
        
        console.log('Hero Carousel initialized with', this.totalSlides, 'slides');
    }
    
    setupEventListeners() {
        // Navigation buttons
        if (this.prevBtn) {
            this.prevBtn.addEventListener('click', () => this.previousSlide());
        }
        
        if (this.nextBtn) {
            this.nextBtn.addEventListener('click', () => this.nextSlide());
        }
        
        // Indicators
        this.indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => this.goToSlide(index));
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
        
        // Touch/swipe events
        this.carousel.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: true });
        this.carousel.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: true });
        
        // Mouse events for auto-advance control
        this.carousel.addEventListener('mouseenter', () => this.pauseAutoAdvance());
        this.carousel.addEventListener('mouseleave', () => this.resumeAutoAdvance());
        
        // Focus events for accessibility
        this.carousel.addEventListener('focusin', () => this.pauseAutoAdvance());
        this.carousel.addEventListener('focusout', () => this.resumeAutoAdvance());
        
        // Visibility change (pause when tab is not active)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAutoAdvance();
            } else {
                this.resumeAutoAdvance();
            }
        });
    }
    
    nextSlide() {
        const nextIndex = (this.currentSlide + 1) % this.totalSlides;
        this.goToSlide(nextIndex);
    }
    
    previousSlide() {
        const prevIndex = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
        this.goToSlide(prevIndex);
    }
    
    goToSlide(index) {
        if (index === this.currentSlide || index < 0 || index >= this.totalSlides) return;
        
        // Remove active class from current slide and indicator
        this.slides[this.currentSlide].classList.remove('active');
        if (this.indicators[this.currentSlide]) {
            this.indicators[this.currentSlide].classList.remove('active');
            this.indicators[this.currentSlide].setAttribute('aria-selected', 'false');
        }
        
        // Add active class to new slide and indicator
        this.slides[index].classList.add('active');
        if (this.indicators[index]) {
            this.indicators[index].classList.add('active');
            this.indicators[index].setAttribute('aria-selected', 'true');
        }
        
        this.currentSlide = index;
        this.updateAriaAttributes();
        
        // Restart auto-advance timer
        this.restartAutoAdvance();
    }
    
    startAutoAdvance() {
        if (this.totalSlides <= 1) return;
        
        this.autoAdvanceTimer = setInterval(() => {
            if (this.isPlaying) {
                this.nextSlide();
            }
        }, this.autoAdvanceTime);
    }
    
    pauseAutoAdvance() {
        this.isPlaying = false;
    }
    
    resumeAutoAdvance() {
        this.isPlaying = true;
    }
    
    restartAutoAdvance() {
        if (this.autoAdvanceTimer) {
            clearInterval(this.autoAdvanceTimer);
        }
        this.startAutoAdvance();
    }
    
    handleKeyboard(e) {
        if (!this.carousel.contains(document.activeElement)) return;
        
        switch (e.key) {
            case 'ArrowLeft':
                e.preventDefault();
                this.nextSlide(); // RTL: left arrow goes to next
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.previousSlide(); // RTL: right arrow goes to previous
                break;
            case 'Home':
                e.preventDefault();
                this.goToSlide(0);
                break;
            case 'End':
                e.preventDefault();
                this.goToSlide(this.totalSlides - 1);
                break;
        }
    }
    
    handleTouchStart(e) {
        this.touchStartX = e.touches[0].clientX;
    }
    
    handleTouchEnd(e) {
        this.touchEndX = e.changedTouches[0].clientX;
        this.handleSwipe();
    }
    
    handleSwipe() {
        const swipeDistance = this.touchStartX - this.touchEndX;
        
        if (Math.abs(swipeDistance) > this.touchThreshold) {
            if (swipeDistance > 0) {
                // Swipe left - next slide (RTL)
                this.nextSlide();
            } else {
                // Swipe right - previous slide (RTL)
                this.previousSlide();
            }
        }
    }
    
    updateAriaAttributes() {
        // Update carousel aria-label
        this.carousel.setAttribute('aria-label', `شريحة ${this.currentSlide + 1} من ${this.totalSlides}`);
        
        // Update slide aria-labels
        this.slides.forEach((slide, index) => {
            slide.setAttribute('aria-hidden', index !== this.currentSlide);
        });
    }
    
    initializeAnimations() {
        // Initialize animations for default hero section
        const defaultHero = document.querySelector('.hero-default');
        if (defaultHero) {
            setTimeout(() => {
                const animatedElements = defaultHero.querySelectorAll('[data-animation]');
                animatedElements.forEach(el => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                });
            }, 100);
        }
        
        // Handle image loading
        this.slides.forEach((slide, index) => {
            const img = slide.querySelector('.slide-image');
            const loading = slide.querySelector('.slide-loading');
            
            if (img && loading) {
                img.addEventListener('load', () => {
                    loading.style.opacity = '0';
                    setTimeout(() => {
                        loading.style.display = 'none';
                    }, 300);
                });
                
                img.addEventListener('error', () => {
                    loading.innerHTML = `
                        <div style="text-align: center; color: #6c757d;">
                            <i class="bi bi-image" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                            <div>فشل في تحميل الصورة</div>
                        </div>
                    `;
                    loading.style.background = '#f8f9fa';
                    slide.classList.add('image-error');
                });
            }
        });
    }
    
    destroy() {
        if (this.autoAdvanceTimer) {
            clearInterval(this.autoAdvanceTimer);
        }
        
        // Remove event listeners
        document.removeEventListener('keydown', this.handleKeyboard);
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        
        console.log('Hero Carousel destroyed');
    }
}

// Initialize carousel when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    const heroCarousel = document.getElementById('heroCarousel');
    
    if (heroCarousel) {
        window.heroCarouselInstance = new HeroCarousel(heroCarousel);
    }
    
    // Initialize default hero animations
    const defaultHero = document.querySelector('.hero-default');
    if (defaultHero) {
        setTimeout(() => {
            const animatedElements = defaultHero.querySelectorAll('[data-animation]');
            animatedElements.forEach((el, index) => {
                setTimeout(() => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 200);
            });
        }, 300);
    }
});

// Handle page unload
window.addEventListener('beforeunload', function() {
    if (window.heroCarouselInstance) {
        window.heroCarouselInstance.destroy();
    }
});
