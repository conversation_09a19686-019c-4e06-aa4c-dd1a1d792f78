<?php
/**
 * Trace checkout submission process step by step
 */

require_once 'config/config.php';

echo "<h1>🔍 Checkout Submission Tracer</h1>";
echo "<p>This tool will trace exactly what happens during checkout form submission.</p>";

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['trace_checkout'])) {
    echo "<h2>📝 Tracing Checkout Process</h2>";
    
    // Step 1: Simulate cart setup
    if (!isset($_SESSION)) {
        session_start();
    }
    
    // Create test cart if empty
    if (empty($_SESSION['cart'])) {
        $_SESSION['cart'] = [1 => 2]; // Product 1, quantity 2
        echo "✅ Test cart created<br>";
    }
    
    // Step 2: Get cart data (simulate checkout.php logic)
    $cart = getCart();
    echo "<strong>Step 1 - Cart Data:</strong><br>";
    echo "Cart contents: " . json_encode($cart, JSON_UNESCAPED_UNICODE) . "<br>";
    
    if (empty($cart)) {
        echo "❌ Cart is empty - checkout would redirect<br>";
        exit;
    }
    
    // Step 3: Get product details (simulate checkout.php logic)
    $productIds = array_keys($cart);
    $placeholders = str_repeat('?,', count($productIds) - 1) . '?';
    
    $products = fetchAll("
        SELECT id, name, price, discount, image, stock 
        FROM products 
        WHERE id IN ($placeholders) AND status = 'active'
    ", $productIds);
    
    echo "<strong>Step 2 - Product Data:</strong><br>";
    echo "Products found: " . count($products) . "<br>";
    
    if (empty($products)) {
        echo "❌ No products found - this would cause checkout to fail<br>";
        exit;
    }
    
    // Step 4: Calculate cart items and totals
    $cartItems = [];
    $subtotal = 0;
    
    foreach ($products as $product) {
        $quantity = $cart[$product['id']];
        $price = $product['price'];
        
        // Apply discount if exists
        if ($product['discount'] > 0) {
            $price = $price - ($price * $product['discount'] / 100);
        }
        
        $total = $price * $quantity;
        $subtotal += $total;
        
        $cartItems[] = [
            'product' => $product,
            'quantity' => $quantity,
            'price' => $price,
            'total' => $total
        ];
    }
    
    echo "<strong>Step 3 - Cart Calculations:</strong><br>";
    echo "Cart items: " . count($cartItems) . "<br>";
    echo "Subtotal: " . number_format($subtotal) . " IQD<br>";
    
    // Step 5: Process form data
    $customerName = sanitizeInput($_POST['customer_name']);
    $customerPhone = sanitizeInput($_POST['customer_phone']);
    $address = sanitizeInput($_POST['address']);
    $province = sanitizeInput($_POST['province']);
    $paymentMethod = sanitizeInput($_POST['payment_method']);
    $notes = sanitizeInput($_POST['notes']);
    
    echo "<strong>Step 4 - Form Data Processing:</strong><br>";
    echo "Customer: $customerName<br>";
    echo "Phone: $customerPhone<br>";
    echo "Province: $province<br>";
    
    // Step 6: Validation
    $errors = [];
    if (empty($customerName)) $errors[] = 'الاسم مطلوب';
    if (empty($customerPhone)) $errors[] = 'رقم الهاتف مطلوب';
    if (empty($address)) $errors[] = 'العنوان مطلوب';
    if (empty($province)) $errors[] = 'المحافظة مطلوبة';
    
    echo "<strong>Step 5 - Validation:</strong><br>";
    if (empty($errors)) {
        echo "✅ Validation passed<br>";
    } else {
        echo "❌ Validation failed: " . implode(', ', $errors) . "<br>";
        exit;
    }
    
    // Step 7: Calculate delivery and totals
    $deliveryPrice = 5000; // Default for Baghdad
    $discountAmount = 0;
    $finalTotal = $subtotal - $discountAmount + $deliveryPrice;
    
    echo "<strong>Step 6 - Final Calculations:</strong><br>";
    echo "Delivery: " . number_format($deliveryPrice) . " IQD<br>";
    echo "Final Total: " . number_format($finalTotal) . " IQD<br>";
    
    // Step 8: Prepare order data
    $orderData = [
        'customer_name' => $customerName,
        'customer_phone' => $customerPhone,
        'address' => $address,
        'province' => $province,
        'subtotal' => $subtotal,
        'delivery_price' => $deliveryPrice,
        'discount_amount' => $discountAmount,
        'total_price' => $finalTotal,
        'payment_method' => $paymentMethod,
        'notes' => $notes,
        'status' => 'pending'
    ];
    
    echo "<strong>Step 7 - Order Data Preparation:</strong><br>";
    echo "<pre>" . json_encode($orderData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    
    // Step 9: Database transaction
    echo "<strong>Step 8 - Database Transaction:</strong><br>";
    
    try {
        // Begin transaction
        $pdo->beginTransaction();
        echo "✅ Transaction started<br>";
        
        // Insert order
        echo "Attempting order insertion...<br>";
        $orderId = insertData('orders', $orderData);
        
        if ($orderId) {
            echo "✅ Order inserted successfully - ID: $orderId<br>";
            
            // Insert order items
            echo "Inserting order items...<br>";
            $itemsInserted = 0;
            foreach ($cartItems as $item) {
                $orderItemData = [
                    'order_id' => $orderId,
                    'product_id' => $item['product']['id'],
                    'product_name' => $item['product']['name'],
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                    'total' => $item['total']
                ];
                
                $itemId = insertData('order_items', $orderItemData);
                if ($itemId) {
                    $itemsInserted++;
                    echo "✅ Order item $itemsInserted inserted - ID: $itemId<br>";
                } else {
                    echo "❌ Failed to insert order item $itemsInserted<br>";
                    throw new Exception('Failed to insert order item');
                }
            }
            
            // Commit transaction
            $pdo->commit();
            echo "✅ Transaction committed successfully<br>";
            
            // Verify order exists
            $verifyOrder = fetchOne("SELECT * FROM orders WHERE id = ?", [$orderId]);
            if ($verifyOrder) {
                echo "✅ Order verification successful<br>";
                echo "<strong>Order Details:</strong><br>";
                echo "- ID: " . $verifyOrder['id'] . "<br>";
                echo "- Customer: " . $verifyOrder['customer_name'] . "<br>";
                echo "- Total: " . number_format($verifyOrder['total_price']) . " IQD<br>";
                echo "- Status: " . $verifyOrder['status'] . "<br>";
                echo "- Created: " . $verifyOrder['created_at'] . "<br>";
            } else {
                echo "❌ Order verification failed<br>";
            }
            
            // Test cart clearing
            echo "<strong>Step 9 - Cart Clearing:</strong><br>";
            clearCart();
            $clearedCart = getCart();
            if (empty($clearedCart)) {
                echo "✅ Cart cleared successfully<br>";
            } else {
                echo "❌ Cart clearing failed<br>";
            }
            
            // Test admin visibility
            echo "<strong>Step 10 - Admin Visibility:</strong><br>";
            $adminOrders = fetchAll("SELECT * FROM orders WHERE id = ?", [$orderId]);
            if (count($adminOrders) > 0) {
                echo "✅ Order visible in admin queries<br>";
            } else {
                echo "❌ Order not visible in admin queries<br>";
            }
            
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h3>🎉 Checkout Process Successful!</h3>";
            echo "<p>Order ID $orderId has been created and should appear in the admin panel.</p>";
            echo "<p><a href='admin/orders.php' target='_blank'>Check Admin Orders</a></p>";
            echo "<p><a href='order-success.php?order=$orderId' target='_blank'>View Order Success Page</a></p>";
            echo "</div>";
            
            // Clean up test data
            $pdo->prepare("DELETE FROM order_items WHERE order_id = ?")->execute([$orderId]);
            $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$orderId]);
            echo "✅ Test data cleaned up<br>";
            
        } else {
            echo "❌ Order insertion failed<br>";
            $pdo->rollBack();
            echo "Transaction rolled back<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Exception occurred: " . $e->getMessage() . "<br>";
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
            echo "Transaction rolled back<br>";
        }
    }
    
} else {
    // Show the test form
    echo "<h2>🧪 Test Checkout Submission</h2>";
    echo "<p>Use this form to trace the exact checkout process:</p>";
    
    echo '<form method="POST" style="max-width: 600px; margin: 20px 0;">';
    echo '<input type="hidden" name="trace_checkout" value="1">';
    
    echo '<div style="margin-bottom: 15px;">';
    echo '<label>اسم العميل:</label><br>';
    echo '<input type="text" name="customer_name" value="Trace Test Customer" required style="width: 100%; padding: 8px;">';
    echo '</div>';
    
    echo '<div style="margin-bottom: 15px;">';
    echo '<label>رقم الهاتف:</label><br>';
    echo '<input type="text" name="customer_phone" value="07123456789" required style="width: 100%; padding: 8px;">';
    echo '</div>';
    
    echo '<div style="margin-bottom: 15px;">';
    echo '<label>العنوان:</label><br>';
    echo '<textarea name="address" required style="width: 100%; padding: 8px; height: 60px;">Test Address, Baghdad, Iraq</textarea>';
    echo '</div>';
    
    echo '<div style="margin-bottom: 15px;">';
    echo '<label>المحافظة:</label><br>';
    echo '<select name="province" required style="width: 100%; padding: 8px;">';
    echo '<option value="بغداد" selected>بغداد</option>';
    echo '<option value="البصرة">البصرة</option>';
    echo '</select>';
    echo '</div>';
    
    echo '<div style="margin-bottom: 15px;">';
    echo '<label>طريقة الدفع:</label><br>';
    echo '<select name="payment_method" style="width: 100%; padding: 8px;">';
    echo '<option value="cash_on_delivery" selected>الدفع عند الاستلام</option>';
    echo '<option value="bank_transfer">تحويل بنكي</option>';
    echo '</select>';
    echo '</div>';
    
    echo '<div style="margin-bottom: 15px;">';
    echo '<label>ملاحظات:</label><br>';
    echo '<textarea name="notes" style="width: 100%; padding: 8px; height: 60px;">Checkout trace test</textarea>';
    echo '</div>';
    
    echo '<button type="submit" style="background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer;">';
    echo 'تتبع عملية الطلب';
    echo '</button>';
    
    echo '</form>';
}
?>
