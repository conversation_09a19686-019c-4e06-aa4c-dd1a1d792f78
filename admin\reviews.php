<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'إدارة التقييمات';

// معالجة تحديث حالة التقييم
if (isset($_POST['update_status'])) {
    $reviewId = (int)$_POST['review_id'];
    $newStatus = sanitizeInput($_POST['status']);

    $validStatuses = ['pending', 'approved', 'rejected'];

    if (in_array($newStatus, $validStatuses)) {
        // جلب بيانات التقييم للتأكد من وجوده
        $review = fetchOne("SELECT r.*, p.name as product_name FROM reviews r LEFT JOIN products p ON r.product_id = p.id WHERE r.id = ?", [$reviewId]);

        if ($review) {
            $result = updateData('reviews', ['status' => $newStatus], 'id = ?', [$reviewId]);

            if ($result !== false && $result > 0) {
                $statusText = [
                    'approved' => 'تم اعتماد',
                    'rejected' => 'تم رفض',
                    'pending' => 'تم تعيين حالة الانتظار لـ'
                ];
                $_SESSION['success'] = $statusText[$newStatus] . ' تقييم "' . htmlspecialchars($review['customer_name']) . '" للمنتج "' . htmlspecialchars($review['product_name']) . '" بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء تحديث حالة التقييم. يرجى المحاولة مرة أخرى.';
            }
        } else {
            $_SESSION['error'] = 'التقييم المطلوب تحديثه غير موجود.';
        }
    } else {
        $_SESSION['error'] = 'حالة التقييم المحددة غير صحيحة.';
    }

    header('Location: reviews.php');
    exit();
}

// معالجة الحذف
if (isset($_POST['delete_review']) && is_numeric($_POST['delete_review'])) {
    $reviewId = (int)$_POST['delete_review'];

    // جلب بيانات التقييم للتأكد من وجوده
    $review = fetchOne("SELECT r.*, p.name as product_name FROM reviews r LEFT JOIN products p ON r.product_id = p.id WHERE r.id = ?", [$reviewId]);

    if ($review) {
        // حذف التقييم من قاعدة البيانات
        $result = deleteData('reviews', 'id = ?', [$reviewId]);

        if ($result) {
            $_SESSION['success'] = 'تم حذف تقييم "' . htmlspecialchars($review['customer_name']) . '" للمنتج "' . htmlspecialchars($review['product_name']) . '" بنجاح';
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء حذف التقييم. يرجى المحاولة مرة أخرى.';
        }
    } else {
        $_SESSION['error'] = 'التقييم المطلوب حذفه غير موجود.';
    }

    header('Location: reviews.php');
    exit();
}

// الفلترة والبحث
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$status = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
$rating = isset($_GET['rating']) ? (int)$_GET['rating'] : 0;

$whereClause = "1=1";
$params = [];

if (!empty($search)) {
    $whereClause .= " AND (r.customer_name LIKE ? OR r.customer_email LIKE ? OR p.name LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($status)) {
    $whereClause .= " AND r.status = ?";
    $params[] = $status;
}

if ($rating > 0) {
    $whereClause .= " AND r.rating = ?";
    $params[] = $rating;
}

// الترقيم
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 20;
$offset = ($page - 1) * $perPage;

// عدد التقييمات الإجمالي
$countResult = fetchOne("
    SELECT COUNT(*) as total 
    FROM reviews r 
    LEFT JOIN products p ON r.product_id = p.id 
    WHERE $whereClause
", $params);
$totalReviews = ($countResult && isset($countResult['total'])) ? $countResult['total'] : 0;
$totalPages = ceil($totalReviews / $perPage);

// جلب التقييمات
$reviews = fetchAll("
    SELECT r.*, p.name as product_name, p.image as product_image
    FROM reviews r 
    LEFT JOIN products p ON r.product_id = p.id 
    WHERE $whereClause 
    ORDER BY r.created_at DESC 
    LIMIT $perPage OFFSET $offset
", $params);

// إحصائيات سريعة
$stats = [
    'pending' => fetchOne("SELECT COUNT(*) as count FROM reviews WHERE status = 'pending'"),
    'approved' => fetchOne("SELECT COUNT(*) as count FROM reviews WHERE status = 'approved'"),
    'rejected' => fetchOne("SELECT COUNT(*) as count FROM reviews WHERE status = 'rejected'")
];

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card-professional text-center slide-up-professional border-professional-warning">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="bi bi-clock-history text-professional-warning me-2" style="font-size: 1.5rem;"></i>
                        <h4 class="text-professional-warning mb-0"><?php echo ($stats['pending'] && isset($stats['pending']['count'])) ? $stats['pending']['count'] : 0; ?></h4>
                    </div>
                    <small class="text-professional-muted">في الانتظار</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card-professional text-center slide-up-professional border-professional-success">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="bi bi-check-circle text-professional-success me-2" style="font-size: 1.5rem;"></i>
                        <h4 class="text-professional-success mb-0"><?php echo ($stats['approved'] && isset($stats['approved']['count'])) ? $stats['approved']['count'] : 0; ?></h4>
                    </div>
                    <small class="text-professional-muted">معتمدة</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card-professional text-center slide-up-professional border-professional-danger">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="bi bi-x-circle text-professional-danger me-2" style="font-size: 1.5rem;"></i>
                        <h4 class="text-professional-danger mb-0"><?php echo ($stats['rejected'] && isset($stats['rejected']['count'])) ? $stats['rejected']['count'] : 0; ?></h4>
                    </div>
                    <small class="text-professional-muted">مرفوضة</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card-professional fade-in-professional">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-professional-dark">
                            <i class="bi bi-star text-professional-primary"></i> إدارة التقييمات
                        </h5>
                        <span class="badge-professional badge-info">إجمالي التقييمات: <?php echo $totalReviews; ?></span>
                    </div>
                </div>

                <div class="card-body">
                    <!-- رسائل النجاح والخطأ -->
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert-professional alert-success slide-up-professional" role="alert">
                            <i class="bi bi-check-circle-fill alert-icon"></i>
                            <div class="alert-content">
                                <strong>تم بنجاح!</strong><br>
                                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert-professional alert-danger slide-up-professional" role="alert">
                            <i class="bi bi-exclamation-triangle-fill alert-icon"></i>
                            <div class="alert-content">
                                <strong>حدث خطأ!</strong><br>
                                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- فلاتر البحث -->
                    <form method="GET" class="form-professional row g-3 mb-4">
                        <div class="col-md-4">
                            <input type="text" class="form-control focus-professional" name="search"
                                   placeholder="البحث في التقييمات..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-select focus-professional">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo $status == 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                                <option value="approved" <?php echo $status == 'approved' ? 'selected' : ''; ?>>معتمدة</option>
                                <option value="rejected" <?php echo $status == 'rejected' ? 'selected' : ''; ?>>مرفوضة</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="rating" class="form-select focus-professional">
                                <option value="">جميع التقييمات</option>
                                <option value="5" <?php echo $rating == 5 ? 'selected' : ''; ?>>5 نجوم</option>
                                <option value="4" <?php echo $rating == 4 ? 'selected' : ''; ?>>4 نجوم</option>
                                <option value="3" <?php echo $rating == 3 ? 'selected' : ''; ?>>3 نجوم</option>
                                <option value="2" <?php echo $rating == 2 ? 'selected' : ''; ?>>2 نجوم</option>
                                <option value="1" <?php echo $rating == 1 ? 'selected' : ''; ?>>1 نجمة</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn-professional btn-primary w-100 hover-lift-professional">
                                <i class="bi bi-search"></i> بحث
                            </button>
                        </div>
                    </form>
                    
                    <!-- قائمة التقييمات -->
                    <?php if (!empty($reviews)): ?>
                        <div class="row">
                            <?php foreach ($reviews as $review): ?>
                                <div class="col-md-6 mb-4">
                                    <div class="card-professional h-100 slide-up-professional">
                                        <div class="card-body">
                                            <div class="d-flex align-items-start mb-3">
                                                <?php if ($review['product_image']): ?>
                                                    <img src="<?php echo UPLOAD_URL; ?>/products/<?php echo $review['product_image']; ?>" 
                                                         alt="<?php echo htmlspecialchars($review['product_name']); ?>" 
                                                         style="width: 60px; height: 60px; object-fit: cover;" 
                                                         class="rounded me-3">
                                                <?php else: ?>
                                                    <div class="bg-light d-flex align-items-center justify-content-center rounded me-3" 
                                                         style="width: 60px; height: 60px;">
                                                        <i class="bi bi-image text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($review['product_name'] ?? 'منتج محذوف'); ?></h6>
                                                    <div class="mb-2">
                                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                                            <i class="bi bi-star<?php echo $i <= $review['rating'] ? '-fill text-warning' : ' text-muted'; ?>"></i>
                                                        <?php endfor; ?>
                                                        <span class="ms-2 text-muted">(<?php echo $review['rating']; ?>/5)</span>
                                                    </div>
                                                    <small class="text-muted">
                                                        بواسطة: <?php echo htmlspecialchars($review['customer_name']); ?>
                                                        <?php if ($review['customer_email']): ?>
                                                            (<?php echo htmlspecialchars($review['customer_email']); ?>)
                                                        <?php endif; ?>
                                                    </small>
                                                </div>
                                                
                                                <div class="status-indicator-professional <?php
                                                    echo $review['status'] == 'approved' ? 'active' :
                                                        ($review['status'] == 'rejected' ? 'inactive' : 'pending');
                                                ?>">
                                                    <?php
                                                    echo $review['status'] == 'approved' ? 'معتمد' :
                                                        ($review['status'] == 'rejected' ? 'مرفوض' : 'في الانتظار');
                                                    ?>
                                                </div>
                                            </div>
                                            
                                            <?php if ($review['comment']): ?>
                                                <p class="mb-3"><?php echo htmlspecialchars($review['comment']); ?></p>
                                            <?php endif; ?>
                                            
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <?php echo date('Y-m-d H:i', strtotime($review['created_at'])); ?>
                                                </small>
                                                
                                                <div class="btn-group-professional">
                                                    <?php if ($review['status'] != 'approved'): ?>
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="review_id" value="<?php echo $review['id']; ?>">
                                                            <input type="hidden" name="status" value="approved">
                                                            <button type="submit" name="update_status"
                                                                    class="btn-professional btn-success btn-sm hover-lift-professional" title="اعتماد التقييم">
                                                                <i class="bi bi-check-circle"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>

                                                    <?php if ($review['status'] != 'rejected'): ?>
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="review_id" value="<?php echo $review['id']; ?>">
                                                            <input type="hidden" name="status" value="rejected">
                                                            <button type="submit" name="update_status"
                                                                    class="btn-professional btn-warning btn-sm hover-lift-professional" title="رفض التقييم">
                                                                <i class="bi bi-x-circle"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>

                                                    <form method="POST" class="d-inline"
                                                          onsubmit="return handleDeleteReview(event, '<?php echo htmlspecialchars($review['customer_name'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($review['product_name'], ENT_QUOTES); ?>');">
                                                        <input type="hidden" name="delete_review" value="<?php echo $review['id']; ?>">
                                                        <button type="submit" class="btn-professional btn-danger btn-sm hover-lift-professional" title="حذف التقييم">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <!-- الترقيم -->
                        <?php if ($totalPages > 1): ?>
                            <nav aria-label="ترقيم الصفحات">
                                <ul class="pagination justify-content-center">
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status; ?>&rating=<?php echo $rating; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                        
                    <?php else: ?>
                        <div class="text-center py-5 fade-in-professional">
                            <i class="bi bi-star display-1 text-professional-muted"></i>
                            <h4 class="mt-3 text-professional-dark">لا توجد تقييمات</h4>
                            <p class="text-professional-muted">لم يتم العثور على تقييمات تطابق معايير البحث</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Professional delete confirmation handler
async function handleDeleteReview(event, customerName, productName) {
    event.preventDefault();

    const confirmed = await confirmDelete(
        'هل أنت متأكد من حذف هذا التقييم؟',
        'تأكيد حذف التقييم',
        `تقييم ${customerName} للمنتج ${productName}`
    );

    if (confirmed) {
        event.target.closest('form').submit();
    }

    return false;
}
</script>

<?php require_once 'includes/footer.php'; ?>
