<?php
/**
 * تصحيح مشكلة تسجيل الدخول
 * Debug Login Issues
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>تصحيح مشكلة تسجيل الدخول</h2>";

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'shop_db';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // اختبار تسجيل الدخول
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $testUsername = trim($_POST['username']);
        $testPassword = $_POST['password'];
        
        echo "<h3>تفاصيل محاولة تسجيل الدخول:</h3>";
        echo "<ul>";
        echo "<li><strong>اسم المستخدم المدخل:</strong> '" . htmlspecialchars($testUsername) . "'</li>";
        echo "<li><strong>كلمة المرور المدخلة:</strong> '" . htmlspecialchars($testPassword) . "'</li>";
        echo "<li><strong>طول اسم المستخدم:</strong> " . strlen($testUsername) . "</li>";
        echo "<li><strong>طول كلمة المرور:</strong> " . strlen($testPassword) . "</li>";
        echo "</ul>";
        
        // البحث عن المستخدم
        $stmt = $pdo->prepare("SELECT id, username, password FROM admins WHERE username = ?");
        $stmt->execute([$testUsername]);
        $admin = $stmt->fetch();
        
        echo "<h3>نتائج البحث في قاعدة البيانات:</h3>";
        if ($admin) {
            echo "<ul>";
            echo "<li><strong>تم العثور على المستخدم:</strong> ✅</li>";
            echo "<li><strong>ID:</strong> " . $admin['id'] . "</li>";
            echo "<li><strong>اسم المستخدم في قاعدة البيانات:</strong> '" . $admin['username'] . "'</li>";
            echo "<li><strong>كلمة المرور المشفرة:</strong> " . substr($admin['password'], 0, 20) . "...</li>";
            echo "</ul>";
            
            // اختبار كلمة المرور
            echo "<h3>اختبار كلمة المرور:</h3>";
            $passwordMatch = password_verify($testPassword, $admin['password']);
            echo "<ul>";
            echo "<li><strong>نتيجة التحقق:</strong> " . ($passwordMatch ? '✅ صحيحة' : '❌ خاطئة') . "</li>";
            echo "</ul>";
            
            if ($passwordMatch) {
                echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<h4>✅ تسجيل الدخول نجح!</h4>";
                echo "<p>بيانات تسجيل الدخول صحيحة. يمكنك الآن استخدام لوحة التحكم.</p>";
                echo "<a href='admin/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب إلى لوحة التحكم</a>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<h4>❌ كلمة المرور خاطئة</h4>";
                echo "<p>اسم المستخدم صحيح لكن كلمة المرور خاطئة.</p>";
                echo "<p><strong>الحل:</strong> استخدم <a href='fix_admin_login.php'>أداة إصلاح كلمة المرور</a></p>";
                echo "</div>";
            }
            
        } else {
            echo "<ul>";
            echo "<li><strong>المستخدم غير موجود:</strong> ❌</li>";
            echo "</ul>";
            
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4>❌ اسم المستخدم غير موجود</h4>";
            echo "<p>لم يتم العثور على مستخدم بهذا الاسم في قاعدة البيانات.</p>";
            echo "<p><strong>الحل:</strong> استخدم <a href='fix_admin_login.php'>أداة إنشاء المدير</a></p>";
            echo "</div>";
        }
        
        // عرض جميع المستخدمين الموجودين
        echo "<h3>جميع المديرين في قاعدة البيانات:</h3>";
        $allAdmins = $pdo->query("SELECT id, username, email FROM admins")->fetchAll();
        
        if (empty($allAdmins)) {
            echo "<p style='color: red;'>❌ لا يوجد مديرين في قاعدة البيانات</p>";
            echo "<p><a href='fix_admin_login.php'>إنشاء مدير افتراضي</a></p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 10px;'>ID</th>";
            echo "<th style='padding: 10px;'>اسم المستخدم</th>";
            echo "<th style='padding: 10px;'>البريد الإلكتروني</th>";
            echo "</tr>";
            
            foreach ($allAdmins as $admin) {
                echo "<tr>";
                echo "<td style='padding: 10px;'>" . $admin['id'] . "</td>";
                echo "<td style='padding: 10px;'>" . $admin['username'] . "</td>";
                echo "<td style='padding: 10px;'>" . $admin['email'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}
?>

<hr>

<h3>اختبار تسجيل الدخول:</h3>
<div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <form method="POST">
        <div style="margin-bottom: 15px;">
            <label for="username"><strong>اسم المستخدم:</strong></label><br>
            <input type="text" id="username" name="username" value="admin" 
                   style="padding: 10px; width: 250px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        
        <div style="margin-bottom: 15px;">
            <label for="password"><strong>كلمة المرور:</strong></label><br>
            <input type="password" id="password" name="password" value="password" 
                   style="padding: 10px; width: 250px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        
        <button type="submit" 
                style="background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px;">
            اختبار تسجيل الدخول
        </button>
    </form>
</div>

<div style="background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h4>🔧 أدوات الإصلاح:</h4>
    <ul>
        <li><a href="fix_admin_login.php">إصلاح مشكلة تسجيل الدخول</a> - إنشاء/إعادة تعيين المدير</li>
        <li><a href="setup_database.php">إعداد قاعدة البيانات</a> - إنشاء قاعدة البيانات والجداول</li>
        <li><a href="test_connection.php">اختبار الاتصال</a> - فحص شامل للنظام</li>
    </ul>
</div>

<div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h4>📋 البيانات الافتراضية:</h4>
    <ul>
        <li><strong>اسم المستخدم:</strong> admin</li>
        <li><strong>كلمة المرور:</strong> password</li>
        <li><strong>البريد الإلكتروني:</strong> <EMAIL></li>
    </ul>
</div>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h2, h3, h4 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

button:hover {
    opacity: 0.9;
}

table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
