<?php
require_once 'config/config.php';

// التحقق من وجود معرف المنتج
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: ' . SITE_URL . '/products.php');
    exit();
}

$productId = (int)$_GET['id'];

// جلب بيانات المنتج
$product = fetchOne("
    SELECT p.*, c.name as category_name, c.id as category_id
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    WHERE p.id = ? AND p.status = 'active'
", [$productId]);

if (!$product) {
    header('Location: ' . SITE_URL . '/products.php');
    exit();
}

$pageTitle = $product['name'];

// جلب صور المنتج الإضافية
$productImages = fetchAll("SELECT image_path FROM product_images WHERE product_id = ? ORDER BY is_primary DESC", [$productId]);

// جلب تقييمات المنتج
$reviews = fetchAll("
    SELECT * FROM reviews 
    WHERE product_id = ? AND status = 'approved' 
    ORDER BY created_at DESC 
    LIMIT 10
", [$productId]);

// حساب متوسط التقييم
$ratingData = fetchOne("
    SELECT AVG(rating) as avg_rating, COUNT(*) as total_reviews 
    FROM reviews 
    WHERE product_id = ? AND status = 'approved'
", [$productId]);

$avgRating = $ratingData['avg_rating'] ? round($ratingData['avg_rating'], 1) : 0;
$totalReviews = $ratingData['total_reviews'];

// جلب المنتجات ذات الصلة
$relatedProducts = fetchAll("
    SELECT p.*, c.name as category_name 
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    WHERE p.category_id = ? AND p.id != ? AND p.status = 'active' 
    ORDER BY RAND() 
    LIMIT 4
", [$product['category_id'], $productId]);

// معالجة إضافة تقييم
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_review'])) {
    $customerName = sanitizeInput($_POST['customer_name']);
    $customerEmail = sanitizeInput($_POST['customer_email']);
    $rating = (int)$_POST['rating'];
    $comment = sanitizeInput($_POST['comment']);
    
    if (!empty($customerName) && $rating >= 1 && $rating <= 5) {
        $reviewData = [
            'product_id' => $productId,
            'customer_name' => $customerName,
            'customer_email' => $customerEmail,
            'rating' => $rating,
            'comment' => $comment,
            'status' => 'approved'  // تغيير إلى الاعتماد التلقائي
        ];
        
        if (insertData('reviews', $reviewData)) {
            $_SESSION['message'] = [
                'text' => 'تم إرسال تقييمك بنجاح! شكراً لك على مشاركة رأيك.',
                'type' => 'success'
            ];
        } else {
            $_SESSION['message'] = [
                'text' => 'حدث خطأ أثناء إرسال التقييم، يرجى المحاولة مرة أخرى.',
                'type' => 'error'
            ];
        }
        
        header('Location: ' . SITE_URL . '/product.php?id=' . $productId);
        exit();
    }
}

require_once 'includes/header.php';
?>

<!-- Breadcrumb -->
<div class="bg-light py-3">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>/products.php">المنتجات</a></li>
                <?php if ($product['category_name']): ?>
                    <li class="breadcrumb-item">
                        <a href="<?php echo SITE_URL; ?>/products.php?category=<?php echo $product['category_id']; ?>">
                            <?php echo htmlspecialchars($product['category_name']); ?>
                        </a>
                    </li>
                <?php endif; ?>
                <li class="breadcrumb-item active"><?php echo htmlspecialchars($product['name']); ?></li>
            </ol>
        </nav>
    </div>
</div>

<div class="container my-5">
    <div class="row">
        <!-- Product Images -->
        <div class="col-lg-6 mb-4">
            <div class="product-images">
                <?php
                // جمع جميع الصور المتاحة (5 روابط خارجية + الصور المرفوعة)
                $allImages = [];

                // إضافة الصور من الروابط الخارجية (1-5)
                for ($i = 1; $i <= 5; $i++) {
                    $imageField = 'image_url_' . $i;
                    if (!empty($product[$imageField])) {
                        $allImages[] = $product[$imageField];
                    }
                }

                // إضافة الصورة المرفوعة القديمة (للتوافق مع النسخ السابقة)
                if (!empty($product['image'])) {
                    $allImages[] = UPLOAD_URL . '/' . $product['image'];
                }

                // إضافة الصور الإضافية من جدول product_images
                foreach ($productImages as $image) {
                    $allImages[] = UPLOAD_URL . '/' . $image['image_path'];
                }

                $mainImage = !empty($allImages) ? $allImages[0] : 'https://via.placeholder.com/500x500/f8f9fa/6c757d?text=' . urlencode('صورة المنتج');
                $imageCount = count($allImages);
                ?>

                <!-- Main Image -->
                <div class="main-image mb-3 position-relative">
                    <img id="mainProductImage"
                         src="<?php echo $mainImage; ?>"
                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                         class="img-fluid rounded shadow"
                         style="width: 100%; height: 400px; object-fit: cover;">

                    <?php if ($product['discount'] > 0): ?>
                        <span class="discount-badge position-absolute" style="top: 20px; right: 20px;">
                            خصم <?php echo $product['discount']; ?>%
                        </span>
                    <?php endif; ?>
                </div>

                <!-- Thumbnail Images Gallery -->
                <?php if ($imageCount > 1): ?>
                    <div class="thumbnail-images">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">
                                <i class="bi bi-images"></i> معرض الصور (<?php echo $imageCount; ?> صور)
                            </h6>
                            <?php if ($imageCount > 4): ?>
                                <div class="thumbnail-controls">
                                    <button class="btn btn-sm btn-outline-secondary" id="prevThumbs">
                                        <i class="bi bi-chevron-right"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" id="nextThumbs">
                                        <i class="bi bi-chevron-left"></i>
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="thumbnail-container" style="overflow-x: auto;">
                            <div class="row g-2 flex-nowrap" id="thumbnailRow">
                                <?php foreach ($allImages as $index => $imageUrl): ?>
                                    <div class="col-auto thumbnail-item">
                                        <img src="<?php echo $imageUrl; ?>"
                                             alt="صورة المنتج <?php echo $index + 1; ?>"
                                             class="img-fluid rounded thumbnail-img <?php echo $index === 0 ? 'active' : ''; ?>"
                                             onclick="changeMainImage(this.src, <?php echo $index; ?>)"
                                             style="width: 80px; height: 80px; object-fit: cover; cursor: pointer; border: 2px solid transparent;"
                                             data-index="<?php echo $index; ?>">
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <div class="text-center mt-2">
                            <small class="text-muted">انقر على الصورة لعرضها بالحجم الكامل</small>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Product Details -->
        <div class="col-lg-6">
            <div class="product-details">
                <h1 class="h2 mb-3"><?php echo htmlspecialchars($product['name']); ?></h1>
                
                <!-- Category -->
                <?php if ($product['category_name']): ?>
                    <p class="text-muted mb-3">
                        <i class="bi bi-tag"></i> 
                        <a href="<?php echo SITE_URL; ?>/products.php?category=<?php echo $product['category_id']; ?>" 
                           class="text-decoration-none">
                            <?php echo htmlspecialchars($product['category_name']); ?>
                        </a>
                    </p>
                <?php endif; ?>
                
                <!-- Rating -->
                <?php if ($totalReviews > 0): ?>
                    <div class="rating mb-3">
                        <div class="d-flex align-items-center">
                            <div class="stars me-2">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="bi bi-star<?php echo $i <= $avgRating ? '-fill text-warning' : ' text-muted'; ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <span class="text-muted">
                                (<?php echo $avgRating; ?>/5 - <?php echo $totalReviews; ?> تقييم)
                            </span>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Price -->
                <div class="price-section mb-4">
                    <?php if ($product['discount'] > 0): ?>
                        <?php $discountedPrice = $product['price'] - ($product['price'] * $product['discount'] / 100); ?>
                        <div class="d-flex align-items-center gap-3">
                            <span class="h3 text-primary mb-0"><?php echo formatPrice($discountedPrice); ?></span>
                            <span class="h5 text-muted text-decoration-line-through mb-0"><?php echo formatPrice($product['price']); ?></span>
                            <span class="badge bg-danger">وفر <?php echo formatPrice($product['price'] - $discountedPrice); ?></span>
                        </div>
                    <?php else: ?>
                        <span class="h3 text-primary"><?php echo formatPrice($product['price']); ?></span>
                    <?php endif; ?>
                </div>
                
                <!-- Stock Status -->
                <div class="stock-status mb-4">
                    <?php if ($product['stock'] > 0): ?>
                        <span class="badge bg-success fs-6">
                            <i class="bi bi-check-circle"></i> متوفر في المخزن (<?php echo $product['stock']; ?> قطعة)
                        </span>
                        <?php if ($product['stock'] <= 5): ?>
                            <div class="text-warning mt-2">
                                <i class="bi bi-exclamation-triangle"></i> 
                                <strong>كمية محدودة!</strong> متبقي <?php echo $product['stock']; ?> قطع فقط
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <span class="badge bg-danger fs-6">
                            <i class="bi bi-x-circle"></i> غير متوفر حالياً
                        </span>
                    <?php endif; ?>
                </div>
                
                <!-- Short Description -->
                <?php if ($product['short_description']): ?>
                    <div class="short-description mb-4">
                        <p class="lead"><?php echo htmlspecialchars($product['short_description']); ?></p>
                    </div>
                <?php endif; ?>
                
                <!-- Add to Cart Form -->
                <form class="add-to-cart-form mb-4" onsubmit="return addProductToCart(event)">
                    <div class="row align-items-end">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">الكمية</label>
                            <div class="input-group">
                                <button type="button" class="btn btn-outline-secondary" onclick="decreaseQuantity()">-</button>
                                <input type="number" id="quantity" name="quantity" class="form-control text-center" 
                                       value="1" min="1" max="<?php echo $product['stock']; ?>" 
                                       <?php echo $product['stock'] == 0 ? 'disabled' : ''; ?>>
                                <button type="button" class="btn btn-outline-secondary" onclick="increaseQuantity()">+</button>
                            </div>
                        </div>
                        <div class="col-md-8 mb-3">
                            <?php if ($product['stock'] > 0): ?>
                                <button type="submit" class="btn btn-primary btn-lg w-100">
                                    <i class="bi bi-cart-plus"></i> أضف إلى السلة
                                </button>
                            <?php else: ?>
                                <button type="button" class="btn btn-secondary btn-lg w-100" disabled>
                                    <i class="bi bi-x-circle"></i> غير متوفر
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                </form>
                
                <!-- Additional Actions -->
                <div class="additional-actions mb-4">
                    <div class="row">
                        <div class="col-6">
                            <a href="https://wa.me/<?php echo str_replace('+', '', getSetting('whatsapp_number')); ?>?text=<?php echo urlencode('أريد الاستفسار عن المنتج: ' . $product['name']); ?>"
                               class="btn btn-success w-100" target="_blank">
                                <i class="bi bi-whatsapp"></i> استفسار واتساب
                            </a>
                        </div>
                        <div class="col-6">
                            <button class="btn btn-outline-primary w-100" onclick="shareProduct()">
                                <i class="bi bi-share"></i> مشاركة المنتج
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Product Features -->
                <div class="product-features">
                    <div class="row text-center">
                        <div class="col-4">
                            <i class="bi bi-truck text-primary fs-3"></i>
                            <p class="small mb-0 mt-2">توصيل سريع</p>
                        </div>
                        <div class="col-4">
                            <i class="bi bi-shield-check text-success fs-3"></i>
                            <p class="small mb-0 mt-2">ضمان الجودة</p>
                        </div>
                        <div class="col-4">
                            <i class="bi bi-arrow-return-left text-warning fs-3"></i>
                            <p class="small mb-0 mt-2">إمكانية الإرجاع</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Tabs -->
    <div class="row mt-5">
        <div class="col-12">
            <ul class="nav nav-tabs" id="productTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="description-tab" data-bs-toggle="tab"
                            data-bs-target="#description" type="button" role="tab">
                        <i class="bi bi-file-text"></i> الوصف
                    </button>
                </li>
                <?php if (!empty($product['ingredients'])): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="ingredients-tab" data-bs-toggle="tab"
                                data-bs-target="#ingredients" type="button" role="tab">
                            <i class="bi bi-list-ul"></i> المكونات
                        </button>
                    </li>
                <?php endif; ?>
                <?php if (!empty($product['usage_instructions'])): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="usage-tab" data-bs-toggle="tab"
                                data-bs-target="#usage" type="button" role="tab">
                            <i class="bi bi-info-circle"></i> الاستخدام
                        </button>
                    </li>
                <?php endif; ?>
                <?php
                // جمع جميع الفيديوهات المتاحة
                $allVideos = [];
                if (!empty($product['video_url'])) $allVideos[] = ['url' => $product['video_url'], 'title' => 'الفيديو الرئيسي'];
                if (!empty($product['video_url_1'])) $allVideos[] = ['url' => $product['video_url_1'], 'title' => 'الفيديو الثاني'];
                if (!empty($product['video_url_2'])) $allVideos[] = ['url' => $product['video_url_2'], 'title' => 'الفيديو الثالث'];
                if (!empty($product['video_url_3'])) $allVideos[] = ['url' => $product['video_url_3'], 'title' => 'الفيديو الرابع'];
                ?>

                <?php if (!empty($allVideos)): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="videos-tab" data-bs-toggle="tab"
                                data-bs-target="#videos" type="button" role="tab">
                            <i class="bi bi-play-circle"></i> الفيديوهات (<?php echo count($allVideos); ?>)
                        </button>
                    </li>
                <?php endif; ?>
                <?php if ($totalReviews > 0): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="reviews-tab" data-bs-toggle="tab"
                                data-bs-target="#reviews" type="button" role="tab">
                            <i class="bi bi-star"></i> التقييمات (<?php echo $totalReviews; ?>)
                        </button>
                    </li>
                <?php endif; ?>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="add-review-tab" data-bs-toggle="tab"
                            data-bs-target="#add-review" type="button" role="tab">
                        <i class="bi bi-plus-circle"></i> إضافة تقييم
                    </button>
                </li>
            </ul>

            <div class="tab-content mt-4" id="productTabsContent">
                <!-- Description Tab -->
                <div class="tab-pane fade show active" id="description" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <?php if ($product['description']): ?>
                                <div class="description-content">
                                    <?php echo nl2br(htmlspecialchars($product['description'])); ?>
                                </div>
                            <?php else: ?>
                                <p class="text-muted">لا يوجد وصف مفصل لهذا المنتج حالياً.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Ingredients Tab -->
                <?php if (!empty($product['ingredients'])): ?>
                    <div class="tab-pane fade" id="ingredients" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-list-ul text-primary"></i> مكونات المنتج
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="ingredients-content">
                                    <?php echo nl2br(htmlspecialchars($product['ingredients'])); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Usage Instructions Tab -->
                <?php if (!empty($product['usage_instructions'])): ?>
                    <div class="tab-pane fade" id="usage" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-info-circle text-success"></i> طريقة الاستخدام
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="usage-content">
                                    <?php echo nl2br(htmlspecialchars($product['usage_instructions'])); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Videos Tab -->
                <?php if (!empty($allVideos)): ?>
                    <div class="tab-pane fade" id="videos" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-play-circle text-danger"></i> فيديوهات المنتج (<?php echo count($allVideos); ?>)
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (count($allVideos) > 1): ?>
                                    <!-- Video Navigation -->
                                    <div class="video-navigation mb-3">
                                        <div class="btn-group w-100" role="group">
                                            <?php foreach ($allVideos as $index => $video): ?>
                                                <button type="button"
                                                        class="btn btn-outline-primary video-nav-btn <?php echo $index === 0 ? 'active' : ''; ?>"
                                                        data-video-index="<?php echo $index; ?>">
                                                    <i class="bi bi-play-circle"></i> <?php echo $video['title']; ?>
                                                </button>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Video Players -->
                                <?php foreach ($allVideos as $index => $video): ?>
                                    <div class="video-container mb-4 <?php echo $index === 0 ? '' : 'd-none'; ?>"
                                         id="video-<?php echo $index; ?>">
                                        <h6 class="mb-3">
                                            <i class="bi bi-play-circle-fill text-danger"></i>
                                            <?php echo $video['title']; ?>
                                        </h6>

                                        <?php
                                        $videoUrl = $video['url'];
                                        $embedUrl = null;
                                        $platform = 'external';

                                        // تحويل رابط YouTube إلى embed
                                        if (strpos($videoUrl, 'youtube.com/watch?v=') !== false) {
                                            $videoId = parse_url($videoUrl, PHP_URL_QUERY);
                                            parse_str($videoId, $params);
                                            if (isset($params['v'])) {
                                                $embedUrl = 'https://www.youtube.com/embed/' . $params['v'];
                                                $platform = 'youtube';
                                            }
                                        } elseif (strpos($videoUrl, 'youtu.be/') !== false) {
                                            $videoId = basename(parse_url($videoUrl, PHP_URL_PATH));
                                            $embedUrl = 'https://www.youtube.com/embed/' . $videoId;
                                            $platform = 'youtube';
                                        } elseif (strpos($videoUrl, 'vimeo.com/') !== false) {
                                            $videoId = basename(parse_url($videoUrl, PHP_URL_PATH));
                                            if (is_numeric($videoId)) {
                                                $embedUrl = 'https://player.vimeo.com/video/' . $videoId;
                                                $platform = 'vimeo';
                                            }
                                        }
                                        ?>

                                        <?php if ($embedUrl): ?>
                                            <div class="ratio ratio-16x9">
                                                <iframe src="<?php echo $embedUrl; ?>"
                                                        title="<?php echo htmlspecialchars($video['title']); ?>"
                                                        allowfullscreen
                                                        <?php if ($platform === 'vimeo'): ?>
                                                            allow="autoplay; fullscreen; picture-in-picture"
                                                        <?php endif; ?>></iframe>
                                            </div>
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    <i class="bi bi-<?php echo $platform === 'youtube' ? 'youtube' : ($platform === 'vimeo' ? 'vimeo' : 'link-45deg'); ?>"></i>
                                                    <?php echo $platform === 'youtube' ? 'YouTube' : ($platform === 'vimeo' ? 'Vimeo' : 'رابط خارجي'); ?>
                                                </small>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-center p-4 bg-light rounded">
                                                <i class="bi bi-play-circle display-4 text-muted mb-3"></i>
                                                <p class="mb-3">فيديو خارجي</p>
                                                <a href="<?php echo htmlspecialchars($videoUrl); ?>"
                                                   target="_blank" class="btn btn-primary">
                                                    <i class="bi bi-box-arrow-up-right"></i> مشاهدة الفيديو
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Reviews Tab -->
                <?php if ($totalReviews > 0): ?>
                    <div class="tab-pane fade" id="reviews" role="tabpanel">
                        <div class="card">
                            <div class="card-body">
                                <!-- Rating Summary -->
                                <div class="rating-summary mb-4 p-3 bg-light rounded">
                                    <div class="row align-items-center">
                                        <div class="col-md-4 text-center">
                                            <div class="avg-rating">
                                                <span class="display-4 fw-bold text-primary"><?php echo $avgRating; ?></span>
                                                <div class="stars">
                                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                                        <i class="bi bi-star<?php echo $i <= $avgRating ? '-fill text-warning' : ' text-muted'; ?> fs-5"></i>
                                                    <?php endfor; ?>
                                                </div>
                                                <p class="text-muted mb-0"><?php echo $totalReviews; ?> تقييم</p>
                                            </div>
                                        </div>
                                        <div class="col-md-8">
                                            <?php
                                            // حساب توزيع التقييمات
                                            $ratingDistribution = fetchAll("
                                                SELECT rating, COUNT(*) as count
                                                FROM reviews
                                                WHERE product_id = ? AND status = 'approved'
                                                GROUP BY rating
                                                ORDER BY rating DESC
                                            ", [$productId]);

                                            $distribution = array_fill(1, 5, 0);
                                            foreach ($ratingDistribution as $dist) {
                                                $distribution[$dist['rating']] = $dist['count'];
                                            }
                                            ?>

                                            <?php for ($i = 5; $i >= 1; $i--): ?>
                                                <div class="d-flex align-items-center mb-1">
                                                    <span class="me-2"><?php echo $i; ?> نجوم</span>
                                                    <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                                        <div class="progress-bar bg-warning"
                                                             style="width: <?php echo $totalReviews > 0 ? ($distribution[$i] / $totalReviews) * 100 : 0; ?>%"></div>
                                                    </div>
                                                    <span class="text-muted small"><?php echo $distribution[$i]; ?></span>
                                                </div>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Reviews List -->
                                <div class="reviews-list">
                                    <?php foreach ($reviews as $review): ?>
                                        <div class="review-item border-bottom pb-3 mb-3">
                                            <div class="d-flex align-items-start">
                                                <div class="flex-shrink-0">
                                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                                                         style="width: 50px; height: 50px;">
                                                        <i class="bi bi-person text-white fs-4"></i>
                                                    </div>
                                                </div>
                                                <div class="ms-3 flex-grow-1">
                                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                                        <div>
                                                            <h6 class="mb-0"><?php echo htmlspecialchars($review['customer_name']); ?></h6>
                                                            <div class="stars">
                                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                                    <i class="bi bi-star<?php echo $i <= $review['rating'] ? '-fill text-warning' : ' text-muted'; ?>"></i>
                                                                <?php endfor; ?>
                                                            </div>
                                                        </div>
                                                        <small class="text-muted"><?php echo formatDate($review['created_at']); ?></small>
                                                    </div>
                                                    <?php if ($review['comment']): ?>
                                                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($review['comment'])); ?></p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Add Review Tab -->
                <div class="tab-pane fade" id="add-review" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">إضافة تقييم جديد</h5>
                            <form method="POST" action="">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="customer_name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" name="customer_email">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">التقييم <span class="text-danger">*</span></label>
                                    <div class="rating-input">
                                        <?php for ($i = 5; $i >= 1; $i--): ?>
                                            <input type="radio" id="star<?php echo $i; ?>" name="rating" value="<?php echo $i; ?>" required>
                                            <label for="star<?php echo $i; ?>" class="star-label">
                                                <i class="bi bi-star-fill"></i>
                                            </label>
                                        <?php endfor; ?>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">التعليق</label>
                                    <textarea class="form-control" name="comment" rows="4"
                                              placeholder="شاركنا رأيك في هذا المنتج..."></textarea>
                                </div>

                                <button type="submit" name="add_review" class="btn btn-primary">
                                    <i class="bi bi-send"></i> إرسال التقييم
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Products -->
    <?php if ($relatedProducts): ?>
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="mb-4">منتجات ذات صلة</h3>
                <div class="row">
                    <?php foreach ($relatedProducts as $relatedProduct): ?>
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card product-card h-100 position-relative">
                                <?php if ($relatedProduct['discount'] > 0): ?>
                                    <span class="discount-badge">خصم <?php echo $relatedProduct['discount']; ?>%</span>
                                <?php endif; ?>

                                <img src="<?php echo $relatedProduct['image'] ? UPLOAD_URL . '/' . $relatedProduct['image'] : 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=صورة+المنتج'; ?>"
                                     class="card-img-top" alt="<?php echo htmlspecialchars($relatedProduct['name']); ?>">

                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title"><?php echo htmlspecialchars($relatedProduct['name']); ?></h6>
                                    <p class="text-muted small mb-2">
                                        <i class="bi bi-tag"></i> <?php echo htmlspecialchars($relatedProduct['category_name']); ?>
                                    </p>

                                    <div class="price-section mb-3 mt-auto">
                                        <?php if ($relatedProduct['discount'] > 0): ?>
                                            <?php $discountedPrice = $relatedProduct['price'] - ($relatedProduct['price'] * $relatedProduct['discount'] / 100); ?>
                                            <span class="price text-primary"><?php echo formatPrice($discountedPrice); ?></span>
                                            <span class="original-price ms-2 small"><?php echo formatPrice($relatedProduct['price']); ?></span>
                                        <?php else: ?>
                                            <span class="price text-primary"><?php echo formatPrice($relatedProduct['price']); ?></span>
                                        <?php endif; ?>
                                    </div>

                                    <div class="d-flex gap-2">
                                        <button class="btn btn-primary btn-sm flex-grow-1" onclick="addToCart(<?php echo $relatedProduct['id']; ?>)">
                                            <i class="bi bi-cart-plus"></i> أضف
                                        </button>
                                        <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $relatedProduct['id']; ?>"
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.thumbnail-img {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.thumbnail-img:hover,
.thumbnail-img.active {
    border-color: #007bff;
    transform: scale(1.05);
}

.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
}

.rating-input input[type="radio"] {
    display: none;
}

.rating-input .star-label {
    font-size: 1.5rem;
    color: #ddd;
    cursor: pointer;
    transition: color 0.2s ease;
}

.rating-input input[type="radio"]:checked ~ .star-label,
.rating-input .star-label:hover,
.rating-input .star-label:hover ~ .star-label {
    color: #ffc107;
}

.product-images .main-image {
    position: relative;
}

.product-images .main-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

@media (max-width: 768px) {
    .product-images .main-image img {
        height: 300px;
    }
}
</style>

<script>
// تغيير الصورة الرئيسية
function changeMainImage(src) {
    document.getElementById('mainProductImage').src = src;

    // تحديث الصورة النشطة
    document.querySelectorAll('.thumbnail-img').forEach(img => {
        img.classList.remove('active');
    });
    event.target.classList.add('active');
}

// زيادة الكمية
function increaseQuantity() {
    const quantityInput = document.getElementById('quantity');
    const currentValue = parseInt(quantityInput.value);
    const maxValue = parseInt(quantityInput.max);

    if (currentValue < maxValue) {
        quantityInput.value = currentValue + 1;
    }
}

// تقليل الكمية
function decreaseQuantity() {
    const quantityInput = document.getElementById('quantity');
    const currentValue = parseInt(quantityInput.value);

    if (currentValue > 1) {
        quantityInput.value = currentValue - 1;
    }
}

// إضافة المنتج إلى السلة
function addProductToCart(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);

    fetch('<?php echo SITE_URL; ?>/ajax/cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateCartCount();
            showToast('تم إضافة المنتج إلى السلة بنجاح!', 'success');

            // إضافة تأثير بصري
            const button = form.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-check-circle"></i> تم الإضافة';
            button.classList.add('btn-success');
            button.classList.remove('btn-primary');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.add('btn-primary');
                button.classList.remove('btn-success');
            }, 2000);
        } else {
            showToast(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
    });

    return false;
}

// مشاركة المنتج
function shareProduct() {
    if (navigator.share) {
        navigator.share({
            title: '<?php echo htmlspecialchars($product['name']); ?>',
            text: '<?php echo htmlspecialchars($product['short_description']); ?>',
            url: window.location.href
        });
    } else {
        // نسخ الرابط إلى الحافظة
        navigator.clipboard.writeText(window.location.href).then(() => {
            showToast('تم نسخ رابط المنتج إلى الحافظة', 'success');
        });
    }
}

// تحديث التقييم التفاعلي
document.addEventListener('DOMContentLoaded', function() {
    const ratingInputs = document.querySelectorAll('.rating-input input[type="radio"]');
    const starLabels = document.querySelectorAll('.rating-input .star-label');

    starLabels.forEach((label, index) => {
        label.addEventListener('mouseenter', function() {
            highlightStars(starLabels.length - index);
        });

        label.addEventListener('mouseleave', function() {
            const checkedInput = document.querySelector('.rating-input input[type="radio"]:checked');
            if (checkedInput) {
                highlightStars(parseInt(checkedInput.value));
            } else {
                highlightStars(0);
            }
        });

        label.addEventListener('click', function() {
            const input = document.getElementById(label.getAttribute('for'));
            input.checked = true;
            highlightStars(parseInt(input.value));
        });
    });

    function highlightStars(rating) {
        starLabels.forEach((label, index) => {
            if (starLabels.length - index <= rating) {
                label.style.color = '#ffc107';
            } else {
                label.style.color = '#ddd';
            }
        });
    }
});

// Enhanced image gallery functionality
function changeMainImage(src, index) {
    const mainImage = document.getElementById('mainProductImage');
    mainImage.src = src;

    // Update active thumbnail
    const thumbnails = document.querySelectorAll('.thumbnail-img');
    thumbnails.forEach((thumb, i) => {
        if (i === index) {
            thumb.style.border = '2px solid #007bff';
            thumb.classList.add('active');
        } else {
            thumb.style.border = '2px solid transparent';
            thumb.classList.remove('active');
        }
    });
}

// Video navigation functionality
document.addEventListener('DOMContentLoaded', function() {
    const videoNavButtons = document.querySelectorAll('.video-nav-btn');
    const videoContainers = document.querySelectorAll('.video-container[id^="video-"]');

    videoNavButtons.forEach((button, index) => {
        button.addEventListener('click', function() {
            // Update active button
            videoNavButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Show corresponding video
            videoContainers.forEach((container, i) => {
                if (i === index) {
                    container.classList.remove('d-none');
                } else {
                    container.classList.add('d-none');
                }
            });
        });
    });

    // Thumbnail navigation for large galleries
    const prevBtn = document.getElementById('prevThumbs');
    const nextBtn = document.getElementById('nextThumbs');
    const thumbnailRow = document.getElementById('thumbnailRow');

    if (prevBtn && nextBtn && thumbnailRow) {
        let scrollPosition = 0;
        const scrollAmount = 200;

        nextBtn.addEventListener('click', function() {
            scrollPosition += scrollAmount;
            thumbnailRow.style.transform = `translateX(-${scrollPosition}px)`;
        });

        prevBtn.addEventListener('click', function() {
            scrollPosition = Math.max(0, scrollPosition - scrollAmount);
            thumbnailRow.style.transform = `translateX(-${scrollPosition}px)`;
        });
    }
});

// تكبير الصورة عند النقر
document.addEventListener('DOMContentLoaded', function() {
    const mainImage = document.getElementById('mainProductImage');

    mainImage.addEventListener('click', function() {
        // إنشاء modal لعرض الصورة بحجم كبير
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-zoom-in"></i> <?php echo htmlspecialchars($product['name']); ?>
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center p-0">
                        <img src="${this.src}" alt="صورة المنتج" class="img-fluid" style="max-height: 80vh;">
                    </div>
                    <div class="modal-footer justify-content-center">
                        <small class="text-muted">انقر خارج الصورة للإغلاق</small>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        modal.addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(modal);
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
