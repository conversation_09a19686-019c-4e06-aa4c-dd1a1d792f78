# متجر إلكتروني شامل - E-Commerce Store

متجر إلكتروني متكامل مبني بـ PHP و MySQL مع لوحة تحكم إدارية شاملة.

## المميزات الرئيسية

### للعملاء:
- 🏠 صفحة رئيسية جذابة مع عرض المنتجات المميزة
- 🛍️ تصفح المنتجات مع فلترة وبحث متقدم
- 🛒 سلة تسوق تفاعلية
- 💳 نظام طلبات متكامل
- ⭐ نظام تقييمات المنتجات
- 📱 تصميم متجاوب (Responsive Design)
- 🎨 واجهة مستخدم عصرية وسهلة الاستخدام

### للإدارة:
- 📊 لوحة قيادة شاملة مع الإحصائيات
- 📦 إدارة المنتجات والتصنيفات
- 📋 إدارة الطلبات وتتبع حالتها
- ⭐ إدارة التقييمات والتعليقات
- 💰 إدارة أكواد الخصم
- 📧 إدارة النشرة البريدية
- ⚙️ إعدادات الموقع

## متطلبات التشغيل

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx Web Server
- مكتبة PDO PHP

## التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone [repository-url]
cd ecommerce-store
```

### 2. إعداد قاعدة البيانات
1. أنشئ قاعدة بيانات جديدة في MySQL
2. استورد ملف `database.sql`
```sql
mysql -u username -p database_name < database.sql
```

### 3. تكوين الاتصال
عدّل ملف `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 4. إعداد الصلاحيات
تأكد من أن مجلد `uploads` قابل للكتابة:
```bash
chmod 755 uploads/
chmod 755 uploads/products/
chmod 755 uploads/categories/
```

### 5. تكوين الموقع
عدّل ملف `config/config.php`:
```php
define('SITE_URL', 'http://your-domain.com');
```

## بيانات الدخول الافتراضية

### لوحة التحكم الإدارية:
- **الرابط:** `/admin/login.php`
- **اسم المستخدم:** admin
- **كلمة المرور:** password

⚠️ **مهم:** غيّر كلمة المرور فور تسجيل الدخول الأول!

## هيكل المشروع

```
├── admin/                  # لوحة التحكم الإدارية
│   ├── includes/          # ملفات مشتركة للإدارة
│   ├── ajax/              # ملفات AJAX
│   ├── login.php          # تسجيل دخول الإدارة
│   ├── dashboard.php      # لوحة القيادة
│   └── ...
├── config/                # ملفات التكوين
│   ├── database.php       # إعدادات قاعدة البيانات
│   └── config.php         # إعدادات عامة
├── includes/              # ملفات مشتركة للموقع
│   ├── header.php         # رأس الصفحة
│   └── footer.php         # تذييل الصفحة
├── ajax/                  # ملفات AJAX للموقع
├── uploads/               # مجلد الصور المرفوعة
├── index.php              # الصفحة الرئيسية
├── products.php           # صفحة المنتجات
├── product.php            # صفحة المنتج الواحد
├── cart.php               # سلة التسوق
├── checkout.php           # إتمام الطلب
├── contact.php            # صفحة الاتصال
└── database.sql           # ملف قاعدة البيانات
```

## الاستخدام

### إضافة منتجات جديدة:
1. سجل دخول إلى لوحة التحكم
2. اذهب إلى "المنتجات" > "إضافة منتج جديد"
3. املأ البيانات المطلوبة وارفع الصور
4. احفظ المنتج

### إدارة الطلبات:
1. اذهب إلى "الطلبات" في لوحة التحكم
2. اعرض تفاصيل الطلب
3. غيّر حالة الطلب حسب الحاجة
4. تواصل مع العميل عند الضرورة

### تخصيص الموقع:
1. اذهب إلى "الإعدادات"
2. عدّل معلومات الموقع
3. حدّث معلومات الاتصال
4. اضبط إعدادات التوصيل والأسعار

## المميزات التقنية

- **أمان عالي:** حماية من SQL Injection و XSS
- **تصميم متجاوب:** يعمل على جميع الأجهزة
- **أداء محسّن:** استعلامات قاعدة بيانات محسّنة
- **سهولة الصيانة:** كود منظم وموثق
- **قابلية التوسع:** بنية قابلة للتطوير

## التخصيص والتطوير

### إضافة ميزات جديدة:
1. أنشئ ملفات PHP جديدة
2. استخدم الدوال الموجودة في `config/config.php`
3. اتبع نفس نمط التصميم المستخدم

### تخصيص التصميم:
1. عدّل ملفات CSS في `includes/header.php`
2. استخدم Bootstrap 5 للتصميم
3. أضف JavaScript مخصص حسب الحاجة

## الدعم والمساعدة

### مشاكل شائعة:

**خطأ في الاتصال بقاعدة البيانات:**
- تأكد من صحة بيانات الاتصال في `config/database.php`
- تأكد من تشغيل خدمة MySQL

**مشاكل في رفع الصور:**
- تأكد من صلاحيات مجلد `uploads`
- تحقق من حجم الملف المسموح في PHP

**مشاكل في التصميم:**
- تأكد من تحميل Bootstrap و CSS بشكل صحيح
- تحقق من وجود أخطاء JavaScript في المتصفح

## الأمان

### نصائح أمنية مهمة:
1. غيّر كلمة مرور الإدارة الافتراضية
2. استخدم HTTPS في الإنتاج
3. حدّث PHP و MySQL بانتظام
4. اعمل نسخ احتياطية دورية
5. راقب ملفات السجل للأنشطة المشبوهة

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إرسال Pull Request

---

**تم تطوير هذا المشروع بـ ❤️ لخدمة المجتمع العربي**
