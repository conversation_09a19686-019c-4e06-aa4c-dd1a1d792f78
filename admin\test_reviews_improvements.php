<?php
/**
 * اختبار تحسينات إدارة التقييمات
 * Reviews Management Improvements Test
 */

require_once '../config/config.php';
require_once '../config/database.php';

// التحقق من الجلسة
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$testResults = [];

// Test 1: Review CRUD Operations
try {
    // First, get a product to test with
    $testProduct = fetchOne("SELECT id, name FROM products LIMIT 1");
    
    if ($testProduct) {
        // Test review creation
        $testReviewData = [
            'product_id' => $testProduct['id'],
            'customer_name' => 'عميل اختبار ' . time(),
            'customer_email' => 'test' . time() . '@example.com',
            'rating' => 5,
            'comment' => 'تقييم اختبار ممتاز',
            'status' => 'approved'  // Should be auto-approved now
        ];
        
        $reviewId = insertData('reviews', $testReviewData);
        
        if ($reviewId) {
            $testResults['create'] = '✅ إنشاء التقييم: نجح';
            
            // Test review status update (approve/reject)
            $updateResult = updateData('reviews', ['status' => 'rejected'], 'id = ?', [$reviewId]);
            
            if ($updateResult !== false && $updateResult > 0) {
                $testResults['status_update'] = '✅ تحديث حالة التقييم: نجح';
                
                // Test approve functionality
                $approveResult = updateData('reviews', ['status' => 'approved'], 'id = ?', [$reviewId]);
                
                if ($approveResult !== false && $approveResult > 0) {
                    $testResults['approve'] = '✅ اعتماد التقييم: نجح';
                } else {
                    $testResults['approve'] = '❌ اعتماد التقييم: فشل';
                }
            } else {
                $testResults['status_update'] = '❌ تحديث حالة التقييم: فشل';
                $testResults['approve'] = '⏭️ تم تخطي اختبار الاعتماد';
            }
            
            // Test review deletion
            $deleteResult = deleteData('reviews', 'id = ?', [$reviewId]);
            
            if ($deleteResult) {
                $testResults['delete'] = '✅ حذف التقييم: نجح';
            } else {
                $testResults['delete'] = '❌ حذف التقييم: فشل';
            }
            
        } else {
            $testResults['create'] = '❌ إنشاء التقييم: فشل';
            $testResults['status_update'] = '⏭️ تم تخطي اختبار تحديث الحالة';
            $testResults['approve'] = '⏭️ تم تخطي اختبار الاعتماد';
            $testResults['delete'] = '⏭️ تم تخطي اختبار الحذف';
        }
    } else {
        $testResults['create'] = '❌ لا توجد منتجات للاختبار';
        $testResults['status_update'] = '⏭️ تم تخطي اختبار تحديث الحالة';
        $testResults['approve'] = '⏭️ تم تخطي اختبار الاعتماد';
        $testResults['delete'] = '⏭️ تم تخطي اختبار الحذف';
    }
    
} catch (Exception $e) {
    $testResults['crud'] = '❌ خطأ في اختبار عمليات CRUD: ' . $e->getMessage();
}

// Test 2: Auto-Approval Functionality
try {
    // Check if new reviews are set to 'approved' by default
    $testProduct = fetchOne("SELECT id FROM products LIMIT 1");
    
    if ($testProduct) {
        $autoApprovalData = [
            'product_id' => $testProduct['id'],
            'customer_name' => 'اختبار الاعتماد التلقائي',
            'customer_email' => '<EMAIL>',
            'rating' => 4,
            'comment' => 'اختبار الاعتماد التلقائي',
            'status' => 'approved'
        ];
        
        $autoReviewId = insertData('reviews', $autoApprovalData);
        
        if ($autoReviewId) {
            $createdReview = fetchOne("SELECT status FROM reviews WHERE id = ?", [$autoReviewId]);
            
            if ($createdReview && $createdReview['status'] == 'approved') {
                $testResults['auto_approval'] = '✅ الاعتماد التلقائي: يعمل بشكل صحيح';
            } else {
                $testResults['auto_approval'] = '❌ الاعتماد التلقائي: لا يعمل';
            }
            
            // Clean up
            deleteData('reviews', 'id = ?', [$autoReviewId]);
        } else {
            $testResults['auto_approval'] = '❌ الاعتماد التلقائي: فشل في إنشاء التقييم';
        }
    } else {
        $testResults['auto_approval'] = '❌ الاعتماد التلقائي: لا توجد منتجات للاختبار';
    }
    
} catch (Exception $e) {
    $testResults['auto_approval'] = '❌ خطأ في اختبار الاعتماد التلقائي: ' . $e->getMessage();
}

// Test 3: Rating Validation
try {
    $validRatings = [1, 2, 3, 4, 5];
    $invalidRatings = [0, 6, -1, 10];
    
    $validCount = 0;
    $invalidCount = 0;
    
    foreach ($validRatings as $rating) {
        if ($rating >= 1 && $rating <= 5) {
            $validCount++;
        }
    }
    
    foreach ($invalidRatings as $rating) {
        if ($rating < 1 || $rating > 5) {
            $invalidCount++;
        }
    }
    
    if ($validCount == count($validRatings) && $invalidCount == count($invalidRatings)) {
        $testResults['rating_validation'] = '✅ التحقق من صحة التقييمات: نجح';
    } else {
        $testResults['rating_validation'] = '❌ التحقق من صحة التقييمات: فشل';
    }
    
} catch (Exception $e) {
    $testResults['rating_validation'] = '❌ خطأ في اختبار التحقق من التقييمات: ' . $e->getMessage();
}

// Test 4: Database Functions
try {
    $reviews = fetchAll("SELECT * FROM reviews LIMIT 5");
    $reviewsCount = fetchOne("SELECT COUNT(*) as count FROM reviews");
    
    if (is_array($reviews) && isset($reviewsCount['count'])) {
        $testResults['database'] = '✅ دوال قاعدة البيانات: تعمل بشكل صحيح';
    } else {
        $testResults['database'] = '❌ دوال قاعدة البيانات: خطأ في الاستعلام';
    }
    
} catch (Exception $e) {
    $testResults['database'] = '❌ خطأ في اختبار قاعدة البيانات: ' . $e->getMessage();
}

// Test 5: Review Statistics
try {
    $stats = [
        'pending' => fetchOne("SELECT COUNT(*) as count FROM reviews WHERE status = 'pending'"),
        'approved' => fetchOne("SELECT COUNT(*) as count FROM reviews WHERE status = 'approved'"),
        'rejected' => fetchOne("SELECT COUNT(*) as count FROM reviews WHERE status = 'rejected'")
    ];
    
    $statsValid = true;
    foreach ($stats as $status => $stat) {
        if (!isset($stat['count']) || !is_numeric($stat['count'])) {
            $statsValid = false;
            break;
        }
    }
    
    if ($statsValid) {
        $testResults['statistics'] = '✅ إحصائيات التقييمات: تعمل بشكل صحيح';
    } else {
        $testResults['statistics'] = '❌ إحصائيات التقييمات: خطأ في البيانات';
    }
    
} catch (Exception $e) {
    $testResults['statistics'] = '❌ خطأ في اختبار الإحصائيات: ' . $e->getMessage();
}

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-professional fade-in-professional">
                <div class="card-header">
                    <h5 class="mb-0 text-professional-dark">
                        <i class="bi bi-check2-square text-professional-success"></i> 
                        اختبار تحسينات إدارة التقييمات
                    </h5>
                </div>
                
                <div class="card-body">
                    <div class="alert-professional alert-info slide-up-professional">
                        <i class="bi bi-info-circle-fill alert-icon"></i>
                        <div class="alert-content">
                            <strong>معلومات الاختبار:</strong><br>
                            هذه الصفحة تختبر جميع التحسينات المطبقة على نظام إدارة التقييمات
                        </div>
                    </div>
                    
                    <div class="row">
                        <?php foreach ($testResults as $testName => $result): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <?php 
                                            $titles = [
                                                'create' => 'إنشاء التقييم',
                                                'status_update' => 'تحديث حالة التقييم',
                                                'approve' => 'اعتماد التقييم',
                                                'delete' => 'حذف التقييم',
                                                'auto_approval' => 'الاعتماد التلقائي',
                                                'rating_validation' => 'التحقق من صحة التقييمات',
                                                'database' => 'دوال قاعدة البيانات',
                                                'statistics' => 'إحصائيات التقييمات',
                                                'crud' => 'عمليات CRUD'
                                            ];
                                            echo $titles[$testName] ?? $testName;
                                            ?>
                                        </h6>
                                        <p class="card-text"><?php echo $result; ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <hr class="border-professional">
                    
                    <div class="text-center">
                        <h5 class="text-professional-dark mb-3">الخطوات التالية</h5>
                        <div class="btn-group-professional">
                            <a href="reviews.php" class="btn-professional btn-primary hover-lift-professional">
                                <i class="bi bi-star"></i> إدارة التقييمات
                            </a>
                            <a href="discount_codes.php" class="btn-professional btn-outline-primary hover-lift-professional">
                                <i class="bi bi-percent"></i> إدارة أكواد الخصم
                            </a>
                            <a href="categories.php" class="btn-professional btn-outline-primary hover-lift-professional">
                                <i class="bi bi-tags"></i> إدارة التصنيفات
                            </a>
                            <a href="products.php" class="btn-professional btn-outline-primary hover-lift-professional">
                                <i class="bi bi-box-seam"></i> إدارة المنتجات
                            </a>
                            <a href="dashboard.php" class="btn-professional btn-outline-secondary hover-lift-professional">
                                <i class="bi bi-speedometer2"></i> لوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations to test result cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.3s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
