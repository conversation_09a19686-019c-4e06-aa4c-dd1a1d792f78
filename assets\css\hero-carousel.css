/**
 * Professional Hero Carousel Styles
 * Modern, Clean, and Error-Free Implementation
 * 
 * @version 2.0
 * <AUTHOR> Development Team
 */

/* ==========================================================================
   Hero Section Base Styles
   ========================================================================== */

.hero-section {
    position: relative;
    width: 100%;
    min-height: 70vh;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin-bottom: 3rem;
}

/* ==========================================================================
   Carousel Container
   ========================================================================== */

.hero-carousel {
    position: relative;
    width: 100%;
    height: 70vh;
    overflow: hidden;
}

.carousel-slides {
    position: relative;
    width: 100%;
    height: 100%;
}

/* ==========================================================================
   Individual Slide Styles
   ========================================================================== */

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(100%);
}

.carousel-slide.active {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
    z-index: 2;
}

.carousel-slide.prev {
    transform: translateX(-100%);
}

/* ==========================================================================
   Image Container & Loading States
   ========================================================================== */

.slide-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.slide-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
}

.carousel-slide:hover .slide-image {
    transform: scale(1.05);
}

.slide-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

@keyframes shimmer {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e0e0e0;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #666;
    font-size: 0.9rem;
    font-weight: 500;
}

.image-error .slide-loading {
    background: #f8f9fa;
    color: #6c757d;
}

.image-error .slide-loading::before {
    content: "⚠️";
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* ==========================================================================
   Content Overlay
   ========================================================================== */

.slide-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.1) 100%);
    display: flex;
    align-items: center;
    z-index: 3;
}

.content-wrapper {
    max-width: 600px;
    padding: 2rem;
    text-align: right;
}

/* ==========================================================================
   Typography
   ========================================================================== */

.slide-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: #ffffff;
    text-shadow: 2px 2px 8px rgba(0,0,0,0.5);
    margin-bottom: 1.5rem;
    line-height: 1.2;
    opacity: 0;
    transform: translateY(50px);
}

.slide-subtitle {
    font-size: 1.4rem;
    color: rgba(255,255,255,0.95);
    text-shadow: 1px 1px 4px rgba(0,0,0,0.5);
    margin-bottom: 2rem;
    line-height: 1.6;
    font-weight: 400;
    opacity: 0;
    transform: translateY(30px);
}

.slide-actions {
    opacity: 0;
    transform: translateY(20px);
}

/* ==========================================================================
   Buttons
   ========================================================================== */

.btn-hero {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: 1rem;
}

.btn-hero:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.3);
}

.btn-hero i {
    font-size: 1.2rem;
}

/* ==========================================================================
   Navigation Controls
   ========================================================================== */

.carousel-controls {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 2rem;
    z-index: 4;
    pointer-events: none;
}

.carousel-btn {
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    color: #ffffff;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    pointer-events: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    transform: scale(1.1);
}

.carousel-btn:active {
    transform: scale(0.95);
}

/* ==========================================================================
   Indicators
   ========================================================================== */

.carousel-indicators {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.75rem;
    z-index: 4;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.4);
    border: 2px solid rgba(255,255,255,0.6);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: #ffffff;
    transform: scale(1.3);
    box-shadow: 0 4px 15px rgba(255,255,255,0.3);
}

.indicator:hover {
    background: rgba(255,255,255,0.7);
    transform: scale(1.1);
}

/* ==========================================================================
   Default Hero Section
   ========================================================================== */

.hero-default {
    position: relative;
    width: 100%;
    min-height: 70vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
}

.default-content {
    position: relative;
    z-index: 2;
    max-width: 600px;
    padding: 2rem;
    text-align: right;
}

.default-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: #ffffff;
    text-shadow: 2px 2px 8px rgba(0,0,0,0.3);
    margin-bottom: 1.5rem;
    line-height: 1.2;
    opacity: 0;
    transform: translateY(50px);
}

.default-subtitle {
    font-size: 1.4rem;
    color: rgba(255,255,255,0.95);
    text-shadow: 1px 1px 4px rgba(0,0,0,0.3);
    margin-bottom: 2.5rem;
    line-height: 1.6;
    font-weight: 400;
    opacity: 0;
    transform: translateY(30px);
}

.default-actions {
    opacity: 0;
    transform: translateY(20px);
}

/* ==========================================================================
   Animations
   ========================================================================== */

[data-animation="slideUp"] {
    animation: slideUp 1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

[data-animation="slideUp"][data-delay="200"] {
    animation-delay: 0.2s;
}

[data-animation="slideUp"][data-delay="400"] {
    animation-delay: 0.4s;
}

[data-animation="fadeIn"] {
    animation: fadeIn 1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

[data-animation="fadeIn"][data-delay="200"] {
    animation-delay: 0.2s;
}

[data-animation="fadeIn"][data-delay="400"] {
    animation-delay: 0.4s;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Active slide animations */
.carousel-slide.active .slide-title {
    animation: slideUp 1s cubic-bezier(0.4, 0, 0.2, 1) 0.3s forwards;
}

.carousel-slide.active .slide-subtitle {
    animation: slideUp 1s cubic-bezier(0.4, 0, 0.2, 1) 0.5s forwards;
}

.carousel-slide.active .slide-actions {
    animation: fadeIn 1s cubic-bezier(0.4, 0, 0.2, 1) 0.7s forwards;
}

/* ==========================================================================
   Responsive Design - Mobile First Approach
   ========================================================================== */

/* Large Tablets and Small Desktops */
@media (max-width: 1200px) {
    .slide-title,
    .default-title {
        font-size: 3rem;
    }

    .slide-subtitle,
    .default-subtitle {
        font-size: 1.2rem;
    }

    .content-wrapper,
    .default-content {
        max-width: 500px;
    }
}

/* Tablets */
@media (max-width: 768px) {
    .hero-section,
    .hero-carousel,
    .hero-default {
        min-height: 60vh;
        height: 60vh;
    }

    .slide-title,
    .default-title {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .slide-subtitle,
    .default-subtitle {
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
    }

    .content-wrapper,
    .default-content {
        max-width: 400px;
        padding: 1.5rem;
    }

    .carousel-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .carousel-controls {
        padding: 0 1rem;
    }

    .btn-hero {
        padding: 0.8rem 2rem;
        font-size: 1rem;
        margin-left: 0.5rem;
        margin-bottom: 0.5rem;
    }
}

/* Mobile Phones */
@media (max-width: 576px) {
    .hero-section,
    .hero-carousel,
    .hero-default {
        min-height: 50vh;
        height: 50vh;
        margin-bottom: 2rem;
    }

    .slide-title,
    .default-title {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .slide-subtitle,
    .default-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .content-wrapper,
    .default-content {
        max-width: 100%;
        padding: 1rem;
        text-align: center;
    }

    .carousel-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .carousel-controls {
        padding: 0 0.5rem;
    }

    .btn-hero {
        padding: 0.7rem 1.5rem;
        font-size: 0.9rem;
        margin: 0.25rem;
        display: block;
        text-align: center;
    }

    .indicator {
        width: 10px;
        height: 10px;
    }

    .carousel-indicators {
        bottom: 1rem;
        gap: 0.5rem;
    }
}

/* Extra Small Devices */
@media (max-width: 400px) {
    .slide-title,
    .default-title {
        font-size: 1.8rem;
    }

    .slide-subtitle,
    .default-subtitle {
        font-size: 0.9rem;
    }

    .btn-hero {
        padding: 0.6rem 1.2rem;
        font-size: 0.85rem;
    }
}

/* ==========================================================================
   Accessibility & Performance
   ========================================================================== */

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .carousel-slide,
    .slide-image,
    .carousel-btn,
    .indicator,
    [data-animation] {
        transition: none !important;
        animation: none !important;
    }

    .hero-background {
        animation: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .carousel-btn {
        border-width: 3px;
        background: rgba(0,0,0,0.8);
    }

    .indicator {
        border-width: 3px;
        background: rgba(255,255,255,0.9);
    }

    .slide-content,
    .background-overlay {
        background: rgba(0,0,0,0.8);
    }
}

/* Focus styles for accessibility */
.carousel-btn:focus,
.indicator:focus {
    outline: 3px solid #007bff;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .hero-section {
        height: auto !important;
        min-height: auto !important;
        page-break-inside: avoid;
    }

    .carousel-controls,
    .carousel-indicators {
        display: none !important;
    }

    .carousel-slide {
        position: static !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: none !important;
    }
}
