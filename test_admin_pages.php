<?php
/**
 * اختبار جميع صفحات لوحة التحكم
 * Test All Admin Pages
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>اختبار صفحات لوحة التحكم</h2>";

// قائمة صفحات لوحة التحكم
$adminPages = [
    'admin/login.php' => 'صفحة تسجيل الدخول',
    'admin/dashboard.php' => 'لوحة القيادة',
    'admin/products.php' => 'إدارة المنتجات',
    'admin/add_product.php' => 'إضافة منتج جديد',
    'admin/orders.php' => 'إدارة الطلبات',
    'admin/categories.php' => 'إدارة التصنيفات',
    'admin/reviews.php' => 'إدارة التقييمات',
    'admin/discount_codes.php' => 'إدارة أكواد الخصم',
    'admin/newsletter.php' => 'إدارة النشرة البريدية',
    'admin/settings.php' => 'إعدادات الموقع'
];

// قائمة الملفات المطلوبة
$requiredFiles = [
    'config/config.php' => 'ملف التكوين الرئيسي',
    'config/database.php' => 'ملف قاعدة البيانات',
    'config/functions.php' => 'ملف الدوال المساعدة',
    'admin/includes/header.php' => 'رأس صفحات الإدارة',
    'admin/includes/footer.php' => 'تذييل صفحات الإدارة'
];

echo "<h3>التحقق من الملفات المطلوبة:</h3>";
echo "<ul>";
$missingFiles = [];
foreach ($requiredFiles as $file => $description) {
    $exists = file_exists($file);
    echo "<li><strong>$description:</strong> ";
    if ($exists) {
        echo "<span style='color: green;'>✅ $file</span>";
    } else {
        echo "<span style='color: red;'>❌ $file غير موجود</span>";
        $missingFiles[] = $file;
    }
    echo "</li>";
}
echo "</ul>";

if (!empty($missingFiles)) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ ملفات مفقودة</h4>";
    echo "<p>يجب إنشاء الملفات التالية أولاً:</p>";
    echo "<ul>";
    foreach ($missingFiles as $file) {
        echo "<li>$file</li>";
    }
    echo "</ul>";
    echo "</div>";
    exit();
}

echo "<h3>التحقق من صفحات لوحة التحكم:</h3>";

try {
    // تضمين ملفات التكوين
    require_once 'config/config.php';
    echo "<p style='color: green;'>✅ تم تضمين ملفات التكوين بنجاح</p>";
    
    // محاكاة جلسة المدير للاختبار
    $_SESSION[ADMIN_SESSION_NAME] = true;
    $_SESSION['admin_id'] = 1;
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>الصفحة</th>";
    echo "<th style='padding: 10px;'>الوصف</th>";
    echo "<th style='padding: 10px;'>الحالة</th>";
    echo "<th style='padding: 10px;'>الملاحظات</th>";
    echo "</tr>";
    
    foreach ($adminPages as $page => $description) {
        echo "<tr>";
        echo "<td style='padding: 10px;'><a href='$page' target='_blank'>$page</a></td>";
        echo "<td style='padding: 10px;'>$description</td>";
        
        if (file_exists($page)) {
            // محاولة تضمين الصفحة للتحقق من الأخطاء
            ob_start();
            $error = false;
            
            set_error_handler(function($severity, $message, $file, $line) use (&$error) {
                $error = "خطأ: $message";
                return true;
            });
            
            try {
                include $page;
                restore_error_handler();
                $output = ob_get_clean();
                
                if ($error) {
                    echo "<td style='padding: 10px; color: red;'>❌ خطأ</td>";
                    echo "<td style='padding: 10px; color: red;'>$error</td>";
                } else {
                    echo "<td style='padding: 10px; color: green;'>✅ يعمل</td>";
                    echo "<td style='padding: 10px; color: green;'>تم تحميل الصفحة بنجاح</td>";
                }
            } catch (Exception $e) {
                restore_error_handler();
                ob_end_clean();
                echo "<td style='padding: 10px; color: red;'>❌ استثناء</td>";
                echo "<td style='padding: 10px; color: red;'>" . $e->getMessage() . "</td>";
            }
        } else {
            echo "<td style='padding: 10px; color: red;'>❌ غير موجود</td>";
            echo "<td style='padding: 10px; color: red;'>الملف غير موجود</td>";
        }
        
        echo "</tr>";
    }
    
    echo "</table>";
    
    // اختبار قاعدة البيانات
    echo "<h3>اختبار قاعدة البيانات:</h3>";
    
    $tables = ['admins', 'categories', 'products', 'orders', 'reviews', 'discount_codes', 'newsletter', 'site_settings'];
    
    echo "<ul>";
    foreach ($tables as $table) {
        try {
            $count = fetchOne("SELECT COUNT(*) as count FROM $table");
            $recordCount = ($count && isset($count['count'])) ? $count['count'] : 0;
            echo "<li style='color: green;'>✅ جدول <strong>$table</strong>: $recordCount سجل</li>";
        } catch (Exception $e) {
            echo "<li style='color: red;'>❌ جدول <strong>$table</strong>: " . $e->getMessage() . "</li>";
        }
    }
    echo "</ul>";
    
    // اختبار الدوال المساعدة
    echo "<h3>اختبار الدوال المساعدة:</h3>";
    
    $functions = [
        'sanitizeInput' => 'تنظيف البيانات',
        'formatPrice' => 'تنسيق السعر',
        'insertData' => 'إدراج البيانات',
        'updateData' => 'تحديث البيانات',
        'deleteData' => 'حذف البيانات',
        'getSetting' => 'الحصول على الإعدادات',
        'calculateDiscountedPrice' => 'حساب السعر بعد الخصم'
    ];
    
    echo "<ul>";
    foreach ($functions as $function => $description) {
        if (function_exists($function)) {
            echo "<li style='color: green;'>✅ <strong>$function</strong>: $description</li>";
        } else {
            echo "<li style='color: red;'>❌ <strong>$function</strong>: غير موجودة</li>";
        }
    }
    echo "</ul>";
    
    // اختبار الإعدادات
    echo "<h3>اختبار الإعدادات:</h3>";
    
    $settings = [
        'site_name' => getSetting('site_name', 'غير محدد'),
        'delivery_price' => getSetting('delivery_price', '0'),
        'currency' => getSetting('currency', 'غير محدد'),
        'contact_phone' => getSetting('contact_phone', 'غير محدد')
    ];
    
    echo "<ul>";
    foreach ($settings as $key => $value) {
        echo "<li><strong>$key:</strong> $value</li>";
    }
    echo "</ul>";
    
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ اختبار لوحة التحكم مكتمل!</h4>";
    echo "<p>جميع صفحات لوحة التحكم جاهزة للاستخدام.</p>";
    echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
    echo "<ul>";
    echo "<li><strong>الرابط:</strong> <a href='admin/login.php'>admin/login.php</a></li>";
    echo "<li><strong>اسم المستخدم:</strong> admin</li>";
    echo "<li><strong>كلمة المرور:</strong> password</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ في الاختبار</h4>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>الميزات المتاحة في لوحة التحكم:</h3>";
echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px;'>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>📊 إدارة المحتوى:</h5>";
echo "<ul>";
echo "<li>إدارة المنتجات (إضافة، تعديل، حذف)</li>";
echo "<li>إدارة التصنيفات مع الصور</li>";
echo "<li>رفع وإدارة الصور</li>";
echo "<li>إدارة المخزون والأسعار</li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h5>🛒 إدارة الطلبات:</h5>";
echo "<ul>";
echo "<li>عرض وإدارة الطلبات</li>";
echo "<li>تحديث حالات الطلبات</li>";
echo "<li>إحصائيات المبيعات</li>";
echo "<li>تقارير مفصلة</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>⭐ إدارة التقييمات:</h5>";
echo "<ul>";
echo "<li>مراجعة واعتماد التقييمات</li>";
echo "<li>إدارة التعليقات</li>";
echo "<li>فلترة حسب التقييم</li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h5>💰 إدارة الخصومات:</h5>";
echo "<ul>";
echo "<li>إنشاء أكواد خصم</li>";
echo "<li>تحديد شروط الاستخدام</li>";
echo "<li>متابعة الاستخدام</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>📧 النشرة البريدية:</h5>";
echo "<ul>";
echo "<li>إدارة المشتركين</li>";
echo "<li>تصدير قوائم البريد</li>";
echo "<li>إرسال النشرات</li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h5>⚙️ الإعدادات:</h5>";
echo "<ul>";
echo "<li>إعدادات الموقع العامة</li>";
echo "<li>معلومات الاتصال</li>";
echo "<li>إعدادات التوصيل</li>";
echo "<li>وسائل التواصل الاجتماعي</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h4>📋 الخطوات التالية:</h4>";
echo "<ol>";
echo "<li>سجل دخول إلى لوحة التحكم</li>";
echo "<li>أضف بعض التصنيفات</li>";
echo "<li>أضف منتجات مع الصور</li>";
echo "<li>اختبر عملية الطلب من الموقع</li>";
echo "<li>راجع الطلبات في لوحة التحكم</li>";
echo "<li>احذف ملفات الاختبار لأسباب أمنية</li>";
echo "</ol>";
echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h2, h3, h4, h5 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 15px 0;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 10px 0;
}

hr {
    margin: 30px 0;
    border: none;
    border-top: 2px solid #dee2e6;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col-md-6 {
    flex: 0 0 50%;
    padding: 0 15px;
}

@media (max-width: 768px) {
    .col-md-6 {
        flex: 0 0 100%;
    }
}
</style>
