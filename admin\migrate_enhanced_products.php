<?php
/**
 * Enhanced Products Migration Script
 * Adds support for 5 image URLs and 3 video URLs
 */

require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'تحديث جدول المنتجات المحسن';

// Check if migration is needed
function checkEnhancedMigrationNeeded() {
    global $pdo;
    try {
        $columns = fetchAll("SHOW COLUMNS FROM products");
        $columnNames = array_column($columns, 'Field');
        
        $requiredColumns = [
            'image_url_4', 'image_url_5', 
            'video_url_1', 'video_url_2', 'video_url_3'
        ];
        
        $missingColumns = [];
        foreach ($requiredColumns as $col) {
            if (!in_array($col, $columnNames)) {
                $missingColumns[] = $col;
            }
        }
        
        return $missingColumns;
    } catch (PDOException $e) {
        return ['error' => $e->getMessage()];
    }
}

$missingColumns = checkEnhancedMigrationNeeded();
$migrationResult = null;

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['run_migration'])) {
    try {
        // Add new columns to products table
        $migrations = [
            "ALTER TABLE products ADD COLUMN image_url_4 VARCHAR(500) DEFAULT NULL AFTER image_url_3",
            "ALTER TABLE products ADD COLUMN image_url_5 VARCHAR(500) DEFAULT NULL AFTER image_url_4",
            "ALTER TABLE products ADD COLUMN video_url_1 VARCHAR(500) DEFAULT NULL AFTER video_url",
            "ALTER TABLE products ADD COLUMN video_url_2 VARCHAR(500) DEFAULT NULL AFTER video_url_1",
            "ALTER TABLE products ADD COLUMN video_url_3 VARCHAR(500) DEFAULT NULL AFTER video_url_2"
        ];
        
        $addedColumns = [];
        $skippedColumns = [];
        
        foreach ($migrations as $sql) {
            try {
                $pdo->exec($sql);
                // Extract column name from SQL
                preg_match('/ADD COLUMN (\w+)/', $sql, $matches);
                if (isset($matches[1])) {
                    $addedColumns[] = $matches[1];
                }
            } catch (PDOException $e) {
                // Check if column already exists
                if ($e->getCode() == '42S21') { // Duplicate column name
                    preg_match('/ADD COLUMN (\w+)/', $sql, $matches);
                    if (isset($matches[1])) {
                        $skippedColumns[] = $matches[1];
                    }
                } else {
                    throw $e;
                }
            }
        }
        
        $migrationResult = 'success';
        
    } catch (PDOException $e) {
        $migrationResult = 'error';
        $errorMessage = $e->getMessage();
    }
}

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-database-up"></i> تحديث جدول المنتجات المحسن
                    </h5>
                </div>
                
                <div class="card-body">
                    <?php if ($migrationResult == 'success'): ?>
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i>
                            <strong>تم تحديث جدول المنتجات بنجاح!</strong>
                            <br><br>
                            <h6>الأعمدة المضافة:</h6>
                            <ul class="mb-2">
                                <?php if (!empty($addedColumns)): ?>
                                    <?php foreach ($addedColumns as $col): ?>
                                        <li><strong><?php echo $col; ?></strong> - تم إضافته بنجاح</li>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                                <?php if (!empty($skippedColumns)): ?>
                                    <?php foreach ($skippedColumns as $col): ?>
                                        <li><strong><?php echo $col; ?></strong> - موجود مسبقاً (تم تخطيه)</li>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </ul>
                            
                            <h6>الميزات الجديدة:</h6>
                            <ul class="mb-0">
                                <li>دعم 5 روابط صور للمنتج الواحد</li>
                                <li>دعم 3 روابط فيديو للمنتج الواحد</li>
                                <li>معرض صور محسن في صفحة المنتج</li>
                                <li>عرض متعدد للفيديوهات</li>
                            </ul>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <a href="products.php" class="btn btn-primary">
                                <i class="bi bi-arrow-left"></i> العودة إلى إدارة المنتجات
                            </a>
                            <a href="add_product.php" class="btn btn-success">
                                <i class="bi bi-plus-circle"></i> إضافة منتج جديد
                            </a>
                        </div>
                        
                    <?php elseif ($migrationResult == 'error'): ?>
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>حدث خطأ أثناء التحديث:</strong>
                            <br><?php echo htmlspecialchars($errorMessage); ?>
                        </div>
                        
                        <a href="products.php" class="btn btn-secondary">العودة إلى إدارة المنتجات</a>
                        
                    <?php elseif (!empty($missingColumns) && !isset($missingColumns['error'])): ?>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>يحتاج جدول المنتجات إلى تحديث لإضافة الميزات الجديدة:</strong>
                            <br><br>
                            
                            <h6>الأعمدة المطلوب إضافتها:</h6>
                            <ul class="mb-3">
                                <?php foreach ($missingColumns as $col): ?>
                                    <li><strong><?php echo $col; ?></strong> - 
                                        <?php 
                                        if (strpos($col, 'image_url') !== false) {
                                            echo 'رابط صورة إضافية';
                                        } elseif (strpos($col, 'video_url') !== false) {
                                            echo 'رابط فيديو';
                                        }
                                        ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                            
                            <h6>الميزات الجديدة التي ستتم إضافتها:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">إدارة الصور المحسنة:</h6>
                                    <ul>
                                        <li>5 حقول لروابط الصور الخارجية</li>
                                        <li>معرض صور تفاعلي</li>
                                        <li>معاينة الصور في النماذج</li>
                                        <li>دعم أفضل للصور عالية الجودة</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-success">إدارة الفيديوهات المحسنة:</h6>
                                    <ul>
                                        <li>3 حقول لروابط الفيديوهات</li>
                                        <li>دعم YouTube و Vimeo</li>
                                        <li>تشغيل متعدد للفيديوهات</li>
                                        <li>عرض منظم في تبويبات</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <form method="POST">
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                                <strong>تنبيه:</strong> تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل التحديث.
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button type="submit" name="run_migration" class="btn btn-primary btn-lg" 
                                        onclick="return confirm('هل أنت متأكد من تحديث جدول المنتجات؟\n\nسيتم إضافة الأعمدة الجديدة للميزات المحسنة.')">
                                    <i class="bi bi-database-up"></i> تحديث الجدول
                                </button>
                                <a href="products.php" class="btn btn-secondary">إلغاء</a>
                            </div>
                        </form>
                        
                    <?php elseif (isset($missingColumns['error'])): ?>
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>خطأ في فحص قاعدة البيانات:</strong>
                            <br><?php echo htmlspecialchars($missingColumns['error']); ?>
                        </div>
                        
                    <?php else: ?>
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i>
                            <strong>جدول المنتجات محدث ويحتوي على جميع الميزات المحسنة!</strong>
                            <br><br>
                            <p class="mb-0">يمكنك الآن استخدام:</p>
                            <ul class="mb-0">
                                <li>5 روابط صور للمنتج الواحد</li>
                                <li>3 روابط فيديو للمنتج الواحد</li>
                                <li>معرض الصور المحسن</li>
                                <li>عرض الفيديوهات المتعددة</li>
                            </ul>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <a href="products.php" class="btn btn-primary">
                                <i class="bi bi-arrow-left"></i> العودة إلى إدارة المنتجات
                            </a>
                            <a href="add_product.php" class="btn btn-success">
                                <i class="bi bi-plus-circle"></i> إضافة منتج جديد
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
