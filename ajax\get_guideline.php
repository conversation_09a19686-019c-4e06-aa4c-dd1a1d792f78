<?php
require_once 'config/config.php';

header('Content-Type: application/json');

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف الإرشاد غير صحيح']);
    exit();
}

$id = (int)$_GET['id'];

try {
    $guideline = fetchOne("SELECT * FROM guidelines WHERE id = ? AND status = 'published'", [$id]);
    
    if (!$guideline) {
        echo json_encode(['success' => false, 'message' => 'الإرشاد غير موجود']);
        exit();
    }
    
    // إنشاء HTML للعرض
    $html = generateGuidelineHTML($guideline);
    
    echo json_encode([
        'success' => true,
        'html' => $html
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في جلب البيانات'
    ]);
}

function generateGuidelineHTML($guideline) {
    $html = '<div class="guideline-detail">';
    
    // Header with badges
    $html .= '<div class="d-flex justify-content-between align-items-start mb-4">';
    $html .= '<div>';
    $html .= '<h3>' . htmlspecialchars($guideline['title']) . '</h3>';
    if ($guideline['short_description']) {
        $html .= '<p class="text-muted">' . htmlspecialchars($guideline['short_description']) . '</p>';
    }
    $html .= '</div>';
    
    // Badges
    $html .= '<div class="text-end">';
    
    // Icon
    $iconClasses = [
        'warning' => 'bi-exclamation-triangle-fill text-warning',
        'info' => 'bi-info-circle-fill text-info',
        'tip' => 'bi-lightbulb-fill text-success',
        'steps' => 'bi-list-ol text-primary',
        'danger' => 'bi-shield-exclamation text-danger',
        'success' => 'bi-check-circle-fill text-success'
    ];
    $iconClass = $iconClasses[$guideline['icon_type']] ?? 'bi-info-circle-fill text-info';
    $html .= '<i class="' . $iconClass . ' fs-2 mb-2"></i><br>';
    
    // Importance badge
    $importanceColors = ['critical' => 'danger', 'important' => 'warning', 'normal' => 'info'];
    $importanceNames = ['critical' => 'حرج', 'important' => 'مهم', 'normal' => 'عادي'];
    $html .= '<span class="badge bg-' . $importanceColors[$guideline['importance_level']] . ' me-2">';
    $html .= $importanceNames[$guideline['importance_level']] . '</span>';
    
    // Difficulty badge
    $difficultyColors = ['easy' => 'success', 'medium' => 'warning', 'hard' => 'danger'];
    $difficultyNames = ['easy' => 'سهل', 'medium' => 'متوسط', 'hard' => 'صعب'];
    $html .= '<span class="badge bg-' . $difficultyColors[$guideline['difficulty_level']] . '">';
    $html .= $difficultyNames[$guideline['difficulty_level']] . '</span>';
    
    $html .= '</div>';
    $html .= '</div>';
    
    // Main content
    if ($guideline['content']) {
        $html .= '<div class="mb-4">';
        $html .= '<div class="content-text">';
        $html .= nl2br(htmlspecialchars($guideline['content']));
        $html .= '</div>';
        $html .= '</div>';
    }
    
    // Video (if exists)
    if ($guideline['video_url']) {
        $html .= '<div class="mb-4">';
        $html .= '<h5 class="text-danger"><i class="bi bi-play-circle"></i> فيديو تعليمي</h5>';
        $html .= '<div class="ratio ratio-16x9 mb-3">';
        
        $videoUrl = $guideline['video_url'];
        $platform = $guideline['video_platform'] ?? 'youtube';
        
        if ($platform === 'youtube' && (strpos($videoUrl, 'youtube.com') !== false || strpos($videoUrl, 'youtu.be') !== false)) {
            // Extract YouTube video ID
            preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $videoUrl, $matches);
            if (isset($matches[1])) {
                $videoId = $matches[1];
                $html .= '<iframe src="https://www.youtube.com/embed/' . $videoId . '" allowfullscreen></iframe>';
            }
        } else {
            $html .= '<div class="d-flex align-items-center justify-content-center bg-light">';
            $html .= '<a href="' . htmlspecialchars($videoUrl) . '" target="_blank" class="btn btn-danger">';
            $html .= '<i class="bi bi-play-circle"></i> مشاهدة الفيديو';
            $html .= '</a>';
            $html .= '</div>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
    }
    
    // Steps (if exists)
    if ($guideline['steps']) {
        $steps = json_decode($guideline['steps'], true);
        if (is_array($steps) && !empty($steps)) {
            $html .= '<div class="mb-4">';
            $html .= '<h5 class="text-primary"><i class="bi bi-list-ol"></i> الخطوات التفصيلية</h5>';
            $html .= '<div class="steps-container">';
            foreach ($steps as $index => $step) {
                if (!empty(trim($step))) {
                    $html .= '<div class="step-item d-flex align-items-start mb-3">';
                    $html .= '<div class="step-number bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-weight: bold;">';
                    $html .= ($index + 1);
                    $html .= '</div>';
                    $html .= '<div class="step-content flex-grow-1">';
                    $html .= '<p class="mb-0">' . htmlspecialchars($step) . '</p>';
                    $html .= '</div>';
                    $html .= '</div>';
                }
            }
            $html .= '</div>';
            $html .= '</div>';
        }
    }
    
    // Tips (if exists)
    if ($guideline['tips']) {
        $tips = json_decode($guideline['tips'], true);
        if (is_array($tips) && !empty($tips)) {
            $html .= '<div class="mb-4">';
            $html .= '<h5 class="text-success"><i class="bi bi-lightbulb"></i> نصائح مفيدة</h5>';
            $html .= '<div class="tips-container">';
            foreach ($tips as $tip) {
                if (!empty(trim($tip))) {
                    $html .= '<div class="alert alert-success d-flex align-items-center mb-2">';
                    $html .= '<i class="bi bi-lightbulb-fill me-2"></i>';
                    $html .= '<span>' . htmlspecialchars($tip) . '</span>';
                    $html .= '</div>';
                }
            }
            $html .= '</div>';
            $html .= '</div>';
        }
    }
    
    // Warnings (if exists)
    if ($guideline['warnings']) {
        $warnings = json_decode($guideline['warnings'], true);
        if (is_array($warnings) && !empty($warnings)) {
            $html .= '<div class="mb-4">';
            $html .= '<h5 class="text-danger"><i class="bi bi-exclamation-triangle"></i> تحذيرات مهمة</h5>';
            $html .= '<div class="warnings-container">';
            foreach ($warnings as $warning) {
                if (!empty(trim($warning))) {
                    $html .= '<div class="alert alert-danger d-flex align-items-center mb-2">';
                    $html .= '<i class="bi bi-exclamation-triangle-fill me-2"></i>';
                    $html .= '<span>' . htmlspecialchars($warning) . '</span>';
                    $html .= '</div>';
                }
            }
            $html .= '</div>';
            $html .= '</div>';
        }
    }
    
    // Usage stage info
    $html .= '<div class="border-top pt-3 mt-4">';
    $html .= '<div class="row text-muted small">';
    $html .= '<div class="col-md-6">';
    $html .= '<i class="bi bi-info-circle"></i> <strong>مرحلة الاستخدام:</strong> ';
    $usageStages = [
        'general' => 'عام',
        'pre_purchase' => 'قبل الشراء',
        'post_purchase' => 'بعد الشراء',
        'maintenance' => 'الصيانة'
    ];
    $html .= $usageStages[$guideline['usage_stage']] ?? 'غير محدد';
    $html .= '</div>';
    $html .= '<div class="col-md-6 text-end">';
    $html .= '<i class="bi bi-calendar"></i> <strong>تاريخ النشر:</strong> ';
    $html .= date('Y/m/d', strtotime($guideline['published_at'] ?? $guideline['created_at']));
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</div>';
    
    $html .= '</div>';
    
    return $html;
}
?>
