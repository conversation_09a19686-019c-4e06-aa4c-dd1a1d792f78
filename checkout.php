<?php
// Handle all redirects and processing BEFORE any output
require_once 'config/config.php';

// التحقق من وجود منتجات في السلة
$cart = getCart();
if (empty($cart)) {
    $_SESSION['message'] = [
        'text' => 'سلة التسوق فارغة. يرجى إضافة منتجات قبل إتمام الطلب.',
        'type' => 'error'
    ];
    header('Location: ' . SITE_URL . '/cart.php');
    exit();
}

// معالجة إرسال الطلب
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['place_order'])) {
    error_log("=== CHECKOUT PROCESS STARTED ===");
    error_log("POST data: " . json_encode($_POST, JSON_UNESCAPED_UNICODE));
    
    // التحقق من وجود السلة مرة أخرى
    $cart = getCart();
    if (empty($cart)) {
        error_log("CHECKOUT ERROR: Cart is empty during form submission");
        $_SESSION['message'] = [
            'text' => 'سلة التسوق فارغة. يرجى إضافة منتجات قبل إتمام الطلب.',
            'type' => 'error'
        ];
        header('Location: ' . SITE_URL . '/cart.php');
        exit();
    }
    
    // استخراج وتنظيف بيانات النموذج
    $customerName = sanitizeInput($_POST['customer_name']);
    $customerPhone = sanitizeInput($_POST['customer_phone']);
    $address = sanitizeInput($_POST['address']);
    $province = sanitizeInput($_POST['province']);
    $paymentMethod = sanitizeInput($_POST['payment_method']);
    $notes = sanitizeInput($_POST['notes']);

    $errors = [];

    // التحقق من البيانات المطلوبة
    if (empty($customerName)) $errors[] = 'الاسم مطلوب';
    if (empty($customerPhone)) $errors[] = 'رقم الهاتف مطلوب';
    if (empty($address)) $errors[] = 'العنوان مطلوب';
    if (empty($province)) $errors[] = 'المحافظة مطلوبة';

    if (empty($errors)) {
        try {
            // بدء المعاملة
            $pdo->beginTransaction();
            error_log("Transaction started");

            // جلب تفاصيل المنتجات
            $productIds = array_keys($cart);
            $placeholders = str_repeat('?,', count($productIds) - 1) . '?';
            
            $products = fetchAll("
                SELECT id, name, price, discount, image, stock 
                FROM products 
                WHERE id IN ($placeholders) AND status = 'active'
            ", $productIds);

            if (empty($products)) {
                throw new Exception('لم يتم العثور على المنتجات المطلوبة');
            }

            // حساب تفاصيل السلة والمجاميع
            $cartItems = [];
            $subtotal = 0;

            foreach ($products as $product) {
                $quantity = $cart[$product['id']];
                $price = $product['price'];
                
                // تطبيق الخصم إن وجد
                if ($product['discount'] > 0) {
                    $price = $price - ($price * $product['discount'] / 100);
                }
                
                // التحقق من توفر المخزون
                if ($product['stock'] < $quantity) {
                    throw new Exception("المنتج '{$product['name']}' غير متوفر بالكمية المطلوبة. المتوفر: {$product['stock']}");
                }
                
                $total = $price * $quantity;
                $subtotal += $total;
                
                $cartItems[] = [
                    'product' => $product,
                    'quantity' => $quantity,
                    'price' => $price,
                    'total' => $total
                ];
            }

            // حساب تكلفة التوصيل
            $deliveryPrices = [
                'بغداد' => 5000,
                'البصرة' => 8000,
                'نينوى' => 10000,
                'أربيل' => 10000,
                'النجف' => 7000,
                'كربلاء' => 7000,
                'الأنبار' => 12000,
                'ذي قار' => 9000,
                'بابل' => 6000,
                'كركوك' => 9000,
                'ديالى' => 8000,
                'المثنى' => 10000,
                'القادسية' => 8000,
                'ميسان' => 9000,
                'واسط' => 7000,
                'دهوك' => 12000,
                'السليمانية' => 11000,
                'صلاح الدين' => 9000
            ];

            $finalDeliveryPrice = $deliveryPrices[$province] ?? 10000;
            
            // التحقق من التوصيل المجاني
            $freeDeliveryThreshold = 100000;
            if ($subtotal >= $freeDeliveryThreshold) {
                $finalDeliveryPrice = 0;
            }

            // معالجة كود الخصم
            $discountAmount = 0;
            $appliedDiscount = $_SESSION['applied_discount'] ?? null;
            if ($appliedDiscount) {
                $discountAmount = $appliedDiscount['amount'];
            }

            $finalTotal = $subtotal - $discountAmount + $finalDeliveryPrice;

            // إدراج الطلب
            $orderData = [
                'customer_name' => $customerName,
                'customer_phone' => $customerPhone,
                'address' => $address,
                'province' => $province,
                'subtotal' => $subtotal,
                'delivery_price' => $finalDeliveryPrice,
                'discount_amount' => $discountAmount,
                'total_price' => $finalTotal,
                'payment_method' => $paymentMethod,
                'notes' => $notes,
                'status' => 'pending'
            ];

            error_log("=== ORDER INSERTION ATTEMPT ===");
            error_log("Order data: " . json_encode($orderData, JSON_UNESCAPED_UNICODE));

            $orderId = insertData('orders', $orderData);

            if (!$orderId) {
                $pdoError = $pdo->errorInfo();
                error_log("Failed to insert order. PDO Error: " . json_encode($pdoError));
                throw new Exception('فشل في إنشاء الطلب: ' . ($pdoError[2] ?? 'خطأ غير معروف'));
            }

            error_log("=== ORDER CREATED SUCCESSFULLY ===");
            error_log("Order ID: " . $orderId);

            // إدراج عناصر الطلب
            error_log("=== ORDER ITEMS INSERTION ===");
            $itemsInserted = 0;
            foreach ($cartItems as $item) {
                $orderItemData = [
                    'order_id' => $orderId,
                    'product_id' => $item['product']['id'],
                    'product_name' => $item['product']['name'],
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                    'total' => $item['total']
                ];

                $itemId = insertData('order_items', $orderItemData);
                if (!$itemId) {
                    throw new Exception('فشل في إضافة عناصر الطلب - المنتج: ' . $item['product']['name']);
                }
                
                $itemsInserted++;
                error_log("Order item inserted - ID: " . $itemId);

                // تحديث المخزون
                $newStock = $item['product']['stock'] - $item['quantity'];
                if ($newStock < 0) $newStock = 0;

                $stockUpdateResult = updateData('products', ['stock' => $newStock], 'id = ?', [$item['product']['id']]);
                if ($stockUpdateResult) {
                    error_log("Stock updated for product ID: " . $item['product']['id'] . " - New stock: " . $newStock);
                }
            }

            error_log("Total order items inserted: " . $itemsInserted);

            // تأكيد المعاملة
            error_log("=== COMMITTING TRANSACTION ===");
            $pdo->commit();
            error_log("Transaction committed successfully");

            // التحقق النهائي من وجود الطلب
            $finalVerification = fetchOne("SELECT id, customer_name, total_price, status FROM orders WHERE id = ?", [$orderId]);
            if (!$finalVerification) {
                error_log("CRITICAL ERROR: Order not found after commit - Order ID: " . $orderId);
                throw new Exception('خطأ حرج: لم يتم العثور على الطلب بعد الحفظ');
            }

            // إفراغ السلة وكود الخصم
            error_log("=== CLEARING CART ===");
            clearCart();
            unset($_SESSION['applied_discount']);
            error_log("Cart cleared successfully");

            // تسجيل نجاح العملية الكامل
            error_log("=== ORDER PROCESS COMPLETED SUCCESSFULLY ===");
            error_log("Order ID: " . $orderId . " - Customer: " . $customerName . " - Total: " . number_format($finalTotal) . " IQD");

            // إعداد رسالة نجاح
            $_SESSION['order_success'] = [
                'order_id' => $orderId,
                'customer_name' => $customerName,
                'total_price' => $finalTotal,
                'message' => 'تم إنشاء طلبك بنجاح! رقم الطلب: ' . $orderId
            ];

            // إعادة توجيه إلى صفحة تأكيد الطلب
            error_log("Redirecting to order success page");
            header('Location: ' . SITE_URL . '/order-success.php?order=' . $orderId);
            exit();

        } catch (Exception $e) {
            // التراجع عن المعاملة في حالة الخطأ
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
                error_log("Transaction rolled back due to error");
            }
            
            error_log("Checkout error: " . $e->getMessage());
            $errors[] = $e->getMessage();
        }
    }

    // في حالة وجود أخطاء، عرضها للمستخدم
    if (!empty($errors)) {
        $_SESSION['checkout_errors'] = $errors;
        error_log("Checkout errors: " . json_encode($errors, JSON_UNESCAPED_UNICODE));
    }
}

// جلب تفاصيل المنتجات للعرض
$productIds = array_keys($cart);
$placeholders = str_repeat('?,', count($productIds) - 1) . '?';

$products = fetchAll("
    SELECT id, name, price, discount, image, stock 
    FROM products 
    WHERE id IN ($placeholders) AND status = 'active'
", $productIds);

// حساب تفاصيل السلة للعرض
$cartItems = [];
$subtotal = 0;

foreach ($products as $product) {
    $quantity = $cart[$product['id']];
    $price = $product['price'];
    
    // تطبيق الخصم إن وجد
    if ($product['discount'] > 0) {
        $price = $price - ($price * $product['discount'] / 100);
    }
    
    $total = $price * $quantity;
    $subtotal += $total;
    
    $cartItems[] = [
        'product' => $product,
        'quantity' => $quantity,
        'price' => $price,
        'total' => $total
    ];
}

// حساب تكلفة التوصيل للعرض
$deliveryPrices = [
    'بغداد' => 5000,
    'البصرة' => 8000,
    'نينوى' => 10000,
    'أربيل' => 10000,
    'النجف' => 7000,
    'كربلاء' => 7000,
    'الأنبار' => 12000,
    'ذي قار' => 9000,
    'بابل' => 6000,
    'كركوك' => 9000,
    'ديالى' => 8000,
    'المثنى' => 10000,
    'القادسية' => 8000,
    'ميسان' => 9000,
    'واسط' => 7000,
    'دهوك' => 12000,
    'السليمانية' => 11000,
    'صلاح الدين' => 9000
];

$defaultDeliveryPrice = 5000; // بغداد كافتراضي
$freeDeliveryThreshold = 100000;

// معالجة كود الخصم المطبق
$appliedDiscount = $_SESSION['applied_discount'] ?? null;
$discountAmount = $appliedDiscount['amount'] ?? 0;

// Now include header after all processing
$pageTitle = 'إتمام الطلب';
require_once 'includes/header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="bg-light py-3">
    <div class="container">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="<?= SITE_URL ?>">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="<?= SITE_URL ?>/cart.php">سلة التسوق</a></li>
            <li class="breadcrumb-item active" aria-current="page">إتمام الطلب</li>
        </ol>
    </div>
</nav>

<!-- Error Messages -->
<?php if (isset($_SESSION['checkout_errors'])): ?>
    <div class="container mt-3">
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6><i class="bi bi-exclamation-triangle"></i> يرجى تصحيح الأخطاء التالية:</h6>
            <ul class="mb-0">
                <?php foreach ($_SESSION['checkout_errors'] as $error): ?>
                    <li><?= htmlspecialchars($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
    <?php unset($_SESSION['checkout_errors']); ?>
<?php endif; ?>

<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="bi bi-credit-card"></i>
                إتمام الطلب
            </h2>
        </div>
    </div>

    <div class="row">
        <!-- Checkout Form -->
        <div class="col-lg-8">
            <form id="checkoutForm" method="POST" action="checkout.php">
                <input type="hidden" name="place_order" value="1">

                <!-- Customer Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-person"></i>
                            معلومات العميل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="customer_name" class="form-label">
                                    <strong>اسم العميل *</strong>
                                </label>
                                <input type="text" class="form-control form-control-lg"
                                       id="customer_name" name="customer_name"
                                       value="<?= htmlspecialchars($_POST['customer_name'] ?? '') ?>"
                                       required>
                                <div class="invalid-feedback">
                                    يرجى إدخال اسم العميل
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="customer_phone" class="form-label">
                                    <strong>رقم الهاتف *</strong>
                                </label>
                                <input type="text" class="form-control form-control-lg"
                                       id="customer_phone" name="customer_phone"
                                       value="<?= htmlspecialchars($_POST['customer_phone'] ?? '') ?>"
                                       placeholder="07xxxxxxxxx" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال رقم الهاتف
                                </div>
                                <div class="form-text">
                                    <i class="bi bi-info-circle"></i>
                                    يمكن إدخال رقم الهاتف بأي تنسيق
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Delivery Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-truck"></i>
                            معلومات التوصيل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="address" class="form-label">
                                <strong>العنوان التفصيلي *</strong>
                            </label>
                            <textarea class="form-control form-control-lg"
                                      id="address" name="address" rows="3"
                                      placeholder="يرجى كتابة العنوان التفصيلي مع اسم المنطقة والشارع"
                                      required><?= htmlspecialchars($_POST['address'] ?? '') ?></textarea>
                            <div class="invalid-feedback">
                                يرجى إدخال العنوان التفصيلي
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="province" class="form-label">
                                <strong>المحافظة *</strong>
                            </label>
                            <select class="form-control form-control-lg" id="province" name="province" required>
                                <option value="">اختر المحافظة</option>
                                <option value="بغداد" <?= ($_POST['province'] ?? '') == 'بغداد' ? 'selected' : '' ?>>بغداد</option>
                                <option value="البصرة" <?= ($_POST['province'] ?? '') == 'البصرة' ? 'selected' : '' ?>>البصرة</option>
                                <option value="نينوى" <?= ($_POST['province'] ?? '') == 'نينوى' ? 'selected' : '' ?>>نينوى</option>
                                <option value="أربيل" <?= ($_POST['province'] ?? '') == 'أربيل' ? 'selected' : '' ?>>أربيل</option>
                                <option value="النجف" <?= ($_POST['province'] ?? '') == 'النجف' ? 'selected' : '' ?>>النجف</option>
                                <option value="كربلاء" <?= ($_POST['province'] ?? '') == 'كربلاء' ? 'selected' : '' ?>>كربلاء</option>
                                <option value="الأنبار" <?= ($_POST['province'] ?? '') == 'الأنبار' ? 'selected' : '' ?>>الأنبار</option>
                                <option value="ذي قار" <?= ($_POST['province'] ?? '') == 'ذي قار' ? 'selected' : '' ?>>ذي قار</option>
                                <option value="بابل" <?= ($_POST['province'] ?? '') == 'بابل' ? 'selected' : '' ?>>بابل</option>
                                <option value="كركوك" <?= ($_POST['province'] ?? '') == 'كركوك' ? 'selected' : '' ?>>كركوك</option>
                                <option value="ديالى" <?= ($_POST['province'] ?? '') == 'ديالى' ? 'selected' : '' ?>>ديالى</option>
                                <option value="المثنى" <?= ($_POST['province'] ?? '') == 'المثنى' ? 'selected' : '' ?>>المثنى</option>
                                <option value="القادسية" <?= ($_POST['province'] ?? '') == 'القادسية' ? 'selected' : '' ?>>القادسية</option>
                                <option value="ميسان" <?= ($_POST['province'] ?? '') == 'ميسان' ? 'selected' : '' ?>>ميسان</option>
                                <option value="واسط" <?= ($_POST['province'] ?? '') == 'واسط' ? 'selected' : '' ?>>واسط</option>
                                <option value="دهوك" <?= ($_POST['province'] ?? '') == 'دهوك' ? 'selected' : '' ?>>دهوك</option>
                                <option value="السليمانية" <?= ($_POST['province'] ?? '') == 'السليمانية' ? 'selected' : '' ?>>السليمانية</option>
                                <option value="صلاح الدين" <?= ($_POST['province'] ?? '') == 'صلاح الدين' ? 'selected' : '' ?>>صلاح الدين</option>
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار المحافظة
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>تكلفة التوصيل:</strong>
                            <span id="deliveryInfo">يتم حساب تكلفة التوصيل حسب المحافظة المختارة</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="bi bi-credit-card"></i>
                            معلومات الدفع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="payment_method" class="form-label">
                                <strong>طريقة الدفع</strong>
                            </label>
                            <select class="form-control form-control-lg" id="payment_method" name="payment_method">
                                <option value="cash_on_delivery" <?= ($_POST['payment_method'] ?? 'cash_on_delivery') == 'cash_on_delivery' ? 'selected' : '' ?>>
                                    الدفع عند الاستلام
                                </option>
                                <option value="bank_transfer" <?= ($_POST['payment_method'] ?? '') == 'bank_transfer' ? 'selected' : '' ?>>
                                    تحويل بنكي
                                </option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">
                                <strong>ملاحظات إضافية</strong>
                            </label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"
                                      placeholder="أي ملاحظات أو تعليمات خاصة للتوصيل"><?= htmlspecialchars($_POST['notes'] ?? '') ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="d-grid">
                    <button type="submit" id="submitBtn" class="btn btn-success btn-lg">
                        <span class="btn-text">
                            <i class="bi bi-check-circle"></i>
                            تأكيد الطلب
                        </span>
                        <span class="btn-loading" style="display: none;">
                            <i class="bi bi-hourglass-split"></i>
                            جاري معالجة الطلب...
                        </span>
                    </button>
                </div>
            </form>
        </div>

        <!-- Order Summary -->
        <div class="col-lg-4">
            <div class="card shadow-sm sticky-top" style="top: 20px;">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-receipt"></i>
                        ملخص الطلب
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Cart Items -->
                    <div class="mb-3">
                        <h6 class="border-bottom pb-2">المنتجات المطلوبة:</h6>
                        <?php foreach ($cartItems as $item): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="flex-grow-1">
                                    <div class="fw-bold small"><?= htmlspecialchars($item['product']['name']) ?></div>
                                    <div class="text-muted small">
                                        <?= number_format($item['price']) ?> × <?= $item['quantity'] ?>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <span class="fw-bold"><?= number_format($item['total']) ?> دينار</span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <hr>

                    <!-- Order Totals -->
                    <div class="d-flex justify-content-between mb-2">
                        <span>المجموع الفرعي:</span>
                        <span id="subtotalDisplay"><?= number_format($subtotal) ?> دينار</span>
                    </div>

                    <?php if ($discountAmount > 0): ?>
                        <div class="d-flex justify-content-between mb-2 text-success">
                            <span>الخصم:</span>
                            <span>-<?= number_format($discountAmount) ?> دينار</span>
                        </div>
                    <?php endif; ?>

                    <div class="d-flex justify-content-between mb-2">
                        <span>تكلفة التوصيل:</span>
                        <span id="deliveryDisplay"><?= number_format($defaultDeliveryPrice) ?> دينار</span>
                    </div>

                    <?php if ($subtotal < $freeDeliveryThreshold): ?>
                        <div class="alert alert-info small">
                            <i class="bi bi-info-circle"></i>
                            أضف <?= number_format($freeDeliveryThreshold - $subtotal) ?> دينار للحصول على توصيل مجاني
                        </div>
                    <?php endif; ?>

                    <hr>

                    <div class="d-flex justify-content-between mb-3">
                        <strong>المجموع الكلي:</strong>
                        <strong class="text-primary" id="totalDisplay">
                            <?= number_format($subtotal - $discountAmount + $defaultDeliveryPrice) ?> دينار
                        </strong>
                    </div>

                    <!-- Security Notice -->
                    <div class="alert alert-success small">
                        <i class="bi bi-shield-check"></i>
                        <strong>تسوق آمن:</strong> معلوماتك محمية بأعلى معايير الأمان
                    </div>

                    <!-- Back to Cart -->
                    <div class="d-grid">
                        <a href="<?= SITE_URL ?>/cart.php" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right"></i>
                            العودة إلى السلة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Delivery prices for each province
const deliveryPrices = {
    'بغداد': 5000,
    'البصرة': 8000,
    'نينوى': 10000,
    'أربيل': 10000,
    'النجف': 7000,
    'كربلاء': 7000,
    'الأنبار': 12000,
    'ذي قار': 9000,
    'بابل': 6000,
    'كركوك': 9000,
    'ديالى': 8000,
    'المثنى': 10000,
    'القادسية': 8000,
    'ميسان': 9000,
    'واسط': 7000,
    'دهوك': 12000,
    'السليمانية': 11000,
    'صلاح الدين': 9000
};

const subtotal = <?= $subtotal ?>;
const discountAmount = <?= $discountAmount ?>;
const freeDeliveryThreshold = <?= $freeDeliveryThreshold ?>;

// Update delivery price when province changes
document.getElementById('province').addEventListener('change', function() {
    const selectedProvince = this.value;
    const deliveryDisplay = document.getElementById('deliveryDisplay');
    const totalDisplay = document.getElementById('totalDisplay');
    const deliveryInfo = document.getElementById('deliveryInfo');

    if (selectedProvince && deliveryPrices[selectedProvince]) {
        let deliveryPrice = deliveryPrices[selectedProvince];

        // Check for free delivery
        if (subtotal >= freeDeliveryThreshold) {
            deliveryPrice = 0;
        }

        // Update displays
        if (deliveryPrice === 0) {
            deliveryDisplay.innerHTML = '<span class="text-success">مجاني</span>';
            deliveryInfo.innerHTML = 'توصيل مجاني للطلبات أكثر من ' + new Intl.NumberFormat('ar-IQ').format(freeDeliveryThreshold) + ' دينار';
        } else {
            deliveryDisplay.textContent = new Intl.NumberFormat('ar-IQ').format(deliveryPrice) + ' دينار';
            deliveryInfo.innerHTML = 'تكلفة التوصيل إلى ' + selectedProvince + ': ' + new Intl.NumberFormat('ar-IQ').format(deliveryPrice) + ' دينار';
        }

        // Update total
        const newTotal = subtotal - discountAmount + deliveryPrice;
        totalDisplay.textContent = new Intl.NumberFormat('ar-IQ').format(newTotal) + ' دينار';
    } else {
        deliveryInfo.textContent = 'يتم حساب تكلفة التوصيل حسب المحافظة المختارة';
    }
});

// Form validation and submission
document.getElementById('checkoutForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');
    const requiredFields = this.querySelectorAll('[required]');
    let isValid = true;

    // Validate required fields
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });

    if (!isValid) {
        e.preventDefault();

        // Focus on first invalid field
        const firstInvalidField = this.querySelector('.is-invalid');
        if (firstInvalidField) {
            firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
            firstInvalidField.focus();
        }

        showToast('يرجى ملء جميع الحقول المطلوبة', 'error');
        return false;
    }

    // Show loading state
    console.log('Form validation passed, submitting form...');

    submitBtn.disabled = true;
    if (btnText) btnText.style.display = 'none';
    if (btnLoading) btnLoading.style.display = 'inline-block';

    // Add visual feedback
    this.style.opacity = '0.8';
    this.style.pointerEvents = 'none';

    showToast('جاري معالجة طلبك...', 'info');

    console.log('Form will be submitted to server...');
    return true;
});

// Toast notification function
function showToast(message, type) {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', function(event) {
    console.warn('Unhandled promise rejection prevented:', event.reason);
    event.preventDefault();
});

// Handle JavaScript errors
window.addEventListener('error', function(event) {
    console.warn('JavaScript error handled:', event.error);
});

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Checkout page initialized');

    // Trigger province change to set initial delivery price
    const provinceSelect = document.getElementById('province');
    if (provinceSelect.value) {
        provinceSelect.dispatchEvent(new Event('change'));
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
