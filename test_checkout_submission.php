<?php
/**
 * Test checkout form submission directly
 */

require_once 'config/config.php';

echo "<h2>Checkout Form Submission Test</h2>";

// Simulate adding items to cart first
if (!isset($_SESSION)) {
    session_start();
}

// Add test products to cart
$_SESSION['cart'] = [
    1 => 2,  // Product ID 1, quantity 2
    2 => 1   // Product ID 2, quantity 1
];

echo "<h3>1. Cart Setup</h3>";
echo "Cart contents: " . json_encode($_SESSION['cart']) . "<br>";

// Simulate form submission
$_POST = [
    'place_order' => '1',
    'customer_name' => 'Test Customer Submission',
    'customer_phone' => '07123456789',
    'address' => 'Test Address, Baghdad, Iraq',
    'province' => 'بغداد',
    'payment_method' => 'cash_on_delivery',
    'notes' => 'Test order submission'
];

$_SERVER['REQUEST_METHOD'] = 'POST';

echo "<h3>2. Simulating Form Submission</h3>";
echo "POST data: " . json_encode($_POST, JSON_UNESCAPED_UNICODE) . "<br>";

// Capture any output from checkout processing
ob_start();

// Include the checkout processing logic
try {
    // We need to simulate the checkout process without the full page
    $cart = $_SESSION['cart'] ?? [];
    if (empty($cart)) {
        echo "❌ Cart is empty<br>";
        exit;
    }

    // Get product details
    $productIds = array_keys($cart);
    $placeholders = str_repeat('?,', count($productIds) - 1) . '?';
    
    $products = fetchAll("
        SELECT id, name, price, discount, image, stock 
        FROM products 
        WHERE id IN ($placeholders) AND status = 'active'
    ", $productIds);

    if (empty($products)) {
        echo "❌ No products found<br>";
        exit;
    }

    $cartItems = [];
    $subtotal = 0;

    foreach ($products as $product) {
        $quantity = $cart[$product['id']];
        $price = $product['price'];
        
        // Apply discount if exists
        if ($product['discount'] > 0) {
            $price = $price - ($price * $product['discount'] / 100);
        }
        
        $total = $price * $quantity;
        $subtotal += $total;
        
        $cartItems[] = [
            'product' => $product,
            'quantity' => $quantity,
            'price' => $price,
            'total' => $total
        ];
    }

    echo "✅ Cart items processed: " . count($cartItems) . " items<br>";
    echo "Subtotal: " . number_format($subtotal) . " IQD<br>";

    // Process the order
    $customerName = $_POST['customer_name'];
    $customerPhone = $_POST['customer_phone'];
    $address = $_POST['address'];
    $province = $_POST['province'];
    $paymentMethod = $_POST['payment_method'];
    $notes = $_POST['notes'];

    $errors = [];

    // Validate required fields
    if (empty($customerName)) $errors[] = 'الاسم مطلوب';
    if (empty($customerPhone)) $errors[] = 'رقم الهاتف مطلوب';
    if (empty($address)) $errors[] = 'العنوان مطلوب';
    if (empty($province)) $errors[] = 'المحافظة مطلوبة';

    if (!empty($errors)) {
        echo "❌ Validation errors: " . implode(', ', $errors) . "<br>";
        exit;
    }

    echo "✅ Form validation passed<br>";

    // Calculate delivery price
    $deliveryPrice = 5000; // Default for Baghdad
    $discountAmount = 0;
    $finalTotal = $subtotal - $discountAmount + $deliveryPrice;

    // Prepare order data
    $orderData = [
        'customer_name' => $customerName,
        'customer_phone' => $customerPhone,
        'address' => $address,
        'province' => $province,
        'subtotal' => $subtotal,
        'delivery_price' => $deliveryPrice,
        'discount_amount' => $discountAmount,
        'total_price' => $finalTotal,
        'payment_method' => $paymentMethod,
        'notes' => $notes,
        'status' => 'pending'
    ];

    echo "<h3>3. Order Data</h3>";
    echo "Order data: " . json_encode($orderData, JSON_UNESCAPED_UNICODE) . "<br>";

    // Insert order
    $orderId = insertData('orders', $orderData);

    if ($orderId) {
        echo "✅ Order inserted successfully - Order ID: {$orderId}<br>";

        // Insert order items
        foreach ($cartItems as $item) {
            $orderItemData = [
                'order_id' => $orderId,
                'product_id' => $item['product']['id'],
                'product_name' => $item['product']['name'],
                'quantity' => $item['quantity'],
                'price' => $item['price'],
                'total' => $item['total']
            ];

            $itemId = insertData('order_items', $orderItemData);
            if ($itemId) {
                echo "✅ Order item inserted - Item ID: {$itemId}<br>";
            } else {
                echo "❌ Failed to insert order item for product: " . $item['product']['name'] . "<br>";
            }
        }

        // Verify order exists
        $verifyOrder = fetchOne("SELECT * FROM orders WHERE id = ?", [$orderId]);
        if ($verifyOrder) {
            echo "<h3>4. Order Verification</h3>";
            echo "✅ Order verified in database<br>";
            echo "Customer: " . $verifyOrder['customer_name'] . "<br>";
            echo "Phone: " . $verifyOrder['customer_phone'] . "<br>";
            echo "Total: " . number_format($verifyOrder['total_price']) . " IQD<br>";
            echo "Status: " . $verifyOrder['status'] . "<br>";
            echo "Created: " . $verifyOrder['created_at'] . "<br>";

            echo "<h3>5. Admin View Test</h3>";
            echo "<p><a href='admin/orders.php' target='_blank'>Check Order in Admin Panel</a></p>";
            echo "<p><a href='order-success.php?order={$orderId}' target='_blank'>View Order Success Page</a></p>";

        } else {
            echo "❌ Order not found after insertion<br>";
        }

    } else {
        echo "❌ Failed to insert order<br>";
    }

} catch (Exception $e) {
    echo "❌ Error during checkout process: " . $e->getMessage() . "<br>";
    echo "Error details: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

$output = ob_get_clean();
echo $output;

echo "<h3>6. Test Summary</h3>";
echo "<p>This test simulates the complete checkout process. If successful, the order should appear in the admin panel.</p>";
echo "<p><a href='checkout.php'>Test Real Checkout Page</a></p>";
?>
