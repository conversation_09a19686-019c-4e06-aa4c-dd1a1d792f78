<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>لوحة التحكم - <?php echo getSetting('site_name'); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            transform: translateX(0);
            box-shadow: -5px 0 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            overflow-y: auto;
            overflow-x: hidden;
        }

        .sidebar.hidden {
            transform: translateX(100%);
            box-shadow: none;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.05) 0%, transparent 50%, rgba(255,255,255,0.02) 100%);
            pointer-events: none;
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 15px 20px;
            border-radius: 12px;
            margin: 3px 15px;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
            font-weight: 500;
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .sidebar .nav-link:hover::before {
            left: 100%;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: rgba(255,255,255,0.15);
            color: white;
            transform: translateX(-8px) scale(1.02);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .sidebar .nav-link.active {
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
            border-left: 4px solid #fff;
        }
        
        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            margin-left: 10px;
        }

        /* Custom Scrollbar for Sidebar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            transition: background 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        /* Firefox Scrollbar */
        .sidebar {
            scrollbar-width: thin;
            scrollbar-color: rgba(255,255,255,0.3) rgba(255,255,255,0.1);
        }
        
        .main-content {
            margin-right: 250px;
            transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            min-height: 100vh;
            position: relative;
        }

        .main-content.full-width {
            margin-right: 0;
        }

        .main-content.transitioning {
            overflow-x: hidden;
        }
        
        .top-navbar {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 15px 30px;
            margin-bottom: 30px;
        }
        
        .bg-gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }

        .bg-gradient-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            pointer-events: none;
        }
        
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .card:hover::before {
            opacity: 1;
        }
        
        .btn {
            border-radius: 12px;
            font-weight: 600;
            padding: 10px 20px;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 6px 12px;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
                box-shadow: -5px 0 15px rgba(0,0,0,0.1);
                overflow-y: auto;
                overflow-x: hidden;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .top-navbar {
                padding: 15px;
            }

            body.sidebar-open {
                overflow: hidden;
            }

            body.sidebar-open::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 999;
            }
        }
        
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
        }
        
        .sidebar-brand h4 {
            color: white;
            margin: 0;
            font-weight: 700;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            color: white;
            font-size: 1.3rem;
            padding: 12px;
            border-radius: 10px;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
        }

        .sidebar-toggle::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .sidebar-toggle:hover::before {
            width: 100px;
            height: 100px;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.15);
            color: white;
            transform: scale(1.1);
        }
        
        .user-dropdown .dropdown-toggle::after {
            display: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Enhanced Statistics Cards Styling */
        .stats-icon-container {
            background: linear-gradient(135deg, var(--icon-color-1, #667eea) 0%, var(--icon-color-2, #764ba2) 100%);
            border-radius: 50%;
            padding: 20px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-icon-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .stats-icon-container:hover::before {
            opacity: 1;
            transform: rotate(45deg) translate(50%, 50%);
        }

        .stats-icon-container:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        /* Enhanced warning icon styling for better visual hierarchy */
        .stats-icon-container.warning i {
            position: relative;
            z-index: 2;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            filter: drop-shadow(0 1px 2px rgba(255, 255, 255, 0.1));
        }

        .stats-icon-container.warning:before {
            background: linear-gradient(45deg,
                rgba(255, 255, 255, 0.1),
                rgba(255, 149, 0, 0.1),
                rgba(255, 255, 255, 0.05));
        }

        .stats-icon-container.primary {
            --icon-color-1: #667eea;
            --icon-color-2: #764ba2;
        }

        .stats-icon-container.success {
            --icon-color-1: #56ab2f;
            --icon-color-2: #a8e6cf;
        }

        .stats-icon-container.warning {
            --icon-color-1: #ff9500;
            --icon-color-2: #ff6b35;
            position: relative;
            background: linear-gradient(135deg, var(--icon-color-1) 0%, var(--icon-color-2) 100%);
            box-shadow:
                0 8px 25px rgba(255, 149, 0, 0.25),
                0 4px 12px rgba(255, 107, 53, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .stats-icon-container.warning::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                transparent 50%,
                rgba(255, 149, 0, 0.1) 100%);
            border-radius: inherit;
            pointer-events: none;
        }

        .stats-icon-container.warning:hover {
            transform: translateY(-6px) scale(1.08);
            box-shadow:
                0 15px 40px rgba(255, 149, 0, 0.35),
                0 8px 20px rgba(255, 107, 53, 0.25),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .stats-icon-container.warning:hover::after {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.2) 0%,
                transparent 50%,
                rgba(255, 149, 0, 0.15) 100%);
        }

        .stats-icon-container.info {
            --icon-color-1: #4facfe;
            --icon-color-2: #00f2fe;
        }

        .stats-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            overflow: hidden;
            position: relative;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        }

        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0;
        }

        .stats-label {
            color: #6c757d;
            font-weight: 500;
            font-size: 0.95rem;
        }

        /* Enhanced Welcome Section */
        .welcome-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 25px;
            position: relative;
            overflow: hidden;
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Enhanced Table Styling */
        .table {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        /* ===== PROFESSIONAL PRODUCT MANAGEMENT ENHANCEMENTS ===== */

        /* Professional Button System */
        .btn-professional {
            border-radius: 10px;
            font-weight: 600;
            padding: 12px 24px;
            font-size: 14px;
            line-height: 1.4;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-height: 44px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .btn-professional:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .btn-professional:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .btn-professional:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        /* Primary Button Variant */
        .btn-professional.btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .btn-professional.btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            border-color: #5a6fd8;
            color: white;
        }

        /* Success Button Variant */
        .btn-professional.btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-color: #28a745;
        }

        .btn-professional.btn-success:hover {
            background: linear-gradient(135deg, #218838 0%, #1ba085 100%);
            border-color: #218838;
            color: white;
        }

        /* Danger Button Variant */
        .btn-professional.btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
            color: white;
            border-color: #dc3545;
        }

        .btn-professional.btn-danger:hover {
            background: linear-gradient(135deg, #c82333 0%, #d62c1a 100%);
            border-color: #c82333;
            color: white;
        }

        /* Warning Button Variant */
        .btn-professional.btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #ff9500 100%);
            color: #212529;
            border-color: #ffc107;
        }

        .btn-professional.btn-warning:hover {
            background: linear-gradient(135deg, #e0a800 0%, #e68500 100%);
            border-color: #e0a800;
            color: #212529;
        }

        /* Outline Button Variants */
        .btn-professional.btn-outline-primary {
            background: transparent;
            color: #667eea;
            border-color: #667eea;
        }

        .btn-professional.btn-outline-primary:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .btn-professional.btn-outline-secondary {
            background: transparent;
            color: #6c757d;
            border-color: #6c757d;
        }

        .btn-professional.btn-outline-secondary:hover {
            background: #6c757d;
            color: white;
            border-color: #6c757d;
        }

        /* Button Sizes */
        .btn-professional.btn-sm {
            padding: 8px 16px;
            font-size: 12px;
            min-height: 36px;
        }

        .btn-professional.btn-lg {
            padding: 16px 32px;
            font-size: 16px;
            min-height: 52px;
        }

        /* Button Groups */
        .btn-group-professional {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            align-items: center;
        }

        .btn-group-professional .btn-professional {
            margin: 0;
        }

        /* Professional Form Styling */
        .form-professional {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
        }

        .form-professional .form-label {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-professional .form-control {
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .form-professional .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
            outline: none;
        }

        .form-professional .form-control:hover {
            border-color: #cbd5e0;
            background: white;
        }

        .form-professional .form-select {
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .form-professional .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
            outline: none;
        }

        .form-professional textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }

        /* Form Groups */
        .form-group-professional {
            margin-bottom: 24px;
        }

        .form-group-professional:last-child {
            margin-bottom: 0;
        }

        /* Form Row */
        .form-row-professional {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        /* Professional Alert System */
        .alert-professional {
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 24px;
            border: none;
            position: relative;
            overflow: hidden;
            font-weight: 500;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }

        .alert-professional::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: currentColor;
        }

        .alert-professional.alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .alert-professional.alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .alert-professional.alert-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            border-left: 4px solid #ffc107;
        }

        .alert-professional.alert-info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }

        .alert-professional .alert-icon {
            font-size: 20px;
            margin-left: 12px;
            vertical-align: middle;
        }

        .alert-professional .alert-content {
            display: inline-block;
            vertical-align: middle;
        }

        .alert-professional .btn-close {
            position: absolute;
            top: 12px;
            left: 12px;
            background: none;
            border: none;
            font-size: 18px;
            opacity: 0.6;
            cursor: pointer;
            transition: opacity 0.3s ease;
        }

        .alert-professional .btn-close:hover {
            opacity: 1;
        }

        /* Professional Card System */
        .card-professional {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .card-professional:hover {
            transform: translateY(-4px);
            box-shadow: 0 16px 48px rgba(0,0,0,0.12);
        }

        .card-professional .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid rgba(0,0,0,0.08);
            padding: 20px 24px;
            font-weight: 600;
            color: #2d3748;
        }

        .card-professional .card-body {
            padding: 24px;
        }

        .card-professional .card-footer {
            background: #f8fafc;
            border-top: 1px solid rgba(0,0,0,0.08);
            padding: 16px 24px;
        }

        /* Professional Table System */
        .table-professional {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
        }

        .table-professional thead th {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
            color: white;
            font-weight: 600;
            padding: 16px 20px;
            border: none;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table-professional tbody td {
            padding: 16px 20px;
            border-bottom: 1px solid #e2e8f0;
            vertical-align: middle;
            font-size: 14px;
        }

        .table-professional tbody tr:hover {
            background: #f8fafc;
        }

        .table-professional tbody tr:last-child td {
            border-bottom: none;
        }

        /* Professional Badge System */
        .badge-professional {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .badge-professional.badge-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .badge-professional.badge-danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }

        .badge-professional.badge-warning {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
        }

        .badge-professional.badge-info {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
        }

        .badge-professional.badge-secondary {
            background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
            color: white;
        }

        /* Professional Modal System */
        .modal-professional .modal-dialog {
            max-width: 600px;
            margin: 2rem auto;
        }

        .modal-professional .modal-content {
            border-radius: 16px;
            border: none;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .modal-professional .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 24px;
            border-bottom: none;
        }

        .modal-professional .modal-title {
            font-weight: 600;
            font-size: 18px;
        }

        .modal-professional .modal-body {
            padding: 24px;
        }

        .modal-professional .modal-footer {
            background: #f8fafc;
            padding: 16px 24px;
            border-top: 1px solid rgba(0,0,0,0.08);
        }

        /* Professional Pagination */
        .pagination-professional {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin: 24px 0;
        }

        .pagination-professional .page-link {
            padding: 10px 16px;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            color: #4a5568;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            background: white;
        }

        .pagination-professional .page-link:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .pagination-professional .page-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        /* Professional Search and Filter */
        .search-filter-professional {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
        }

        .search-filter-professional .form-control {
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            padding: 10px 16px;
            transition: all 0.3s ease;
        }

        .search-filter-professional .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* Professional Loading States */
        .loading-professional {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #667eea;
            font-weight: 500;
        }

        .loading-professional .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #e2e8f0;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Professional Responsive Design */
        @media (max-width: 768px) {
            .form-row-professional {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .btn-group-professional {
                flex-direction: column;
                align-items: stretch;
            }

            .btn-professional {
                width: 100%;
                justify-content: center;
            }

            .card-professional .card-body {
                padding: 16px;
            }

            .form-professional {
                padding: 20px;
            }

            .table-professional {
                font-size: 12px;
            }

            .table-professional thead th,
            .table-professional tbody td {
                padding: 12px 8px;
            }
        }

        @media (max-width: 576px) {
            .btn-professional {
                padding: 10px 16px;
                font-size: 13px;
                min-height: 40px;
            }

            .btn-professional.btn-sm {
                padding: 8px 12px;
                font-size: 11px;
                min-height: 32px;
            }

            .alert-professional {
                padding: 12px 16px;
                font-size: 14px;
            }

            .modal-professional .modal-dialog {
                margin: 1rem;
            }
        }

        /* Professional Utility Classes */
        .text-professional-primary { color: #667eea !important; }
        .text-professional-success { color: #28a745 !important; }
        .text-professional-danger { color: #dc3545 !important; }
        .text-professional-warning { color: #ffc107 !important; }
        .text-professional-info { color: #17a2b8 !important; }
        .text-professional-dark { color: #2d3748 !important; }
        .text-professional-muted { color: #718096 !important; }

        .bg-professional-light { background-color: #f8fafc !important; }
        .bg-professional-white { background-color: #ffffff !important; }

        .border-professional { border: 1px solid #e2e8f0 !important; }
        .border-professional-primary { border-color: #667eea !important; }
        .border-professional-success { border-color: #28a745 !important; }
        .border-professional-danger { border-color: #dc3545 !important; }

        .rounded-professional { border-radius: 12px !important; }
        .rounded-professional-lg { border-radius: 16px !important; }
        .rounded-professional-xl { border-radius: 20px !important; }

        .shadow-professional { box-shadow: 0 4px 16px rgba(0,0,0,0.08) !important; }
        .shadow-professional-lg { box-shadow: 0 8px 32px rgba(0,0,0,0.12) !important; }
        .shadow-professional-xl { box-shadow: 0 16px 48px rgba(0,0,0,0.16) !important; }

        /* Professional Animation Classes */
        .fade-in-professional {
            animation: fadeInProfessional 0.5s ease-in-out;
        }

        .slide-up-professional {
            animation: slideUpProfessional 0.5s ease-out;
        }

        @keyframes fadeInProfessional {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUpProfessional {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Professional Focus States */
        .focus-professional:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }

        /* Professional Hover Effects */
        .hover-lift-professional {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .hover-lift-professional:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.12);
        }

        /* Professional Status Indicators */
        .status-indicator-professional {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-indicator-professional.active {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .status-indicator-professional.inactive {
            background: rgba(108, 117, 125, 0.1);
            color: #6c757d;
        }

        .status-indicator-professional.pending {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }

        .status-indicator-professional::before {
            content: '';
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: currentColor;
        }

        /* Professional Confirmation Messages */
        .confirmation-professional {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            border: 1px solid rgba(0,0,0,0.05);
            text-align: center;
            max-width: 400px;
            margin: 0 auto;
        }

        .confirmation-professional .icon {
            font-size: 48px;
            margin-bottom: 16px;
            display: block;
        }

        .confirmation-professional .icon.success { color: #28a745; }
        .confirmation-professional .icon.danger { color: #dc3545; }
        .confirmation-professional .icon.warning { color: #ffc107; }
        .confirmation-professional .icon.info { color: #17a2b8; }

        .confirmation-professional .title {
            font-size: 20px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .confirmation-professional .message {
            color: #718096;
            margin-bottom: 24px;
            line-height: 1.6;
        }

        .confirmation-professional .actions {
            display: flex;
            gap: 12px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .table thead th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            font-weight: 700;
            color: #2c3e50;
            padding: 15px;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
        }

        .table tbody tr {
            transition: all 0.3s ease;
        }

        .table tbody tr:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            transform: scale(1.01);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .table tbody td {
            padding: 15px;
            border: none;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }

        /* Enhanced Badge Styling */
        .badge {
            font-size: 0.75rem;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .badge:hover::before {
            left: 100%;
        }

        /* Quick Actions Enhancement */
        .quick-action-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid transparent;
            border-radius: 20px;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
        }

        .quick-action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .quick-action-card:hover {
            transform: translateY(-10px) scale(1.05);
            border-color: #667eea;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
        }

        .quick-action-card:hover::before {
            opacity: 0.1;
        }

        .quick-action-card .btn {
            position: relative;
            z-index: 2;
        }

        /* Notification Enhancements */
        .notification-row {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .notification-row:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-left-color: #667eea;
            transform: translateX(5px);
        }

        .notification-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .avatar-sm {
            width: 35px;
            height: 35px;
        }

        /* Enhanced No Notifications State */
        .no-notifications {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 20px;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .no-notifications::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-notifications .content {
            position: relative;
            z-index: 2;
        }

        /* Enhanced Animation Keyframes */
        @keyframes ripple {
            0% {
                transform: translate(-50%, -50%) scale(0);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(4);
                opacity: 0;
            }
        }

        @keyframes subtle-shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px); }
            75% { transform: translateX(2px); }
        }

        /* Sidebar Brand Enhancement */
        .sidebar-brand {
            padding: 25px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .sidebar-brand::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .sidebar-brand:hover::before {
            left: 100%;
        }

        .sidebar-brand h4 {
            color: white;
            margin: 0;
            font-weight: 700;
            font-size: 1.3rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        /* Enhanced Mobile Responsiveness */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
                box-shadow: -10px 0 30px rgba(0,0,0,0.2);
                backdrop-filter: blur(15px);
                overflow-y: auto;
                overflow-x: hidden;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .top-navbar {
                padding: 15px;
            }

            body.sidebar-open {
                overflow: hidden;
            }

            body.sidebar-open::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.6);
                z-index: 999;
                backdrop-filter: blur(5px);
                animation: fadeIn 0.3s ease;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
        }

        /* Professional warning container enhancements for consistency */
        .stats-icon-container.warning.mx-auto {
            margin-left: auto !important;
            margin-right: auto !important;
        }

        .stats-icon-container.warning.mb-4 {
            margin-bottom: 1.5rem !important;
        }

        /* Subtle pulsing animation for warning containers to draw attention */
        .stats-icon-container.warning {
            animation: subtle-warning-pulse 3s ease-in-out infinite;
        }

        @keyframes subtle-warning-pulse {
            0%, 100% {
                box-shadow:
                    0 8px 25px rgba(255, 149, 0, 0.25),
                    0 4px 12px rgba(255, 107, 53, 0.15),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
            }
            50% {
                box-shadow:
                    0 8px 25px rgba(255, 149, 0, 0.35),
                    0 4px 12px rgba(255, 107, 53, 0.25),
                    inset 0 1px 0 rgba(255, 255, 255, 0.25);
            }
        }

        /* Pause animation on hover for better UX */
        .stats-icon-container.warning:hover {
            animation-play-state: paused;
        }

        /* Enhanced focus states for accessibility */
        .stats-icon-container.warning:focus-within {
            outline: 2px solid rgba(255, 149, 0, 0.5);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-brand d-flex align-items-center justify-content-between">
            <h4 class="sidebar-title">لوحة التحكم</h4>
            <button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
                <i class="bi bi-x"></i>
            </button>
        </div>
        
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>" 
                   href="dashboard.php">
                    <i class="bi bi-speedometer2"></i>
                    <span class="nav-text">الرئيسية</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'products.php' ? 'active' : ''; ?>" 
                   href="products.php">
                    <i class="bi bi-box"></i>
                    <span class="nav-text">المنتجات</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'orders.php' ? 'active' : ''; ?>" 
                   href="orders.php">
                    <i class="bi bi-cart-check"></i>
                    <span class="nav-text">الطلبات</span>
                    <?php
                    // Get unread orders count
                    $unreadOrdersResult = fetchOne("
                        SELECT COUNT(*) as count
                        FROM orders o
                        LEFT JOIN admin_notification_reads anr ON (anr.notification_type = 'order' AND anr.reference_id = o.id)
                        WHERE o.status = 'pending' AND anr.id IS NULL
                    ");
                    $unreadOrders = ($unreadOrdersResult && isset($unreadOrdersResult['count'])) ? $unreadOrdersResult['count'] : 0;
                    if ($unreadOrders > 0):
                    ?>
                        <span class="notification-badge"><?php echo $unreadOrders; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'categories.php' ? 'active' : ''; ?>" 
                   href="categories.php">
                    <i class="bi bi-tags"></i>
                    <span class="nav-text">التصنيفات</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'reviews.php' ? 'active' : ''; ?>" 
                   href="reviews.php">
                    <i class="bi bi-star"></i>
                    <span class="nav-text">التقييمات</span>
                    <?php
                    // Get unread reviews count
                    $unreadReviewsResult = fetchOne("
                        SELECT COUNT(*) as count
                        FROM reviews r
                        LEFT JOIN admin_notification_reads anr ON (anr.notification_type = 'review' AND anr.reference_id = r.id)
                        WHERE r.status = 'pending' AND anr.id IS NULL
                    ");
                    $unreadReviews = ($unreadReviewsResult && isset($unreadReviewsResult['count'])) ? $unreadReviewsResult['count'] : 0;
                    if ($unreadReviews > 0):
                    ?>
                        <span class="notification-badge"><?php echo $unreadReviews; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'guidelines.php' ? 'active' : ''; ?>"
                   href="guidelines.php">
                    <i class="bi bi-lightbulb"></i>
                    <span class="nav-text">الإرشادات</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'influencers.php' ? 'active' : ''; ?>"
                   href="influencers.php">
                    <i class="bi bi-people"></i>
                    <span class="nav-text">المؤثرين</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'discount_codes.php' ? 'active' : ''; ?>"
                   href="discount_codes.php">
                    <i class="bi bi-percent"></i>
                    <span class="nav-text">أكواد الخصم</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'delivery_pricing.php' ? 'active' : ''; ?>"
                   href="delivery_pricing.php">
                    <i class="bi bi-truck"></i>
                    <span class="nav-text">أسعار التوصيل</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'newsletter.php' ? 'active' : ''; ?>"
                   href="newsletter.php">
                    <i class="bi bi-envelope"></i>
                    <span class="nav-text">النشرة البريدية</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : ''; ?>"
                   href="settings.php">
                    <i class="bi bi-gear"></i>
                    <span class="nav-text">الإعدادات</span>
                </a>
            </li>
            
            <li class="nav-item mt-auto">
                <a class="nav-link" href="../index.php" target="_blank">
                    <i class="bi bi-globe"></i>
                    <span class="nav-text">زيارة الموقع</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link text-danger" href="logout.php" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                    <i class="bi bi-box-arrow-right"></i>
                    <span class="nav-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </nav>
    
    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Top Navbar -->
        <nav class="top-navbar d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <button class="btn btn-outline-secondary me-3 d-md-none" onclick="toggleSidebar()">
                    <i class="bi bi-list"></i>
                </button>
                <button class="btn btn-outline-secondary me-3 d-none d-md-block" onclick="toggleSidebarDesktop()" id="sidebarToggleBtn" title="تبديل الشريط الجانبي">
                    <i class="bi bi-layout-sidebar-inset-reverse" id="sidebarToggleIcon"></i>
                </button>
                <h4 class="mb-0"><?php echo isset($pageTitle) ? $pageTitle : 'لوحة التحكم'; ?></h4>
            </div>
            
            <div class="d-flex align-items-center">
                <!-- Notifications -->
                <div class="dropdown me-3">
                    <button class="btn btn-outline-secondary position-relative" type="button" 
                            data-bs-toggle="dropdown">
                        <i class="bi bi-bell"></i>
                        <?php if ($unreadOrders > 0 || $unreadReviews > 0): ?>
                            <span class="notification-badge"><?php echo $unreadOrders + $unreadReviews; ?></span>
                        <?php endif; ?>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" style="min-width: 300px;">
                        <li class="dropdown-header d-flex justify-content-between align-items-center">
                            <span>الإشعارات</span>
                            <?php if ($unreadOrders > 0 || $unreadReviews > 0): ?>
                                <button class="btn btn-sm btn-outline-primary" onclick="markAllNotificationsRead()" id="markAllReadBtn">
                                    <i class="bi bi-check-all me-1"></i>
                                    تحديد الكل كمقروء
                                </button>
                            <?php endif; ?>
                        </li>
                        <?php if ($unreadOrders > 0 || $unreadReviews > 0): ?>
                            <li><hr class="dropdown-divider"></li>
                        <?php endif; ?>
                        <?php if ($unreadOrders > 0): ?>
                            <li>
                                <a class="dropdown-item d-flex align-items-center py-2" href="orders.php">
                                    <div class="flex-shrink-0">
                                        <div class="bg-warning rounded-circle p-2">
                                            <i class="bi bi-cart-check text-white"></i>
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <div class="fw-bold">طلبات جديدة</div>
                                        <small class="text-muted"><?php echo $unreadOrders; ?> طلب في انتظار المعالجة</small>
                                    </div>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if ($unreadReviews > 0): ?>
                            <li>
                                <a class="dropdown-item d-flex align-items-center py-2" href="reviews.php">
                                    <div class="flex-shrink-0">
                                        <div class="bg-info rounded-circle p-2">
                                            <i class="bi bi-star text-white"></i>
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <div class="fw-bold">تقييمات جديدة</div>
                                        <small class="text-muted"><?php echo $unreadReviews; ?> تقييم في انتظار الموافقة</small>
                                    </div>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if ($unreadOrders == 0 && $unreadReviews == 0): ?>
                            <li>
                                <div class="dropdown-item text-center py-4">
                                    <i class="bi bi-bell-slash text-muted display-6"></i>
                                    <div class="text-muted mt-2">لا توجد إشعارات جديدة</div>
                                </div>
                            </li>
                        <?php endif; ?>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-center text-primary" href="notifications.php">
                                <i class="bi bi-eye me-1"></i>
                                عرض جميع الإشعارات
                            </a>
                        </li>
                    </ul>
                </div>
                
                <!-- User Menu -->
                <div class="dropdown user-dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle d-flex align-items-center" 
                            type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-2"></i>
                        <?php echo htmlspecialchars($_SESSION['admin_username']); ?>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <a class="dropdown-item" href="settings.php">
                                <i class="bi bi-gear me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="logout.php" 
                               onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
        
        <!-- Messages -->
        <?php if (isset($_SESSION['message'])): ?>
            <div class="container-fluid">
                <?php 
                echo showMessage($_SESSION['message']['text'], $_SESSION['message']['type']);
                unset($_SESSION['message']);
                ?>
            </div>
        <?php endif; ?>
        
        <!-- Page Content -->
