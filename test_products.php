<?php
/**
 * اختبار صفحة المنتجات
 * Test Products Page
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>اختبار صفحة المنتجات</h2>";

try {
    // تضمين ملف التكوين
    require_once 'config/config.php';
    
    echo "<p style='color: green;'>✅ تم تضمين ملف التكوين بنجاح</p>";
    
    // اختبار دوال قاعدة البيانات
    echo "<h3>اختبار دوال قاعدة البيانات:</h3>";
    
    // اختبار fetchAll
    $categories = fetchAll("SELECT id, name FROM categories ORDER BY name");
    echo "<p><strong>التصنيفات:</strong> " . (is_array($categories) ? count($categories) . " تصنيف" : "خطأ") . "</p>";
    
    // اختبار fetchOne
    $totalProducts = fetchOne("SELECT COUNT(*) as total FROM products WHERE status = 'active'");
    echo "<p><strong>عدد المنتجات:</strong> " . (is_array($totalProducts) ? $totalProducts['total'] : "0") . "</p>";
    
    // اختبار المنتجات
    $products = fetchAll("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE p.status = 'active' LIMIT 5");
    echo "<p><strong>المنتجات:</strong> " . (is_array($products) ? count($products) . " منتج" : "خطأ") . "</p>";
    
    // عرض التصنيفات
    if (!empty($categories)) {
        echo "<h4>التصنيفات الموجودة:</h4>";
        echo "<ul>";
        foreach ($categories as $cat) {
            echo "<li>" . htmlspecialchars($cat['name']) . " (ID: " . $cat['id'] . ")</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد تصنيفات في قاعدة البيانات</p>";
    }
    
    // عرض المنتجات
    if (!empty($products)) {
        echo "<h4>المنتجات الموجودة:</h4>";
        echo "<ul>";
        foreach ($products as $product) {
            echo "<li>" . htmlspecialchars($product['name']) . " - " . $product['price'] . " دينار عراقي</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد منتجات في قاعدة البيانات</p>";
        echo "<p>يمكنك إضافة منتجات من <a href='admin/login.php'>لوحة التحكم</a></p>";
    }
    
    // اختبار صفحة المنتجات
    echo "<h3>اختبار صفحة المنتجات:</h3>";
    echo "<p><a href='products.php' target='_blank'>فتح صفحة المنتجات</a></p>";
    echo "<p><a href='index.php' target='_blank'>فتح الصفحة الرئيسية</a></p>";
    
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ الاختبار نجح!</h4>";
    echo "<p>دوال قاعدة البيانات تعمل بشكل صحيح الآن.</p>";
    echo "<p>يجب أن تعمل صفحة المنتجات بدون أخطاء.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ في الاختبار</h4>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
    
    echo "<h4>خطوات حل المشكلة:</h4>";
    echo "<ol>";
    echo "<li>تأكد من تشغيل MySQL في XAMPP</li>";
    echo "<li>تأكد من وجود قاعدة البيانات shop_db</li>";
    echo "<li>استخدم <a href='setup_database.php'>أداة إعداد قاعدة البيانات</a></li>";
    echo "<li>تحقق من <a href='test_connection.php'>اختبار الاتصال</a></li>";
    echo "</ol>";
}

// معلومات إضافية
echo "<hr>";
echo "<h3>معلومات النظام:</h3>";
echo "<ul>";
echo "<li><strong>إصدار PHP:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>الوقت:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "<li><strong>المجلد:</strong> " . __DIR__ . "</li>";
echo "</ul>";

echo "<h3>الملفات المطلوبة:</h3>";
$requiredFiles = [
    'config/config.php',
    'config/database.php',
    'products.php',
    'index.php',
    'admin/login.php'
];

echo "<ul>";
foreach ($requiredFiles as $file) {
    $exists = file_exists($file);
    echo "<li><strong>$file:</strong> " . ($exists ? '✅ موجود' : '❌ غير موجود') . "</li>";
}
echo "</ul>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h2, h3, h4 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

hr {
    margin: 30px 0;
    border: none;
    border-top: 2px solid #dee2e6;
}
</style>
