<?php
/**
 * Design Quality and Performance Validation Test
 * Professional Arabic E-commerce Homepage Quality Assurance
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

// Test Results Array
$testResults = [];
$totalScore = 0;
$maxScore = 0;

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار جودة التصميم والأداء</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css' rel='stylesheet'>
    <style>
        body { font-family: 'Cairo', sans-serif; direction: rtl; background: #f8f9fa; }
        .test-section { margin: 2rem 0; padding: 2rem; background: white; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .test-pass { color: #28a745; font-weight: 600; }
        .test-fail { color: #dc3545; font-weight: 600; }
        .test-warning { color: #ffc107; font-weight: 600; }
        .score-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px; padding: 2rem; text-align: center; }
        .progress-ring { transform: rotate(-90deg); }
        .progress-ring-circle { transition: stroke-dashoffset 0.35s; }
    </style>
</head>
<body>
<div class='container my-5'>
    <h1 class='text-center mb-5'><i class='bi bi-award'></i> اختبار جودة التصميم والأداء الشامل</h1>";

// Test 1: CSS Quality and Structure
echo "<div class='test-section'>
    <h3><i class='bi bi-palette-fill'></i> جودة وهيكل CSS</h3>";

$cssFile = 'assets/css/homepage.css';
if (file_exists($cssFile)) {
    $cssContent = file_get_contents($cssFile);
    $cssLines = count(explode("\n", $cssContent));
    
    $cssTests = [
        'CSS File Size' => ['test' => $cssLines > 3000, 'score' => 10, 'message' => "عدد الأسطر: $cssLines"],
        'CSS Variables' => ['test' => strpos($cssContent, ':root') !== false, 'score' => 15, 'message' => 'استخدام CSS Custom Properties'],
        'Responsive Design' => ['test' => substr_count($cssContent, '@media') >= 5, 'score' => 20, 'message' => 'تصميم متجاوب شامل'],
        'Advanced Animations' => ['test' => substr_count($cssContent, '@keyframes') >= 5, 'score' => 15, 'message' => 'حركات متقدمة'],
        'RTL Support' => ['test' => strpos($cssContent, '[dir="rtl"]') !== false, 'score' => 20, 'message' => 'دعم الاتجاه من اليمين لليسار'],
        'Modern CSS Features' => ['test' => strpos($cssContent, 'backdrop-filter') !== false, 'score' => 10, 'message' => 'استخدام ميزات CSS الحديثة']
    ];
    
    foreach ($cssTests as $testName => $testData) {
        $maxScore += $testData['score'];
        if ($testData['test']) {
            $totalScore += $testData['score'];
            $status = 'test-pass';
            $icon = 'bi-check-circle-fill';
            $result = 'نجح';
        } else {
            $status = 'test-fail';
            $icon = 'bi-x-circle-fill';
            $result = 'فشل';
        }
        echo "<p class='$status'><i class='bi $icon'></i> $testName: $result ({$testData['score']} نقطة) - {$testData['message']}</p>";
    }
} else {
    echo "<p class='test-fail'><i class='bi bi-x-circle-fill'></i> ملف CSS غير موجود</p>";
}

echo "</div>";

// Test 2: JavaScript Functionality and Performance
echo "<div class='test-section'>
    <h3><i class='bi bi-code-slash'></i> وظائف JavaScript والأداء</h3>";

$indexContent = file_get_contents('index.php');
$jsTests = [
    'Advanced Interactions' => ['test' => strpos($indexContent, 'initAdvancedFeatures') !== false, 'score' => 15, 'message' => 'تفاعلات متقدمة'],
    'Performance Monitoring' => ['test' => strpos($indexContent, 'initPerformanceMonitoring') !== false, 'score' => 20, 'message' => 'مراقبة الأداء'],
    'Error Handling' => ['test' => strpos($indexContent, 'initErrorHandling') !== false, 'score' => 15, 'message' => 'معالجة الأخطاء'],
    'Accessibility Features' => ['test' => strpos($indexContent, 'initKeyboardNavigation') !== false, 'score' => 20, 'message' => 'ميزات إمكانية الوصول'],
    'Service Worker' => ['test' => file_exists('sw.js'), 'score' => 25, 'message' => 'Service Worker للتخزين المؤقت'],
    'Modern JS Features' => ['test' => strpos($indexContent, 'IntersectionObserver') !== false, 'score' => 15, 'message' => 'استخدام APIs الحديثة']
];

foreach ($jsTests as $testName => $testData) {
    $maxScore += $testData['score'];
    if ($testData['test']) {
        $totalScore += $testData['score'];
        $status = 'test-pass';
        $icon = 'bi-check-circle-fill';
        $result = 'نجح';
    } else {
        $status = 'test-fail';
        $icon = 'bi-x-circle-fill';
        $result = 'فشل';
    }
    echo "<p class='$status'><i class='bi $icon'></i> $testName: $result ({$testData['score']} نقطة) - {$testData['message']}</p>";
}

echo "</div>";

// Test 3: Arabic E-commerce Features
echo "<div class='test-section'>
    <h3><i class='bi bi-translate'></i> الميزات العربية للتجارة الإلكترونية</h3>";

$arabicTests = [
    'Arabic Typography' => ['test' => strpos($cssContent, '.arabic-title') !== false, 'score' => 15, 'message' => 'تصميم الخطوط العربية'],
    'RTL Animations' => ['test' => strpos($cssContent, 'slideInFromRight') !== false, 'score' => 20, 'message' => 'حركات RTL'],
    'Currency Widget' => ['test' => strpos($indexContent, 'currency-widget') !== false, 'score' => 15, 'message' => 'أداة العملة'],
    'Regional Pricing' => ['test' => strpos($indexContent, 'updateRegionPricing') !== false, 'score' => 20, 'message' => 'التسعير الإقليمي'],
    'Arabic Dates' => ['test' => strpos($indexContent, 'updateArabicDates') !== false, 'score' => 15, 'message' => 'التواريخ العربية'],
    'Professional Arabic UI' => ['test' => strpos($indexContent, 'arabic-content') !== false, 'score' => 15, 'message' => 'واجهة عربية احترافية']
];

foreach ($arabicTests as $testName => $testData) {
    $maxScore += $testData['score'];
    if ($testData['test']) {
        $totalScore += $testData['score'];
        $status = 'test-pass';
        $icon = 'bi-check-circle-fill';
        $result = 'نجح';
    } else {
        $status = 'test-fail';
        $icon = 'bi-x-circle-fill';
        $result = 'فشل';
    }
    echo "<p class='$status'><i class='bi $icon'></i> $testName: $result ({$testData['score']} نقطة) - {$testData['message']}</p>";
}

echo "</div>";

// Test 4: Performance and Optimization
echo "<div class='test-section'>
    <h3><i class='bi bi-speedometer2'></i> الأداء والتحسين</h3>";

$performanceTests = [
    'Caching Headers' => ['test' => strpos($indexContent, 'Cache-Control') !== false, 'score' => 15, 'message' => 'رؤوس التخزين المؤقت'],
    'Compression' => ['test' => strpos($indexContent, 'ob_gzhandler') !== false, 'score' => 15, 'message' => 'ضغط المحتوى'],
    'Lazy Loading' => ['test' => strpos($indexContent, 'loading="lazy"') !== false, 'score' => 20, 'message' => 'التحميل التدريجي'],
    'WebP Support' => ['test' => strpos($indexContent, 'checkWebPSupport') !== false, 'score' => 20, 'message' => 'دعم تنسيق WebP'],
    'Preloading' => ['test' => strpos($indexContent, 'preloadCriticalResources') !== false, 'score' => 15, 'message' => 'التحميل المسبق'],
    'Font Optimization' => ['test' => strpos($indexContent, 'optimizeFontLoading') !== false, 'score' => 15, 'message' => 'تحسين الخطوط']
];

foreach ($performanceTests as $testName => $testData) {
    $maxScore += $testData['score'];
    if ($testData['test']) {
        $totalScore += $testData['score'];
        $status = 'test-pass';
        $icon = 'bi-check-circle-fill';
        $result = 'نجح';
    } else {
        $status = 'test-fail';
        $icon = 'bi-x-circle-fill';
        $result = 'فشل';
    }
    echo "<p class='$status'><i class='bi $icon'></i> $testName: $result ({$testData['score']} نقطة) - {$testData['message']}</p>";
}

echo "</div>";

// Test 5: Database and Backend Integration
echo "<div class='test-section'>
    <h3><i class='bi bi-database-fill'></i> التكامل مع قاعدة البيانات</h3>";

try {
    // Test database connection and data
    $productCount = fetchOne("SELECT COUNT(*) as count FROM products WHERE status = 'active'")['count'];
    $categoryCount = fetchOne("SELECT COUNT(*) as count FROM categories WHERE status = 'active'")['count'];
    
    $dbTests = [
        'Database Connection' => ['test' => true, 'score' => 20, 'message' => 'اتصال ناجح'],
        'Active Products' => ['test' => $productCount > 0, 'score' => 15, 'message' => "$productCount منتج نشط"],
        'Active Categories' => ['test' => $categoryCount > 0, 'score' => 15, 'message' => "$categoryCount فئة نشطة"],
        'AJAX Files' => ['test' => file_exists('ajax/search_suggestions.php'), 'score' => 20, 'message' => 'ملفات AJAX موجودة'],
        'Homepage Settings' => ['test' => fetchOne("SHOW TABLES LIKE 'homepage_settings'") !== false, 'score' => 15, 'message' => 'جدول الإعدادات'],
        'Error Handling' => ['test' => true, 'score' => 15, 'message' => 'معالجة الأخطاء نشطة']
    ];
    
    foreach ($dbTests as $testName => $testData) {
        $maxScore += $testData['score'];
        if ($testData['test']) {
            $totalScore += $testData['score'];
            $status = 'test-pass';
            $icon = 'bi-check-circle-fill';
            $result = 'نجح';
        } else {
            $status = 'test-fail';
            $icon = 'bi-x-circle-fill';
            $result = 'فشل';
        }
        echo "<p class='$status'><i class='bi $icon'></i> $testName: $result ({$testData['score']} نقطة) - {$testData['message']}</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='test-fail'><i class='bi bi-x-circle-fill'></i> خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Calculate final score and grade
$percentage = $maxScore > 0 ? round(($totalScore / $maxScore) * 100, 1) : 0;
$grade = 'F';
$gradeColor = 'danger';
$gradeIcon = 'bi-x-circle-fill';

if ($percentage >= 90) {
    $grade = 'A+';
    $gradeColor = 'success';
    $gradeIcon = 'bi-award-fill';
} elseif ($percentage >= 80) {
    $grade = 'A';
    $gradeColor = 'success';
    $gradeIcon = 'bi-check-circle-fill';
} elseif ($percentage >= 70) {
    $grade = 'B';
    $gradeColor = 'info';
    $gradeIcon = 'bi-info-circle-fill';
} elseif ($percentage >= 60) {
    $grade = 'C';
    $gradeColor = 'warning';
    $gradeIcon = 'bi-exclamation-circle-fill';
} else {
    $grade = 'D';
    $gradeColor = 'danger';
    $gradeIcon = 'bi-x-circle-fill';
}

// Final Score Display
echo "<div class='score-card mb-4'>
    <div class='row align-items-center'>
        <div class='col-md-8'>
            <h2><i class='bi $gradeIcon'></i> النتيجة النهائية</h2>
            <h1 class='display-4'>$percentage% - $grade</h1>
            <p class='lead'>$totalScore من $maxScore نقطة</p>
        </div>
        <div class='col-md-4 text-center'>
            <div class='progress-circle' style='width: 120px; height: 120px; margin: 0 auto;'>
                <svg class='progress-ring' width='120' height='120'>
                    <circle class='progress-ring-circle' stroke='rgba(255,255,255,0.3)' stroke-width='8' fill='transparent' r='52' cx='60' cy='60'/>
                    <circle class='progress-ring-circle' stroke='white' stroke-width='8' fill='transparent' r='52' cx='60' cy='60' 
                            stroke-dasharray='326.73' stroke-dashoffset='" . (326.73 - (326.73 * $percentage / 100)) . "'/>
                </svg>
                <div style='position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 1.5rem; font-weight: bold;'>$percentage%</div>
            </div>
        </div>
    </div>
</div>";

// Recommendations
echo "<div class='test-section'>
    <h3><i class='bi bi-lightbulb-fill'></i> التوصيات والتحسينات</h3>";

if ($percentage >= 90) {
    echo "<div class='alert alert-success'><i class='bi bi-check-circle-fill'></i> <strong>ممتاز!</strong> التصميم يحقق معايير الجودة العالية للتجارة الإلكترونية العربية.</div>";
} elseif ($percentage >= 70) {
    echo "<div class='alert alert-info'><i class='bi bi-info-circle-fill'></i> <strong>جيد جداً!</strong> التصميم احترافي مع إمكانية تحسينات طفيفة.</div>";
} else {
    echo "<div class='alert alert-warning'><i class='bi bi-exclamation-triangle-fill'></i> <strong>يحتاج تحسين!</strong> هناك مجالات تحتاج إلى تطوير إضافي.</div>";
}

echo "<ul class='list-group list-group-flush'>
    <li class='list-group-item'><i class='bi bi-arrow-right'></i> تأكد من تحسين جميع الصور لتنسيق WebP</li>
    <li class='list-group-item'><i class='bi bi-arrow-right'></i> اختبر الموقع على أجهزة مختلفة للتأكد من التجاوب</li>
    <li class='list-group-item'><i class='bi bi-arrow-right'></i> راجع إمكانية الوصول باستخدام قارئات الشاشة</li>
    <li class='list-group-item'><i class='bi bi-arrow-right'></i> اختبر الأداء باستخدام أدوات Google PageSpeed</li>
    <li class='list-group-item'><i class='bi bi-arrow-right'></i> تأكد من عمل جميع الوظائف التفاعلية</li>
</ul>";

echo "</div>";

echo "<div class='text-center mt-4'>
    <a href='index.php' class='btn btn-primary btn-lg me-3'>
        <i class='bi bi-house-fill'></i> عرض الصفحة الرئيسية
    </a>
    <a href='test_homepage_enhancements_validation.php' class='btn btn-outline-primary btn-lg'>
        <i class='bi bi-clipboard-check'></i> اختبار الوظائف
    </a>
</div>";

echo "</div></body></html>";
?>
