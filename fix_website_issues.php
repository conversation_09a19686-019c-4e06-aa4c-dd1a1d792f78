<?php
/**
 * Website Issues Fix Script
 * Comprehensive script to diagnose and fix all website issues
 */

// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح مشاكل الموقع - Website Fix</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background: #f8f9fa; }";
echo "h1, h2, h3 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".step { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }";
echo ".btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }";
echo ".btn:hover { background: #0056b3; }";
echo ".btn-success { background: #28a745; } .btn-success:hover { background: #1e7e34; }";
echo ".btn-danger { background: #dc3545; } .btn-danger:hover { background: #c82333; }";
echo ".btn-warning { background: #ffc107; color: #212529; } .btn-warning:hover { background: #e0a800; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 إصلاح مشاكل الموقع الإلكتروني</h1>";
echo "<p class='info'>هذا السكريبت سيقوم بتشخيص وإصلاح جميع مشاكل الموقع تلقائياً</p>";

$issues = [];
$fixes = [];
$step = 1;

// Step 1: Check PHP Environment
echo "<div class='step'>";
echo "<h2>الخطوة {$step}: فحص بيئة PHP</h2>";

echo "<h3>معلومات النظام:</h3>";
echo "<ul>";
echo "<li><strong>إصدار PHP:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>نظام التشغيل:</strong> " . PHP_OS . "</li>";
echo "<li><strong>المجلد الحالي:</strong> " . __DIR__ . "</li>";
echo "<li><strong>الوقت:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "</ul>";

// Check required extensions
$requiredExtensions = ['pdo', 'pdo_mysql', 'mysqli', 'gd', 'session'];
$missingExtensions = [];

echo "<h3>الإضافات المطلوبة:</h3>";
echo "<ul>";
foreach ($requiredExtensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "<li><strong>$ext:</strong> " . ($loaded ? '<span style="color: green;">✅ متوفر</span>' : '<span style="color: red;">❌ غير متوفر</span>') . "</li>";
    if (!$loaded) {
        $missingExtensions[] = $ext;
    }
}
echo "</ul>";

if (!empty($missingExtensions)) {
    $issues[] = "إضافات PHP مفقودة: " . implode(', ', $missingExtensions);
    echo "<div class='error'>❌ إضافات PHP مفقودة. يرجى تثبيت: " . implode(', ', $missingExtensions) . "</div>";
} else {
    echo "<div class='success'>✅ جميع إضافات PHP المطلوبة متوفرة</div>";
}

echo "</div>";
$step++;

// Step 2: Check File Structure
echo "<div class='step'>";
echo "<h2>الخطوة {$step}: فحص هيكل الملفات</h2>";

$requiredFiles = [
    'config/database.php',
    'config/config.php',
    'config/functions.php',
    'includes/header.php',
    'includes/footer.php',
    'index.php',
    'database.sql'
];

$missingFiles = [];
echo "<h3>الملفات المطلوبة:</h3>";
echo "<ul>";
foreach ($requiredFiles as $file) {
    $exists = file_exists($file);
    echo "<li><strong>$file:</strong> " . ($exists ? '<span style="color: green;">✅ موجود</span>' : '<span style="color: red;">❌ غير موجود</span>') . "</li>";
    if (!$exists) {
        $missingFiles[] = $file;
    }
}
echo "</ul>";

$requiredDirs = ['uploads', 'uploads/products', 'uploads/categories', 'admin', 'assets', 'assets/css', 'assets/js'];
$missingDirs = [];

echo "<h3>المجلدات المطلوبة:</h3>";
echo "<ul>";
foreach ($requiredDirs as $dir) {
    $exists = is_dir($dir);
    $writable = $exists ? is_writable($dir) : false;
    echo "<li><strong>$dir:</strong> ";
    if ($exists) {
        echo '<span style="color: green;">✅ موجود</span>';
        echo $writable ? ' <span style="color: green;">(قابل للكتابة)</span>' : ' <span style="color: orange;">(غير قابل للكتابة)</span>';
    } else {
        echo '<span style="color: red;">❌ غير موجود</span>';
        $missingDirs[] = $dir;
    }
    echo "</li>";
}
echo "</ul>";

if (!empty($missingFiles)) {
    $issues[] = "ملفات مفقودة: " . implode(', ', $missingFiles);
}
if (!empty($missingDirs)) {
    $issues[] = "مجلدات مفقودة: " . implode(', ', $missingDirs);
}

echo "</div>";
$step++;

// Step 3: Database Connection Test
echo "<div class='step'>";
echo "<h2>الخطوة {$step}: اختبار الاتصال بقاعدة البيانات</h2>";

$host = 'localhost';
$username = 'root';
$password = '';
$database = 'shop_db';

try {
    // Test MySQL connection first
    echo "<p>محاولة الاتصال بخادم MySQL...</p>";
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='success'>✅ تم الاتصال بخادم MySQL بنجاح</div>";
    
    // Check if database exists
    $databases = $pdo->query("SHOW DATABASES")->fetchAll(PDO::FETCH_COLUMN);
    $dbExists = in_array($database, $databases);
    
    echo "<p><strong>قواعد البيانات الموجودة:</strong></p>";
    echo "<ul>";
    foreach ($databases as $db) {
        $highlight = ($db === $database) ? " style='color: green; font-weight: bold;'" : "";
        echo "<li$highlight>$db</li>";
    }
    echo "</ul>";
    
    if (!$dbExists) {
        echo "<div class='warning'>⚠️ قاعدة البيانات '$database' غير موجودة - سيتم إنشاؤها</div>";
        
        // Create database
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<div class='success'>✅ تم إنشاء قاعدة البيانات '$database' بنجاح</div>";
        $fixes[] = "إنشاء قاعدة البيانات";
    } else {
        echo "<div class='success'>✅ قاعدة البيانات '$database' موجودة</div>";
    }
    
    // Connect to the database
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check tables
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<div class='warning'>⚠️ قاعدة البيانات فارغة - سيتم إنشاء الجداول</div>";
        
        // Import database.sql if it exists
        if (file_exists('database.sql')) {
            $sql = file_get_contents('database.sql');
            if ($sql) {
                // Split SQL into individual statements
                $statements = array_filter(array_map('trim', explode(';', $sql)));
                $successCount = 0;
                
                foreach ($statements as $statement) {
                    if (!empty($statement) && !preg_match('/^--/', $statement)) {
                        try {
                            $pdo->exec($statement);
                            $successCount++;
                        } catch (PDOException $e) {
                            echo "<div class='error'>خطأ في تنفيذ: " . substr($statement, 0, 50) . "... - " . $e->getMessage() . "</div>";
                        }
                    }
                }
                
                echo "<div class='success'>✅ تم تنفيذ $successCount استعلام من ملف database.sql</div>";
                $fixes[] = "إنشاء جداول قاعدة البيانات";
            }
        } else {
            echo "<div class='error'>❌ ملف database.sql غير موجود</div>";
            $issues[] = "ملف database.sql مفقود";
        }
    } else {
        echo "<div class='success'>✅ الجداول موجودة (" . count($tables) . " جدول)</div>";
        echo "<ul>";
        foreach ($tables as $table) {
            try {
                $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
                echo "<li><strong>$table:</strong> $count سجل</li>";
            } catch (Exception $e) {
                echo "<li><strong>$table:</strong> خطأ في القراءة</li>";
            }
        }
        echo "</ul>";
    }
    
} catch (PDOException $e) {
    echo "<div class='error'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    $issues[] = "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
    
    echo "<h4>خطوات حل المشكلة:</h4>";
    echo "<ol>";
    echo "<li><strong>تأكد من تشغيل XAMPP:</strong> افتح XAMPP Control Panel وتأكد من تشغيل MySQL</li>";
    echo "<li><strong>تحقق من المنفذ:</strong> المنفذ الافتراضي هو 3306</li>";
    echo "<li><strong>تحقق من كلمة المرور:</strong> عادة تكون فارغة في XAMPP</li>";
    echo "<li><strong>أعد تشغيل XAMPP:</strong> أوقف وأعد تشغيل Apache و MySQL</li>";
    echo "</ol>";
}

echo "</div>";
$step++;

// Step 4: Test Configuration Files
echo "<div class='step'>";
echo "<h2>الخطوة {$step}: اختبار ملفات التكوين</h2>";

try {
    if (file_exists('config/config.php')) {
        echo "<p>اختبار تضمين config.php...</p>";
        ob_start();
        require_once 'config/config.php';
        $output = ob_get_clean();
        
        echo "<div class='success'>✅ تم تضمين config.php بنجاح</div>";
        
        // Test constants
        echo "<p><strong>الثوابت المعرّفة:</strong></p>";
        echo "<ul>";
        $constants = ['SITE_URL', 'DB_HOST', 'DB_NAME', 'DB_USER'];
        foreach ($constants as $const) {
            if (defined($const)) {
                echo "<li><strong>$const:</strong> " . constant($const) . "</li>";
            } else {
                echo "<li><strong>$const:</strong> <span style='color: red;'>غير معرّف</span></li>";
            }
        }
        echo "</ul>";
        
    } else {
        echo "<div class='error'>❌ ملف config.php غير موجود</div>";
        $issues[] = "ملف config.php مفقود";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في تضمين config.php: " . $e->getMessage() . "</div>";
    $issues[] = "خطأ في ملف config.php: " . $e->getMessage();
}

echo "</div>";
$step++;

// Summary
echo "<div class='step'>";
echo "<h2>📋 ملخص التشخيص</h2>";

if (empty($issues)) {
    echo "<div class='success'>";
    echo "<h3>🎉 تهانينا! لا توجد مشاكل</h3>";
    echo "<p>جميع الفحوصات نجحت. الموقع جاهز للعمل.</p>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h3>❌ المشاكل المكتشفة (" . count($issues) . "):</h3>";
    echo "<ol>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ol>";
    echo "</div>";
}

if (!empty($fixes)) {
    echo "<div class='success'>";
    echo "<h3>✅ الإصلاحات المطبقة (" . count($fixes) . "):</h3>";
    echo "<ol>";
    foreach ($fixes as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ol>";
    echo "</div>";
}

echo "<h3>الخطوات التالية:</h3>";
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='index.php' class='btn btn-success'>🏠 زيارة الصفحة الرئيسية</a>";
echo "<a href='admin/login.php' class='btn btn-warning'>🔐 لوحة التحكم</a>";
echo "<a href='test_connection.php' class='btn'>🔍 اختبار الاتصال</a>";
echo "<a href='setup_database.php' class='btn'>🗄️ إعداد قاعدة البيانات</a>";
echo "</div>";

echo "</div>";

echo "</body>";
echo "</html>";
?>
