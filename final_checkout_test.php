<?php
/**
 * Final comprehensive checkout test
 */

require_once 'config/config.php';

echo "<h1>🎯 Final Checkout Process Test</h1>";
echo "<p>This is the definitive test to verify the checkout process is fully functional.</p>";

$allTestsPassed = true;

// Test 1: Database Connection
echo "<h2>Test 1: Database Connection</h2>";
if ($pdo) {
    try {
        $pdo->query("SELECT 1");
        echo "✅ Database connection working<br>";
    } catch (Exception $e) {
        echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
        $allTestsPassed = false;
    }
} else {
    echo "❌ PDO connection is null<br>";
    $allTestsPassed = false;
}

// Test 2: Table Structure Verification
echo "<h2>Test 2: Table Structure</h2>";
$requiredTables = ['orders', 'order_items', 'products'];
foreach ($requiredTables as $table) {
    try {
        $result = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($result->rowCount() > 0) {
            echo "✅ Table '$table' exists<br>";
        } else {
            echo "❌ Table '$table' missing<br>";
            $allTestsPassed = false;
        }
    } catch (Exception $e) {
        echo "❌ Error checking table '$table': " . $e->getMessage() . "<br>";
        $allTestsPassed = false;
    }
}

// Test 3: Function Availability
echo "<h2>Test 3: Required Functions</h2>";
$requiredFunctions = ['insertData', 'fetchOne', 'fetchAll', 'getCart', 'clearCart'];
foreach ($requiredFunctions as $func) {
    if (function_exists($func)) {
        echo "✅ Function '$func' available<br>";
    } else {
        echo "❌ Function '$func' missing<br>";
        $allTestsPassed = false;
    }
}

// Test 4: Complete Order Creation Workflow
echo "<h2>Test 4: Complete Order Workflow</h2>";

// Simulate cart session
if (!isset($_SESSION)) {
    session_start();
}

// Create test cart
$_SESSION['cart'] = [1 => 2, 2 => 1]; // Product 1: qty 2, Product 2: qty 1

echo "<h3>4.1: Cart Simulation</h3>";
$cart = getCart();
if (!empty($cart)) {
    echo "✅ Cart simulation working - Items: " . count($cart) . "<br>";
} else {
    echo "❌ Cart simulation failed<br>";
    $allTestsPassed = false;
}

echo "<h3>4.2: Order Data Preparation</h3>";
$orderData = [
    'customer_name' => 'Final Test Customer',
    'customer_phone' => '07123456789',
    'address' => 'Final Test Address, Baghdad, Iraq',
    'province' => 'بغداد',
    'subtotal' => 75000.00,
    'delivery_price' => 5000.00,
    'discount_amount' => 0.00,
    'total_price' => 80000.00,
    'payment_method' => 'cash_on_delivery',
    'status' => 'pending',
    'notes' => 'Final comprehensive test order'
];

echo "Order data prepared: " . count($orderData) . " fields<br>";

echo "<h3>4.3: Order Insertion</h3>";
$orderId = insertData('orders', $orderData);

if ($orderId) {
    echo "✅ Order inserted successfully - ID: $orderId<br>";
    
    echo "<h3>4.4: Order Items Insertion</h3>";
    $orderItems = [
        [
            'order_id' => $orderId,
            'product_id' => 1,
            'product_name' => 'Test Product 1',
            'quantity' => 2,
            'price' => 30000.00,
            'total' => 60000.00
        ],
        [
            'order_id' => $orderId,
            'product_id' => 2,
            'product_name' => 'Test Product 2',
            'quantity' => 1,
            'price' => 15000.00,
            'total' => 15000.00
        ]
    ];
    
    $itemsInserted = 0;
    foreach ($orderItems as $item) {
        $itemId = insertData('order_items', $item);
        if ($itemId) {
            $itemsInserted++;
            echo "✅ Order item $itemsInserted inserted - ID: $itemId<br>";
        } else {
            echo "❌ Failed to insert order item $itemsInserted<br>";
            $allTestsPassed = false;
        }
    }
    
    echo "<h3>4.5: Data Verification</h3>";
    $verifyOrder = fetchOne("SELECT * FROM orders WHERE id = ?", [$orderId]);
    $verifyItems = fetchAll("SELECT * FROM order_items WHERE order_id = ?", [$orderId]);
    
    if ($verifyOrder) {
        echo "✅ Order verification successful<br>";
        echo "- Customer: " . $verifyOrder['customer_name'] . "<br>";
        echo "- Phone: " . $verifyOrder['customer_phone'] . "<br>";
        echo "- Total: " . number_format($verifyOrder['total_price']) . " IQD<br>";
        echo "- Status: " . $verifyOrder['status'] . "<br>";
        echo "- Created: " . $verifyOrder['created_at'] . "<br>";
    } else {
        echo "❌ Order verification failed<br>";
        $allTestsPassed = false;
    }
    
    if (count($verifyItems) === $itemsInserted) {
        echo "✅ Order items verification successful - " . count($verifyItems) . " items<br>";
    } else {
        echo "❌ Order items verification failed<br>";
        $allTestsPassed = false;
    }
    
    echo "<h3>4.6: Admin Query Test</h3>";
    $adminOrders = fetchAll("SELECT * FROM orders WHERE id = ?", [$orderId]);
    if (count($adminOrders) > 0) {
        echo "✅ Order visible in admin queries<br>";
    } else {
        echo "❌ Order not visible in admin queries<br>";
        $allTestsPassed = false;
    }
    
    echo "<h3>4.7: Cart Clearing Test</h3>";
    clearCart();
    $clearedCart = getCart();
    if (empty($clearedCart)) {
        echo "✅ Cart clearing successful<br>";
    } else {
        echo "❌ Cart clearing failed<br>";
        $allTestsPassed = false;
    }
    
    // Clean up test data
    $pdo->prepare("DELETE FROM order_items WHERE order_id = ?")->execute([$orderId]);
    $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$orderId]);
    echo "✅ Test data cleaned up<br>";
    
} else {
    echo "❌ Order insertion failed<br>";
    $allTestsPassed = false;
}

// Test 5: Error Handling
echo "<h2>Test 5: Error Handling</h2>";
try {
    // Test with invalid data
    $invalidData = ['invalid_column' => 'test'];
    $result = insertData('orders', $invalidData);
    if ($result === false) {
        echo "✅ Error handling working - Invalid data rejected<br>";
    } else {
        echo "⚠️ Error handling may need improvement<br>";
    }
} catch (Exception $e) {
    echo "✅ Exception handling working<br>";
}

// Final Results
echo "<h2>🏁 Final Test Results</h2>";

if ($allTestsPassed) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
    echo "<h2>🎉 ALL TESTS PASSED! 🎉</h2>";
    echo "<h3>✅ Checkout Process is FULLY FUNCTIONAL</h3>";
    echo "<p><strong>The checkout system is now working correctly and ready for use!</strong></p>";
    echo "<ul style='text-align: left; display: inline-block;'>";
    echo "<li>✅ Database connection established</li>";
    echo "<li>✅ All required tables exist</li>";
    echo "<li>✅ All functions are available</li>";
    echo "<li>✅ Orders can be created successfully</li>";
    echo "<li>✅ Order items are properly linked</li>";
    echo "<li>✅ Data can be retrieved correctly</li>";
    echo "<li>✅ Orders will appear in admin dashboard</li>";
    echo "<li>✅ Cart functionality works</li>";
    echo "<li>✅ Error handling is functional</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🚀 Ready to Use!</h3>";
    echo "<p>You can now:</p>";
    echo "<ul>";
    echo "<li><a href='products.php' target='_blank'><strong>Add products to cart</strong></a></li>";
    echo "<li><a href='checkout.php' target='_blank'><strong>Complete checkout process</strong></a></li>";
    echo "<li><a href='admin/orders.php' target='_blank'><strong>View orders in admin panel</strong></a></li>";
    echo "</ul>";
    
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>❌ Some Tests Failed</h3>";
    echo "<p>Please review the failed tests above and address the issues.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Final test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
