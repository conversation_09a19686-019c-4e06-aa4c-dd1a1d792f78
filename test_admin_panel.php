<?php
/**
 * Admin Panel Test Script
 * Tests admin login and management functions
 */

// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار لوحة الإدارة - Admin Panel Test</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background: #f8f9fa; }";
echo "h1, h2, h3 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".test-section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }";
echo ".btn { display: inline-block; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 3px; }";
echo "table { width: 100%; border-collapse: collapse; margin: 10px 0; }";
echo "th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }";
echo "th { background-color: #f2f2f2; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔐 اختبار لوحة الإدارة</h1>";
echo "<p class='info'>فحص شامل لوظائف لوحة الإدارة والصلاحيات</p>";

// Test 1: Check admin files
echo "<div class='test-section'>";
echo "<h2>1. فحص ملفات الإدارة</h2>";

$adminFiles = [
    'admin/login.php' => 'صفحة تسجيل الدخول',
    'admin/dashboard.php' => 'لوحة القيادة',
    'admin/products.php' => 'إدارة المنتجات',
    'admin/categories.php' => 'إدارة التصنيفات',
    'admin/orders.php' => 'إدارة الطلبات',
    'admin/settings.php' => 'الإعدادات',
    'admin/logout.php' => 'تسجيل الخروج'
];

echo "<table>";
echo "<tr><th>الملف</th><th>الوصف</th><th>الحالة</th><th>الحجم</th></tr>";

$missingFiles = [];
foreach ($adminFiles as $file => $desc) {
    $exists = file_exists($file);
    $size = $exists ? number_format(filesize($file)) . ' بايت' : 'غير متوفر';
    $status = $exists ? '<span style="color: green;">✅ موجود</span>' : '<span style="color: red;">❌ مفقود</span>';
    
    echo "<tr><td><strong>$file</strong></td><td>$desc</td><td>$status</td><td>$size</td></tr>";
    
    if (!$exists) {
        $missingFiles[] = $file;
    }
}

echo "</table>";

if (empty($missingFiles)) {
    echo "<div class='success'>✅ جميع ملفات الإدارة موجودة</div>";
} else {
    echo "<div class='error'>❌ ملفات مفقودة: " . implode(', ', $missingFiles) . "</div>";
}

echo "</div>";

// Test 2: Check admin database
echo "<div class='test-section'>";
echo "<h2>2. فحص قاعدة بيانات الإدارة</h2>";

try {
    require_once 'config/config.php';
    
    if (isset($pdo) && $pdo) {
        // Check admins table
        $adminTableExists = $pdo->query("SHOW TABLES LIKE 'admins'")->rowCount() > 0;
        
        if ($adminTableExists) {
            echo "<div class='success'>✅ جدول المديرين موجود</div>";
            
            // Check admin count
            $adminCount = $pdo->query("SELECT COUNT(*) FROM admins")->fetchColumn();
            echo "<p><strong>عدد المديرين:</strong> $adminCount</p>";
            
            if ($adminCount > 0) {
                // Get admin details
                $admins = $pdo->query("SELECT id, username, email, created_at FROM admins")->fetchAll();
                
                echo "<h3>قائمة المديرين:</h3>";
                echo "<table>";
                echo "<tr><th>ID</th><th>اسم المستخدم</th><th>البريد الإلكتروني</th><th>تاريخ الإنشاء</th></tr>";
                
                foreach ($admins as $admin) {
                    echo "<tr>";
                    echo "<td>" . $admin['id'] . "</td>";
                    echo "<td><strong>" . htmlspecialchars($admin['username']) . "</strong></td>";
                    echo "<td>" . htmlspecialchars($admin['email'] ?? 'غير محدد') . "</td>";
                    echo "<td>" . $admin['created_at'] . "</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
                
                echo "<div class='info'>";
                echo "<h4>معلومات تسجيل الدخول الافتراضية:</h4>";
                echo "<ul>";
                echo "<li><strong>اسم المستخدم:</strong> admin</li>";
                echo "<li><strong>كلمة المرور:</strong> password</li>";
                echo "</ul>";
                echo "</div>";
                
            } else {
                echo "<div class='warning'>⚠️ لا يوجد مديرين في النظام</div>";
                
                // Try to create default admin
                try {
                    $defaultPassword = password_hash('password', PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("INSERT INTO admins (username, password, email) VALUES (?, ?, ?)");
                    $stmt->execute(['admin', $defaultPassword, '<EMAIL>']);
                    
                    echo "<div class='success'>✅ تم إنشاء مدير افتراضي (admin/password)</div>";
                } catch (Exception $e) {
                    echo "<div class='error'>❌ فشل في إنشاء مدير افتراضي: " . $e->getMessage() . "</div>";
                }
            }
            
        } else {
            echo "<div class='error'>❌ جدول المديرين غير موجود</div>";
        }
        
    } else {
        echo "<div class='error'>❌ لا يمكن الاتصال بقاعدة البيانات</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في فحص قاعدة البيانات: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 3: Test admin login page
echo "<div class='test-section'>";
echo "<h2>3. اختبار صفحة تسجيل الدخول</h2>";

if (file_exists('admin/login.php')) {
    try {
        ob_start();
        $error = false;
        
        set_error_handler(function($severity, $message, $file, $line) use (&$error) {
            if ($severity & (E_ERROR | E_PARSE | E_CORE_ERROR | E_COMPILE_ERROR)) {
                $error = "خطأ فادح: $message في $file على السطر $line";
            }
            return true;
        });
        
        include 'admin/login.php';
        restore_error_handler();
        
        $output = ob_get_clean();
        
        if ($error) {
            echo "<div class='error'>❌ $error</div>";
        } else {
            echo "<div class='success'>✅ صفحة تسجيل الدخول تعمل بدون أخطاء</div>";
            echo "<p><strong>حجم المحتوى:</strong> " . number_format(strlen($output)) . " حرف</p>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في صفحة تسجيل الدخول: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<div class='error'>❌ صفحة تسجيل الدخول غير موجودة</div>";
}

echo "</div>";

// Test 4: Check admin includes
echo "<div class='test-section'>";
echo "<h2>4. فحص ملفات الإدراج</h2>";

$includeFiles = [
    'admin/includes/header.php' => 'رأس الصفحة',
    'admin/includes/footer.php' => 'تذييل الصفحة',
    'admin/includes/sidebar.php' => 'الشريط الجانبي'
];

echo "<table>";
echo "<tr><th>الملف</th><th>الوصف</th><th>الحالة</th></tr>";

foreach ($includeFiles as $file => $desc) {
    $exists = file_exists($file);
    $status = $exists ? '<span style="color: green;">✅ موجود</span>' : '<span style="color: red;">❌ مفقود</span>';
    
    echo "<tr><td><strong>$file</strong></td><td>$desc</td><td>$status</td></tr>";
}

echo "</table>";

echo "</div>";

// Test 5: Check admin permissions
echo "<div class='test-section'>";
echo "<h2>5. فحص الصلاحيات</h2>";

// Check if admin directory is accessible
$adminDir = 'admin';
if (is_dir($adminDir)) {
    $readable = is_readable($adminDir);
    $writable = is_writable($adminDir);
    
    echo "<table>";
    echo "<tr><th>الصلاحية</th><th>الحالة</th></tr>";
    echo "<tr><td><strong>قراءة مجلد admin</strong></td><td>" . ($readable ? '<span style="color: green;">✅ متاح</span>' : '<span style="color: red;">❌ غير متاح</span>') . "</td></tr>";
    echo "<tr><td><strong>كتابة مجلد admin</strong></td><td>" . ($writable ? '<span style="color: green;">✅ متاح</span>' : '<span style="color: red;">❌ غير متاح</span>') . "</td></tr>";
    echo "</table>";
    
    if ($readable && $writable) {
        echo "<div class='success'>✅ صلاحيات مجلد الإدارة صحيحة</div>";
    } else {
        echo "<div class='warning'>⚠️ قد تحتاج إلى تعديل صلاحيات مجلد الإدارة</div>";
    }
} else {
    echo "<div class='error'>❌ مجلد الإدارة غير موجود</div>";
}

echo "</div>";

// Summary and Actions
echo "<div class='test-section'>";
echo "<h2>📋 الملخص والإجراءات</h2>";

echo "<h3>روابط سريعة:</h3>";
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='admin/login.php' class='btn' style='background: #28a745;'>🔐 تسجيل دخول الإدارة</a>";
echo "<a href='setup_database.php' class='btn' style='background: #ffc107; color: #212529;'>🗄️ إعداد قاعدة البيانات</a>";
echo "<a href='test_website_functionality.php' class='btn'>🧪 اختبار الوظائف</a>";
echo "<a href='index.php' class='btn' style='background: #17a2b8;'>🏠 الصفحة الرئيسية</a>";
echo "</div>";

echo "<div class='info'>";
echo "<h4>معلومات مهمة:</h4>";
echo "<ul>";
echo "<li><strong>رابط لوحة الإدارة:</strong> <a href='admin/login.php' target='_blank'>http://localhost/shop/admin/login.php</a></li>";
echo "<li><strong>اسم المستخدم الافتراضي:</strong> admin</li>";
echo "<li><strong>كلمة المرور الافتراضية:</strong> password</li>";
echo "<li><strong>يُنصح بتغيير كلمة المرور بعد تسجيل الدخول الأول</strong></li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "</body>";
echo "</html>";
?>
