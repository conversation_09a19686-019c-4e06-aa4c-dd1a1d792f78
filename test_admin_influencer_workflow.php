<?php
require_once 'config/config.php';

echo "=== اختبار سير العمل الكامل للمؤثرين في لوحة التحكم ===\n";

// محاكاة جلسة المدير
session_start();
$_SESSION['admin_id'] = 1;
$_SESSION['admin_logged_in'] = true;

echo "\n=== 1. اختبار إضافة مؤثر جديد (محاكاة النموذج) ===\n";

// محاكاة بيانات POST من النموذج
$_POST = [
    'action' => 'add',
    'influencer_name' => 'مؤثر تجريبي - ' . date('H:i:s'),
    'content_type' => 'review',
    'content_title' => 'مراجعة منتج رائع',
    'content_text' => 'هذا المنتج رائع جداً وأنصح الجميع بشرائه. جودة عالية وسعر مناسب.',
    'rating' => '5',
    'product_id' => '',
    'video_url' => '',
    'video_platform' => '',
    'category_id' => '',
    'status' => 'published',
    'is_featured' => '1',
    'sort_order' => '0',
    'image_type' => 'url',
    'image_url' => 'https://via.placeholder.com/150x150/007bff/ffffff?text=مؤثر'
];

echo "البيانات المرسلة من النموذج:\n";
foreach ($_POST as $key => $value) {
    echo "- {$key}: " . (empty($value) ? '[فارغ]' : $value) . "\n";
}

try {
    // تنفيذ نفس المنطق الموجود في admin/influencers.php
    $action = $_POST['action'] ?? '';
    $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
    $influencerName = sanitizeInput($_POST['influencer_name'] ?? '');
    $contentType = sanitizeInput($_POST['content_type'] ?? '');
    $contentTitle = sanitizeInput($_POST['content_title'] ?? '');
    $contentText = $_POST['content_text'] ?? '';
    $rating = isset($_POST['rating']) && $_POST['rating'] !== '' ? (int)$_POST['rating'] : null;
    $productId = isset($_POST['product_id']) && $_POST['product_id'] ? (int)$_POST['product_id'] : null;
    $videoUrl = sanitizeInput($_POST['video_url'] ?? '');
    $videoPlatform = sanitizeInput($_POST['video_platform'] ?? '');
    $categoryId = isset($_POST['category_id']) && $_POST['category_id'] ? (int)$_POST['category_id'] : null;
    $status = sanitizeInput($_POST['status'] ?? 'published');
    $isFeatured = isset($_POST['is_featured']) ? 1 : 0;
    $sortOrder = (int)($_POST['sort_order'] ?? 0);
    
    echo "\nالبيانات بعد المعالجة:\n";
    echo "- اسم المؤثر: '{$influencerName}'\n";
    echo "- نوع المحتوى: '{$contentType}'\n";
    echo "- عنوان المحتوى: '{$contentTitle}'\n";
    echo "- التقييم: " . ($rating ?? 'NULL') . "\n";
    echo "- الحالة: '{$status}'\n";
    echo "- مميز: " . ($isFeatured ? 'نعم' : 'لا') . "\n";
    
    // التحقق من الحقول المطلوبة والقيود
    $errors = [];
    if (empty($influencerName)) {
        $errors[] = 'اسم المؤثر مطلوب';
    }
    if (empty($contentType)) {
        $errors[] = 'نوع المحتوى مطلوب';
    } elseif (!in_array($contentType, ['video', 'post', 'review'])) {
        $errors[] = 'نوع المحتوى غير صحيح';
    }
    if (empty($contentText)) {
        $errors[] = 'نص المحتوى مطلوب';
    }
    if (!in_array($status, ['draft', 'published', 'archived'])) {
        $errors[] = 'حالة النشر غير صحيحة';
    }
    if ($rating !== null && ($rating < 1 || $rating > 5)) {
        $errors[] = 'التقييم يجب أن يكون بين 1 و 5';
    }
    if (strlen($influencerName) > 255) {
        $errors[] = 'اسم المؤثر طويل جداً (الحد الأقصى 255 حرف)';
    }
    if (!empty($contentTitle) && strlen($contentTitle) > 500) {
        $errors[] = 'عنوان المحتوى طويل جداً (الحد الأقصى 500 حرف)';
    }
    if (!empty($videoUrl) && strlen($videoUrl) > 1000) {
        $errors[] = 'رابط الفيديو طويل جداً (الحد الأقصى 1000 حرف)';
    }
    if (!empty($videoPlatform) && !in_array($videoPlatform, ['youtube', 'instagram', 'tiktok', 'other'])) {
        $errors[] = 'منصة الفيديو غير صحيحة';
    }
    
    if (!empty($errors)) {
        echo "\n✗ أخطاء في التحقق:\n";
        foreach ($errors as $error) {
            echo "  - {$error}\n";
        }
        throw new Exception(implode(', ', $errors));
    } else {
        echo "\n✓ تم التحقق من البيانات بنجاح\n";
    }
    
    // معالجة الصورة
    $imageType = sanitizeInput($_POST['image_type'] ?? 'upload');
    $imagePath = '';

    if ($imageType === 'url') {
        $imagePath = sanitizeInput($_POST['image_url'] ?? '');
        if (!empty($imagePath) && !filter_var($imagePath, FILTER_VALIDATE_URL)) {
            throw new Exception('رابط الصورة غير صحيح');
        }
    }
    
    // إذا لم يتم تحديد صورة، استخدم صورة افتراضية
    if (empty($imagePath) && $imageType === 'url') {
        $imagePath = 'https://via.placeholder.com/150x150/007bff/ffffff?text=' . urlencode('مؤثر');
    }
    
    echo "- نوع الصورة: {$imageType}\n";
    echo "- مسار الصورة: {$imagePath}\n";
    
    if ($action === 'add') {
        echo "\n=== تنفيذ عملية الإدراج ===\n";
        
        // تنظيف وتحضير البيانات للإدراج
        $cleanVideoUrl = !empty($videoUrl) ? $videoUrl : null;
        $cleanVideoPlatform = !empty($videoPlatform) ? $videoPlatform : null;
        $cleanContentTitle = !empty($contentTitle) ? $contentTitle : null;
        $cleanImagePath = !empty($imagePath) ? $imagePath : null;
        
        $sql = "INSERT INTO influencers_content (
            influencer_name, influencer_image, influencer_image_type, content_type,
            content_title, content_text, rating, product_id, video_url, video_platform,
            category_id, status, is_featured, sort_order, created_by, published_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $publishedAt = $status === 'published' ? date('Y-m-d H:i:s') : null;
        
        $params = [
            $influencerName, $cleanImagePath, $imageType, $contentType,
            $cleanContentTitle, $contentText, $rating, $productId, $cleanVideoUrl, $cleanVideoPlatform,
            $categoryId, $status, $isFeatured, $sortOrder, $_SESSION['admin_id'], $publishedAt
        ];
        
        echo "عدد المعاملات في SQL: " . substr_count($sql, '?') . "\n";
        echo "عدد القيم المرسلة: " . count($params) . "\n";
        
        echo "القيم المرسلة:\n";
        $fieldNames = [
            'influencer_name', 'influencer_image', 'influencer_image_type', 'content_type',
            'content_title', 'content_text', 'rating', 'product_id', 'video_url', 'video_platform',
            'category_id', 'status', 'is_featured', 'sort_order', 'created_by', 'published_at'
        ];
        
        foreach ($params as $i => $value) {
            $fieldName = $fieldNames[$i] ?? "field_" . ($i + 1);
            $displayValue = is_null($value) ? 'NULL' : (is_string($value) ? "'{$value}'" : $value);
            echo "  {$fieldName}: {$displayValue}\n";
        }
        
        try {
            $result = executeQuery($sql, $params);
            
            if ($result) {
                $insertedId = $pdo->lastInsertId();
                echo "\n✅ تم إضافة المحتوى بنجاح - ID: {$insertedId}\n";
                
                // التحقق من الإدراج
                $inserted = fetchOne("SELECT * FROM influencers_content WHERE id = ?", [$insertedId]);
                if ($inserted) {
                    echo "✓ تم التحقق من وجود البيانات في قاعدة البيانات:\n";
                    echo "  - الاسم: " . $inserted['influencer_name'] . "\n";
                    echo "  - النوع: " . $inserted['content_type'] . "\n";
                    echo "  - الحالة: " . $inserted['status'] . "\n";
                    echo "  - تاريخ الإنشاء: " . $inserted['created_at'] . "\n";
                    echo "  - تاريخ النشر: " . ($inserted['published_at'] ?? 'غير منشور') . "\n";
                }
                
                echo "\n=== 2. اختبار ظهور المحتوى في لوحة التحكم ===\n";
                
                // محاكاة استعلام لوحة التحكم
                $adminQuery = "SELECT ic.*, cc.name_ar as category_name 
                              FROM influencers_content ic
                              LEFT JOIN content_categories cc ON ic.category_id = cc.id
                              WHERE ic.id = ?";
                
                $adminResult = fetchOne($adminQuery, [$insertedId]);
                if ($adminResult) {
                    echo "✓ المحتوى يظهر في لوحة التحكم\n";
                    echo "  - العنوان: " . ($adminResult['content_title'] ?? 'بدون عنوان') . "\n";
                    echo "  - التصنيف: " . ($adminResult['category_name'] ?? 'بدون تصنيف') . "\n";
                } else {
                    echo "✗ المحتوى لا يظهر في لوحة التحكم\n";
                }
                
                echo "\n=== 3. اختبار ظهور المحتوى في الموقع ===\n";
                
                // محاكاة استعلام الموقع
                $frontendQuery = "SELECT * FROM influencers_content WHERE id = ? AND status = 'published'";
                $frontendResult = fetchOne($frontendQuery, [$insertedId]);
                
                if ($frontendResult) {
                    echo "✓ المحتوى سيظهر في الموقع\n";
                } else {
                    echo "⚠ المحتوى لن يظهر في الموقع (حالة غير منشورة أو محذوف)\n";
                }
                
                // حذف البيانات التجريبية
                echo "\n=== تنظيف البيانات التجريبية ===\n";
                executeQuery("DELETE FROM influencers_content WHERE id = ?", [$insertedId]);
                echo "✓ تم حذف البيانات التجريبية\n";
                
            } else {
                echo "\n✗ فشل في إضافة المحتوى - executeQuery returned false\n";
                $_SESSION['error_message'] = 'فشل في إضافة المحتوى - خطأ في قاعدة البيانات';
            }
        } catch (Exception $e) {
            echo "\n✗ خطأ في الإدراج: " . $e->getMessage() . "\n";
            $_SESSION['error_message'] = 'فشل في إضافة المحتوى: ' . $e->getMessage();
        }
    }
    
} catch (Exception $e) {
    echo "\n✗ خطأ عام: " . $e->getMessage() . "\n";
    $_SESSION['error_message'] = 'حدث خطأ: ' . $e->getMessage();
}

echo "\n=== 4. فحص رسائل الجلسة ===\n";
if (isset($_SESSION['success_message'])) {
    echo "✅ رسالة النجاح: " . $_SESSION['success_message'] . "\n";
    unset($_SESSION['success_message']);
}
if (isset($_SESSION['error_message'])) {
    echo "✗ رسالة الخطأ: " . $_SESSION['error_message'] . "\n";
    unset($_SESSION['error_message']);
}

echo "\n=== 5. إحصائيات قاعدة البيانات ===\n";
try {
    $totalInfluencers = fetchOne("SELECT COUNT(*) as count FROM influencers_content")['count'] ?? 0;
    $publishedInfluencers = fetchOne("SELECT COUNT(*) as count FROM influencers_content WHERE status = 'published'")['count'] ?? 0;
    $totalCategories = fetchOne("SELECT COUNT(*) as count FROM content_categories WHERE type IN ('influencer', 'both')")['count'] ?? 0;
    
    echo "- إجمالي المؤثرين: {$totalInfluencers}\n";
    echo "- المؤثرين المنشورين: {$publishedInfluencers}\n";
    echo "- التصنيفات المتاحة: {$totalCategories}\n";
} catch (Exception $e) {
    echo "✗ خطأ في جلب الإحصائيات: " . $e->getMessage() . "\n";
}

echo "\n🎉 انتهاء اختبار سير العمل الكامل\n";
echo "يمكنك الآن اختبار لوحة التحكم الفعلية على: /admin/influencers.php\n";

echo "\n=== انتهاء الاختبار ===\n";
?>
