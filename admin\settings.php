<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'إعدادات الموقع';

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $settings = [
        'site_name' => sanitizeInput($_POST['site_name']),
        'site_description' => sanitizeInput($_POST['site_description']),
        'contact_phone' => sanitizeInput($_POST['contact_phone']),
        'contact_email' => sanitizeInput($_POST['contact_email']),
        'whatsapp_number' => sanitizeInput($_POST['whatsapp_number']),
        'delivery_price' => (float)$_POST['delivery_price'],
        'free_delivery_threshold' => (float)$_POST['free_delivery_threshold'],
        'currency' => sanitizeInput($_POST['currency']),
        'facebook_url' => sanitizeInput($_POST['facebook_url']),
        'twitter_url' => sanitizeInput($_POST['twitter_url']),
        'instagram_url' => sanitizeInput($_POST['instagram_url']),
        'youtube_url' => sanitizeInput($_POST['youtube_url']),
        'address' => sanitizeInput($_POST['address']),
        'working_hours' => sanitizeInput($_POST['working_hours']),
        'about_us' => sanitizeInput($_POST['about_us']),
        'privacy_policy' => sanitizeInput($_POST['privacy_policy']),
        'terms_conditions' => sanitizeInput($_POST['terms_conditions'])
    ];
    
    $errors = [];
    
    // التحقق من البيانات
    if (empty($settings['site_name'])) {
        $errors[] = 'اسم الموقع مطلوب';
    }
    
    if (!empty($settings['contact_email']) && !filter_var($settings['contact_email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }
    
    if ($settings['delivery_price'] < 0) {
        $errors[] = 'سعر التوصيل لا يمكن أن يكون سالباً';
    }
    
    if ($settings['free_delivery_threshold'] < 0) {
        $errors[] = 'حد التوصيل المجاني لا يمكن أن يكون سالباً';
    }
    
    if (empty($errors)) {
        // تحديث الإعدادات
        foreach ($settings as $key => $value) {
            $existingSetting = fetchOne("SELECT id FROM site_settings WHERE setting_key = ?", [$key]);
            
            if ($existingSetting) {
                updateData('site_settings', ['setting_value' => $value], 'setting_key = ?', ['setting_key' => $key]);
            } else {
                insertData('site_settings', ['setting_key' => $key, 'setting_value' => $value]);
            }
        }
        
        $_SESSION['success'] = 'تم تحديث الإعدادات بنجاح';
        header('Location: settings.php');
        exit();
    }
}

// جلب الإعدادات الحالية
$currentSettings = [];
$settingsData = fetchAll("SELECT setting_key, setting_value FROM site_settings");

foreach ($settingsData as $setting) {
    $currentSettings[$setting['setting_key']] = $setting['setting_value'];
}

// القيم الافتراضية
$defaults = [
    'site_name' => 'متجري الإلكتروني',
    'site_description' => 'متجر إلكتروني شامل لجميع احتياجاتك',
    'contact_phone' => '',
    'contact_email' => '',
    'whatsapp_number' => '',
    'delivery_price' => '25.00',
    'free_delivery_threshold' => '200.00',
    'currency' => 'دينار عراقي',
    'facebook_url' => '',
    'twitter_url' => '',
    'instagram_url' => '',
    'youtube_url' => '',
    'address' => '',
    'working_hours' => '',
    'about_us' => '',
    'privacy_policy' => '',
    'terms_conditions' => ''
];

// دمج الإعدادات الحالية مع القيم الافتراضية
$settings = array_merge($defaults, $currentSettings);

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">إعدادات الموقع</h5>
                </div>
                
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST">
                        <!-- تبويبات الإعدادات -->
                        <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                                    <i class="bi bi-gear"></i> إعدادات عامة
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab">
                                    <i class="bi bi-telephone"></i> معلومات الاتصال
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="delivery-tab" data-bs-toggle="tab" data-bs-target="#delivery" type="button" role="tab">
                                    <i class="bi bi-truck"></i> إعدادات التوصيل
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="social-tab" data-bs-toggle="tab" data-bs-target="#social" type="button" role="tab">
                                    <i class="bi bi-share"></i> وسائل التواصل
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab">
                                    <i class="bi bi-file-text"></i> المحتوى
                                </button>
                            </li>
                        </ul>
                        
                        <div class="tab-content mt-4" id="settingsTabsContent">
                            <!-- الإعدادات العامة -->
                            <div class="tab-pane fade show active" id="general" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="site_name" class="form-label">اسم الموقع *</label>
                                            <input type="text" class="form-control" id="site_name" name="site_name" 
                                                   value="<?php echo htmlspecialchars($settings['site_name']); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="currency" class="form-label">العملة</label>
                                            <input type="text" class="form-control" id="currency" name="currency" 
                                                   value="<?php echo htmlspecialchars($settings['currency']); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="site_description" class="form-label">وصف الموقع</label>
                                    <textarea class="form-control" id="site_description" name="site_description" 
                                              rows="3"><?php echo htmlspecialchars($settings['site_description']); ?></textarea>
                                </div>
                            </div>
                            
                            <!-- معلومات الاتصال -->
                            <div class="tab-pane fade" id="contact" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="contact_phone" class="form-label">رقم الهاتف</label>
                                            <input type="text" class="form-control" id="contact_phone" name="contact_phone" 
                                                   value="<?php echo htmlspecialchars($settings['contact_phone']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="contact_email" class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                                   value="<?php echo htmlspecialchars($settings['contact_email']); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="whatsapp_number" class="form-label">رقم الواتساب</label>
                                            <input type="text" class="form-control" id="whatsapp_number" name="whatsapp_number" 
                                                   value="<?php echo htmlspecialchars($settings['whatsapp_number']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="working_hours" class="form-label">ساعات العمل</label>
                                            <input type="text" class="form-control" id="working_hours" name="working_hours" 
                                                   value="<?php echo htmlspecialchars($settings['working_hours']); ?>" 
                                                   placeholder="مثال: من 9 صباحاً إلى 10 مساءً">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="address" class="form-label">العنوان</label>
                                    <textarea class="form-control" id="address" name="address" 
                                              rows="3"><?php echo htmlspecialchars($settings['address']); ?></textarea>
                                </div>
                            </div>
                            
                            <!-- إعدادات التوصيل -->
                            <div class="tab-pane fade" id="delivery" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="delivery_price" class="form-label">سعر التوصيل</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="delivery_price" name="delivery_price" 
                                                       step="0.01" min="0" value="<?php echo $settings['delivery_price']; ?>">
                                                <span class="input-group-text"><?php echo htmlspecialchars($settings['currency']); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="free_delivery_threshold" class="form-label">حد التوصيل المجاني</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="free_delivery_threshold" name="free_delivery_threshold" 
                                                       step="0.01" min="0" value="<?php echo $settings['free_delivery_threshold']; ?>">
                                                <span class="input-group-text"><?php echo htmlspecialchars($settings['currency']); ?></span>
                                            </div>
                                            <div class="form-text">الطلبات التي تزيد عن هذا المبلغ ستحصل على توصيل مجاني</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- وسائل التواصل الاجتماعي -->
                            <div class="tab-pane fade" id="social" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="facebook_url" class="form-label">رابط فيسبوك</label>
                                            <input type="url" class="form-control" id="facebook_url" name="facebook_url" 
                                                   value="<?php echo htmlspecialchars($settings['facebook_url']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="twitter_url" class="form-label">رابط تويتر</label>
                                            <input type="url" class="form-control" id="twitter_url" name="twitter_url" 
                                                   value="<?php echo htmlspecialchars($settings['twitter_url']); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="instagram_url" class="form-label">رابط إنستغرام</label>
                                            <input type="url" class="form-control" id="instagram_url" name="instagram_url" 
                                                   value="<?php echo htmlspecialchars($settings['instagram_url']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="youtube_url" class="form-label">رابط يوتيوب</label>
                                            <input type="url" class="form-control" id="youtube_url" name="youtube_url" 
                                                   value="<?php echo htmlspecialchars($settings['youtube_url']); ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- المحتوى -->
                            <div class="tab-pane fade" id="content" role="tabpanel">
                                <div class="mb-3">
                                    <label for="about_us" class="form-label">من نحن</label>
                                    <textarea class="form-control" id="about_us" name="about_us" 
                                              rows="5"><?php echo htmlspecialchars($settings['about_us']); ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="privacy_policy" class="form-label">سياسة الخصوصية</label>
                                    <textarea class="form-control" id="privacy_policy" name="privacy_policy" 
                                              rows="5"><?php echo htmlspecialchars($settings['privacy_policy']); ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="terms_conditions" class="form-label">الشروط والأحكام</label>
                                    <textarea class="form-control" id="terms_conditions" name="terms_conditions" 
                                              rows="5"><?php echo htmlspecialchars($settings['terms_conditions']); ?></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
