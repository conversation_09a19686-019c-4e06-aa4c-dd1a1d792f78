<?php
require_once '../config/config.php';
requireAdminLogin();

echo "<h2>اختبار مباشر لوظيفة الحذف</h2>";

// Handle direct delete test
if (isset($_GET['direct_delete']) && is_numeric($_GET['direct_delete'])) {
    $productId = (int)$_GET['direct_delete'];
    
    echo "<h3>محاولة حذف المنتج ID: $productId</h3>";
    
    // Get product info first
    $product = fetchOne("SELECT * FROM products WHERE id = ?", [$productId]);
    
    if ($product) {
        echo "<div style='background: #e3f2fd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<strong>معلومات المنتج قبل الحذف:</strong><br>";
        echo "ID: " . $product['id'] . "<br>";
        echo "الاسم: " . htmlspecialchars($product['name']) . "<br>";
        echo "الحالة: " . $product['status'] . "<br>";
        echo "</div>";
        
        if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
            echo "<h4>تنفيذ الحذف...</h4>";
            
            try {
                // Execute the exact same delete logic as products.php
                echo "<p><strong>الخطوة 1:</strong> البحث عن الصور المرتبطة...</p>";
                $images = fetchAll("SELECT image_path FROM product_images WHERE product_id = ?", [$productId]);
                echo "<p>عدد الصور الموجودة: " . count($images) . "</p>";
                
                echo "<p><strong>الخطوة 2:</strong> حذف الصور المرتبطة...</p>";
                $deleteImages = deleteData('product_images', 'product_id = ?', [$productId]);
                echo "<p>نتيجة حذف الصور: " . var_export($deleteImages, true) . "</p>";
                
                echo "<p><strong>الخطوة 3:</strong> حذف المنتج من قاعدة البيانات...</p>";
                $deleteProduct = deleteData('products', 'id = ?', [$productId]);
                echo "<p>نتيجة حذف المنتج: " . var_export($deleteProduct, true) . "</p>";
                
                echo "<p><strong>الخطوة 4:</strong> التحقق من الحذف...</p>";
                $checkProduct = fetchOne("SELECT id FROM products WHERE id = ?", [$productId]);
                
                if (!$checkProduct) {
                    echo "<div style='background: #c8e6c9; padding: 15px; margin: 10px 0; border-radius: 5px; color: #2e7d32;'>";
                    echo "<h4>✅ نجح الحذف!</h4>";
                    echo "<p>تم حذف المنتج '" . htmlspecialchars($product['name']) . "' بنجاح من قاعدة البيانات.</p>";
                    echo "</div>";
                } else {
                    echo "<div style='background: #ffcdd2; padding: 15px; margin: 10px 0; border-radius: 5px; color: #c62828;'>";
                    echo "<h4>❌ فشل الحذف!</h4>";
                    echo "<p>المنتج ما زال موجود في قاعدة البيانات.</p>";
                    echo "<p>معلومات المنتج المتبقية: " . print_r($checkProduct, true) . "</p>";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div style='background: #ffcdd2; padding: 15px; margin: 10px 0; border-radius: 5px; color: #c62828;'>";
                echo "<h4>❌ خطأ في الحذف!</h4>";
                echo "<p>رسالة الخطأ: " . $e->getMessage() . "</p>";
                echo "</div>";
            }
            
        } else {
            echo "<div style='background: #fff3e0; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h4>⚠️ تأكيد الحذف</h4>";
            echo "<p>هل أنت متأكد من حذف هذا المنتج؟</p>";
            echo "<p><strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!</p>";
            echo "<div style='margin-top: 15px;'>";
            echo "<a href='?direct_delete=$productId&confirm=yes' style='background: #f44336; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>نعم، احذف المنتج</a>";
            echo "<a href='?' style='background: #9e9e9e; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إلغاء</a>";
            echo "</div>";
            echo "</div>";
        }
        
    } else {
        echo "<div style='background: #ffcdd2; padding: 15px; margin: 10px 0; border-radius: 5px; color: #c62828;'>";
        echo "<h4>❌ المنتج غير موجود</h4>";
        echo "<p>لم يتم العثور على منتج بالمعرف: $productId</p>";
        echo "</div>";
    }
}

// Show available products for testing
echo "<h3>المنتجات المتاحة للاختبار:</h3>";

$products = fetchAll("SELECT id, name, status, created_at FROM products ORDER BY id DESC LIMIT 10");

if (!empty($products)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>ID</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>اسم المنتج</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>الحالة</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>تاريخ الإنشاء</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>إجراءات</th>";
    echo "</tr>";
    
    foreach ($products as $prod) {
        echo "<tr>";
        echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . $prod['id'] . "</td>";
        echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . htmlspecialchars($prod['name']) . "</td>";
        echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . $prod['status'] . "</td>";
        echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . $prod['created_at'] . "</td>";
        echo "<td style='padding: 10px; border: 1px solid #ddd;'>";
        echo "<a href='?direct_delete=" . $prod['id'] . "' style='background: #f44336; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>حذف مباشر</a>";
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>لا توجد منتجات في قاعدة البيانات.</p>";
}

// Test the exact URL that would be generated by the modal
echo "<hr>";
echo "<h3>اختبار رابط الحذف من products.php:</h3>";

if (!empty($products)) {
    $testProduct = $products[0];
    $testUrl = "products.php?delete=" . $testProduct['id'];
    
    echo "<p><strong>رابط الحذف المتوقع:</strong> <code>$testUrl</code></p>";
    echo "<p><strong>المنتج المستهدف:</strong> " . htmlspecialchars($testProduct['name']) . " (ID: " . $testProduct['id'] . ")</p>";
    echo "<p><a href='$testUrl' style='background: #2196f3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار رابط الحذف</a></p>";
    echo "<p><small><strong>ملاحظة:</strong> هذا سيحذف المنتج فعلياً إذا كانت وظيفة الحذف تعمل في products.php</small></p>";
}

echo "<hr>";
echo "<div style='background: #e8f5e8; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
echo "<h4>خطوات التشخيص:</h4>";
echo "<ol>";
echo "<li>جرب الحذف المباشر أعلاه لمعرفة ما إذا كانت دوال قاعدة البيانات تعمل</li>";
echo "<li>إذا نجح الحذف المباشر، فالمشكلة في JavaScript أو Modal في products.php</li>";
echo "<li>إذا فشل الحذف المباشر، فالمشكلة في دوال قاعدة البيانات</li>";
echo "<li>افحص وحدة تحكم المتصفح (F12) للأخطاء عند استخدام products.php</li>";
echo "</ol>";
echo "</div>";

echo "<p><a href='products.php' style='background: #4caf50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة إلى products.php</a></p>";
?>
