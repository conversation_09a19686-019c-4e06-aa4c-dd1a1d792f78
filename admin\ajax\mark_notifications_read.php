<?php
// Prevent any output before JSON
ob_start();

require_once '../../config/config.php';
requireAdminLogin();

// Clean any output buffer and set JSON header
ob_clean();
header('Content-Type: application/json; charset=utf-8');

try {
    // Create the notification reads table if it doesn't exist
    $createTable = "
        CREATE TABLE IF NOT EXISTS admin_notification_reads (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT NOT NULL DEFAULT 1,
            notification_type ENUM('order', 'review') NOT NULL,
            reference_id INT NOT NULL,
            read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_read (admin_id, notification_type, reference_id),
            INDEX idx_admin_type (admin_id, notification_type),
            INDEX idx_reference (reference_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $pdo->exec($createTable);

    // Get all pending orders and mark them as read
    $pendingOrders = fetchAll("SELECT id FROM orders WHERE status = 'pending'");
    $markedOrders = 0;

    foreach ($pendingOrders as $order) {
        $insertRead = "INSERT IGNORE INTO admin_notification_reads (admin_id, notification_type, reference_id) VALUES (1, 'order', ?)";
        $stmt = $pdo->prepare($insertRead);
        if ($stmt->execute([$order['id']])) {
            $markedOrders++;
        }
    }

    // Get all pending reviews and mark them as read
    $pendingReviews = fetchAll("SELECT id FROM reviews WHERE status = 'pending'");
    $markedReviews = 0;

    foreach ($pendingReviews as $review) {
        $insertRead = "INSERT IGNORE INTO admin_notification_reads (admin_id, notification_type, reference_id) VALUES (1, 'review', ?)";
        $stmt = $pdo->prepare($insertRead);
        if ($stmt->execute([$review['id']])) {
            $markedReviews++;
        }
    }

    $totalMarked = $markedOrders + $markedReviews;

    echo json_encode([
        'success' => true,
        'message' => 'تم تحديد جميع الإشعارات كمقروءة بشكل دائم',
        'marked_count' => $totalMarked,
        'marked_orders' => $markedOrders,
        'marked_reviews' => $markedReviews
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ أثناء تحديث الإشعارات: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

exit;
?>
