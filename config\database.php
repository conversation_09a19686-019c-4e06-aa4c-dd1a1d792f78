<?php
/**
 * ملف الاتصال بقاعدة البيانات
 * Database Connection Configuration
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'shop_db');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    public $conn;

    /**
     * الاتصال بقاعدة البيانات
     */
    public function getConnection() {
        $this->conn = null;

        try {
            // محاولة الاتصال بقاعدة البيانات
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            // إذا فشل الاتصال بسبب عدم وجود قاعدة البيانات
            if ($exception->getCode() == 1049) {
                try {
                    // الاتصال بدون تحديد قاعدة البيانات
                    $dsn = "mysql:host=" . $this->host . ";charset=" . $this->charset;
                    $tempConn = new PDO($dsn, $this->username, $this->password);
                    $tempConn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                    // إنشاء قاعدة البيانات
                    $tempConn->exec("CREATE DATABASE IF NOT EXISTS `" . $this->db_name . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

                    // الاتصال بقاعدة البيانات الجديدة
                    $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
                    $this->conn = new PDO($dsn, $this->username, $this->password);
                    $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

                    // تحقق من وجود الجداول وإنشاؤها إذا لزم الأمر
                    $this->createTablesIfNotExist();

                } catch(PDOException $e) {
                    die("خطأ في إنشاء قاعدة البيانات: " . $e->getMessage() . "<br><br>يرجى التأكد من:<br>1. تشغيل خدمة MySQL في XAMPP<br>2. صحة بيانات الاتصال<br>3. صلاحيات إنشاء قواعد البيانات");
                }
            } else {
                die("خطأ في الاتصال: " . $exception->getMessage() . "<br><br>يرجى التأكد من:<br>1. تشغيل خدمة MySQL في XAMPP<br>2. صحة بيانات الاتصال");
            }
        }

        return $this->conn;
    }

    /**
     * إنشاء الجداول إذا لم تكن موجودة
     */
    private function createTablesIfNotExist() {
        try {
            // تحقق من وجود جدول المديرين
            $result = $this->conn->query("SHOW TABLES LIKE 'admins'");
            if ($result->rowCount() == 0) {
                $this->createDatabaseTables();
            }
        } catch(PDOException $e) {
            error_log("Error checking tables: " . $e->getMessage());
        }
    }

    /**
     * إنشاء جداول قاعدة البيانات
     */
    private function createDatabaseTables() {
        $sqlFile = __DIR__ . '/../database.sql';

        if (!file_exists($sqlFile)) {
            throw new Exception("ملف database.sql غير موجود");
        }

        $sql = file_get_contents($sqlFile);

        if ($sql === false) {
            throw new Exception("لا يمكن قراءة ملف database.sql");
        }

        // تقسيم الاستعلامات وتنفيذها
        $statements = explode(';', $sql);

        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) &&
                $statement !== 'COMMIT' &&
                $statement !== 'START TRANSACTION' &&
                !preg_match('/^(SET|--)/i', $statement)) {
                try {
                    $this->conn->exec($statement);
                } catch (PDOException $e) {
                    // تجاهل أخطاء الجداول الموجودة مسبقاً
                    if ($e->getCode() != '42S01') { // Table already exists
                        error_log("SQL Error: " . $e->getMessage() . " - Statement: " . $statement);
                    }
                }
            }
        }
    }

    /**
     * إغلاق الاتصال
     */
    public function closeConnection() {
        $this->conn = null;
    }
}

// إنشاء اتصال عام
$database = new Database();
$pdo = $database->getConnection();

// تسجيل حالة الاتصال
if ($pdo) {
    error_log("Database connection established successfully");
} else {
    error_log("Database connection failed - PDO is null");
}

/**
 * دالة تنفيذ استعلام آمن
 */
function executeQuery($sql, $params = []) {
    global $pdo;
    try {
        $stmt = $pdo->prepare($sql);
        if (!$stmt) {
            error_log("Database Error: Failed to prepare statement");
            error_log("SQL: " . $sql);
            return false;
        }

        $result = $stmt->execute($params);
        if (!$result) {
            error_log("Database Error: Failed to execute statement");
            error_log("SQL: " . $sql);
            error_log("Params: " . json_encode($params, JSON_UNESCAPED_UNICODE));
            error_log("Error Info: " . json_encode($stmt->errorInfo()));
            return false;
        }

        return $stmt;
    } catch(PDOException $e) {
        error_log("Database PDO Exception: " . $e->getMessage());
        error_log("SQL: " . $sql);
        error_log("Params: " . json_encode($params, JSON_UNESCAPED_UNICODE));
        error_log("Error Code: " . $e->getCode());
        return false;
    }
}

/**
 * دالة جلب سجل واحد
 */
function fetchOne($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetch() : null;
}

/**
 * دالة جلب عدة سجلات
 */
function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetchAll() : [];
}

/**
 * دالة إدراج بيانات
 */
function insertData($table, $data) {
    global $pdo;

    // تسجيل محاولة الإدراج
    error_log("insertData called - Table: {$table}, Data: " . json_encode($data, JSON_UNESCAPED_UNICODE));

    if (!$pdo) {
        error_log("insertData Error: PDO connection is null");
        return false;
    }

    $columns = implode(',', array_keys($data));
    $placeholders = ':' . implode(', :', array_keys($data));

    $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
    error_log("insertData SQL: " . $sql);

    try {
        $stmt = $pdo->prepare($sql);
        if (!$stmt) {
            $errorInfo = $pdo->errorInfo();
            error_log("insertData Error: Failed to prepare statement - " . json_encode($errorInfo));
            return false;
        }

        // تسجيل البيانات قبل التنفيذ
        error_log("insertData: About to execute with data: " . json_encode($data, JSON_UNESCAPED_UNICODE));

        $result = $stmt->execute($data);
        if (!$result) {
            $errorInfo = $stmt->errorInfo();
            error_log("insertData Error: Execute failed - " . json_encode($errorInfo));
            error_log("insertData Error: SQL State: " . $errorInfo[0] . ", Error Code: " . $errorInfo[1] . ", Message: " . $errorInfo[2]);
            return false;
        }

        $lastId = $pdo->lastInsertId();
        if ($lastId) {
            error_log("insertData Success: Inserted ID = " . $lastId);
            return $lastId;
        } else {
            error_log("insertData Warning: Execute succeeded but no lastInsertId returned");
            return true; // Return true for successful insert even if no ID
        }
    } catch(PDOException $e) {
        error_log("insertData PDO Exception: " . $e->getMessage());
        error_log("insertData PDO Code: " . $e->getCode());
        error_log("insertData SQL: " . $sql);
        error_log("insertData Data: " . json_encode($data, JSON_UNESCAPED_UNICODE));
        error_log("insertData Stack Trace: " . $e->getTraceAsString());
        return false;
    }
}

/**
 * دالة تحديث بيانات - محدثة لإصلاح مشكلة ربط المعاملات
 */
function updateData($table, $data, $where, $whereParams = []) {
    global $pdo;

    // Build SET clause with named parameters
    $setClause = [];
    $allParams = [];

    foreach($data as $key => $value) {
        $paramName = "set_" . $key;
        $setClause[] = "{$key} = :{$paramName}";
        $allParams[$paramName] = $value;
    }
    $setClause = implode(', ', $setClause);

    // Convert WHERE clause to use named parameters
    $whereNamed = $where;
    $paramIndex = 0;

    // Replace ? with named parameters to avoid conflicts
    while (strpos($whereNamed, '?') !== false) {
        $paramName = "where_param_" . $paramIndex;
        $whereNamed = preg_replace('/\?/', ":" . $paramName, $whereNamed, 1);
        if (isset($whereParams[$paramIndex])) {
            $allParams[$paramName] = $whereParams[$paramIndex];
        }
        $paramIndex++;
    }

    $sql = "UPDATE {$table} SET {$setClause} WHERE {$whereNamed}";

    try {
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($allParams);

        if ($result) {
            return $stmt->rowCount();
        } else {
            error_log("Update Error: SQL execution failed. SQL: " . $sql . " Params: " . print_r($allParams, true));
            return false;
        }
    } catch(PDOException $e) {
        error_log("Update Error: " . $e->getMessage() . " SQL: " . $sql . " Params: " . print_r($allParams, true));
        return false;
    }
}

/**
 * دالة حذف بيانات - محدثة لإصلاح مشكلة ربط المعاملات
 */
function deleteData($table, $where, $params = []) {
    global $pdo;

    // Convert WHERE clause to use named parameters for consistency
    $whereNamed = $where;
    $namedParams = [];
    $paramIndex = 0;

    // Replace ? with named parameters
    while (strpos($whereNamed, '?') !== false) {
        $paramName = "param_" . $paramIndex;
        $whereNamed = preg_replace('/\?/', ":" . $paramName, $whereNamed, 1);
        if (isset($params[$paramIndex])) {
            $namedParams[$paramName] = $params[$paramIndex];
        }
        $paramIndex++;
    }

    $sql = "DELETE FROM {$table} WHERE {$whereNamed}";

    try {
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($namedParams);

        if ($result) {
            return $stmt->rowCount();
        } else {
            error_log("Delete Error: SQL execution failed. SQL: " . $sql . " Params: " . print_r($namedParams, true));
            return false;
        }
    } catch(PDOException $e) {
        error_log("Delete Error: " . $e->getMessage() . " SQL: " . $sql . " Params: " . print_r($namedParams, true));
        return false;
    }
}

/**
 * دالة تنظيف البيانات
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * دالة التحقق من صحة البريد الإلكتروني
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * دالة تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * دالة التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}
?>
