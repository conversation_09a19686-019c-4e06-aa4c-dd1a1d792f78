<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'إدارة الطلبات';

// معالجة الإجراءات المختلفة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];

    switch ($action) {
        case 'update_status':
            $orderId = (int)$_POST['order_id'];
            $newStatus = sanitizeInput($_POST['status']);

            $validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];

            if (in_array($newStatus, $validStatuses)) {
                // جلب الحالة القديمة للتسجيل
                $oldOrder = fetchOne("SELECT status FROM orders WHERE id = ?", [$orderId]);

                $updateResult = updateData('orders', ['status' => $newStatus], 'id = ?', [$orderId]);

                if ($updateResult) {
                    // تسجيل تغيير الحالة
                    try {
                        $pdo->exec("CREATE TABLE IF NOT EXISTS order_status_log (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            order_id INT NOT NULL,
                            old_status VARCHAR(50),
                            new_status VARCHAR(50),
                            admin_id INT,
                            changed_at DATETIME,
                            notes TEXT,
                            INDEX(order_id)
                        )");

                        $logData = [
                            'order_id' => $orderId,
                            'old_status' => $oldOrder['status'] ?? '',
                            'new_status' => $newStatus,
                            'admin_id' => $_SESSION['admin_id'] ?? 0,
                            'changed_at' => date('Y-m-d H:i:s'),
                            'notes' => 'تم تغيير الحالة من لوحة إدارة الطلبات'
                        ];

                        insertData('order_status_log', $logData);
                    } catch (Exception $e) {
                        error_log("Failed to log status change: " . $e->getMessage());
                    }

                    $_SESSION['success'] = 'تم تحديث حالة الطلب بنجاح';
                } else {
                    $_SESSION['error'] = 'فشل في تحديث حالة الطلب';
                }
            } else {
                $_SESSION['error'] = 'حالة الطلب غير صحيحة';
            }
            break;

        case 'delete_order':
            $orderId = (int)$_POST['order_id'];

            try {
                $pdo->beginTransaction();

                // التحقق من وجود الطلب
                $order = fetchOne("SELECT * FROM orders WHERE id = ?", [$orderId]);
                if (!$order) {
                    throw new Exception('الطلب غير موجود');
                }

                // حذف عناصر الطلب أولاً
                $deleteItemsResult = $pdo->prepare("DELETE FROM order_items WHERE order_id = ?");
                $deleteItemsResult->execute([$orderId]);

                // حذف سجل تغييرات الحالة إن وجد
                $pdo->prepare("DELETE FROM order_status_log WHERE order_id = ?")->execute([$orderId]);

                // حذف الطلب
                $deleteOrderResult = $pdo->prepare("DELETE FROM orders WHERE id = ?");
                $deleteOrderResult->execute([$orderId]);

                if ($deleteOrderResult->rowCount() > 0) {
                    $pdo->commit();

                    // تسجيل عملية الحذف
                    error_log("Order deleted: ID=$orderId, Customer={$order['customer_name']}, Admin=" . ($_SESSION['admin_id'] ?? 'unknown'));

                    $_SESSION['success'] = "تم حذف الطلب #$orderId بنجاح";
                } else {
                    throw new Exception('فشل في حذف الطلب');
                }

            } catch (Exception $e) {
                $pdo->rollBack();
                $_SESSION['error'] = 'خطأ في حذف الطلب: ' . $e->getMessage();
                error_log("Failed to delete order $orderId: " . $e->getMessage());
            }
            break;

        case 'bulk_status_update':
            $orderIds = $_POST['order_ids'] ?? [];
            $newStatus = sanitizeInput($_POST['bulk_status']);

            if (!empty($orderIds) && !empty($newStatus)) {
                $validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];

                if (in_array($newStatus, $validStatuses)) {
                    $updatedCount = 0;

                    foreach ($orderIds as $orderId) {
                        $orderId = (int)$orderId;
                        $updateResult = updateData('orders', ['status' => $newStatus], 'id = ?', [$orderId]);
                        if ($updateResult) {
                            $updatedCount++;
                        }
                    }

                    $_SESSION['success'] = "تم تحديث حالة $updatedCount طلب بنجاح";
                } else {
                    $_SESSION['error'] = 'حالة الطلب غير صحيحة';
                }
            } else {
                $_SESSION['error'] = 'يرجى اختيار الطلبات والحالة الجديدة';
            }
            break;

        case 'bulk_delete':
            $orderIds = $_POST['order_ids'] ?? [];

            if (!empty($orderIds)) {
                try {
                    $pdo->beginTransaction();

                    $deletedCount = 0;
                    $deletedOrders = [];

                    foreach ($orderIds as $orderId) {
                        $orderId = (int)$orderId;

                        // التحقق من وجود الطلب وجلب معلوماته للتسجيل
                        $order = fetchOne("SELECT id, customer_name FROM orders WHERE id = ?", [$orderId]);
                        if (!$order) {
                            continue; // تخطي الطلبات غير الموجودة
                        }

                        // حذف عناصر الطلب أولاً
                        $deleteItemsResult = $pdo->prepare("DELETE FROM order_items WHERE order_id = ?");
                        $deleteItemsResult->execute([$orderId]);

                        // حذف سجل تغييرات الحالة إن وجد
                        $pdo->prepare("DELETE FROM order_status_log WHERE order_id = ?")->execute([$orderId]);

                        // حذف الطلب
                        $deleteOrderResult = $pdo->prepare("DELETE FROM orders WHERE id = ?");
                        $deleteOrderResult->execute([$orderId]);

                        if ($deleteOrderResult->rowCount() > 0) {
                            $deletedCount++;
                            $deletedOrders[] = "#{$orderId} ({$order['customer_name']})";
                        }
                    }

                    if ($deletedCount > 0) {
                        $pdo->commit();

                        // تسجيل عملية الحذف المجمع
                        error_log("Bulk delete completed: $deletedCount orders deleted by admin " . ($_SESSION['admin_id'] ?? 'unknown'));
                        error_log("Deleted orders: " . implode(', ', $deletedOrders));

                        $_SESSION['success'] = "تم حذف $deletedCount طلب بنجاح";
                    } else {
                        $pdo->rollBack();
                        $_SESSION['error'] = 'لم يتم حذف أي طلب. تأكد من وجود الطلبات المحددة';
                    }

                } catch (Exception $e) {
                    $pdo->rollBack();
                    $_SESSION['error'] = 'خطأ في حذف الطلبات: ' . $e->getMessage();
                    error_log("Bulk delete failed: " . $e->getMessage());
                }
            } else {
                $_SESSION['error'] = 'يرجى اختيار الطلبات المراد حذفها';
            }
            break;

        case 'export_excel':
            // معالجة تصدير Excel
            exportOrdersToExcel($whereClause, $params);
            exit(); // إنهاء التنفيذ بعد التصدير
            break;
    }

    header('Location: orders.php');
    exit();
}

// معالجة طلبات AJAX
if (isset($_GET['ajax']) && $_GET['ajax'] === '1') {
    // تأكد من عدم وجود أي إخراج HTML قبل JSON
    ob_clean();
    header('Content-Type: application/json; charset=utf-8');

    try {
        if (isset($_POST['action'])) {
            $action = $_POST['action'];

            switch ($action) {
                case 'quick_status_update':
                    $orderId = (int)$_POST['order_id'];
                    $newStatus = sanitizeInput($_POST['status']);

                    $validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];

                    if (in_array($newStatus, $validStatuses)) {
                        $updateResult = updateData('orders', ['status' => $newStatus], 'id = ?', [$orderId]);

                        if ($updateResult) {
                            echo json_encode([
                                'success' => true,
                                'message' => 'تم تحديث حالة الطلب بنجاح',
                                'new_status' => $newStatus
                            ], JSON_UNESCAPED_UNICODE);
                        } else {
                            echo json_encode([
                                'success' => false,
                                'message' => 'فشل في تحديث حالة الطلب'
                            ], JSON_UNESCAPED_UNICODE);
                        }
                    } else {
                        echo json_encode([
                            'success' => false,
                            'message' => 'حالة الطلب غير صحيحة'
                        ], JSON_UNESCAPED_UNICODE);
                    }
                    break;

                default:
                    echo json_encode([
                        'success' => false,
                        'message' => 'إجراء غير صحيح'
                    ], JSON_UNESCAPED_UNICODE);
                    break;
            }
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'لم يتم تحديد الإجراء المطلوب'
            ], JSON_UNESCAPED_UNICODE);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'حدث خطأ: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
        error_log("AJAX Error in orders.php: " . $e->getMessage());
    }

    exit();
}

// دالة تصدير الطلبات إلى Excel (XLSX Format)
function exportOrdersToExcel($whereClause, $params) {
    global $pdo;

    try {
        // جلب الطلبات حسب الفلاتر
        $sql = "SELECT o.*,
                       COUNT(oi.id) as items_count,
                       SUM(oi.quantity) as total_quantity
                FROM orders o
                LEFT JOIN order_items oi ON o.id = oi.order_id
                $whereClause
                GROUP BY o.id
                ORDER BY o.created_at DESC";

        $orders = fetchAll($sql, $params);

        if (empty($orders)) {
            $_SESSION['error'] = 'لا توجد طلبات للتصدير';
            header('Location: orders.php');
            exit();
        }

        // إعداد اسم الملف
        $filename = 'orders_export_' . date('Y-m-d_H-i-s') . '.xlsx';

        // إنشاء ملف Excel باستخدام XML
        $excel_data = generateExcelXML($orders);

        // إعداد headers للتحميل
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        echo $excel_data;
        exit();

    } catch (Exception $e) {
        error_log("Excel export error: " . $e->getMessage());
        $_SESSION['error'] = 'خطأ في تصدير البيانات: ' . $e->getMessage();
        header('Location: orders.php');
        exit();
    }
}

// دالة إنشاء ملف Excel XML
function generateExcelXML($orders) {
    $statusNames = [
        'pending' => 'معلق',
        'confirmed' => 'مؤكد',
        'processing' => 'قيد التحضير',
        'shipped' => 'مشحون',
        'delivered' => 'مكتمل',
        'cancelled' => 'ملغي'
    ];

    $paymentMethods = [
        'cash_on_delivery' => 'الدفع عند الاستلام',
        'bank_transfer' => 'تحويل بنكي'
    ];

    // بداية ملف Excel XML
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">' . "\n";

    // إعدادات المستند
    $xml .= '<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">' . "\n";
    $xml .= '<Title>تقرير الطلبات</Title>' . "\n";
    $xml .= '<Author>نظام إدارة المتجر</Author>' . "\n";
    $xml .= '<Created>' . date('Y-m-d\TH:i:s\Z') . '</Created>' . "\n";
    $xml .= '</DocumentProperties>' . "\n";

    // الأنماط
    $xml .= '<Styles>' . "\n";
    $xml .= '<Style ss:ID="Header">' . "\n";
    $xml .= '<Font ss:Bold="1" ss:Size="12"/>' . "\n";
    $xml .= '<Interior ss:Color="#4472C4" ss:Pattern="Solid"/>' . "\n";
    $xml .= '<Font ss:Color="#FFFFFF"/>' . "\n";
    $xml .= '<Alignment ss:Horizontal="Center" ss:Vertical="Center"/>' . "\n";
    $xml .= '<Borders>' . "\n";
    $xml .= '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    $xml .= '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    $xml .= '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    $xml .= '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    $xml .= '</Borders>' . "\n";
    $xml .= '</Style>' . "\n";

    $xml .= '<Style ss:ID="Data">' . "\n";
    $xml .= '<Font ss:Size="10"/>' . "\n";
    $xml .= '<Alignment ss:Horizontal="Right" ss:Vertical="Center"/>' . "\n";
    $xml .= '<Borders>' . "\n";
    $xml .= '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '</Borders>' . "\n";
    $xml .= '</Style>' . "\n";

    $xml .= '<Style ss:ID="Currency">' . "\n";
    $xml .= '<Font ss:Size="10"/>' . "\n";
    $xml .= '<Alignment ss:Horizontal="Right" ss:Vertical="Center"/>' . "\n";
    $xml .= '<NumberFormat ss:Format="#,##0 &quot;دينار&quot;"/>' . "\n";
    $xml .= '<Borders>' . "\n";
    $xml .= '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#D0D0D0"/>' . "\n";
    $xml .= '</Borders>' . "\n";
    $xml .= '</Style>' . "\n";
    $xml .= '</Styles>' . "\n";

    // ورقة العمل
    $xml .= '<Worksheet ss:Name="الطلبات">' . "\n";
    $xml .= '<Table>' . "\n";

    // تحديد عرض الأعمدة
    $xml .= '<Column ss:Width="80"/>' . "\n";  // رقم الطلب
    $xml .= '<Column ss:Width="120"/>' . "\n"; // اسم العميل
    $xml .= '<Column ss:Width="100"/>' . "\n"; // رقم الهاتف
    $xml .= '<Column ss:Width="80"/>' . "\n";  // المحافظة
    $xml .= '<Column ss:Width="200"/>' . "\n"; // العنوان
    $xml .= '<Column ss:Width="100"/>' . "\n"; // المبلغ الإجمالي
    $xml .= '<Column ss:Width="80"/>' . "\n";  // الحالة
    $xml .= '<Column ss:Width="120"/>' . "\n"; // طريقة الدفع
    $xml .= '<Column ss:Width="80"/>' . "\n";  // عدد المنتجات
    $xml .= '<Column ss:Width="80"/>' . "\n";  // إجمالي الكمية
    $xml .= '<Column ss:Width="120"/>' . "\n"; // تاريخ الطلب
    $xml .= '<Column ss:Width="150"/>' . "\n"; // ملاحظات

    // صف العناوين
    $xml .= '<Row>' . "\n";
    $headers = [
        'رقم الطلب', 'اسم العميل', 'رقم الهاتف', 'المحافظة',
        'العنوان', 'المبلغ الإجمالي', 'الحالة', 'طريقة الدفع',
        'عدد المنتجات', 'إجمالي الكمية', 'تاريخ الطلب', 'ملاحظات'
    ];

    foreach ($headers as $header) {
        $xml .= '<Cell ss:StyleID="Header"><Data ss:Type="String">' . htmlspecialchars($header, ENT_XML1, 'UTF-8') . '</Data></Cell>' . "\n";
    }
    $xml .= '</Row>' . "\n";

    // صفوف البيانات
    foreach ($orders as $order) {
        $xml .= '<Row>' . "\n";

        // رقم الطلب
        $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">#' . $order['id'] . '</Data></Cell>' . "\n";

        // اسم العميل
        $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($order['customer_name'], ENT_XML1, 'UTF-8') . '</Data></Cell>' . "\n";

        // رقم الهاتف
        $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($order['customer_phone'], ENT_XML1, 'UTF-8') . '</Data></Cell>' . "\n";

        // المحافظة
        $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($order['province'], ENT_XML1, 'UTF-8') . '</Data></Cell>' . "\n";

        // العنوان
        $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($order['address'], ENT_XML1, 'UTF-8') . '</Data></Cell>' . "\n";

        // المبلغ الإجمالي
        $xml .= '<Cell ss:StyleID="Currency"><Data ss:Type="Number">' . $order['total_price'] . '</Data></Cell>' . "\n";

        // الحالة
        $statusText = $statusNames[$order['status']] ?? $order['status'];
        $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($statusText, ENT_XML1, 'UTF-8') . '</Data></Cell>' . "\n";

        // طريقة الدفع
        $paymentText = $paymentMethods[$order['payment_method']] ?? $order['payment_method'];
        $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($paymentText, ENT_XML1, 'UTF-8') . '</Data></Cell>' . "\n";

        // عدد المنتجات
        $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="Number">' . ($order['items_count'] ?? 0) . '</Data></Cell>' . "\n";

        // إجمالي الكمية
        $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="Number">' . ($order['total_quantity'] ?? 0) . '</Data></Cell>' . "\n";

        // تاريخ الطلب
        $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . date('Y-m-d H:i', strtotime($order['created_at'])) . '</Data></Cell>' . "\n";

        // ملاحظات
        $notes = $order['notes'] ? htmlspecialchars($order['notes'], ENT_XML1, 'UTF-8') : '';
        $xml .= '<Cell ss:StyleID="Data"><Data ss:Type="String">' . $notes . '</Data></Cell>' . "\n";

        $xml .= '</Row>' . "\n";
    }

    $xml .= '</Table>' . "\n";
    $xml .= '</Worksheet>' . "\n";
    $xml .= '</Workbook>' . "\n";

    return $xml;
}

// الفلترة والبحث
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$status = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
$dateFrom = isset($_GET['date_from']) ? sanitizeInput($_GET['date_from']) : '';
$dateTo = isset($_GET['date_to']) ? sanitizeInput($_GET['date_to']) : '';

$whereClause = "1=1";
$params = [];

if (!empty($search)) {
    $whereClause .= " AND (customer_name LIKE ? OR customer_phone LIKE ? OR customer_email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($status)) {
    $whereClause .= " AND status = ?";
    $params[] = $status;
}

if (!empty($dateFrom)) {
    $whereClause .= " AND DATE(created_at) >= ?";
    $params[] = $dateFrom;
}

if (!empty($dateTo)) {
    $whereClause .= " AND DATE(created_at) <= ?";
    $params[] = $dateTo;
}

// الترقيم
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 20;
$offset = ($page - 1) * $perPage;

// عدد الطلبات الإجمالي
$countResult = fetchOne("SELECT COUNT(*) as total FROM orders WHERE $whereClause", $params);
$totalOrders = ($countResult && isset($countResult['total'])) ? $countResult['total'] : 0;
$totalPages = ceil($totalOrders / $perPage);

// جلب الطلبات
$orders = fetchAll("
    SELECT * FROM orders 
    WHERE $whereClause 
    ORDER BY created_at DESC 
    LIMIT $perPage OFFSET $offset
", $params);

// إحصائيات سريعة
$stats = [
    'pending' => fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'"),
    'confirmed' => fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'confirmed'"),
    'processing' => fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'processing'"),
    'shipped' => fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'shipped'"),
    'delivered' => fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'delivered'"),
    'cancelled' => fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'cancelled'")
];

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card text-center border-warning">
                <div class="card-body">
                    <h5 class="text-warning"><?php echo ($stats['pending'] && isset($stats['pending']['count'])) ? $stats['pending']['count'] : 0; ?></h5>
                    <small>معلقة</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center border-info">
                <div class="card-body">
                    <h5 class="text-info"><?php echo ($stats['confirmed'] && isset($stats['confirmed']['count'])) ? $stats['confirmed']['count'] : 0; ?></h5>
                    <small>مؤكدة</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center border-primary">
                <div class="card-body">
                    <h5 class="text-primary"><?php echo ($stats['processing'] && isset($stats['processing']['count'])) ? $stats['processing']['count'] : 0; ?></h5>
                    <small>قيد التحضير</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center border-secondary">
                <div class="card-body">
                    <h5 class="text-secondary"><?php echo ($stats['shipped'] && isset($stats['shipped']['count'])) ? $stats['shipped']['count'] : 0; ?></h5>
                    <small>مشحونة</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center border-success">
                <div class="card-body">
                    <h5 class="text-success"><?php echo ($stats['delivered'] && isset($stats['delivered']['count'])) ? $stats['delivered']['count'] : 0; ?></h5>
                    <small>مكتملة</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card text-center border-danger">
                <div class="card-body">
                    <h5 class="text-danger"><?php echo ($stats['cancelled'] && isset($stats['cancelled']['count'])) ? $stats['cancelled']['count'] : 0; ?></h5>
                    <small>ملغية</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">إدارة الطلبات</h5>
                </div>
                
                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <div class="filters-section" style="font-size: 1.1em;">
                        <form method="GET" class="row g-3 mb-4">
                            <div class="col-md-3">
                                <input type="text" class="form-control form-control-lg" name="search"
                                       placeholder="البحث في العملاء..." value="<?php echo htmlspecialchars($search); ?>"
                                       style="font-size: 1em;">
                            </div>
                            <div class="col-md-2">
                                <select name="status" class="form-select form-select-lg" style="font-size: 1em;">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending" <?php echo $status == 'pending' ? 'selected' : ''; ?>>معلق</option>
                                    <option value="confirmed" <?php echo $status == 'confirmed' ? 'selected' : ''; ?>>مؤكد</option>
                                    <option value="processing" <?php echo $status == 'processing' ? 'selected' : ''; ?>>قيد التحضير</option>
                                    <option value="shipped" <?php echo $status == 'shipped' ? 'selected' : ''; ?>>مشحون</option>
                                    <option value="delivered" <?php echo $status == 'delivered' ? 'selected' : ''; ?>>مكتمل</option>
                                    <option value="cancelled" <?php echo $status == 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="date" class="form-control form-control-lg" name="date_from"
                                       placeholder="من تاريخ" value="<?php echo $dateFrom; ?>" style="font-size: 1em;">
                            </div>
                            <div class="col-md-2">
                                <input type="date" class="form-control form-control-lg" name="date_to"
                                       placeholder="إلى تاريخ" value="<?php echo $dateTo; ?>" style="font-size: 1em;">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-outline-primary btn-lg w-100" style="font-size: 1em;">
                                    <i class="bi bi-search"></i> بحث
                                </button>
                            </div>
                            <div class="col-md-1">
                                <a href="orders.php" class="btn btn-outline-secondary btn-lg w-100" style="font-size: 1em;">
                                    <i class="bi bi-arrow-clockwise"></i> مسح
                                </a>
                            </div>
                        </form>

                        <!-- أزرار العمليات الإضافية -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="d-flex gap-2">
                                    <!-- زر تصدير Excel -->
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="export_excel">
                                        <input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>">
                                        <input type="hidden" name="status" value="<?php echo htmlspecialchars($status); ?>">
                                        <input type="hidden" name="date_from" value="<?php echo htmlspecialchars($dateFrom); ?>">
                                        <input type="hidden" name="date_to" value="<?php echo htmlspecialchars($dateTo); ?>">
                                        <button type="submit" class="btn btn-success btn-lg" style="font-size: 1em;">
                                            <i class="bi bi-file-earmark-excel"></i>
                                            تصدير Excel
                                        </button>
                                    </form>

                                    <!-- زر الحذف المجمع -->
                                    <form method="POST" id="bulkDeleteForm" class="d-inline">
                                        <input type="hidden" name="action" value="bulk_delete">
                                        <button type="submit" class="btn btn-danger btn-lg" id="bulkDeleteBtn"
                                                disabled style="font-size: 1em;">
                                            <i class="bi bi-trash"></i>
                                            حذف المحدد
                                        </button>
                                    </form>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <span class="text-muted" style="font-size: 1.1em;">
                                    إجمالي الطلبات: <strong><?= $totalOrders ?></strong>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Success/Error Messages -->
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle"></i>
                            <?= $_SESSION['success'] ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php unset($_SESSION['success']); ?>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle"></i>
                            <?= $_SESSION['error'] ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php unset($_SESSION['error']); ?>
                    <?php endif; ?>

                    <!-- Bulk Status Update Form (for selected orders) -->
                    <?php if (!empty($orders)): ?>
                        <div class="row mb-3" id="bulkStatusSection" style="display: none;">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <form method="POST" id="bulkStatusForm" class="d-flex gap-2 align-items-center">
                                        <input type="hidden" name="action" value="bulk_status_update">
                                        <span><strong>تغيير حالة الطلبات المحددة إلى:</strong></span>
                                        <select name="bulk_status" class="form-select" style="width: auto; font-size: 1em;">
                                            <option value="">اختر الحالة الجديدة</option>
                                            <option value="pending">معلق</option>
                                            <option value="confirmed">مؤكد</option>
                                            <option value="processing">قيد التحضير</option>
                                            <option value="shipped">مشحون</option>
                                            <option value="delivered">مكتمل</option>
                                            <option value="cancelled">ملغي</option>
                                        </select>
                                        <button type="submit" class="btn btn-primary" style="font-size: 1em;">
                                            <i class="bi bi-arrow-repeat"></i>
                                            تطبيق التغيير
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="cancelBulkSelection()" style="font-size: 1em;">
                                            <i class="bi bi-x"></i>
                                            إلغاء
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- جدول الطلبات -->
                    <?php if (!empty($orders)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="selectAll" class="form-check-input">
                                        </th>
                                        <th>رقم الطلب</th>
                                        <th>العميل</th>
                                        <th>الهاتف</th>
                                        <th>المحافظة</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>الحالة</th>
                                        <th>طريقة الدفع</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($orders as $order): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" name="order_ids[]" value="<?= $order['id'] ?>"
                                                       class="form-check-input order-checkbox">
                                            </td>
                                            <td>
                                                <strong>#<?= $order['id'] ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <?= date('d/m/Y', strtotime($order['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="fw-bold"><?= htmlspecialchars($order['customer_name']) ?></div>
                                                <?php if (!empty($order['customer_email'])): ?>
                                                    <small class="text-muted"><?= htmlspecialchars($order['customer_email']) ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="tel:<?= htmlspecialchars($order['customer_phone']) ?>"
                                                   class="text-decoration-none">
                                                    <i class="bi bi-telephone"></i>
                                                    <?= htmlspecialchars($order['customer_phone']) ?>
                                                </a>
                                            </td>
                                            <td>
                                                <i class="bi bi-geo-alt"></i>
                                                <?= htmlspecialchars($order['province']) ?>
                                            </td>
                                            <td>
                                                <span class="fw-bold text-success">
                                                    <?= number_format($order['total_price']) ?> دينار
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $statusConfig = [
                                                    'pending' => ['color' => 'warning', 'icon' => 'clock', 'text' => 'معلق'],
                                                    'confirmed' => ['color' => 'info', 'icon' => 'check-circle', 'text' => 'مؤكد'],
                                                    'processing' => ['color' => 'primary', 'icon' => 'gear', 'text' => 'قيد التحضير'],
                                                    'shipped' => ['color' => 'secondary', 'icon' => 'truck', 'text' => 'مشحون'],
                                                    'delivered' => ['color' => 'success', 'icon' => 'check-circle-fill', 'text' => 'مكتمل'],
                                                    'cancelled' => ['color' => 'danger', 'icon' => 'x-circle', 'text' => 'ملغي']
                                                ];
                                                $config = $statusConfig[$order['status']] ?? ['color' => 'secondary', 'icon' => 'circle', 'text' => $order['status']];
                                                ?>
                                                <span class="badge bg-<?= $config['color'] ?> fs-6">
                                                    <i class="bi bi-<?= $config['icon'] ?>"></i>
                                                    <?= $config['text'] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($order['payment_method'] === 'cash_on_delivery'): ?>
                                                    <i class="bi bi-cash text-success"></i>
                                                    <small>الدفع عند الاستلام</small>
                                                <?php elseif ($order['payment_method'] === 'bank_transfer'): ?>
                                                    <i class="bi bi-bank text-info"></i>
                                                    <small>تحويل بنكي</small>
                                                <?php else: ?>
                                                    <?= htmlspecialchars($order['payment_method']) ?>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <?= date('Y-m-d', strtotime($order['created_at'])) ?>
                                                    <br>
                                                    <span class="text-muted"><?= date('H:i', strtotime($order['created_at'])) ?></span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="view_order.php?id=<?= $order['id'] ?>"
                                                       class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                        <i class="bi bi-eye"></i>
                                                    </a>

                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                            onclick="confirmDeleteOrder(<?= $order['id'] ?>, '<?= addslashes($order['customer_name']) ?>')"
                                                            title="حذف الطلب">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- الترقيم -->
                        <?php if ($totalPages > 1): ?>
                            <nav aria-label="ترقيم الصفحات">
                                <ul class="pagination justify-content-center">
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status; ?>&date_from=<?php echo $dateFrom; ?>&date_to=<?php echo $dateTo; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                        
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-cart-x display-1 text-muted"></i>
                            <h4 class="mt-3">لا توجد طلبات</h4>
                            <p class="text-muted">لم يتم العثور على طلبات تطابق معايير البحث</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Professional Confirmation Modals -->
<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-danger text-white border-0">
                <h5 class="modal-title" id="deleteConfirmModalLabel">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    تأكيد حذف الطلب
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i class="bi bi-trash3 text-danger" style="font-size: 3rem;"></i>
                </div>
                <h6 class="mb-3" id="deleteMessage">هل أنت متأكد من حذف هذا الطلب؟</h6>
                <div class="alert alert-warning border-0 bg-warning bg-opacity-10">
                    <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع بيانات الطلب نهائياً
                </div>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-danger px-4" id="confirmDeleteBtn">
                    <i class="bi bi-trash me-1"></i>
                    <span class="btn-text">حذف الطلب</span>
                    <span class="spinner-border spinner-border-sm d-none me-1" role="status"></span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Confirmation Modal -->
<div class="modal fade" id="bulkDeleteConfirmModal" tabindex="-1" aria-labelledby="bulkDeleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-danger text-white border-0">
                <h5 class="modal-title" id="bulkDeleteConfirmModalLabel">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    تأكيد الحذف المجمع
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i class="bi bi-trash3-fill text-danger" style="font-size: 3rem;"></i>
                </div>
                <h6 class="mb-3" id="bulkDeleteMessage">هل أنت متأكد من حذف الطلبات المحددة؟</h6>
                <div class="alert alert-danger border-0 bg-danger bg-opacity-10">
                    <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>
                    <strong>تحذير شديد:</strong> سيتم حذف جميع الطلبات المحددة وعناصرها نهائياً ولا يمكن استرجاعها
                </div>
                <div class="bg-light rounded p-3 mt-3">
                    <small class="text-muted">عدد الطلبات المحددة: <strong id="selectedCount">0</strong></small>
                </div>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-danger px-4" id="confirmBulkDeleteBtn">
                    <i class="bi bi-trash me-1"></i>
                    <span class="btn-text">حذف الطلبات</span>
                    <span class="spinner-border spinner-border-sm d-none me-1" role="status"></span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Confirmation Modal -->
<div class="modal fade" id="statusUpdateConfirmModal" tabindex="-1" aria-labelledby="statusUpdateConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-primary text-white border-0">
                <h5 class="modal-title" id="statusUpdateConfirmModalLabel">
                    <i class="bi bi-arrow-repeat me-2"></i>
                    تأكيد تحديث الحالة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i class="bi bi-arrow-up-circle text-primary" style="font-size: 3rem;"></i>
                </div>
                <h6 class="mb-3" id="statusUpdateMessage">هل أنت متأكد من تحديث حالة الطلبات؟</h6>
                <div class="bg-light rounded p-3">
                    <div class="row text-start">
                        <div class="col-6">
                            <small class="text-muted">عدد الطلبات:</small>
                            <div><strong id="statusUpdateCount">0</strong></div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">الحالة الجديدة:</small>
                            <div><strong id="statusUpdateNewStatus">-</strong></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-primary px-4" id="confirmStatusUpdateBtn">
                    <i class="bi bi-check-circle me-1"></i>
                    <span class="btn-text">تحديث الحالة</span>
                    <span class="spinner-border spinner-border-sm d-none me-1" role="status"></span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Success Toast Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
    <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="bi bi-check-circle-fill me-2"></i>
                <span id="successMessage">تم تنفيذ العملية بنجاح</span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="إغلاق"></button>
        </div>
    </div>
</div>

<!-- Error Toast Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999; margin-top: 80px;">
    <div id="errorToast" class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <span id="errorMessage">حدث خطأ أثناء تنفيذ العملية</span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="إغلاق"></button>
        </div>
    </div>
</div>

<style>
/* Professional Modal Styling */
.modal-content {
    border-radius: 15px;
    overflow: hidden;
}

.modal-header {
    padding: 1.5rem;
}

.modal-body {
    padding: 2rem 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
}

/* Button Loading State */
.btn .spinner-border {
    width: 1rem;
    height: 1rem;
}

.btn.loading .btn-text {
    opacity: 0.7;
}

.btn.loading .spinner-border {
    display: inline-block !important;
}

/* Toast Styling */
.toast {
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.toast-body {
    padding: 1rem;
    font-weight: 500;
}

/* RTL Support for Modals */
.modal-dialog {
    direction: rtl;
}

.modal-content {
    text-align: right;
}

.modal-footer {
    flex-direction: row-reverse;
}

/* Animation for icons */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.modal-body i[style*="font-size: 3rem"] {
    animation: pulse 2s infinite;
}
</style>

<script>
// Global variables
let selectedOrders = [];

// Select All functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.order-checkbox');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const bulkStatusSection = document.getElementById('bulkStatusSection');

    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });

    updateBulkActionButtons();
});

// Individual checkbox handling
document.querySelectorAll('.order-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        updateBulkActionButtons();
    });
});

// Update bulk action buttons based on selection
function updateBulkActionButtons() {
    const checkedBoxes = document.querySelectorAll('.order-checkbox:checked');
    const selectAllBox = document.getElementById('selectAll');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const bulkStatusSection = document.getElementById('bulkStatusSection');

    // Update select all checkbox
    selectAllBox.checked = checkedBoxes.length === document.querySelectorAll('.order-checkbox').length;
    selectAllBox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < document.querySelectorAll('.order-checkbox').length;

    // Enable/disable bulk action buttons
    const hasSelection = checkedBoxes.length > 0;
    bulkDeleteBtn.disabled = !hasSelection;

    // Show/hide bulk status section
    if (hasSelection) {
        bulkStatusSection.style.display = 'block';
    } else {
        bulkStatusSection.style.display = 'none';
    }

    // Update selected orders array
    selectedOrders = Array.from(checkedBoxes).map(cb => cb.value);
}

// Cancel bulk selection
function cancelBulkSelection() {
    document.querySelectorAll('.order-checkbox').forEach(cb => cb.checked = false);
    document.getElementById('selectAll').checked = false;
    updateBulkActionButtons();
}

// Bulk delete form submission
document.getElementById('bulkDeleteForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const checkedBoxes = document.querySelectorAll('.order-checkbox:checked');

    if (checkedBoxes.length === 0) {
        showErrorToast('يرجى اختيار طلب واحد على الأقل للحذف');
        return;
    }

    // Show professional confirmation modal
    showBulkDeleteConfirmation(checkedBoxes.length, this);
});

// Bulk status update form submission
document.getElementById('bulkStatusForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const checkedBoxes = document.querySelectorAll('.order-checkbox:checked');
    const bulkStatus = document.querySelector('[name="bulk_status"]').value;

    if (checkedBoxes.length === 0) {
        showErrorToast('يرجى اختيار طلب واحد على الأقل');
        return;
    }

    if (!bulkStatus) {
        showErrorToast('يرجى اختيار الحالة الجديدة');
        return;
    }

    // Show professional confirmation modal
    showStatusUpdateConfirmation(checkedBoxes.length, bulkStatus, this);
});

// Professional Modal Functions

// Show bulk delete confirmation modal
function showBulkDeleteConfirmation(count, form) {
    document.getElementById('bulkDeleteMessage').textContent = `هل أنت متأكد من حذف ${count} طلب؟`;
    document.getElementById('selectedCount').textContent = count;

    const modal = new bootstrap.Modal(document.getElementById('bulkDeleteConfirmModal'));
    modal.show();

    // Handle confirmation
    document.getElementById('confirmBulkDeleteBtn').onclick = function() {
        showButtonLoading(this);

        // Add selected order IDs to form
        const checkedBoxes = document.querySelectorAll('.order-checkbox:checked');
        checkedBoxes.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'order_ids[]';
            input.value = checkbox.value;
            form.appendChild(input);
        });

        // Submit the form
        form.submit();
    };
}

// Show status update confirmation modal
function showStatusUpdateConfirmation(count, newStatus, form) {
    const statusNames = {
        'pending': 'معلق',
        'confirmed': 'مؤكد',
        'processing': 'قيد التحضير',
        'shipped': 'مشحون',
        'delivered': 'مكتمل',
        'cancelled': 'ملغي'
    };

    document.getElementById('statusUpdateMessage').textContent = `هل أنت متأكد من تحديث حالة ${count} طلب؟`;
    document.getElementById('statusUpdateCount').textContent = count;
    document.getElementById('statusUpdateNewStatus').textContent = statusNames[newStatus] || newStatus;

    const modal = new bootstrap.Modal(document.getElementById('statusUpdateConfirmModal'));
    modal.show();

    // Handle confirmation
    document.getElementById('confirmStatusUpdateBtn').onclick = function() {
        showButtonLoading(this);

        // Add selected order IDs to form
        const checkedBoxes = document.querySelectorAll('.order-checkbox:checked');
        checkedBoxes.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'order_ids[]';
            input.value = checkbox.value;
            form.appendChild(input);
        });

        // Submit the form
        form.submit();
    };
}

// Show individual delete confirmation modal
function confirmDeleteOrder(orderId, customerName) {
    document.getElementById('deleteMessage').innerHTML = `
        هل أنت متأكد من حذف الطلب <strong>#${orderId}</strong> للعميل <strong>"${customerName}"</strong>؟
    `;

    const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    modal.show();

    // Handle confirmation
    document.getElementById('confirmDeleteBtn').onclick = function() {
        showButtonLoading(this);

        // Create and submit delete form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'orders.php';

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'delete_order';

        const orderIdInput = document.createElement('input');
        orderIdInput.type = 'hidden';
        orderIdInput.name = 'order_id';
        orderIdInput.value = orderId;

        form.appendChild(actionInput);
        form.appendChild(orderIdInput);
        document.body.appendChild(form);
        form.submit();
    };
}

// Toast notification function
function showToast(message, type) {
    // Remove existing toasts
    const existingToasts = document.querySelectorAll('.toast-notification');
    existingToasts.forEach(toast => toast.remove());

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show toast-notification`;
    toast.style.cssText = 'position: fixed; top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}

// Utility Functions

// Show button loading state
function showButtonLoading(button) {
    button.classList.add('loading');
    button.disabled = true;
    const spinner = button.querySelector('.spinner-border');
    const text = button.querySelector('.btn-text');

    if (spinner) spinner.classList.remove('d-none');
    if (text) text.style.opacity = '0.7';
}

// Hide button loading state
function hideButtonLoading(button) {
    button.classList.remove('loading');
    button.disabled = false;
    const spinner = button.querySelector('.spinner-border');
    const text = button.querySelector('.btn-text');

    if (spinner) spinner.classList.add('d-none');
    if (text) text.style.opacity = '1';
}

// Show success toast
function showSuccessToast(message) {
    document.getElementById('successMessage').textContent = message;
    const toast = new bootstrap.Toast(document.getElementById('successToast'), {
        autohide: true,
        delay: 5000
    });
    toast.show();
}

// Show error toast
function showErrorToast(message) {
    document.getElementById('errorMessage').textContent = message;
    const toast = new bootstrap.Toast(document.getElementById('errorToast'), {
        autohide: true,
        delay: 5000
    });
    toast.show();
}

// Initialize page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize bulk action buttons
    updateBulkActionButtons();

    // Add hover effects to table rows
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    // Handle form submissions to prevent double-submit
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function() {
            this.setAttribute('data-submitting', 'true');

            // Re-enable after 10 seconds as fallback
            setTimeout(() => {
                this.removeAttribute('data-submitting');
            }, 10000);
        });
    });

    // Track user interactions for auto-refresh
    ['click', 'keypress', 'scroll', 'mousemove'].forEach(event => {
        document.addEventListener(event, () => {
            window.lastUserInteraction = Date.now();
        }, { passive: true });
    });

    console.log('Enhanced orders management page initialized');
});
</script>

<?php require_once 'includes/footer.php'; ?>
