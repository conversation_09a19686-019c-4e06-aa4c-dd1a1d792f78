# دليل إعداد المتجر الإلكتروني على XAMPP
# E-Commerce Setup Guide for XAMPP

## المشكلة الحالية
```
خطأ في الاتصال: SQLSTATE[HY000] [1049] Unknown database 'shop_db'
Fatal error: Call to a member function prepare() on null
```

## الحل السريع (Quick Fix)

### الخطوة 1: تأكد من تشغيل XAMPP
1. افتح **XAMPP Control Panel**
2. تأكد من تشغيل **Apache** و **MySQL**
3. إذا لم يكونا يعملان، اضغط على **Start** لكل منهما

### الخطوة 2: إعداد قاعدة البيانات تلقائياً
1. افتح المتصفح واذهب إلى: `http://localhost/web/setup_database.php`
2. سيقوم الملف بإنشاء قاعدة البيانات والجداول تلقائياً
3. اتبع التعليمات على الشاشة

### الخطوة 3: اختبار الاتصال
1. اذهب إلى: `http://localhost/web/test_connection.php`
2. تحقق من أن جميع الاختبارات ناجحة
3. إذا كان هناك أخطاء، اتبع التعليمات المعروضة

### الخطوة 4: زيارة الموقع
1. اذهب إلى: `http://localhost/web/index.php`
2. يجب أن يعمل الموقع الآن بشكل طبيعي

## الحل التفصيلي (Detailed Solution)

### 1. التحقق من إعدادات XAMPP

#### تشغيل الخدمات:
- افتح XAMPP Control Panel
- تأكد من تشغيل Apache (Port 80, 443)
- تأكد من تشغيل MySQL (Port 3306)

#### في حالة مشاكل المنافذ:
- إذا كان Port 80 مستخدم، غيّر Apache إلى 8080
- إذا كان Port 3306 مستخدم، غيّر MySQL إلى 3307
- عدّل الإعدادات في `config/database.php` وفقاً لذلك

### 2. إنشاء قاعدة البيانات يدوياً

#### باستخدام phpMyAdmin:
1. اذهب إلى: `http://localhost/phpmyadmin`
2. اضغط على **New** في الشريط الجانبي
3. اكتب اسم قاعدة البيانات: `shop_db`
4. اختر **Collation**: `utf8mb4_unicode_ci`
5. اضغط **Create**

#### استيراد الجداول:
1. اختر قاعدة البيانات `shop_db`
2. اضغط على تبويب **Import**
3. اختر ملف `database.sql` من مجلد المشروع
4. اضغط **Go**

### 3. تعديل إعدادات الاتصال

#### تحديث config/database.php:
```php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'shop_db');
define('DB_USER', 'root');
define('DB_PASS', ''); // فارغة في XAMPP
define('DB_PORT', '3306'); // أضف هذا إذا كان المنفذ مختلف
define('DB_CHARSET', 'utf8mb4');
```

#### تحديث config/config.php:
```php
// تأكد من أن SITE_URL صحيح
define('SITE_URL', 'http://localhost/web');
```

### 4. إعداد الصلاحيات

#### مجلدات الرفع:
```bash
# في Windows، تأكد من أن هذه المجلدات قابلة للكتابة:
uploads/
uploads/products/
uploads/categories/
```

#### في حالة مشاكل الصلاحيات:
1. انقر بزر الماوس الأيمن على مجلد `uploads`
2. اختر **Properties** > **Security**
3. أضف صلاحيات **Full Control** للمستخدم الحالي

### 5. اختبار التثبيت

#### الملفات المطلوبة للاختبار:
- `test_connection.php` - اختبار الاتصال
- `setup_database.php` - إعداد قاعدة البيانات
- `index.php` - الصفحة الرئيسية
- `admin/login.php` - لوحة التحكم

#### بيانات تسجيل الدخول الافتراضية:
- **اسم المستخدم:** admin
- **كلمة المرور:** password

## مشاكل شائعة وحلولها

### المشكلة: "Access denied for user 'root'@'localhost'"
**الحل:**
1. افتح phpMyAdmin
2. اذهب إلى **User accounts**
3. تأكد من أن المستخدم `root` له صلاحيات كاملة
4. أو أنشئ مستخدم جديد مع صلاحيات كاملة

### المشكلة: "Can't connect to MySQL server"
**الحل:**
1. أعد تشغيل MySQL من XAMPP Control Panel
2. تحقق من أن المنفذ 3306 غير مستخدم من برنامج آخر
3. تحقق من إعدادات Firewall

### المشكلة: "Table doesn't exist"
**الحل:**
1. استخدم `setup_database.php` لإعادة إنشاء الجداول
2. أو استورد `database.sql` يدوياً من phpMyAdmin

### المشكلة: صفحة بيضاء أو أخطاء PHP
**الحل:**
1. فعّل عرض الأخطاء في PHP:
```php
ini_set('display_errors', 1);
error_reporting(E_ALL);
```
2. تحقق من ملف error log في XAMPP

## التحقق من نجاح التثبيت

### ✅ علامات النجاح:
- [ ] XAMPP يعمل (Apache + MySQL)
- [ ] قاعدة البيانات `shop_db` موجودة
- [ ] الجداول منشأة (8 جداول على الأقل)
- [ ] `http://localhost/web/index.php` يعمل
- [ ] `http://localhost/web/admin/login.php` يعمل
- [ ] يمكن تسجيل الدخول بـ admin/password

### 🔧 خطوات ما بعد التثبيت:
1. احذف ملفات الاختبار:
   - `setup_database.php`
   - `test_connection.php`
   - `install.php`

2. غيّر كلمة مرور المدير من لوحة التحكم

3. ابدأ في إضافة المنتجات والتصنيفات

4. اختبر عملية الشراء كاملة

## الدعم الفني

إذا استمرت المشاكل:
1. تحقق من ملف error log في XAMPP
2. تأكد من إصدار PHP (7.4+ مطلوب)
3. تأكد من تفعيل إضافات PDO و MySQL
4. جرب إعادة تثبيت XAMPP

---

**ملاحظة:** هذا الدليل مخصص لبيئة التطوير المحلية فقط. للإنتاج، ستحتاج إعدادات أمان إضافية.
