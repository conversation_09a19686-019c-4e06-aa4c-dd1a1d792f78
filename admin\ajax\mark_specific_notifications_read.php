<?php
// Prevent any output before JSON
ob_start();

require_once '../../config/config.php';
requireAdminLogin();

// Clean any output buffer and set JSON header
ob_clean();
header('Content-Type: application/json; charset=utf-8');

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['type']) || !isset($input['ids'])) {
        throw new Exception('بيانات غير صحيحة');
    }
    
    $type = $input['type'];
    $ids = $input['ids'];
    
    if (!in_array($type, ['order', 'review'])) {
        throw new Exception('نوع الإشعار غير صحيح');
    }
    
    if (!is_array($ids) || empty($ids)) {
        throw new Exception('لا توجد إشعارات للتحديث');
    }
    
    // Create the notification reads table if it doesn't exist
    $createTable = "
        CREATE TABLE IF NOT EXISTS admin_notification_reads (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT NOT NULL DEFAULT 1,
            notification_type ENUM('order', 'review') NOT NULL,
            reference_id INT NOT NULL,
            read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_read (admin_id, notification_type, reference_id),
            INDEX idx_admin_type (admin_id, notification_type),
            INDEX idx_reference (reference_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTable);
    
    $markedCount = 0;
    
    // Mark each notification as read
    foreach ($ids as $id) {
        $id = (int)$id;
        if ($id > 0) {
            $insertRead = "INSERT IGNORE INTO admin_notification_reads (admin_id, notification_type, reference_id) VALUES (1, ?, ?)";
            $stmt = $pdo->prepare($insertRead);
            if ($stmt->execute([$type, $id])) {
                $markedCount++;
            }
        }
    }
    
    $typeText = $type === 'order' ? 'الطلبات' : 'التقييمات';
    
    echo json_encode([
        'success' => true,
        'message' => "تم تحديد $markedCount من $typeText كمقروءة",
        'marked_count' => $markedCount,
        'type' => $type
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

exit;
?>
