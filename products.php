<?php
$pageTitle = 'المنتجات';
require_once 'includes/header.php';

// معاملات البحث والفلترة
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$category = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$minPrice = isset($_GET['min_price']) ? (float)$_GET['min_price'] : 0;
$maxPrice = isset($_GET['max_price']) ? (float)$_GET['max_price'] : 0;
$sortBy = isset($_GET['sort']) ? sanitizeInput($_GET['sort']) : 'newest';

// بناء استعلام البحث
$whereConditions = ["p.status = 'active'"];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(p.name LIKE ? OR p.description LIKE ? OR p.short_description LIKE ?)";
    $searchTerm = "%{$search}%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if ($category > 0) {
    $whereConditions[] = "p.category_id = ?";
    $params[] = $category;
}

if ($minPrice > 0) {
    $whereConditions[] = "p.price >= ?";
    $params[] = $minPrice;
}

if ($maxPrice > 0) {
    $whereConditions[] = "p.price <= ?";
    $params[] = $maxPrice;
}

$whereClause = implode(' AND ', $whereConditions);

// ترتيب النتائج
$orderBy = "p.created_at DESC";
switch ($sortBy) {
    case 'price_low':
        $orderBy = "p.price ASC";
        break;
    case 'price_high':
        $orderBy = "p.price DESC";
        break;
    case 'name':
        $orderBy = "p.name ASC";
        break;
    case 'featured':
        $orderBy = "p.is_featured DESC, p.created_at DESC";
        break;
}

// التصفح (Pagination)
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 12;
$offset = ($page - 1) * $perPage;

// عدد المنتجات الإجمالي
$countSql = "SELECT COUNT(*) as total FROM products p WHERE {$whereClause}";
$totalResult = fetchOne($countSql, $params);
$totalProducts = ($totalResult && isset($totalResult['total'])) ? $totalResult['total'] : 0;
$totalPages = ceil($totalProducts / $perPage);

// جلب المنتجات
$sql = "
    SELECT p.*, c.name as category_name
    FROM products p
    LEFT JOIN categories c ON p.category_id = c.id
    WHERE {$whereClause}
    ORDER BY {$orderBy}
    LIMIT {$perPage} OFFSET {$offset}
";

$products = fetchAll($sql, $params);
// fetchAll now returns [] instead of false, so no need to check

// جلب التصنيفات للفلتر
$categories = fetchAll("SELECT id, name FROM categories ORDER BY name");
// fetchAll now returns [] instead of false, so no need to check

// جلب نطاق الأسعار
$priceRange = fetchOne("SELECT MIN(price) as min_price, MAX(price) as max_price FROM products WHERE status = 'active'");
if (!$priceRange || !isset($priceRange['min_price'])) {
    $priceRange = ['min_price' => 0, 'max_price' => 1000];
}
?>

<!-- Page Header -->
<div class="bg-light py-4">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="h3 mb-0">
                    <?php if (!empty($search)): ?>
                        نتائج البحث عن: "<?php echo htmlspecialchars($search); ?>"
                    <?php elseif ($category > 0): ?>
                        <?php
                        $categoryName = fetchOne("SELECT name FROM categories WHERE id = ?", [$category]);
                        echo ($categoryName && isset($categoryName['name'])) ? htmlspecialchars($categoryName['name']) : 'تصنيف غير موجود';
                        ?>
                    <?php else: ?>
                        جميع المنتجات
                    <?php endif; ?>
                </h1>
                <p class="text-muted mb-0">
                    <?php echo $totalProducts; ?> منتج متاح
                </p>
            </div>
            <div class="col-md-6 text-md-end">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                        <li class="breadcrumb-item active">المنتجات</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="container my-5">
    <div class="row">
        <!-- Sidebar Filters -->
        <div class="col-lg-3 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-funnel"></i> فلترة المنتجات</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="">
                        <!-- البحث -->
                        <div class="mb-3">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="ابحث عن منتج...">
                        </div>
                        
                        <!-- التصنيف -->
                        <div class="mb-3">
                            <label class="form-label">التصنيف</label>
                            <select class="form-select" name="category">
                                <option value="">جميع التصنيفات</option>
                                <?php foreach ($categories as $cat): ?>
                                    <option value="<?php echo $cat['id']; ?>" 
                                            <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($cat['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <!-- نطاق السعر -->
                        <div class="mb-3">
                            <label class="form-label">نطاق السعر</label>
                            <div class="row">
                                <div class="col-6">
                                    <input type="number" class="form-control" name="min_price" 
                                           value="<?php echo $minPrice > 0 ? $minPrice : ''; ?>" 
                                           placeholder="من" min="0" step="0.01">
                                </div>
                                <div class="col-6">
                                    <input type="number" class="form-control" name="max_price" 
                                           value="<?php echo $maxPrice > 0 ? $maxPrice : ''; ?>" 
                                           placeholder="إلى" min="0" step="0.01">
                                </div>
                            </div>
                            <?php if ($priceRange): ?>
                                <small class="text-muted">
                                    السعر من <?php echo formatPrice($priceRange['min_price']); ?> 
                                    إلى <?php echo formatPrice($priceRange['max_price']); ?>
                                </small>
                            <?php endif; ?>
                        </div>
                        
                        <!-- أزرار التحكم -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> تطبيق الفلتر
                            </button>
                            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Featured Categories -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">التصنيفات الشائعة</h6>
                </div>
                <div class="card-body">
                    <?php foreach ($categories as $cat): ?>
                        <a href="<?php echo SITE_URL; ?>/products.php?category=<?php echo $cat['id']; ?>" 
                           class="d-block text-decoration-none mb-2 <?php echo $category == $cat['id'] ? 'text-primary fw-bold' : 'text-muted'; ?>">
                            <i class="bi bi-tag"></i> <?php echo htmlspecialchars($cat['name']); ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <!-- Products Grid -->
        <div class="col-lg-9">
            <!-- Sort Options -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <span class="text-muted">عرض <?php echo count($products); ?> من أصل <?php echo $totalProducts; ?> منتج</span>
                </div>
                <div class="d-flex align-items-center">
                    <label class="form-label me-2 mb-0">ترتيب حسب:</label>
                    <select class="form-select" style="width: auto;" onchange="changeSorting(this.value)">
                        <option value="newest" <?php echo $sortBy == 'newest' ? 'selected' : ''; ?>>الأحدث</option>
                        <option value="featured" <?php echo $sortBy == 'featured' ? 'selected' : ''; ?>>المميز</option>
                        <option value="price_low" <?php echo $sortBy == 'price_low' ? 'selected' : ''; ?>>السعر: من الأقل للأعلى</option>
                        <option value="price_high" <?php echo $sortBy == 'price_high' ? 'selected' : ''; ?>>السعر: من الأعلى للأقل</option>
                        <option value="name" <?php echo $sortBy == 'name' ? 'selected' : ''; ?>>الاسم</option>
                    </select>
                </div>
            </div>
            
            <?php if ($products): ?>
                <div class="row">
                    <?php foreach ($products as $product): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card product-card h-100 position-relative">
                                <?php if ($product['discount'] > 0): ?>
                                    <span class="discount-badge">خصم <?php echo $product['discount']; ?>%</span>
                                <?php endif; ?>
                                
                                <?php if ($product['is_featured']): ?>
                                    <span class="badge bg-warning position-absolute" style="top: 10px; left: 10px; z-index: 1;">
                                        <i class="bi bi-star"></i> مميز
                                    </span>
                                <?php endif; ?>
                                
                                <?php
                                // البحث عن أول صورة متاحة من الصور الخمس
                                $imageUrl = 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج');
                                for ($i = 1; $i <= 5; $i++) {
                                    if (!empty($product['image_url_' . $i])) {
                                        $imageUrl = $product['image_url_' . $i];
                                        break;
                                    }
                                }
                                // إذا لم توجد صور خارجية، استخدم الصورة المرفوعة
                                if ($imageUrl === 'https://via.placeholder.com/300x250/f8f9fa/6c757d?text=' . urlencode('صورة المنتج') && !empty($product['image'])) {
                                    $imageUrl = UPLOAD_URL . '/' . $product['image'];
                                }

                                // عد الصور والفيديوهات للعرض
                                $totalImages = 0;
                                for ($i = 1; $i <= 5; $i++) {
                                    if (!empty($product['image_url_' . $i])) $totalImages++;
                                }
                                if (!empty($product['image'])) $totalImages++;

                                $totalVideos = 0;
                                if (!empty($product['video_url'])) $totalVideos++;
                                if (!empty($product['video_url_1'])) $totalVideos++;
                                if (!empty($product['video_url_2'])) $totalVideos++;
                                if (!empty($product['video_url_3'])) $totalVideos++;
                                ?>
                                <img src="<?php echo $imageUrl; ?>"
                                     class="card-img-top" alt="<?php echo htmlspecialchars($product['name']); ?>"
                                     style="height: 250px; object-fit: cover;">
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                                    <p class="text-muted small mb-2">
                                        <i class="bi bi-tag"></i> <?php echo htmlspecialchars($product['category_name']); ?>
                                    </p>
                                    <p class="card-text text-muted flex-grow-1">
                                        <?php echo htmlspecialchars(substr($product['short_description'], 0, 100)) . '...'; ?>
                                    </p>

                                    <!-- Media indicators -->
                                    <?php if ($totalImages > 1 || $totalVideos > 0): ?>
                                        <div class="mb-2">
                                            <?php if ($totalImages > 1): ?>
                                                <span class="badge bg-primary me-1" title="<?php echo $totalImages; ?> صور">
                                                    <i class="bi bi-images"></i> <?php echo $totalImages; ?>
                                                </span>
                                            <?php endif; ?>
                                            <?php if ($totalVideos > 0): ?>
                                                <span class="badge bg-danger me-1" title="<?php echo $totalVideos; ?> فيديو">
                                                    <i class="bi bi-play-circle"></i> <?php echo $totalVideos; ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="price-section mb-3">
                                        <?php if ($product['discount'] > 0): ?>
                                            <?php $discountedPrice = $product['price'] - ($product['price'] * $product['discount'] / 100); ?>
                                            <span class="price text-primary"><?php echo formatPrice($discountedPrice); ?></span>
                                            <span class="original-price ms-2"><?php echo formatPrice($product['price']); ?></span>
                                        <?php else: ?>
                                            <span class="price text-primary"><?php echo formatPrice($product['price']); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="d-flex gap-2">
                                        <?php if ($product['stock'] > 0): ?>
                                            <button class="btn btn-primary flex-grow-1" onclick="addToCart(<?php echo $product['id']; ?>)">
                                                <i class="bi bi-cart-plus"></i> أضف للسلة
                                            </button>
                                        <?php else: ?>
                                            <button class="btn btn-secondary flex-grow-1" disabled>
                                                <i class="bi bi-x-circle"></i> غير متوفر
                                            </button>
                                        <?php endif; ?>
                                        <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>" 
                                           class="btn btn-outline-primary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </div>
                                    
                                    <?php if ($product['stock'] <= 5 && $product['stock'] > 0): ?>
                                        <small class="text-warning mt-2">
                                            <i class="bi bi-exclamation-triangle"></i> 
                                            متبقي <?php echo $product['stock']; ?> قطع فقط
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="bi bi-search display-1 text-muted"></i>
                    <h3 class="mt-3">لا توجد منتجات</h3>
                    <p class="text-muted">لم نجد أي منتجات تطابق معايير البحث الخاصة بك</p>
                    <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary">
                        عرض جميع المنتجات
                    </a>
                </div>
            <?php endif; ?>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="تصفح المنتجات" class="mt-5">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo buildPaginationUrl($page - 1); ?>">
                                    <i class="bi bi-chevron-right"></i> السابق
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php
                        $startPage = max(1, $page - 2);
                        $endPage = min($totalPages, $page + 2);

                        if ($startPage > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo buildPaginationUrl(1); ?>">1</a>
                            </li>
                            <?php if ($startPage > 2): ?>
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="<?php echo buildPaginationUrl($i); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($endPage < $totalPages): ?>
                            <?php if ($endPage < $totalPages - 1): ?>
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo buildPaginationUrl($totalPages); ?>"><?php echo $totalPages; ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo buildPaginationUrl($page + 1); ?>">
                                    التالي <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// دالة بناء رابط التصفح
function buildPaginationUrl($pageNum) {
    $params = $_GET;
    $params['page'] = $pageNum;
    return SITE_URL . '/products.php?' . http_build_query($params);
}
?>

<script>
// تغيير الترتيب
function changeSorting(sortValue) {
    const url = new URL(window.location);
    url.searchParams.set('sort', sortValue);
    url.searchParams.delete('page'); // إعادة تعيين الصفحة عند تغيير الترتيب
    window.location.href = url.toString();
}

// تحديث الفلاتر تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.querySelector('form');
    const inputs = filterForm.querySelectorAll('input, select');

    // إضافة مستمع للتغييرات في الفلاتر
    inputs.forEach(input => {
        if (input.name !== 'search') { // استثناء حقل البحث
            input.addEventListener('change', function() {
                // تأخير قصير للسماح للمستخدم بإدخال القيم
                setTimeout(() => {
                    filterForm.submit();
                }, 300);
            });
        }
    });

    // البحث المباشر
    const searchInput = document.querySelector('input[name="search"]');
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (this.value.length >= 3 || this.value.length === 0) {
                filterForm.submit();
            }
        }, 500);
    });
});

// عرض المزيد من التفاصيل عند التمرير
document.addEventListener('DOMContentLoaded', function() {
    const productCards = document.querySelectorAll('.product-card');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1
    });

    productCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
