<?php
/**
 * Simple database test for insertData function
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Database Insert Test</h2>";

try {
    require_once 'config/config.php';
    
    echo "<h3>1. Basic Connection Test</h3>";
    if (!isset($pdo) || !$pdo) {
        die("❌ PDO connection not available");
    }
    echo "✅ PDO connection available<br>";
    
    // Test basic query
    try {
        $result = $pdo->query("SELECT 1 as test");
        $row = $result->fetch();
        echo "✅ Basic query works: " . $row['test'] . "<br>";
    } catch (Exception $e) {
        echo "❌ Basic query failed: " . $e->getMessage() . "<br>";
    }
    
    echo "<h3>2. Check Orders Table</h3>";
    try {
        $tableCheck = $pdo->query("SHOW TABLES LIKE 'orders'");
        if ($tableCheck->rowCount() > 0) {
            echo "✅ Orders table exists<br>";
            
            // Show table structure
            $structure = $pdo->query("DESCRIBE orders");
            echo "<strong>Table structure:</strong><br>";
            while ($row = $structure->fetch()) {
                echo "- " . $row['Field'] . " (" . $row['Type'] . ")<br>";
            }
        } else {
            echo "❌ Orders table does not exist<br>";
            echo "Creating orders table...<br>";
            
            $createTable = "
            CREATE TABLE `orders` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `customer_name` varchar(255) NOT NULL,
              `customer_phone` varchar(20) NOT NULL,
              `address` text NOT NULL,
              `province` varchar(100) NOT NULL,
              `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
              `delivery_price` decimal(10,2) NOT NULL DEFAULT 0.00,
              `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
              `total_price` decimal(10,2) NOT NULL,
              `payment_method` varchar(50) NOT NULL DEFAULT 'cash_on_delivery',
              `notes` text,
              `status` enum('pending','processing','shipped','delivered','cancelled') NOT NULL DEFAULT 'pending',
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";
            
            $pdo->exec($createTable);
            echo "✅ Orders table created<br>";
        }
    } catch (Exception $e) {
        echo "❌ Table check failed: " . $e->getMessage() . "<br>";
    }
    
    echo "<h3>3. Test insertData Function</h3>";
    if (!function_exists('insertData')) {
        echo "❌ insertData function not found<br>";
    } else {
        echo "✅ insertData function exists<br>";
        
        // Test data
        $testData = [
            'customer_name' => 'Test Customer',
            'customer_phone' => '07123456789',
            'address' => 'Test Address',
            'province' => 'بغداد',
            'subtotal' => 100.00,
            'delivery_price' => 5000.00,
            'discount_amount' => 0.00,
            'total_price' => 105000.00,
            'payment_method' => 'cash_on_delivery',
            'status' => 'pending'
        ];
        
        echo "Test data: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "<br>";
        
        $result = insertData('orders', $testData);
        
        if ($result) {
            echo "✅ insertData succeeded - ID: " . $result . "<br>";
            
            // Verify the insert
            $verify = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
            $verify->execute([$result]);
            $order = $verify->fetch();
            
            if ($order) {
                echo "✅ Order verified in database<br>";
                echo "Customer: " . $order['customer_name'] . "<br>";
                echo "Phone: " . $order['customer_phone'] . "<br>";
                echo "Total: " . $order['total_price'] . "<br>";
                
                // Clean up
                $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$result]);
                echo "✅ Test data cleaned up<br>";
            } else {
                echo "❌ Order not found in database after insert<br>";
            }
        } else {
            echo "❌ insertData failed<br>";
        }
    }
    
    echo "<h3>4. Manual Insert Test</h3>";
    try {
        $sql = "INSERT INTO orders (customer_name, customer_phone, address, province, subtotal, delivery_price, discount_amount, total_price, payment_method, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            'Manual Test Customer',
            '07987654321',
            'Manual Test Address',
            'البصرة',
            200.00,
            8000.00,
            0.00,
            208000.00,
            'cash_on_delivery',
            'pending'
        ]);
        
        if ($result) {
            $id = $pdo->lastInsertId();
            echo "✅ Manual insert succeeded - ID: " . $id . "<br>";
            
            // Clean up
            $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$id]);
            echo "✅ Manual test data cleaned up<br>";
        } else {
            echo "❌ Manual insert failed<br>";
            print_r($stmt->errorInfo());
        }
    } catch (Exception $e) {
        echo "❌ Manual insert error: " . $e->getMessage() . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}

echo "<h3>Test Complete</h3>";
?>
