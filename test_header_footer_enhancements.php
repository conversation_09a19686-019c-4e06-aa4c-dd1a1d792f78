<?php
/**
 * Test file to validate header, footer, and back-to-top button enhancements
 * This file tests the visual improvements across all website pages
 */

require_once 'config/config.php';

echo "<h1>Header, Footer, and Back-to-Top Button Enhancement Validation</h1>";

// Test pages to validate
$testPages = [
    'index.php' => 'الرئيسية',
    'products.php' => 'المنتجات', 
    'offers.php' => 'العروض',
    'guidelines.php' => 'الإرشادات',
    'influencers.php' => 'المؤثرين',
    'contact.php' => 'اتصل بنا',
    'cart.php' => 'سلة التسوق'
];

echo "<h2>Testing Page Accessibility</h2>";
foreach ($testPages as $file => $name) {
    if (file_exists($file)) {
        echo "✅ $name ($file) - موجودة<br>";
    } else {
        echo "❌ $name ($file) - مفقودة<br>";
    }
}

echo "<h2>Header Enhancement Validation</h2>";

// Check header.php file
if (file_exists('includes/header.php')) {
    $headerContent = file_get_contents('includes/header.php');
    
    // Test navigation icon improvements
    if (strpos($headerContent, 'bi-house-fill') !== false) {
        echo "✅ Navigation icons updated to filled versions<br>";
    } else {
        echo "❌ Navigation icons not updated<br>";
    }
    
    // Test center-aligned navigation structure
    if (strpos($headerContent, 'flex-direction: column') !== false) {
        echo "✅ Navigation links structured for center alignment<br>";
    } else {
        echo "❌ Navigation structure not updated<br>";
    }
    
    // Test cart button redesign
    if (strpos($headerContent, 'cart-btn') !== false) {
        echo "✅ Cart button redesigned with clean styling<br>";
    } else {
        echo "❌ Cart button not redesigned<br>";
    }
    
    // Test search bar improvements
    if (strpos($headerContent, 'border-radius: 25px') !== false) {
        echo "✅ Search bar redesigned with professional styling<br>";
    } else {
        echo "❌ Search bar not redesigned<br>";
    }
    
    // Test responsive design
    if (strpos($headerContent, '@media (max-width: 768px)') !== false) {
        echo "✅ Responsive design implemented<br>";
    } else {
        echo "❌ Responsive design missing<br>";
    }
    
} else {
    echo "❌ Header file not found<br>";
}

echo "<h2>Footer Enhancement Validation</h2>";

// Check footer.php file
if (file_exists('includes/footer.php')) {
    $footerContent = file_get_contents('includes/footer.php');
    
    // Test white text color
    if (strpos($footerContent, 'text-white') !== false) {
        echo "✅ Footer text changed to white color<br>";
    } else {
        echo "❌ Footer text color not updated<br>";
    }
    
    // Test Quick Links section
    if (strpos($footerContent, 'سياسة الخصوصية') !== false && 
        strpos($footerContent, 'الشروط والأحكام') !== false) {
        echo "✅ Quick Links section includes policy links<br>";
    } else {
        echo "❌ Policy links not moved to Quick Links<br>";
    }
    
    // Test promotional text removal
    if (strpos($footerContent, 'توصيل مجاني للطلبات أكثر من') === false &&
        strpos($footerContent, 'ضمان الجودة والأصالة') === false &&
        strpos($footerContent, 'دعم فني على مدار الساعة') === false &&
        strpos($footerContent, 'طرق الدفع المتاحة') === false) {
        echo "✅ Promotional text elements removed<br>";
    } else {
        echo "❌ Promotional text elements still present<br>";
    }
    
    // Test centered copyright
    if (strpos($footerContent, 'text-center') !== false) {
        echo "✅ Copyright text centered<br>";
    } else {
        echo "❌ Copyright text not centered<br>";
    }
    
} else {
    echo "❌ Footer file not found<br>";
}

echo "<h2>Back-to-Top Button Enhancement Validation</h2>";

// Check back-to-top button improvements
if (file_exists('includes/footer.php')) {
    $footerContent = file_get_contents('includes/footer.php');
    
    // Test enhanced button design
    if (strpos($footerContent, 'back-to-top-btn') !== false) {
        echo "✅ Back-to-top button class updated<br>";
    } else {
        echo "❌ Back-to-top button class not updated<br>";
    }
    
    // Test improved styling
    if (strpos($footerContent, 'width: 55px') !== false && 
        strpos($footerContent, 'height: 55px') !== false) {
        echo "✅ Button size improved<br>";
    } else {
        echo "❌ Button size not improved<br>";
    }
    
    // Test arrow direction (chevron-up)
    if (strpos($footerContent, 'bi-chevron-up') !== false) {
        echo "✅ Arrow direction improved<br>";
    } else {
        echo "❌ Arrow direction not improved<br>";
    }
    
    // Test hover effects
    if (strpos($footerContent, 'mouseenter') !== false && 
        strpos($footerContent, 'mouseleave') !== false) {
        echo "✅ Enhanced hover effects implemented<br>";
    } else {
        echo "❌ Enhanced hover effects not implemented<br>";
    }
    
    // Test RTL support
    if (strpos($footerContent, 'document.documentElement.dir === \'rtl\'') !== false) {
        echo "✅ RTL support implemented<br>";
    } else {
        echo "❌ RTL support not implemented<br>";
    }
}

// Check CSS file updates
if (file_exists('assets/css/homepage.css')) {
    $cssContent = file_get_contents('assets/css/homepage.css');
    
    if (strpos($cssContent, 'back-to-top-btn') !== false) {
        echo "✅ CSS updated for back-to-top button<br>";
    } else {
        echo "❌ CSS not updated for back-to-top button<br>";
    }
}

echo "<h2>Arabic RTL Layout Validation</h2>";

// Check RTL support in header
if (file_exists('includes/header.php')) {
    $headerContent = file_get_contents('includes/header.php');
    
    if (strpos($headerContent, 'lang="ar" dir="rtl"') !== false) {
        echo "✅ HTML document set to Arabic RTL<br>";
    } else {
        echo "❌ HTML document not set to Arabic RTL<br>";
    }
    
    if (strpos($headerContent, 'Cairo') !== false) {
        echo "✅ Arabic font (Cairo) loaded<br>";
    } else {
        echo "❌ Arabic font not loaded<br>";
    }
}

echo "<h2>Professional Styling Consistency</h2>";

// Check for consistent gradient usage
if (file_exists('includes/header.php')) {
    $headerContent = file_get_contents('includes/header.php');
    
    if (strpos($headerContent, 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)') !== false) {
        echo "✅ Consistent gradient styling applied<br>";
    } else {
        echo "❌ Gradient styling not consistent<br>";
    }
}

echo "<h2>Validation Complete</h2>";
echo "<p>All enhancements have been validated. Please test the website manually to ensure proper functionality.</p>";

// Generate links to test pages
echo "<h3>Test Links</h3>";
foreach ($testPages as $file => $name) {
    if (file_exists($file)) {
        echo "<a href='$file' target='_blank'>$name</a> | ";
    }
}

?>

<style>
body {
    font-family: 'Cairo', Arial, sans-serif;
    direction: rtl;
    text-align: right;
    margin: 20px;
    background-color: #f8f9fa;
}

h1, h2, h3 {
    color: #2c3e50;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

a {
    color: #667eea;
    text-decoration: none;
    margin: 5px;
    padding: 5px 10px;
    background-color: white;
    border-radius: 5px;
    display: inline-block;
}

a:hover {
    background-color: #667eea;
    color: white;
}
</style>
