<?php
require_once '../../config/config.php';
requireAdminLogin();

header('Content-Type: application/json');

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف المحتوى غير صحيح']);
    exit();
}

$id = (int)$_GET['id'];

try {
    $content = fetchOne("SELECT * FROM influencers_content WHERE id = ?", [$id]);
    
    if ($content) {
        echo json_encode([
            'success' => true,
            'content' => $content
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'المحتوى غير موجود'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في جلب البيانات'
    ]);
}
?>
