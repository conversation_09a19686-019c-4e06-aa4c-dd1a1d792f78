<?php
/**
 * Wishlist AJAX Handler
 * Professional Arabic E-commerce Wishlist Management
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json; charset=utf-8');

try {
    if (!isset($_POST['action']) || !isset($_POST['product_id'])) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
        exit;
    }

    $action = $_POST['action'];
    $productId = (int)$_POST['product_id'];

    // Validate product exists
    $product = fetchOne("SELECT id, name FROM products WHERE id = ? AND status = 'active'", [$productId]);
    if (!$product) {
        echo json_encode(['success' => false, 'message' => 'المنتج غير موجود']);
        exit;
    }

    // For now, we'll use session-based wishlist
    // In a full implementation, this would be user-based
    if (!isset($_SESSION['wishlist'])) {
        $_SESSION['wishlist'] = [];
    }

    switch ($action) {
        case 'add':
            if (!in_array($productId, $_SESSION['wishlist'])) {
                $_SESSION['wishlist'][] = $productId;
                
                // Log wishlist action (optional)
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO user_actions (action_type, product_id, session_id, created_at) 
                        VALUES ('wishlist_add', ?, ?, NOW())
                    ");
                    $stmt->execute([$productId, session_id()]);
                } catch (Exception $e) {
                    // Ignore logging errors
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => 'تم إضافة المنتج للمفضلة بنجاح!',
                    'wishlist_count' => count($_SESSION['wishlist']),
                    'product_name' => $product['name']
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'المنتج موجود بالفعل في المفضلة'
                ]);
            }
            break;

        case 'remove':
            $key = array_search($productId, $_SESSION['wishlist']);
            if ($key !== false) {
                unset($_SESSION['wishlist'][$key]);
                $_SESSION['wishlist'] = array_values($_SESSION['wishlist']); // Reindex array
                
                echo json_encode([
                    'success' => true,
                    'message' => 'تم إزالة المنتج من المفضلة',
                    'wishlist_count' => count($_SESSION['wishlist'])
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'المنتج غير موجود في المفضلة'
                ]);
            }
            break;

        case 'toggle':
            if (in_array($productId, $_SESSION['wishlist'])) {
                // Remove from wishlist
                $key = array_search($productId, $_SESSION['wishlist']);
                unset($_SESSION['wishlist'][$key]);
                $_SESSION['wishlist'] = array_values($_SESSION['wishlist']);
                
                echo json_encode([
                    'success' => true,
                    'action' => 'removed',
                    'message' => 'تم إزالة المنتج من المفضلة',
                    'wishlist_count' => count($_SESSION['wishlist'])
                ]);
            } else {
                // Add to wishlist
                $_SESSION['wishlist'][] = $productId;
                
                echo json_encode([
                    'success' => true,
                    'action' => 'added',
                    'message' => 'تم إضافة المنتج للمفضلة بنجاح!',
                    'wishlist_count' => count($_SESSION['wishlist'])
                ]);
            }
            break;

        case 'get_count':
            echo json_encode([
                'success' => true,
                'wishlist_count' => count($_SESSION['wishlist'])
            ]);
            break;

        case 'get_items':
            if (empty($_SESSION['wishlist'])) {
                echo json_encode([
                    'success' => true,
                    'items' => [],
                    'count' => 0
                ]);
                break;
            }

            $placeholders = str_repeat('?,', count($_SESSION['wishlist']) - 1) . '?';
            $wishlistProducts = fetchAll("
                SELECT p.id, p.name, p.price, p.discount, p.image, p.image_url_1
                FROM products p 
                WHERE p.id IN ($placeholders) AND p.status = 'active'
                ORDER BY p.name
            ", $_SESSION['wishlist']);

            echo json_encode([
                'success' => true,
                'items' => $wishlistProducts,
                'count' => count($wishlistProducts)
            ]);
            break;

        default:
            echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
            break;
    }

} catch (Exception $e) {
    error_log("Wishlist error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ، يرجى المحاولة مرة أخرى'
    ]);
}
?>
