<?php
$pageTitle = 'اتصل بنا';
require_once 'includes/header.php';

// معالجة إرسال رسالة الاتصال
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['send_message'])) {
    $name = sanitizeInput($_POST['name']);
    $email = sanitizeInput($_POST['email']);
    $phone = sanitizeInput($_POST['phone']);
    $subject = sanitizeInput($_POST['subject']);
    $message = sanitizeInput($_POST['message']);
    
    $errors = [];
    
    if (empty($name)) $errors[] = 'الاسم مطلوب';
    if (empty($email)) $errors[] = 'البريد الإلكتروني مطلوب';
    if (!validateEmail($email)) $errors[] = 'البريد الإلكتروني غير صحيح';
    if (empty($subject)) $errors[] = 'الموضوع مطلوب';
    if (empty($message)) $errors[] = 'الرسالة مطلوبة';
    
    if (empty($errors)) {
        // يمكن إضافة كود إرسال البريد الإلكتروني هنا
        // أو حفظ الرسالة في قاعدة البيانات
        
        $_SESSION['message'] = [
            'text' => 'تم إرسال رسالتك بنجاح! سنتواصل معك في أقرب وقت ممكن.',
            'type' => 'success'
        ];
        
        header('Location: ' . SITE_URL . '/contact.php');
        exit();
    } else {
        $_SESSION['message'] = [
            'text' => implode('<br>', $errors),
            'type' => 'error'
        ];
    }
}
?>

<!-- Page Header -->
<div class="bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">تواصل معنا</h1>
                <p class="lead mb-0">
                    نحن هنا لمساعدتك! لا تتردد في التواصل معنا لأي استفسار أو مساعدة
                </p>
            </div>
            <div class="col-lg-4 text-center">
                <i class="bi bi-headset display-1"></i>
            </div>
        </div>
    </div>
</div>

<!-- Breadcrumb -->
<div class="bg-light py-3">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                <li class="breadcrumb-item active">اتصل بنا</li>
            </ol>
        </nav>
    </div>
</div>

<div class="container my-5">
    <div class="row">
        <!-- Contact Information -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-0 shadow">
                <div class="card-body">
                    <h4 class="card-title mb-4">معلومات الاتصال</h4>
                    
                    <div class="contact-item mb-4">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon me-3">
                                <i class="bi bi-telephone-fill text-primary fs-4"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">الهاتف</h6>
                                <a href="tel:<?php echo getSetting('contact_phone'); ?>" class="text-decoration-none">
                                    <?php echo getSetting('contact_phone'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-4">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon me-3">
                                <i class="bi bi-envelope-fill text-success fs-4"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">البريد الإلكتروني</h6>
                                <a href="mailto:<?php echo getSetting('contact_email'); ?>" class="text-decoration-none">
                                    <?php echo getSetting('contact_email'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-4">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon me-3">
                                <i class="bi bi-whatsapp text-success fs-4"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">واتساب</h6>
                                <a href="https://wa.me/<?php echo str_replace('+', '', getSetting('whatsapp_number')); ?>" 
                                   class="text-decoration-none" target="_blank">
                                    <?php echo getSetting('whatsapp_number'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-4">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon me-3">
                                <i class="bi bi-clock-fill text-warning fs-4"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">ساعات العمل</h6>
                                <p class="mb-0 small text-muted">
                                    السبت - الخميس: 9:00 ص - 10:00 م<br>
                                    الجمعة: 2:00 م - 10:00 م
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Social Media -->
                    <div class="social-media mt-4">
                        <h6 class="mb-3">تابعنا على</h6>
                        <div class="d-flex gap-3">
                            <a href="#" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-facebook"></i>
                            </a>
                            <a href="#" class="btn btn-outline-info btn-sm">
                                <i class="bi bi-twitter"></i>
                            </a>
                            <a href="#" class="btn btn-outline-danger btn-sm">
                                <i class="bi bi-instagram"></i>
                            </a>
                            <a href="https://wa.me/<?php echo str_replace('+', '', getSetting('whatsapp_number')); ?>" 
                               class="btn btn-outline-success btn-sm" target="_blank">
                                <i class="bi bi-whatsapp"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Contact Form -->
        <div class="col-lg-8">
            <div class="card border-0 shadow">
                <div class="card-body">
                    <h4 class="card-title mb-4">أرسل لنا رسالة</h4>
                    
                    <form method="POST" action="" id="contactForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="name" 
                                       value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" 
                                       required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" name="email" 
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                       required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" name="phone" 
                                       value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الموضوع <span class="text-danger">*</span></label>
                                <select class="form-select" name="subject" required>
                                    <option value="">اختر الموضوع</option>
                                    <option value="استفسار عام" <?php echo (isset($_POST['subject']) && $_POST['subject'] == 'استفسار عام') ? 'selected' : ''; ?>>استفسار عام</option>
                                    <option value="استفسار عن منتج" <?php echo (isset($_POST['subject']) && $_POST['subject'] == 'استفسار عن منتج') ? 'selected' : ''; ?>>استفسار عن منتج</option>
                                    <option value="مشكلة في الطلب" <?php echo (isset($_POST['subject']) && $_POST['subject'] == 'مشكلة في الطلب') ? 'selected' : ''; ?>>مشكلة في الطلب</option>
                                    <option value="شكوى" <?php echo (isset($_POST['subject']) && $_POST['subject'] == 'شكوى') ? 'selected' : ''; ?>>شكوى</option>
                                    <option value="اقتراح" <?php echo (isset($_POST['subject']) && $_POST['subject'] == 'اقتراح') ? 'selected' : ''; ?>>اقتراح</option>
                                    <option value="أخرى" <?php echo (isset($_POST['subject']) && $_POST['subject'] == 'أخرى') ? 'selected' : ''; ?>>أخرى</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label class="form-label">الرسالة <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="message" rows="6" 
                                      placeholder="اكتب رسالتك هنا..." required><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" name="send_message" class="btn btn-primary btn-lg">
                                <i class="bi bi-send"></i> إرسال الرسالة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- FAQ Section -->
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="text-center mb-5">الأسئلة الشائعة</h3>
            
            <div class="accordion" id="faqAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq1">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#collapse1">
                            كيف يمكنني تتبع طلبي؟
                        </button>
                    </h2>
                    <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            يمكنك تتبع طلبك عن طريق التواصل معنا على الواتساب أو الهاتف مع ذكر رقم الطلب. 
                            سنقوم بإرسال تحديثات منتظمة عن حالة طلبك.
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq2">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#collapse2">
                            ما هي طرق الدفع المتاحة؟
                        </button>
                    </h2>
                    <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            نوفر طريقتين للدفع: الدفع عند الاستلام والتحويل البنكي. 
                            يمكنك اختيار الطريقة الأنسب لك عند إتمام الطلب.
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq3">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#collapse3">
                            كم تستغرق عملية التوصيل؟
                        </button>
                    </h2>
                    <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            عادة ما تستغرق عملية التوصيل من 2-5 أيام عمل حسب المنطقة. 
                            سنتواصل معك لتحديد موعد التوصيل المناسب.
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq4">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#collapse4">
                            هل يمكنني إرجاع المنتج؟
                        </button>
                    </h2>
                    <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            نعم، يمكنك إرجاع المنتج خلال 14 يوم من تاريخ الاستلام شرط أن يكون في حالته الأصلية. 
                            يرجى التواصل معنا لترتيب عملية الإرجاع.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Contact -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body text-center py-5">
                    <h3 class="fw-bold mb-3">تحتاج مساعدة فورية؟</h3>
                    <p class="lead mb-4">تواصل معنا مباشرة عبر الواتساب للحصول على رد سريع</p>
                    <a href="https://wa.me/<?php echo str_replace('+', '', getSetting('whatsapp_number')); ?>" 
                       class="btn btn-success btn-lg" target="_blank">
                        <i class="bi bi-whatsapp"></i> تواصل عبر الواتساب
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.contact-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 50%;
}

.contact-item {
    transition: transform 0.3s ease;
}

.contact-item:hover {
    transform: translateX(-5px);
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.accordion-button:not(.collapsed) {
    background-color: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

@media (max-width: 768px) {
    .display-4 {
        font-size: 2rem;
    }
}
</style>

<script>
// التحقق من صحة النموذج
document.getElementById('contactForm').addEventListener('submit', function(e) {
    const requiredFields = this.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });

    // التحقق من البريد الإلكتروني
    const emailField = this.querySelector('[name="email"]');
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (emailField.value && !emailPattern.test(emailField.value)) {
        emailField.classList.add('is-invalid');
        showToast('البريد الإلكتروني غير صحيح', 'error');
        isValid = false;
    }

    // التحقق من رقم الهاتف
    const phoneField = this.querySelector('[name="phone"]');
    if (phoneField.value) {
        const phonePattern = /^[0-9+\-\s()]+$/;
        if (!phonePattern.test(phoneField.value)) {
            phoneField.classList.add('is-invalid');
            showToast('رقم الهاتف غير صحيح', 'error');
            isValid = false;
        }
    }

    if (!isValid) {
        e.preventDefault();
        showToast('يرجى تصحيح الأخطاء في النموذج', 'error');

        // التمرير إلى أول حقل خاطئ
        const firstInvalidField = this.querySelector('.is-invalid');
        if (firstInvalidField) {
            firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
            firstInvalidField.focus();
        }
    } else {
        // إظهار مؤشر التحميل
        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.innerHTML;
        submitButton.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري الإرسال...';
        submitButton.disabled = true;

        // إعادة تفعيل الزر في حالة فشل الإرسال
        setTimeout(() => {
            if (submitButton.disabled) {
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }
        }, 10000);
    }
});

// إزالة رسائل الخطأ عند الكتابة
document.querySelectorAll('input, select, textarea').forEach(field => {
    field.addEventListener('input', function() {
        this.classList.remove('is-invalid');
    });
});

// تنسيق رقم الهاتف تلقائياً
document.querySelector('[name="phone"]').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, '');

    if (value.startsWith('966')) {
        value = '+' + value;
    } else if (value.startsWith('05')) {
        value = '+966' + value.substring(1);
    } else if (value.startsWith('5') && value.length === 9) {
        value = '+966' + value;
    }

    this.value = value;
});

// تأثيرات بصرية عند التمرير
document.addEventListener('DOMContentLoaded', function() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // مراقبة العناصر
    document.querySelectorAll('.card, .contact-item, .accordion-item').forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(element);
    });
});

// نسخ معلومات الاتصال
function copyContactInfo(type, value) {
    navigator.clipboard.writeText(value).then(() => {
        showToast(`تم نسخ ${type}`, 'success');
    });
}

// إضافة أحداث النسخ
document.addEventListener('DOMContentLoaded', function() {
    const contactLinks = document.querySelectorAll('.contact-item a');
    contactLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                const text = this.textContent.trim();
                copyContactInfo('معلومات الاتصال', text);
            }
        });
    });
});

// إحصائيات الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تتبع زيارة صفحة الاتصال
    console.log('تم زيارة صفحة الاتصال');

    // تتبع النقرات على أزرار التواصل
    document.querySelectorAll('a[href*="wa.me"], a[href*="tel:"], a[href*="mailto:"]').forEach(link => {
        link.addEventListener('click', function() {
            const type = this.href.includes('wa.me') ? 'واتساب' :
                        this.href.includes('tel:') ? 'هاتف' : 'بريد إلكتروني';
            console.log(`تم النقر على رابط ${type}`);
        });
    });
});

// تحسين تجربة المستخدم للأسئلة الشائعة
document.addEventListener('DOMContentLoaded', function() {
    const accordionButtons = document.querySelectorAll('.accordion-button');

    accordionButtons.forEach(button => {
        button.addEventListener('click', function() {
            // إضافة تأثير بصري عند فتح السؤال
            setTimeout(() => {
                const targetId = this.getAttribute('data-bs-target');
                const targetElement = document.querySelector(targetId);
                if (targetElement && targetElement.classList.contains('show')) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest'
                    });
                }
            }, 300);
        });
    });
});

// إضافة تأثير كتابة للعنوان
document.addEventListener('DOMContentLoaded', function() {
    const title = document.querySelector('.display-4');
    if (title) {
        const text = title.textContent;
        title.textContent = '';

        let i = 0;
        const typeWriter = () => {
            if (i < text.length) {
                title.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            }
        };

        setTimeout(typeWriter, 500);
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
