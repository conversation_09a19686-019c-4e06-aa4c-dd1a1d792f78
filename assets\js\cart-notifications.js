/**
 * Professional Cart Notification System
 * Enhanced Toast Notifications with Product Details and Actions
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

class CartNotificationSystem {
    constructor() {
        this.container = null;
        this.notifications = [];
        this.maxNotifications = 3;
        this.defaultDuration = 5000;
        
        this.init();
    }
    
    init() {
        this.createContainer();
        this.setupStyles();
    }
    
    createContainer() {
        if (this.container) return;
        
        this.container = document.createElement('div');
        this.container.className = 'cart-notification-container';
        this.container.setAttribute('aria-live', 'polite');
        this.container.setAttribute('aria-label', 'إشعارات السلة');
        
        document.body.appendChild(this.container);
    }
    
    setupStyles() {
        // Ensure CSS is loaded
        if (!document.querySelector('link[href*="cart-notifications.css"]')) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = '/assets/css/cart-notifications.css';
            document.head.appendChild(link);
        }
    }
    
    showAddToCartNotification(productData) {
        const notification = this.createAddToCartNotification(productData);
        this.addNotification(notification);
    }
    
    createAddToCartNotification(productData) {
        const {
            productId,
            productName,
            productImage,
            productPrice,
            quantity = 1,
            cartCount
        } = productData;
        
        const notification = document.createElement('div');
        notification.className = 'cart-toast';
        notification.setAttribute('role', 'alert');
        notification.setAttribute('aria-labelledby', `toast-title-${productId}`);
        
        notification.innerHTML = `
            <div class="cart-toast-header">
                <div class="cart-toast-icon">
                    <i class="bi bi-check-circle-fill"></i>
                </div>
                <h4 class="cart-toast-title" id="toast-title-${productId}">
                    تم إضافة المنتج إلى السلة بنجاح!
                </h4>
                <button class="cart-toast-close" type="button" aria-label="إغلاق الإشعار">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            
            <div class="cart-toast-body">
                <div class="cart-product-info">
                    <div class="cart-product-image">
                        ${productImage ? 
                            `<img src="${productImage}" alt="${productName}" loading="lazy">` :
                            `<i class="bi bi-image"></i>`
                        }
                    </div>
                    <div class="cart-product-details">
                        <div class="cart-product-name">${productName}</div>
                        <div class="cart-product-meta">
                            <span class="cart-product-quantity">الكمية: ${quantity}</span>
                            ${productPrice ? `<span class="cart-product-price">${productPrice} د.ع</span>` : ''}
                        </div>
                    </div>
                </div>
                
                <div class="cart-toast-actions">
                    <a href="/cart.php" class="cart-action-btn cart-action-btn-primary">
                        <i class="bi bi-cart-check"></i>
                        عرض السلة${cartCount ? ` (${cartCount})` : ''}
                    </a>
                    <button class="cart-action-btn cart-action-btn-outline" onclick="this.closest('.cart-toast').remove()">
                        <i class="bi bi-arrow-left"></i>
                        متابعة التسوق
                    </button>
                </div>
            </div>
            
            <div class="cart-toast-progress"></div>
        `;
        
        // Setup event listeners
        this.setupNotificationEvents(notification);
        
        return notification;
    }
    
    setupNotificationEvents(notification) {
        const closeBtn = notification.querySelector('.cart-toast-close');
        const continueBtn = notification.querySelector('.cart-action-btn-outline');
        
        // Close button
        closeBtn.addEventListener('click', () => {
            this.removeNotification(notification);
        });
        
        // Continue shopping button
        continueBtn.addEventListener('click', () => {
            this.removeNotification(notification);
        });
        
        // Auto-remove after duration
        setTimeout(() => {
            if (notification.parentNode) {
                this.removeNotification(notification);
            }
        }, this.defaultDuration);
        
        // Pause progress on hover
        notification.addEventListener('mouseenter', () => {
            const progress = notification.querySelector('.cart-toast-progress');
            if (progress) {
                progress.style.animationPlayState = 'paused';
            }
        });
        
        notification.addEventListener('mouseleave', () => {
            const progress = notification.querySelector('.cart-toast-progress');
            if (progress) {
                progress.style.animationPlayState = 'running';
            }
        });
    }
    
    addNotification(notification) {
        // Remove oldest notification if at max capacity
        if (this.notifications.length >= this.maxNotifications) {
            const oldest = this.notifications.shift();
            if (oldest && oldest.parentNode) {
                this.removeNotification(oldest, false);
            }
        }
        
        this.container.appendChild(notification);
        this.notifications.push(notification);
        
        // Trigger show animation
        requestAnimationFrame(() => {
            notification.classList.add('show');
        });
        
        // Add to notifications array
        notification.addEventListener('transitionend', (e) => {
            if (e.propertyName === 'opacity' && notification.classList.contains('hide')) {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                this.removeFromArray(notification);
            }
        });
    }
    
    removeNotification(notification, animate = true) {
        if (!notification || !notification.parentNode) return;
        
        if (animate) {
            notification.classList.add('hide');
            notification.classList.remove('show');
        } else {
            notification.parentNode.removeChild(notification);
            this.removeFromArray(notification);
        }
    }
    
    removeFromArray(notification) {
        const index = this.notifications.indexOf(notification);
        if (index > -1) {
            this.notifications.splice(index, 1);
        }
    }
    
    showErrorNotification(message) {
        const notification = this.createErrorNotification(message);
        this.addNotification(notification);
    }
    
    createErrorNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'cart-toast cart-toast-error';
        notification.setAttribute('role', 'alert');
        
        notification.innerHTML = `
            <div class="cart-toast-header" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                <div class="cart-toast-icon">
                    <i class="bi bi-exclamation-triangle-fill"></i>
                </div>
                <h4 class="cart-toast-title">حدث خطأ</h4>
                <button class="cart-toast-close" type="button" aria-label="إغلاق الإشعار">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            
            <div class="cart-toast-body">
                <p style="margin: 0; color: #6c757d; line-height: 1.5;">${message}</p>
            </div>
            
            <div class="cart-toast-progress" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);"></div>
        `;
        
        this.setupNotificationEvents(notification);
        return notification;
    }
    
    clearAllNotifications() {
        this.notifications.forEach(notification => {
            this.removeNotification(notification, false);
        });
        this.notifications = [];
    }
}

// Initialize the cart notification system
let cartNotificationSystem;

document.addEventListener('DOMContentLoaded', function() {
    cartNotificationSystem = new CartNotificationSystem();
});

// Enhanced addToCart function with professional notifications
function addToCartWithNotification(productId, quantity = 1, productData = {}) {
    // Show loading state if button exists
    const button = document.querySelector(`button[onclick*="addToCart(${productId}"]`);
    if (button) {
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري الإضافة...';
        button.disabled = true;
    }
    
    fetch('/ajax/cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=add&product_id=${productId}&quantity=${quantity}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update cart count if element exists
            const cartBadge = document.querySelector('.cart-badge');
            if (cartBadge && data.cart_count) {
                cartBadge.textContent = data.cart_count;
                cartBadge.style.display = 'flex';
            }
            
            // Show professional notification
            if (cartNotificationSystem) {
                cartNotificationSystem.showAddToCartNotification({
                    productId: productId,
                    productName: data.product_name || productData.name || 'المنتج',
                    productImage: productData.image || null,
                    productPrice: productData.price || null,
                    quantity: quantity,
                    cartCount: data.cart_count
                });
            }
            
            // Button success state
            if (button) {
                button.innerHTML = '<i class="bi bi-check-circle"></i> تم الإضافة';
                button.classList.add('btn-success');
                button.classList.remove('btn-primary');
                
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.add('btn-primary');
                    button.classList.remove('btn-success');
                    button.disabled = false;
                }, 2000);
            }
        } else {
            // Show error notification
            if (cartNotificationSystem) {
                cartNotificationSystem.showErrorNotification(
                    data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى'
                );
            }
            
            // Reset button
            if (button) {
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        
        // Show error notification
        if (cartNotificationSystem) {
            cartNotificationSystem.showErrorNotification('حدث خطأ في الاتصال، يرجى المحاولة مرة أخرى');
        }
        
        // Reset button
        if (button) {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    });
}

// Export for global use
window.CartNotificationSystem = CartNotificationSystem;
window.addToCartWithNotification = addToCartWithNotification;
