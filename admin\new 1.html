<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <title>القبض على النجوم</title>
    <link href="https://fonts.googleapis.com/css2?family=Almarai:wght@700&display=swap" rel="stylesheet">
    <style>
        /* نفس الـ CSS السابق (احذف الصوت المضمن من الأسفل) */
        body { margin: 0; overflow: hidden; background: linear-gradient(180deg, #0b1d3a 0%, #000 100%); font-family: 'Almarai', sans-serif; user-select: none; }
        #gameArea { position: relative; width: 100vw; height: 100vh; perspective: 1000px; }
        .star { position: absolute; width: 40px; height: 40px; background-image: url('https://img.icons8.com/color/96/star.png'); background-size: cover; cursor: pointer; transform-style: preserve-3d; transition: all 0.2s; filter: drop-shadow(0 0 5px gold); }
        .star:hover { transform: scale(1.2); filter: drop-shadow(0 0 10px gold); }
        .scoreBoard { position: fixed; top: 10px; left: 10px; color: white; font-size: 24px; z-index: 100; background: rgba(0,0,0,0.5); padding: 10px 20px; border-radius: 15px; border: 2px solid #ffd700; }
        .timer { position: fixed; top: 10px; right: 10px; color: white; font-size: 24px; z-index: 100; background: rgba(0,0,0,0.5); padding: 10px 20px; border-radius: 15px; border: 2px solid #00ff00; }
        .level { position: fixed; top: 60px; right: 10px; color: #00ff00; font-size: 20px; z-index: 100; background: rgba(0,0,0,0.5); padding: 10px 20px; border-radius: 15px; border: 2px solid #00ff00; }
        #startButton { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); padding: 20px 40px; font-size: 28px; background: linear-gradient(45deg, #ffd700, #ff8c00); border: none; border-radius: 20px; cursor: pointer; z-index: 100; box-shadow: 0 0 20px rgba(255, 215, 0, 0.7); }
        #startButton:hover { transform: translate(-50%, -50%) scale(1.1); }
        .gameOver { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 48px; z-index: 200; text-align: center; background: rgba(0,0,0,0.8); padding: 40px; border-radius: 20px; border: 3px solid #ff3333; display: none; }
        .highScore { color: #ffd700; font-size: 20px; margin-top: 10px; }
        .confetti { position: absolute; width: 10px; height: 10px; z-index: 50; }
        .particle { position: absolute; background: radial-gradient(circle, #fff 0%, rgba(255,255,255,0) 70%); border-radius: 50%; pointer-events: none; z-index: 50; }
    </style>
</head>
<body>
    <!-- العناصر الأساسية فقط -->
    <div class="scoreBoard">النقاط: <span id="score">0</span></div>
    <div class="timer">الوقت: <span id="time">60</span> ثانية</div>
    <div class="level">المستوى: <span id="level">1</span></div>
    <button id="startButton">ابدأ اللعبة</button>
    <div id="gameArea"></div>
    <div class="gameOver" id="gameOver">
        <h1>انتهت اللعبة!</h1>
        <p>نقاطك: <span id="finalScore">0</span></p>
        <div class="highScore">أعلى نقاط: <span id="highScore">0</span></div>
        <button id="restartButton" style="padding: 10px 20px; font-size: 18px; margin-top: 20px; background: #ffd700; border: none; border-radius: 10px; cursor: pointer;">العودة للعب</button>
    </div>

    <script>
        // ✅ الإصدار المُصلح (بدون صوت مضمن + تبسيط للتشغيل)
        let score = 0, timeLeft = 60, level = 1, gameActive = false, spawnInterval = 1500, minSpawnTime = 300, maxStars = 15, starsOnScreen = 0, timerInterval, spawnIntervalId, highScore = localStorage.getItem('starCatchHighScore') || 0;
        const gameArea = document.getElementById('gameArea'), startButton = document.getElementById('startButton'), scoreElement = document.getElementById('score'), timeElement = document.getElementById('time'), levelElement = document.getElementById('level'), gameOverScreen = document.getElementById('gameOver'), finalScoreElement = document.getElementById('finalScore'), highScoreElement = document.getElementById('highScore'), restartButton = document.getElementById('restartButton');
        highScoreElement.textContent = highScore;

        // ✅ إزالة الصوت المضمن (مشكلة رئيسية)
        function playSound() { /* تم التعطيل مؤقتًا */ }

        startButton.onclick = function() { startGame(); };
        restartButton.onclick = function() { gameOverScreen.style.display = 'none'; startButton.style.display = 'block'; clearInterval(timerInterval); clearInterval(spawnIntervalId); gameArea.innerHTML = ''; starsOnScreen = 0; };

        function startGame() {
            score = 0; timeLeft = 60; level = 1; spawnInterval = 1500; starsOnScreen = 0;
            scoreElement.textContent = score; timeElement.textContent = timeLeft; levelElement.textContent = level;
            startButton.style.display = 'none'; gameOverScreen.style.display = 'none'; gameActive = true;
            timerInterval = setInterval(() => { timeLeft--; timeElement.textContent = timeLeft; if (timeLeft <= 0) endGame(); }, 1000);
            spawnNewStar();
        }

        function spawnNewStar() {
            if (!gameActive || starsOnScreen >= maxStars) return;
            createStar();
            const nextSpawn = spawnInterval * (0.5 + Math.random());
            spawnIntervalId = setTimeout(spawnNewStar, nextSpawn);
        }

        function createStar() {
            if (!gameActive) return;
            const star = document.createElement('div');
            star.className = 'star';
            star.style.left = Math.random() * (window.innerWidth - 50) + 'px';
            star.style.top = Math.random() * (window.innerHeight - 50) + 'px';
            star.style.width = (30 + Math.random() * 20) + 'px';
            star.style.height = star.style.width;
            star.style.opacity = '0';
            setTimeout(() => { star.style.opacity = '1'; star.style.transform = 'scale(1)'; }, 10);
            const disappearTime = 2000 + (60 - timeLeft) * 20;
            const disappearTimeout = setTimeout(() => { star.style.opacity = '0'; setTimeout(() => { if (star.parentNode) star.remove(); starsOnScreen--; }, 300); }, disappearTime);
            star.onclick = function(e) {
                if (!gameActive) return;
                playSound();
                score++; scoreElement.textContent = score;
                level = Math.floor(score / 5) + 1; levelElement.textContent = level;
                if (score % 5 === 0 && spawnInterval > minSpawnTime) spawnInterval = Math.max(minSpawnTime, spawnInterval - 150);
                star.style.opacity = '0'; star.style.transform = 'scale(2)';
                setTimeout(() => { star.remove(); starsOnScreen--; }, 300);
                clearTimeout(disappearTimeout); e.stopPropagation();
            };
            gameArea.appendChild(star);
            starsOnScreen++;
        }

        function endGame() {
            gameActive = false; clearInterval(timerInterval); clearInterval(spawnIntervalId);
            document.querySelectorAll('.star').forEach(s => s.remove()); starsOnScreen = 0;
            if (score > highScore) { highScore = score; localStorage.setItem('starCatchHighScore', highScore); highScoreElement.textContent = highScore; }
            finalScoreElement.textContent = score; gameOverScreen.style.display = 'block';
        }

        // ✅ دعم اللمس (للموبايل)
        gameArea.ontouchstart = function(e) {
            if (!gameActive) return;
            const touch = e.touches[0];
            const target = document.elementFromPoint(touch.clientX, touch.clientY);
            if (target && target.classList.contains('star')) target.click();
        };
    </script>
</body>
</html>
