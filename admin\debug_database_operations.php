<?php
require_once '../config/config.php';
requireAdminLogin();

echo "<h2>تشخيص مفصل لعمليات قاعدة البيانات</h2>";

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test 1: Check current updateData function behavior
echo "<h3>1. تشخيص دالة updateData</h3>";

// Create a test product first
$testData = [
    'name' => 'منتج اختبار التشخيص',
    'description' => 'منتج للتشخيص',
    'price' => 10.00,
    'stock' => 5,
    'status' => 'active'
];

$testId = insertData('products', $testData);
if ($testId) {
    echo "<p>تم إنشاء منتج اختبار (ID: $testId)</p>";
    
    // Test the problematic updateData call
    echo "<h4>اختبار التحديث الحالي:</h4>";
    
    // This is how it's currently being called
    $updateData = ['status' => 'inactive'];
    $whereClause = 'id = ?';
    $whereParams = [$testId];
    
    echo "<p><strong>البيانات المراد تحديثها:</strong> " . print_r($updateData, true) . "</p>";
    echo "<p><strong>شرط WHERE:</strong> $whereClause</p>";
    echo "<p><strong>معاملات WHERE:</strong> " . print_r($whereParams, true) . "</p>";
    
    // Show what array_merge produces
    $mergedParams = array_merge($updateData, $whereParams);
    echo "<p><strong>المعاملات المدمجة:</strong> " . print_r($mergedParams, true) . "</p>";
    
    // Show the SQL that would be generated
    $setClause = [];
    foreach($updateData as $key => $value) {
        $setClause[] = "{$key} = :{$key}";
    }
    $setClause = implode(', ', $setClause);
    $sql = "UPDATE products SET {$setClause} WHERE {$whereClause}";
    echo "<p><strong>SQL المُولد:</strong> $sql</p>";
    
    // Try the update
    $result = updateData('products', $updateData, $whereClause, $whereParams);
    echo "<p><strong>نتيجة التحديث:</strong> " . var_export($result, true) . "</p>";
    
    if ($result === false || $result === 0) {
        echo "<p style='color: red;'>❌ التحديث فشل!</p>";
        
        // Let's try to understand why
        echo "<h4>تشخيص السبب:</h4>";
        
        // Check if the record exists
        $existingRecord = fetchOne("SELECT * FROM products WHERE id = ?", [$testId]);
        if ($existingRecord) {
            echo "<p style='color: green;'>✅ السجل موجود</p>";
            echo "<p>الحالة الحالية: " . $existingRecord['status'] . "</p>";
        } else {
            echo "<p style='color: red;'>❌ السجل غير موجود!</p>";
        }
        
        // Try manual SQL execution
        echo "<h4>اختبار SQL يدوي:</h4>";
        try {
            global $pdo;
            $stmt = $pdo->prepare("UPDATE products SET status = ? WHERE id = ?");
            $manualResult = $stmt->execute(['inactive', $testId]);
            $rowCount = $stmt->rowCount();
            
            echo "<p><strong>تنفيذ SQL يدوي:</strong> " . ($manualResult ? 'نجح' : 'فشل') . "</p>";
            echo "<p><strong>عدد الصفوف المتأثرة:</strong> $rowCount</p>";
            
            if ($manualResult && $rowCount > 0) {
                echo "<p style='color: green;'>✅ SQL اليدوي نجح - المشكلة في دالة updateData</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في SQL اليدوي: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ التحديث نجح!</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ فشل في إنشاء منتج الاختبار</p>";
}

// Test 2: Analyze the parameter binding issue
echo "<h3>2. تحليل مشكلة ربط المعاملات</h3>";

echo "<h4>المشكلة المحتملة:</h4>";
echo "<p>عندما نستخدم <code>array_merge(\$data, \$whereParams)</code> مع:</p>";
echo "<ul>";
echo "<li><strong>data:</strong> ['status' => 'inactive']</li>";
echo "<li><strong>whereParams:</strong> [123] (product ID)</li>";
echo "</ul>";
echo "<p>النتيجة: ['status' => 'inactive', 0 => 123]</p>";
echo "<p>لكن SQL يتوقع: <code>UPDATE products SET status = :status WHERE id = ?</code></p>";
echo "<p><strong>المشكلة:</strong> المعامل الثاني يجب أن يكون مرقم (?) وليس مسمى (:name)</p>";

// Test 3: Test the correct approach
echo "<h3>3. اختبار الحل الصحيح</h3>";

if ($testId) {
    echo "<h4>الطريقة الصحيحة لربط المعاملات:</h4>";
    
    try {
        global $pdo;
        
        // Method 1: Use positional parameters for WHERE
        $sql1 = "UPDATE products SET status = ? WHERE id = ?";
        $stmt1 = $pdo->prepare($sql1);
        $result1 = $stmt1->execute(['active', $testId]);
        $rowCount1 = $stmt1->rowCount();
        
        echo "<p><strong>الطريقة 1 (معاملات موضعية):</strong></p>";
        echo "<p>SQL: $sql1</p>";
        echo "<p>المعاملات: ['active', $testId]</p>";
        echo "<p>النتيجة: " . ($result1 ? 'نجح' : 'فشل') . " (صفوف متأثرة: $rowCount1)</p>";
        
        // Method 2: Use named parameters for everything
        $sql2 = "UPDATE products SET status = :status WHERE id = :id";
        $stmt2 = $pdo->prepare($sql2);
        $result2 = $stmt2->execute(['status' => 'inactive', 'id' => $testId]);
        $rowCount2 = $stmt2->rowCount();
        
        echo "<p><strong>الطريقة 2 (معاملات مسماة):</strong></p>";
        echo "<p>SQL: $sql2</p>";
        echo "<p>المعاملات: ['status' => 'inactive', 'id' => $testId]</p>";
        echo "<p>النتيجة: " . ($result2 ? 'نجح' : 'فشل') . " (صفوف متأثرة: $rowCount2)</p>";
        
        if ($result1 && $rowCount1 > 0 && $result2 && $rowCount2 > 0) {
            echo "<p style='color: green;'>✅ كلا الطريقتين تعملان - المشكلة في دالة updateData</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في الاختبار: " . $e->getMessage() . "</p>";
    }
}

// Test 4: Test deleteData function
echo "<h3>4. اختبار دالة deleteData</h3>";

if ($testId) {
    // First, let's see what deleteData does
    echo "<h4>اختبار حذف المنتج:</h4>";
    
    $deleteResult = deleteData('products', 'id = ?', [$testId]);
    echo "<p><strong>نتيجة الحذف:</strong> " . var_export($deleteResult, true) . "</p>";
    
    if ($deleteResult === false || $deleteResult === 0) {
        echo "<p style='color: red;'>❌ الحذف فشل!</p>";
        
        // Try manual delete
        try {
            global $pdo;
            $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
            $manualDeleteResult = $stmt->execute([$testId]);
            $deleteRowCount = $stmt->rowCount();
            
            echo "<p><strong>حذف يدوي:</strong> " . ($manualDeleteResult ? 'نجح' : 'فشل') . "</p>";
            echo "<p><strong>صفوف محذوفة:</strong> $deleteRowCount</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في الحذف اليدوي: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ الحذف نجح!</p>";
    }
}

echo "<hr>";
echo "<h3>الخلاصة والحلول المقترحة:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff;'>";
echo "<h4>المشاكل المكتشفة:</h4>";
echo "<ol>";
echo "<li><strong>مشكلة ربط المعاملات:</strong> خلط بين المعاملات الموضعية (?) والمسماة (:name)</li>";
echo "<li><strong>دالة updateData:</strong> تستخدم معاملات مسماة للـ SET ومعاملات موضعية للـ WHERE</li>";
echo "<li><strong>array_merge:</strong> يخلط أنواع المعاملات مما يسبب فشل في التنفيذ</li>";
echo "</ol>";

echo "<h4>الحلول:</h4>";
echo "<ol>";
echo "<li><strong>إصلاح دالة updateData:</strong> استخدام معاملات مسماة للكل أو موضعية للكل</li>";
echo "<li><strong>تحديث استدعاءات الدوال:</strong> تمرير المعاملات بالشكل الصحيح</li>";
echo "<li><strong>تحسين معالجة الأخطاء:</strong> إضافة تسجيل أفضل للأخطاء</li>";
echo "</ol>";
echo "</div>";

echo "<p><a href='fix_database_functions.php'>تطبيق الإصلاحات التلقائية</a></p>";
echo "<p><a href='products.php'>العودة إلى إدارة المنتجات</a></p>";
?>
