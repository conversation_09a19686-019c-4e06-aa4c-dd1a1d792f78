<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التحقق من رقم الهاتف العراقي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="tel"] {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            direction: ltr;
            text-align: left;
        }
        input.valid {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        input.invalid {
            border-color: #dc3545;
            background-color: #fff8f8;
        }
        .validation-message {
            margin-top: 5px;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        .validation-message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .validation-message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-cases {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-case {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            margin: 5px 0;
            background: white;
            border-radius: 4px;
        }
        .test-case.valid {
            border-left: 4px solid #28a745;
        }
        .test-case.invalid {
            border-left: 4px solid #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار التحقق من رقم الهاتف العراقي</h1>
        
        <div class="test-form">
            <h2>اختبار تفاعلي</h2>
            <div class="form-group">
                <label for="phoneInput">أدخل رقم الهاتف:</label>
                <input type="tel" id="phoneInput" placeholder="مثال: 07123456789 أو +9647123456789">
                <div id="validationMessage" class="validation-message" style="display: none;"></div>
            </div>
            <button onclick="testPhone()">اختبار الرقم</button>
        </div>
        
        <div class="test-cases">
            <h2>حالات الاختبار التلقائية</h2>
            <div id="testResults"></div>
            <button onclick="runAllTests()">تشغيل جميع الاختبارات</button>
        </div>
    </div>

    <script>
        // Iraqi phone validation patterns (same as in checkout.php)
        const phonePatterns = [
            /^07[0-9]{8}$/,                    // 07xxxxxxxx
            /^\+9647[0-9]{8}$/,                // +9647xxxxxxxx
            /^9647[0-9]{8}$/,                  // 9647xxxxxxxx
            /^07[7-9][0-9]{7}$/,               // 077xxxxxxx, 078xxxxxxx, 079xxxxxxx
            /^\+96477[0-9]{7}$/,               // +96477xxxxxxx
            /^\+96478[0-9]{7}$/,               // +96478xxxxxxx
            /^\+96479[0-9]{7}$/                // +96479xxxxxxx
        ];

        function validateIraqiPhone(phone) {
            if (!phone) return false;
            
            // Clean phone number
            const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
            
            // Test against patterns
            for (let pattern of phonePatterns) {
                if (pattern.test(cleanPhone)) {
                    return true;
                }
            }
            return false;
        }

        function testPhone() {
            const phoneInput = document.getElementById('phoneInput');
            const messageDiv = document.getElementById('validationMessage');
            const phone = phoneInput.value.trim();
            
            if (!phone) {
                messageDiv.style.display = 'none';
                phoneInput.className = '';
                return;
            }
            
            const isValid = validateIraqiPhone(phone);
            
            messageDiv.style.display = 'block';
            
            if (isValid) {
                phoneInput.className = 'valid';
                messageDiv.className = 'validation-message success';
                messageDiv.textContent = '✅ رقم الهاتف صحيح';
            } else {
                phoneInput.className = 'invalid';
                messageDiv.className = 'validation-message error';
                messageDiv.textContent = '❌ يرجى إدخال رقم هاتف عراقي صحيح (مثال: 07xxxxxxxx أو +9647xxxxxxxx)';
            }
        }

        function runAllTests() {
            const testCases = {
                '07123456789': { description: 'رقم عراقي عادي', expected: true },
                '+9647123456789': { description: 'رقم دولي مع +', expected: true },
                '9647123456789': { description: 'رقم دولي بدون +', expected: true },
                '************': { description: 'رقم مع مسافات', expected: true },
                '************': { description: 'رقم مع شرطات', expected: true },
                '079(123)4567': { description: 'رقم مع أقواس', expected: true },
                '+964 77 123 4567': { description: 'رقم دولي مع مسافات', expected: true },
                '07712345678': { description: 'شبكة 077', expected: true },
                '07812345678': { description: 'شبكة 078', expected: true },
                '07912345678': { description: 'شبكة 079', expected: true },
                '0612345678': { description: 'رقم غير صحيح - ليس موبايل', expected: false },
                '123456789': { description: 'رقم قصير جداً', expected: false },
                '+1234567890': { description: 'كود دولة خاطئ', expected: false },
                '07123': { description: 'رقم ناقص', expected: false },
                'abc123def': { description: 'أحرف مع أرقام', expected: false }
            };

            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '';

            let passedTests = 0;
            let totalTests = Object.keys(testCases).length;

            for (let phone in testCases) {
                const testCase = testCases[phone];
                const result = validateIraqiPhone(phone);
                const passed = result === testCase.expected;
                
                if (passed) passedTests++;

                const testDiv = document.createElement('div');
                testDiv.className = 'test-case ' + (passed ? 'valid' : 'invalid');
                testDiv.innerHTML = `
                    <span><strong>${phone}</strong> - ${testCase.description}</span>
                    <span>${passed ? '✅ نجح' : '❌ فشل'}</span>
                `;
                resultsDiv.appendChild(testDiv);
            }

            // Add summary
            const summaryDiv = document.createElement('div');
            summaryDiv.style.marginTop = '20px';
            summaryDiv.style.padding = '15px';
            summaryDiv.style.backgroundColor = passedTests === totalTests ? '#d4edda' : '#f8d7da';
            summaryDiv.style.borderRadius = '5px';
            summaryDiv.innerHTML = `
                <strong>النتيجة: ${passedTests}/${totalTests} اختبار نجح</strong>
                ${passedTests === totalTests ? '<br>🎉 جميع الاختبارات نجحت!' : '<br>⚠️ بعض الاختبارات فشلت'}
            `;
            resultsDiv.appendChild(summaryDiv);
        }

        // Auto-test on input
        document.getElementById('phoneInput').addEventListener('input', testPhone);
        
        // Run tests on page load
        window.addEventListener('load', runAllTests);
    </script>
</body>
</html>
