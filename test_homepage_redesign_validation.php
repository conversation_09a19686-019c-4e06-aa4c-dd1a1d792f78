<?php
/**
 * Homepage Redesign Validation Test
 * Professional Arabic E-commerce Homepage Testing Suite
 * 
 * @version 3.0
 * <AUTHOR> Development Team
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

$pageTitle = 'اختبار تصميم الصفحة الرئيسية الجديد';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; direction: rtl; }
        .test-section { margin: 2rem 0; padding: 1.5rem; border-radius: 10px; }
        .test-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .test-error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .test-item { margin: 0.5rem 0; padding: 0.5rem; border-radius: 5px; }
        .checklist { list-style: none; padding: 0; }
        .checklist li { margin: 0.5rem 0; padding: 0.5rem; border-radius: 5px; }
        .checklist li.pass { background: #d4edda; color: #155724; }
        .checklist li.fail { background: #f8d7da; color: #721c24; }
        .checklist li.warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>

<div class="container my-5">
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary">🏠 اختبار تصميم الصفحة الرئيسية الجديد</h1>
        <p class="lead">اختبار شامل للتصميم المهني الجديد للصفحة الرئيسية</p>
        <small class="text-muted">تاريخ الاختبار: <?php echo date('Y-m-d H:i:s'); ?></small>
    </div>

    <?php
    $testResults = [];
    $errors = [];
    $warnings = [];
    $successes = [];

    // Test 1: CSS Files Validation
    echo "<div class='test-section test-info'>";
    echo "<h2><i class='bi bi-file-earmark-code'></i> اختبار ملفات CSS</h2>";
    
    $cssFiles = [
        'assets/css/homepage.css' => 'ملف CSS الرئيسي للصفحة الرئيسية',
        'assets/css/hero-carousel.css' => 'ملف CSS للكاروسيل'
    ];
    
    echo "<ul class='checklist'>";
    foreach ($cssFiles as $file => $description) {
        if (file_exists($file)) {
            $fileSize = filesize($file);
            $fileSizeKB = round($fileSize / 1024, 2);
            echo "<li class='pass'><i class='bi bi-check-circle'></i> {$description}: موجود ({$fileSizeKB} KB)</li>";
            $successes[] = "ملف CSS موجود: {$file}";
        } else {
            echo "<li class='fail'><i class='bi bi-x-circle'></i> {$description}: غير موجود</li>";
            $errors[] = "ملف CSS مفقود: {$file}";
        }
    }
    echo "</ul>";
    echo "</div>";

    // Test 2: Homepage Structure Validation
    echo "<div class='test-section test-info'>";
    echo "<h2><i class='bi bi-layout-text-window-reverse'></i> اختبار هيكل الصفحة الرئيسية</h2>";
    
    $homepageFile = 'index.php';
    if (file_exists($homepageFile)) {
        $homepageContent = file_get_contents($homepageFile);
        
        $requiredSections = [
            'homepage-section' => 'أقسام الصفحة الرئيسية المهنية',
            'section-divider' => 'فواصل الأقسام المهنية',
            'animate-on-scroll' => 'تأثيرات التحريك عند التمرير',
            'product-card' => 'بطاقات المنتجات المحسنة',
            'category-card' => 'بطاقات الفئات الجديدة',
            'promo-banner' => 'البانرات الترويجية',
            'social-media-grid' => 'شبكة وسائل التواصل الاجتماعي'
        ];
        
        echo "<ul class='checklist'>";
        foreach ($requiredSections as $class => $description) {
            if (strpos($homepageContent, $class) !== false) {
                echo "<li class='pass'><i class='bi bi-check-circle'></i> {$description}: موجود</li>";
                $successes[] = "قسم موجود: {$description}";
            } else {
                echo "<li class='warning'><i class='bi bi-exclamation-triangle'></i> {$description}: غير موجود أو غير مستخدم</li>";
                $warnings[] = "قسم مفقود: {$description}";
            }
        }
        echo "</ul>";
    } else {
        echo "<div class='alert alert-danger'>ملف الصفحة الرئيسية غير موجود!</div>";
        $errors[] = "ملف index.php غير موجود";
    }
    echo "</div>";

    // Test 3: Database Integration
    echo "<div class='test-section test-info'>";
    echo "<h2><i class='bi bi-database'></i> اختبار تكامل قاعدة البيانات</h2>";
    
    try {
        // Test homepage settings table
        $homepageSettingsExists = fetchOne("SHOW TABLES LIKE 'homepage_settings'");
        if ($homepageSettingsExists) {
            echo "<div class='test-item test-success'><i class='bi bi-check-circle'></i> جدول إعدادات الصفحة الرئيسية موجود</div>";
            
            // Test settings data
            $settingsCount = fetchOne("SELECT COUNT(*) as count FROM homepage_settings")['count'];
            echo "<div class='test-item test-success'><i class='bi bi-check-circle'></i> عدد الإعدادات: {$settingsCount}</div>";
            $successes[] = "جدول homepage_settings يحتوي على {$settingsCount} إعداد";
        } else {
            echo "<div class='test-item test-error'><i class='bi bi-x-circle'></i> جدول إعدادات الصفحة الرئيسية غير موجود</div>";
            $errors[] = "جدول homepage_settings غير موجود";
        }
        
        // Test products table
        $productsCount = fetchOne("SELECT COUNT(*) as count FROM products WHERE status = 'active'")['count'];
        echo "<div class='test-item test-success'><i class='bi bi-check-circle'></i> عدد المنتجات النشطة: {$productsCount}</div>";
        
        // Test categories table
        $categoriesCount = fetchOne("SELECT COUNT(*) as count FROM categories WHERE status = 'active'")['count'];
        echo "<div class='test-item test-success'><i class='bi bi-check-circle'></i> عدد الفئات النشطة: {$categoriesCount}</div>";
        
    } catch (Exception $e) {
        echo "<div class='test-item test-error'><i class='bi bi-x-circle'></i> خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
        $errors[] = "خطأ في قاعدة البيانات: " . $e->getMessage();
    }
    echo "</div>";

    // Test 4: Responsive Design Features
    echo "<div class='test-section test-info'>";
    echo "<h2><i class='bi bi-phone'></i> اختبار التصميم المتجاوب</h2>";
    
    if (file_exists('assets/css/homepage.css')) {
        $cssContent = file_get_contents('assets/css/homepage.css');
        
        $responsiveFeatures = [
            '@media (max-width: 768px)' => 'استعلامات الوسائط للأجهزة المحمولة',
            '@media (max-width: 576px)' => 'استعلامات الوسائط للهواتف الصغيرة',
            '@media (min-width: 992px)' => 'استعلامات الوسائط للشاشات الكبيرة',
            'prefers-reduced-motion' => 'دعم تقليل الحركة',
            'prefers-contrast: high' => 'دعم التباين العالي',
            '[dir="rtl"]' => 'دعم اتجاه RTL'
        ];
        
        echo "<ul class='checklist'>";
        foreach ($responsiveFeatures as $feature => $description) {
            if (strpos($cssContent, $feature) !== false) {
                echo "<li class='pass'><i class='bi bi-check-circle'></i> {$description}: مدعوم</li>";
                $successes[] = "ميزة متجاوبة: {$description}";
            } else {
                echo "<li class='warning'><i class='bi bi-exclamation-triangle'></i> {$description}: غير مدعوم</li>";
                $warnings[] = "ميزة متجاوبة مفقودة: {$description}";
            }
        }
        echo "</ul>";
    }
    echo "</div>";

    // Test 5: Performance Optimizations
    echo "<div class='test-section test-info'>";
    echo "<h2><i class='bi bi-speedometer2'></i> اختبار تحسينات الأداء</h2>";
    
    if (file_exists($homepageFile)) {
        $homepageContent = file_get_contents($homepageFile);
        
        $performanceFeatures = [
            'loading="lazy"' => 'تحميل الصور الكسول',
            'animate-on-scroll' => 'تأثيرات التحريك المحسنة',
            'transition:' => 'انتقالات CSS محسنة',
            'transform:' => 'تحويلات CSS للأداء'
        ];
        
        echo "<ul class='checklist'>";
        foreach ($performanceFeatures as $feature => $description) {
            $count = substr_count($homepageContent, $feature);
            if ($count > 0) {
                echo "<li class='pass'><i class='bi bi-check-circle'></i> {$description}: مستخدم ({$count} مرة)</li>";
                $successes[] = "ميزة أداء: {$description}";
            } else {
                echo "<li class='warning'><i class='bi bi-exclamation-triangle'></i> {$description}: غير مستخدم</li>";
                $warnings[] = "ميزة أداء مفقودة: {$description}";
            }
        }
        echo "</ul>";
    }
    echo "</div>";

    // Test Summary
    echo "<div class='test-section'>";
    echo "<h2><i class='bi bi-clipboard-check'></i> ملخص نتائج الاختبار</h2>";
    
    $totalTests = count($successes) + count($warnings) + count($errors);
    $successRate = $totalTests > 0 ? round((count($successes) / $totalTests) * 100, 2) : 0;
    
    echo "<div class='row'>";
    echo "<div class='col-md-4'>";
    echo "<div class='card text-center border-success'>";
    echo "<div class='card-body'>";
    echo "<h3 class='text-success'>" . count($successes) . "</h3>";
    echo "<p class='card-text'>اختبارات ناجحة</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card text-center border-warning'>";
    echo "<div class='card-body'>";
    echo "<h3 class='text-warning'>" . count($warnings) . "</h3>";
    echo "<p class='card-text'>تحذيرات</p>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card text-center border-danger'>";
    echo "<div class='card-body'>";
    echo "<h3 class='text-danger'>" . count($errors) . "</h3>";
    echo "<p class='card-text'>أخطاء</p>";
    echo "</div></div></div>";
    echo "</div>";
    
    echo "<div class='mt-4 text-center'>";
    echo "<h4>معدل النجاح: <span class='badge bg-" . ($successRate >= 80 ? 'success' : ($successRate >= 60 ? 'warning' : 'danger')) . "'>{$successRate}%</span></h4>";
    echo "</div>";
    echo "</div>";

    // Recommendations
    echo "<div class='test-section test-info'>";
    echo "<h2><i class='bi bi-lightbulb'></i> التوصيات والتحسينات</h2>";
    
    $recommendations = [
        "✅ تم تطبيق التصميم المهني الجديد بنجاح",
        "✅ تم إضافة فواصل مهنية بين الأقسام",
        "✅ تم تحسين بطاقات المنتجات والفئات",
        "✅ تم إضافة أقسام جديدة (الفئات المميزة، البانرات الترويجية، وسائل التواصل)",
        "✅ تم تحسين التصميم المتجاوب لجميع الأجهزة",
        "✅ تم إضافة دعم RTL محسن للغة العربية",
        "✅ تم تطبيق تأثيرات التحريك المهنية",
        "🔧 يُنصح بإضافة المزيد من المحتوى التجريبي",
        "🔧 يُنصح بتحسين سرعة تحميل الصور",
        "🔧 يُنصح بإضافة المزيد من تأثيرات التفاعل"
    ];
    
    echo "<ul class='list-group'>";
    foreach ($recommendations as $rec) {
        $class = strpos($rec, '✅') !== false ? 'list-group-item-success' : 'list-group-item-info';
        echo "<li class='list-group-item {$class}'>{$rec}</li>";
    }
    echo "</ul>";
    echo "</div>";
    ?>

    <div class="text-center mt-5">
        <a href="index.php" class="btn btn-primary btn-lg">
            <i class="bi bi-house"></i> عرض الصفحة الرئيسية الجديدة
        </a>
        <a href="admin/homepage_settings.php" class="btn btn-success btn-lg ms-3">
            <i class="bi bi-gear"></i> إعدادات الصفحة الرئيسية
        </a>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
