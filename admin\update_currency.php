<?php
require_once '../config/config.php';
requireAdminLogin();

// Update currency setting in database
try {
    $newCurrency = 'دينار عراقي';

    // Use the dedicated updateSetting function
    $result = updateSetting('currency', $newCurrency);

    if ($result !== false) {
        echo "<div style='padding: 20px; background: #d4edda; color: #155724; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px;'>";
        echo "<h3>✅ تم تحديث العملة بنجاح</h3>";
        echo "<p>تم تغيير العملة من 'ريال' إلى 'دينار عراقي' في قاعدة البيانات.</p>";
        echo "<p><strong>العملة الحالية:</strong> " . getSetting('currency') . "</p>";
        echo "<p><a href='dashboard.php' class='btn btn-success'>العودة إلى لوحة التحكم</a></p>";
        echo "</div>";
    } else {
        throw new Exception("فشل في تحديث قاعدة البيانات");
    }
    
} catch (Exception $e) {
    echo "<div style='padding: 20px; background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px;'>";
    echo "<h3>❌ حدث خطأ</h3>";
    echo "<p>فشل في تحديث العملة: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><a href='dashboard.php' class='btn btn-danger'>العودة إلى لوحة التحكم</a></p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث العملة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">تحديث العملة</h4>
                    </div>
                    <div class="card-body">
                        <p>هذه الصفحة تقوم بتحديث العملة في النظام من "ريال" إلى "دينار عراقي".</p>
                        <p>سيتم تحديث:</p>
                        <ul>
                            <li>إعدادات قاعدة البيانات</li>
                            <li>عرض الأسعار في جميع أنحاء النظام</li>
                            <li>تنسيق العملة في التقارير</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
