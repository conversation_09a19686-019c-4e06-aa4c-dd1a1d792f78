<?php
/**
 * Comprehensive checkout debugging and testing
 */

require_once 'config/config.php';

echo "<h2>Checkout System Debug & Test</h2>";

// 1. Test database connection
echo "<h3>1. Database Connection Test</h3>";
if (!$pdo) {
    echo "❌ PDO connection is null<br>";
    die("Database connection failed");
}

try {
    $pdo->query("SELECT 1");
    echo "✅ Database connection working<br>";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
    die();
}

// 2. Check required tables
echo "<h3>2. Database Tables Check</h3>";
$requiredTables = ['orders', 'order_items', 'products'];
foreach ($requiredTables as $table) {
    try {
        $result = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($result->rowCount() > 0) {
            echo "✅ Table '$table' exists<br>";
        } else {
            echo "❌ Table '$table' missing<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error checking table '$table': " . $e->getMessage() . "<br>";
    }
}

// 3. Check orders table structure
echo "<h3>3. Orders Table Structure</h3>";
try {
    $columns = $pdo->query("DESCRIBE orders")->fetchAll();
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "❌ Error getting orders table structure: " . $e->getMessage() . "<br>";
}

// 4. Test insertData function
echo "<h3>4. Test insertData Function</h3>";
if (!function_exists('insertData')) {
    echo "❌ insertData function not found<br>";
} else {
    echo "✅ insertData function exists<br>";
    
    // Test with sample data
    $testData = [
        'customer_name' => 'Test Customer Debug',
        'customer_phone' => '07123456789',
        'address' => 'Test Address Baghdad',
        'province' => 'بغداد',
        'subtotal' => 50000.00,
        'delivery_price' => 5000.00,
        'discount_amount' => 0.00,
        'total_price' => 55000.00,
        'payment_method' => 'cash_on_delivery',
        'status' => 'pending'
    ];
    
    echo "Testing insertData with: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "<br>";
    
    $testOrderId = insertData('orders', $testData);
    if ($testOrderId) {
        echo "✅ insertData test successful - Order ID: $testOrderId<br>";
        
        // Verify the order exists
        $verifyOrder = fetchOne("SELECT * FROM orders WHERE id = ?", [$testOrderId]);
        if ($verifyOrder) {
            echo "✅ Order verified in database<br>";
            echo "Customer: " . $verifyOrder['customer_name'] . "<br>";
            echo "Total: " . number_format($verifyOrder['total_price']) . " IQD<br>";
        } else {
            echo "❌ Order not found after insertion<br>";
        }
        
        // Clean up
        $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$testOrderId]);
        echo "✅ Test data cleaned up<br>";
    } else {
        echo "❌ insertData test failed<br>";
    }
}

// 5. Test cart functions
echo "<h3>5. Cart Functions Test</h3>";
if (!function_exists('getCart')) {
    echo "❌ getCart function not found<br>";
} else {
    echo "✅ getCart function exists<br>";
}

if (!function_exists('clearCart')) {
    echo "❌ clearCart function not found<br>";
} else {
    echo "✅ clearCart function exists<br>";
}

// 6. Test session
echo "<h3>6. Session Test</h3>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "✅ Session is active<br>";
} else {
    echo "❌ Session is not active<br>";
}

// 7. Check for any existing orders
echo "<h3>7. Existing Orders Check</h3>";
try {
    $orderCount = fetchOne("SELECT COUNT(*) as count FROM orders");
    echo "Total orders in database: " . $orderCount['count'] . "<br>";
    
    $recentOrders = fetchAll("SELECT id, customer_name, customer_phone, total_price, status, created_at FROM orders ORDER BY created_at DESC LIMIT 5");
    if (count($recentOrders) > 0) {
        echo "<h4>Recent Orders:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Customer</th><th>Phone</th><th>Total</th><th>Status</th><th>Created</th></tr>";
        foreach ($recentOrders as $order) {
            echo "<tr>";
            echo "<td>" . $order['id'] . "</td>";
            echo "<td>" . htmlspecialchars($order['customer_name']) . "</td>";
            echo "<td>" . htmlspecialchars($order['customer_phone']) . "</td>";
            echo "<td>" . number_format($order['total_price']) . " IQD</td>";
            echo "<td>" . $order['status'] . "</td>";
            echo "<td>" . $order['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No orders found in database<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking existing orders: " . $e->getMessage() . "<br>";
}

echo "<h3>8. Recommendations</h3>";
echo "<p>If all tests above passed, the checkout system should work. If orders are still not being saved:</p>";
echo "<ul>";
echo "<li>Check PHP error logs for detailed error messages</li>";
echo "<li>Ensure the checkout form is submitting with name='place_order'</li>";
echo "<li>Verify that all required form fields are being submitted</li>";
echo "<li>Check if JavaScript is preventing form submission</li>";
echo "</ul>";

echo "<p><a href='checkout.php'>Test Checkout Page</a> | <a href='admin/orders.php'>Check Admin Orders</a></p>";
?>
