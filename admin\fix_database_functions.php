<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'إصلاح دوال قاعدة البيانات';

echo "<h2>إصلاح دوال قاعدة البيانات</h2>";

// Show the current problem
echo "<h3>1. المشكلة الحالية</h3>";
echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
echo "<h4>المشكلة:</h4>";
echo "<p>دالة <code>updateData</code> تخلط بين نوعين من ربط المعاملات:</p>";
echo "<ul>";
echo "<li><strong>معاملات مسماة</strong> للـ SET: <code>status = :status</code></li>";
echo "<li><strong>معاملات موضعية</strong> للـ WHERE: <code>id = ?</code></li>";
echo "</ul>";
echo "<p>هذا يسبب تضارب عند استخدام <code>array_merge()</code></p>";
echo "</div>";

// Show the solution
echo "<h3>2. الحل</h3>";
echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
echo "<h4>الحل:</h4>";
echo "<p>إنشاء دوال محدثة تستخدم <strong>معاملات مسماة فقط</strong> لتجنب التضارب</p>";
echo "</div>";

// Apply the fix
if (isset($_POST['apply_fix'])) {
    echo "<h3>3. تطبيق الإصلاح</h3>";
    
    // Create backup of current functions
    $backupContent = "<?php\n// Backup of original database functions - " . date('Y-m-d H:i:s') . "\n\n";
    $backupContent .= file_get_contents('../config/database.php');
    file_put_contents('database_backup_' . date('Ymd_His') . '.php', $backupContent);
    
    echo "<p style='color: green;'>✅ تم إنشاء نسخة احتياطية من الدوال الحالية</p>";
    
    // Read the current database.php file
    $databaseContent = file_get_contents('../config/database.php');
    
    // Replace the updateData function
    $newUpdateFunction = '/**
 * دالة تحديث بيانات - محدثة
 */
function updateData($table, $data, $where, $whereParams = []) {
    global $pdo;
    
    $setClause = [];
    $allParams = [];
    
    // Build SET clause with named parameters
    foreach($data as $key => $value) {
        $paramName = "set_" . $key;
        $setClause[] = "{$key} = :{$paramName}";
        $allParams[$paramName] = $value;
    }
    $setClause = implode(\', \', $setClause);
    
    // Convert WHERE clause to use named parameters
    $whereNamed = $where;
    $paramIndex = 0;
    
    // Replace ? with named parameters
    while (strpos($whereNamed, \'?\') !== false) {
        $paramName = "where_param_" . $paramIndex;
        $whereNamed = preg_replace(\'/\?\/', ":" . $paramName, $whereNamed, 1);
        if (isset($whereParams[$paramIndex])) {
            $allParams[$paramName] = $whereParams[$paramIndex];
        }
        $paramIndex++;
    }
    
    $sql = "UPDATE {$table} SET {$setClause} WHERE {$whereNamed}";
    
    try {
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($allParams);
        
        if ($result) {
            return $stmt->rowCount();
        } else {
            error_log("Update Error: SQL execution failed. SQL: " . $sql . " Params: " . print_r($allParams, true));
            return false;
        }
    } catch(PDOException $e) {
        error_log("Update Error: " . $e->getMessage() . " SQL: " . $sql . " Params: " . print_r($allParams, true));
        return false;
    }
}';

    // Replace the deleteData function  
    $newDeleteFunction = '/**
 * دالة حذف بيانات - محدثة
 */
function deleteData($table, $where, $params = []) {
    global $pdo;
    
    // Convert WHERE clause to use named parameters
    $whereNamed = $where;
    $namedParams = [];
    $paramIndex = 0;
    
    // Replace ? with named parameters
    while (strpos($whereNamed, \'?\') !== false) {
        $paramName = "param_" . $paramIndex;
        $whereNamed = preg_replace(\'/\?\/', ":" . $paramName, $whereNamed, 1);
        if (isset($params[$paramIndex])) {
            $namedParams[$paramName] = $params[$paramIndex];
        }
        $paramIndex++;
    }
    
    $sql = "DELETE FROM {$table} WHERE {$whereNamed}";
    
    try {
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($namedParams);
        
        if ($result) {
            return $stmt->rowCount();
        } else {
            error_log("Delete Error: SQL execution failed. SQL: " . $sql . " Params: " . print_r($namedParams, true));
            return false;
        }
    } catch(PDOException $e) {
        error_log("Delete Error: " . $e->getMessage() . " SQL: " . $sql . " Params: " . print_r($namedParams, true));
        return false;
    }
}';

    // Find and replace the functions
    $pattern1 = '/\/\*\*\s*\*\s*دالة تحديث بيانات.*?\*\/\s*function updateData\([^}]+\{[^}]*\}[^}]*\}/s';
    $pattern2 = '/\/\*\*\s*\*\s*دالة حذف بيانات.*?\*\/\s*function deleteData\([^}]+\{[^}]*\}[^}]*\}/s';
    
    $updatedContent = preg_replace($pattern1, $newUpdateFunction, $databaseContent);
    $updatedContent = preg_replace($pattern2, $newDeleteFunction, $updatedContent);
    
    // Write the updated content
    if (file_put_contents('../config/database.php', $updatedContent)) {
        echo "<p style='color: green;'>✅ تم تحديث دوال قاعدة البيانات بنجاح</p>";
        
        // Test the new functions
        echo "<h4>اختبار الدوال المحدثة:</h4>";
        
        // Test insert
        $testData = [
            'name' => 'منتج اختبار الإصلاح',
            'description' => 'اختبار الدوال المحدثة',
            'price' => 5.00,
            'stock' => 1,
            'status' => 'active'
        ];
        
        $testId = insertData('products', $testData);
        if ($testId) {
            echo "<p style='color: green;'>✅ الإدراج يعمل (ID: $testId)</p>";
            
            // Test update
            $updateResult = updateData('products', ['status' => 'inactive'], 'id = ?', [$testId]);
            if ($updateResult !== false && $updateResult > 0) {
                echo "<p style='color: green;'>✅ التحديث يعمل (صفوف محدثة: $updateResult)</p>";
            } else {
                echo "<p style='color: red;'>❌ التحديث لا يعمل (النتيجة: " . var_export($updateResult, true) . ")</p>";
            }
            
            // Test delete
            $deleteResult = deleteData('products', 'id = ?', [$testId]);
            if ($deleteResult !== false && $deleteResult > 0) {
                echo "<p style='color: green;'>✅ الحذف يعمل (صفوف محذوفة: $deleteResult)</p>";
            } else {
                echo "<p style='color: red;'>❌ الحذف لا يعمل (النتيجة: " . var_export($deleteResult, true) . ")</p>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ الإدراج لا يعمل</p>";
        }
        
        echo "<div style='background: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px; margin-top: 15px;'>";
        echo "<h4>✅ تم الإصلاح بنجاح!</h4>";
        echo "<p>الآن يمكنك:</p>";
        echo "<ul>";
        echo "<li>تحديث حالة المنتجات</li>";
        echo "<li>تعديل المنتجات</li>";
        echo "<li>حذف المنتجات</li>";
        echo "</ul>";
        echo "<p><a href='products.php' class='btn btn-primary'>العودة إلى إدارة المنتجات</a></p>";
        echo "</div>";
        
    } else {
        echo "<p style='color: red;'>❌ فشل في كتابة الملف المحدث</p>";
    }
    
} else {
    // Show the fix preview
    echo "<h3>3. معاينة الإصلاح</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px;'>";
    echo "<h4>التغييرات المقترحة:</h4>";
    echo "<ol>";
    echo "<li><strong>دالة updateData:</strong> تحويل جميع المعاملات إلى معاملات مسماة</li>";
    echo "<li><strong>دالة deleteData:</strong> تحويل المعاملات الموضعية إلى مسماة</li>";
    echo "<li><strong>تحسين معالجة الأخطاء:</strong> إضافة تسجيل مفصل للأخطاء</li>";
    echo "<li><strong>إنشاء نسخة احتياطية:</strong> حفظ الدوال الحالية قبل التحديث</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<form method='POST' style='margin-top: 20px;'>";
    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin-bottom: 15px;'>";
    echo "<h4>⚠️ تحذير</h4>";
    echo "<p>سيتم إنشاء نسخة احتياطية من الدوال الحالية قبل التحديث.</p>";
    echo "<p>تأكد من أن لديك نسخة احتياطية من قاعدة البيانات أيضاً.</p>";
    echo "</div>";
    echo "<button type='submit' name='apply_fix' class='btn btn-warning btn-lg'>تطبيق الإصلاح</button>";
    echo "</form>";
}

echo "<hr>";
echo "<p><a href='debug_database_operations.php'>تشغيل التشخيص المفصل</a></p>";
echo "<p><a href='products.php'>العودة إلى إدارة المنتجات</a></p>";
?>
