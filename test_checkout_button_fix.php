<?php
/**
 * Test checkout button and JavaScript issues
 */

require_once 'config/config.php';

echo "<h1>🔧 Checkout Button & JavaScript Fix Test</h1>";
echo "<p>This test identifies and fixes JavaScript errors preventing proper form submission.</p>";

// Start session
if (!isset($_SESSION)) {
    session_start();
}

// Setup test environment
echo "<h2>1. Test Environment Setup</h2>";

// Clear and setup test cart
clearCart();
addToCart(1, 2);
$cart = getCart();

if (!empty($cart)) {
    echo "✅ Test cart created<br>";
} else {
    echo "❌ Failed to create test cart<br>";
    
    // Create test products if none exist
    $products = fetchAll("SELECT id FROM products WHERE status = 'active' LIMIT 1");
    if (empty($products)) {
        echo "Creating test product...<br>";
        $testProduct = [
            'name' => 'Test Product for Checkout',
            'description' => 'Test product for checkout button testing',
            'price' => 25000,
            'stock' => 100,
            'status' => 'active',
            'category_id' => 1
        ];
        
        $productId = insertData('products', $testProduct);
        if ($productId) {
            addToCart($productId, 2);
            echo "✅ Test product created and added to cart<br>";
        }
    }
}

echo "<h2>2. JavaScript Error Analysis</h2>";
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔍 Common JavaScript Errors in Checkout:</h4>";
echo "<ul>";
echo "<li><strong>Promise Channel Error:</strong> Usually caused by browser extensions or async listeners</li>";
echo "<li><strong>Form Submission Prevention:</strong> JavaScript preventing normal form submission</li>";
echo "<li><strong>Event Listener Conflicts:</strong> Multiple listeners interfering with each other</li>";
echo "<li><strong>Fetch Request Issues:</strong> AJAX calls failing or timing out</li>";
echo "</ul>";
echo "</div>";

echo "<h2>3. Test Checkout Form with Fixed JavaScript</h2>";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout Button Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .test-form-container {
            max-width: 700px;
            margin: 30px auto;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 2px solid #007bff;
        }
        .btn-test-submit {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            font-weight: bold;
            border-radius: 10px;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        .btn-test-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40,167,69,0.3);
        }
        .btn-test-submit:disabled {
            opacity: 0.7;
            transform: none;
        }
        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .error-display {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
        }
        .success-display {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-form-container">
            <h3 class="text-center mb-4">🧪 Fixed Checkout Form Test</h3>
            
            <div id="errorDisplay" class="error-display"></div>
            <div id="successDisplay" class="success-display"></div>
            
            <form id="testCheckoutForm" action="checkout.php" method="POST">
                <input type="hidden" name="place_order" value="1">
                
                <div class="form-section">
                    <h5>معلومات العميل</h5>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم العميل *</label>
                            <input type="text" class="form-control" name="customer_name" 
                                   value="Button Fix Test Customer" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف *</label>
                            <input type="text" class="form-control" name="customer_phone" 
                                   value="07123456789" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">العنوان *</label>
                        <textarea class="form-control" name="address" rows="2" required>Button Fix Test Address, Baghdad, Iraq</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المحافظة *</label>
                            <select class="form-control" name="province" required>
                                <option value="">اختر المحافظة</option>
                                <option value="بغداد" selected>بغداد</option>
                                <option value="البصرة">البصرة</option>
                                <option value="نينوى">نينوى</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">طريقة الدفع</label>
                            <select class="form-control" name="payment_method">
                                <option value="cash_on_delivery" selected>الدفع عند الاستلام</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" name="notes" rows="2">JavaScript fix test - should submit without errors and redirect properly</textarea>
                    </div>
                </div>
                
                <button type="submit" id="testSubmitBtn" class="btn-test-submit">
                    <span class="btn-text">🚀 تأكيد الطلب - Fixed Version</span>
                    <span class="btn-loading" style="display: none;">
                        <i class="bi bi-hourglass-split"></i> جاري المعالجة...
                    </span>
                </button>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Fixed JavaScript for checkout form
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('testCheckoutForm');
            const submitBtn = document.getElementById('testSubmitBtn');
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');
            const errorDisplay = document.getElementById('errorDisplay');
            const successDisplay = document.getElementById('successDisplay');
            
            console.log('Checkout form JavaScript initialized');
            
            // Function to show messages
            function showMessage(message, type) {
                if (type === 'error') {
                    errorDisplay.textContent = message;
                    errorDisplay.style.display = 'block';
                    successDisplay.style.display = 'none';
                } else {
                    successDisplay.textContent = message;
                    successDisplay.style.display = 'block';
                    errorDisplay.style.display = 'none';
                }
            }
            
            // Form submission handler
            form.addEventListener('submit', function(e) {
                console.log('Form submission started');
                
                // Clear previous messages
                errorDisplay.style.display = 'none';
                successDisplay.style.display = 'none';
                
                // Validate required fields
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;
                let firstInvalidField = null;
                
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.classList.add('is-invalid');
                        isValid = false;
                        if (!firstInvalidField) {
                            firstInvalidField = field;
                        }
                    } else {
                        field.classList.remove('is-invalid');
                    }
                });
                
                if (!isValid) {
                    console.log('Form validation failed');
                    e.preventDefault();
                    
                    showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                    
                    if (firstInvalidField) {
                        firstInvalidField.focus();
                        firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                    
                    return false;
                }
                
                console.log('Form validation passed, showing loading state');
                
                // Show loading state
                submitBtn.disabled = true;
                btnText.style.display = 'none';
                btnLoading.style.display = 'inline-block';
                
                showMessage('جاري معالجة طلبك... سيتم توجيهك إلى صفحة التأكيد', 'success');
                
                // Add visual feedback
                form.style.opacity = '0.8';
                form.style.pointerEvents = 'none';
                
                console.log('Form will be submitted to server');
                
                // Allow normal form submission - don't prevent default
                return true;
            });
            
            // Handle any unhandled promise rejections
            window.addEventListener('unhandledrejection', function(event) {
                console.warn('Unhandled promise rejection:', event.reason);
                // Prevent the default browser behavior
                event.preventDefault();
            });
            
            // Handle any JavaScript errors
            window.addEventListener('error', function(event) {
                console.error('JavaScript error:', event.error);
            });
            
            console.log('All event listeners attached successfully');
        });
    </script>
</body>
</html>

<?php
echo "<h2>4. Testing Instructions</h2>";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>";
echo "<h4>🧪 How to Test:</h4>";
echo "<ol>";
echo "<li><strong>Submit the form above</strong> - It uses fixed JavaScript</li>";
echo "<li><strong>Check browser console</strong> - Should show no errors</li>";
echo "<li><strong>Verify redirect</strong> - Should go to order-success.php</li>";
echo "<li><strong>Check admin panel</strong> - Order should appear</li>";
echo "</ol>";
echo "</div>";

echo "<h2>5. JavaScript Fixes Applied</h2>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
echo "<h4>✅ Fixes Applied:</h4>";
echo "<ul>";
echo "<li><strong>Removed preventDefault() for valid forms</strong> - Allows normal submission</li>";
echo "<li><strong>Added proper validation flow</strong> - Only prevents submission on errors</li>";
echo "<li><strong>Added console logging</strong> - For debugging purposes</li>";
echo "<li><strong>Added error handlers</strong> - For unhandled promises and errors</li>";
echo "<li><strong>Simplified event handling</strong> - Reduced complexity</li>";
echo "</ul>";
echo "</div>";

echo "<h2>6. Additional Links</h2>";
echo "<ul>";
echo "<li><a href='checkout.php' target='_blank'>Test Original Checkout Page</a></li>";
echo "<li><a href='admin/orders.php' target='_blank'>Check Admin Orders</a></li>";
echo "<li><a href='products.php' target='_blank'>Add More Products to Cart</a></li>";
echo "</ul>";

echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
