<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'اختبار وإصلاح وظيفة الحذف';

echo "<h2>اختبار وإصلاح وظيفة الحذف</h2>";

// Create a test product for deletion testing
if (isset($_POST['create_test'])) {
    $testProduct = [
        'name' => 'منتج اختبار الحذف ' . date('H:i:s'),
        'description' => 'منتج مؤقت لاختبار وظيفة الحذف',
        'price' => 1.00,
        'stock' => 1,
        'status' => 'inactive'
    ];
    
    $testId = insertData('products', $testProduct);
    if ($testId) {
        echo "<div class='alert alert-success'>تم إنشاء منتج اختبار (ID: $testId)</div>";
    } else {
        echo "<div class='alert alert-danger'>فشل في إنشاء منتج اختبار</div>";
    }
}

// Handle delete test
if (isset($_GET['delete_test']) && is_numeric($_GET['delete_test'])) {
    $productId = (int)$_GET['delete_test'];
    
    echo "<h3>اختبار حذف المنتج ID: $productId</h3>";
    
    // Get product info
    $product = fetchOne("SELECT * FROM products WHERE id = ?", [$productId]);
    
    if ($product) {
        echo "<div class='alert alert-info'>";
        echo "<strong>معلومات المنتج:</strong><br>";
        echo "الاسم: " . htmlspecialchars($product['name']) . "<br>";
        echo "الحالة: " . $product['status'] . "<br>";
        echo "تاريخ الإنشاء: " . $product['created_at'];
        echo "</div>";
        
        if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
            echo "<h4>تنفيذ عملية الحذف...</h4>";
            
            // Step by step deletion process
            echo "<ol>";
            
            // Step 1: Check for related images
            echo "<li><strong>فحص الصور المرتبطة:</strong> ";
            $images = fetchAll("SELECT image_path FROM product_images WHERE product_id = ?", [$productId]);
            echo count($images) . " صور موجودة</li>";
            
            // Step 2: Delete related images
            echo "<li><strong>حذف الصور المرتبطة:</strong> ";
            $deleteImages = deleteData('product_images', 'product_id = ?', [$productId]);
            echo "النتيجة: " . var_export($deleteImages, true) . "</li>";
            
            // Step 3: Delete the product
            echo "<li><strong>حذف المنتج:</strong> ";
            $deleteProduct = deleteData('products', 'id = ?', [$productId]);
            echo "النتيجة: " . var_export($deleteProduct, true) . "</li>";
            
            // Step 4: Verify deletion
            echo "<li><strong>التحقق من الحذف:</strong> ";
            $checkProduct = fetchOne("SELECT id FROM products WHERE id = ?", [$productId]);
            if (!$checkProduct) {
                echo "<span style='color: green;'>✅ المنتج محذوف بنجاح</span>";
            } else {
                echo "<span style='color: red;'>❌ المنتج ما زال موجود</span>";
            }
            echo "</li>";
            
            echo "</ol>";
            
            if ($deleteProduct !== false && $deleteProduct > 0) {
                echo "<div class='alert alert-success'>";
                echo "<h5>✅ تم حذف المنتج بنجاح!</h5>";
                echo "<p>المنتج '" . htmlspecialchars($product['name']) . "' تم حذفه من قاعدة البيانات.</p>";
                echo "</div>";
            } else {
                echo "<div class='alert alert-danger'>";
                echo "<h5>❌ فشل في حذف المنتج</h5>";
                echo "<p>لم يتم حذف أي صف من قاعدة البيانات.</p>";
                echo "</div>";
            }
            
        } else {
            echo "<div class='alert alert-warning'>";
            echo "<h5>تأكيد الحذف</h5>";
            echo "<p>هل أنت متأكد من حذف هذا المنتج؟</p>";
            echo "<div class='btn-group'>";
            echo "<a href='?delete_test=$productId&confirm=yes' class='btn btn-danger'>نعم، احذف المنتج</a>";
            echo "<a href='?delete_test=$productId' class='btn btn-secondary'>إلغاء</a>";
            echo "</div>";
            echo "</div>";
        }
        
    } else {
        echo "<div class='alert alert-danger'>المنتج غير موجود</div>";
    }
}

// Show available products for testing
echo "<h3>المنتجات المتاحة للاختبار:</h3>";

$products = fetchAll("SELECT id, name, status, created_at FROM products ORDER BY id DESC LIMIT 10");

if (!empty($products)) {
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped'>";
    echo "<thead>";
    echo "<tr><th>ID</th><th>الاسم</th><th>الحالة</th><th>تاريخ الإنشاء</th><th>إجراءات</th></tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($products as $prod) {
        echo "<tr>";
        echo "<td>" . $prod['id'] . "</td>";
        echo "<td>" . htmlspecialchars($prod['name']) . "</td>";
        echo "<td>" . $prod['status'] . "</td>";
        echo "<td>" . $prod['created_at'] . "</td>";
        echo "<td>";
        echo "<a href='?delete_test=" . $prod['id'] . "' class='btn btn-sm btn-outline-danger'>اختبار الحذف</a>";
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
} else {
    echo "<div class='alert alert-info'>لا توجد منتجات</div>";
}

// Create test product form
echo "<hr>";
echo "<h3>إنشاء منتج اختبار:</h3>";
echo "<form method='POST'>";
echo "<button type='submit' name='create_test' class='btn btn-primary'>إنشاء منتج اختبار</button>";
echo "</form>";

echo "<hr>";
echo "<h3>اختبار وظيفة الحذف في products.php:</h3>";
echo "<div class='alert alert-info'>";
echo "<p>لاختبار وظيفة الحذف في الصفحة الفعلية:</p>";
echo "<ol>";
echo "<li>انتقل إلى <a href='products.php' target='_blank'>صفحة إدارة المنتجات</a></li>";
echo "<li>انقر على زر الحذف لأي منتج</li>";
echo "<li>تأكد من ظهور نافذة التأكيد</li>";
echo "<li>انقر على 'حذف المنتج' في النافذة</li>";
echo "<li>تحقق من ظهور رسالة النجاح وعدم ظهور المنتج في القائمة</li>";
echo "</ol>";
echo "</div>";

echo "<div class='mt-3'>";
echo "<a href='products.php' class='btn btn-primary'>العودة إلى إدارة المنتجات</a> ";
echo "<a href='debug_delete_issue.php' class='btn btn-info'>تشغيل التشخيص الشامل</a>";
echo "</div>";
?>
