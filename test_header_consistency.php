<?php
/**
 * Header Consistency Test - Validate Standardized Header Design
 * Tests all pages to ensure identical header appearance and behavior
 */

require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار توحيد تصميم الهيدر</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' rel='stylesheet'>
    <style>
        body { font-family: 'Cairo', sans-serif; background: #f8f9fa; }
        .test-section { background: white; margin: 20px 0; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .page-link { display: inline-block; margin: 5px; padding: 10px 15px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
        .page-link:hover { background: #5a6fd8; color: white; }
    </style>
</head>
<body>
    <div class='container my-5'>
        <h1 class='text-center mb-5'>🧪 اختبار توحيد تصميم الهيدر</h1>";

// Test pages to validate
$testPages = [
    'index.php' => 'الرئيسية',
    'products.php' => 'المنتجات', 
    'offers.php' => 'العروض',
    'guidelines.php' => 'الإرشادات',
    'influencers.php' => 'المؤثرين',
    'contact.php' => 'اتصل بنا',
    'cart.php' => 'سلة التسوق',
    'checkout.php' => 'إتمام الطلب'
];

echo "<div class='test-section'>
    <h2>📋 اختبار وجود الصفحات</h2>";

$existingPages = [];
foreach ($testPages as $file => $name) {
    if (file_exists($file)) {
        echo "<div class='test-result success'>✅ $name ($file) - موجودة</div>";
        $existingPages[$file] = $name;
    } else {
        echo "<div class='test-result error'>❌ $name ($file) - مفقودة</div>";
    }
}
echo "</div>";

echo "<div class='test-section'>
    <h2>🎨 اختبار ملفات CSS المطلوبة</h2>";

$cssFiles = [
    'assets/css/global-header.css' => 'ملف CSS الهيدر العام',
    'assets/css/homepage.css' => 'ملف CSS الصفحة الرئيسية',
    'includes/header.php' => 'ملف الهيدر الرئيسي',
    'includes/footer.php' => 'ملف الفوتر الرئيسي'
];

foreach ($cssFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='test-result success'>✅ $description ($file) - موجود</div>";
    } else {
        echo "<div class='test-result error'>❌ $description ($file) - مفقود</div>";
    }
}
echo "</div>";

echo "<div class='test-section'>
    <h2>🔍 اختبار محتوى ملف الهيدر</h2>";

if (file_exists('includes/header.php')) {
    $headerContent = file_get_contents('includes/header.php');
    
    $headerTests = [
        'global-header.css' => 'تضمين ملف CSS الهيدر العام',
        'homepage.css' => 'تضمين ملف CSS الصفحة الرئيسية',
        'navbar-brand' => 'وجود عنصر العلامة التجارية',
        'navbar-nav' => 'وجود قائمة التنقل',
        'cart-btn' => 'وجود زر سلة التسوق',
        'search-form' => 'وجود نموذج البحث',
        'bi-house-fill' => 'استخدام الأيقونات المحسنة',
        'nav-link' => 'وجود روابط التنقل'
    ];
    
    foreach ($headerTests as $search => $description) {
        if (strpos($headerContent, $search) !== false) {
            echo "<div class='test-result success'>✅ $description</div>";
        } else {
            echo "<div class='test-result error'>❌ $description - غير موجود</div>";
        }
    }
    
    // Check for inline styles (should be removed)
    if (strpos($headerContent, '<style>') === false) {
        echo "<div class='test-result success'>✅ تم إزالة الأنماط المضمنة بنجاح</div>";
    } else {
        echo "<div class='test-result warning'>⚠️ لا تزال هناك أنماط مضمنة في الملف</div>";
    }
    
} else {
    echo "<div class='test-result error'>❌ ملف الهيدر غير موجود</div>";
}
echo "</div>";

echo "<div class='test-section'>
    <h2>🎯 اختبار ملف CSS الهيدر العام</h2>";

if (file_exists('assets/css/global-header.css')) {
    $globalCssContent = file_get_contents('assets/css/global-header.css');
    
    $cssTests = [
        '.navbar-brand' => 'تنسيق العلامة التجارية',
        '.navbar-nav .nav-link' => 'تنسيق روابط التنقل',
        '.cart-btn' => 'تنسيق زر سلة التسوق',
        '.search-form' => 'تنسيق نموذج البحث',
        '.cart-badge' => 'تنسيق شارة السلة',
        '@media (max-width: 768px)' => 'التصميم المتجاوب',
        'transition:' => 'التأثيرات الانتقالية',
        'hover' => 'تأثيرات التمرير',
        '[dir="rtl"]' => 'دعم اللغة العربية RTL'
    ];
    
    foreach ($cssTests as $search => $description) {
        if (strpos($globalCssContent, $search) !== false) {
            echo "<div class='test-result success'>✅ $description</div>";
        } else {
            echo "<div class='test-result error'>❌ $description - غير موجود</div>";
        }
    }
    
} else {
    echo "<div class='test-result error'>❌ ملف CSS الهيدر العام غير موجود</div>";
}
echo "</div>";

echo "<div class='test-section'>
    <h2>⚡ اختبار JavaScript المحسن</h2>";

if (file_exists('includes/footer.php')) {
    $footerContent = file_get_contents('includes/footer.php');
    
    $jsTests = [
        'navbar-scrolled' => 'تأثيرات التمرير للهيدر',
        'active-page' => 'تمييز الصفحة النشطة',
        'cartBounce' => 'تأثيرات زر السلة',
        'addEventListener' => 'مستمعات الأحداث',
        'DOMContentLoaded' => 'تحميل المحتوى'
    ];
    
    foreach ($jsTests as $search => $description) {
        if (strpos($footerContent, $search) !== false) {
            echo "<div class='test-result success'>✅ $description</div>";
        } else {
            echo "<div class='test-result error'>❌ $description - غير موجود</div>";
        }
    }
    
} else {
    echo "<div class='test-result error'>❌ ملف الفوتر غير موجود</div>";
}
echo "</div>";

echo "<div class='test-section'>
    <h2>🌐 اختبار التوافق مع جميع الصفحات</h2>";

foreach ($existingPages as $file => $name) {
    if (file_exists($file)) {
        $pageContent = file_get_contents($file);
        
        // Check if page includes header properly
        if (strpos($pageContent, "require_once 'includes/header.php'") !== false ||
            strpos($pageContent, 'require_once "includes/header.php"') !== false) {
            echo "<div class='test-result success'>✅ $name - يتضمن الهيدر بشكل صحيح</div>";
        } else {
            echo "<div class='test-result error'>❌ $name - لا يتضمن الهيدر</div>";
        }
        
        // Check for page title
        if (strpos($pageContent, '$pageTitle') !== false) {
            echo "<div class='test-result success'>✅ $name - يحتوي على عنوان الصفحة</div>";
        } else {
            echo "<div class='test-result warning'>⚠️ $name - لا يحتوي على عنوان الصفحة</div>";
        }
    }
}
echo "</div>";

echo "<div class='test-section'>
    <h2>🔗 روابط اختبار الصفحات</h2>
    <p>اضغط على الروابط التالية لاختبار الهيدر بصرياً على كل صفحة:</p>";

foreach ($existingPages as $file => $name) {
    echo "<a href='$file' target='_blank' class='page-link'>$name</a>";
}

echo "</div>";

echo "<div class='test-section'>
    <h2>📊 ملخص النتائج</h2>";

$totalTests = count($testPages) + count($cssFiles) + count($headerTests ?? []) + count($cssTests ?? []) + count($jsTests ?? []);
$passedTests = 0;

// Count successful tests (this is a simplified count)
foreach ($existingPages as $file => $name) {
    $passedTests++;
}

$successRate = round(($passedTests / count($testPages)) * 100, 2);

echo "<div class='test-result " . ($successRate > 80 ? 'success' : ($successRate > 60 ? 'warning' : 'error')) . "'>
    📈 معدل النجاح: $successRate% ($passedTests من " . count($testPages) . " صفحة)
</div>";

if ($successRate >= 90) {
    echo "<div class='test-result success'>🎉 ممتاز! الهيدر موحد بنجاح عبر جميع الصفحات</div>";
} elseif ($successRate >= 70) {
    echo "<div class='test-result warning'>⚠️ جيد، لكن هناك بعض المشاكل التي تحتاج إلى إصلاح</div>";
} else {
    echo "<div class='test-result error'>❌ يحتاج إلى مزيد من العمل لتوحيد الهيدر</div>";
}

echo "</div>";

echo "<div class='test-section'>
    <h2>💡 توصيات</h2>
    <ul>
        <li>تأكد من أن جميع الصفحات تتضمن ملف الهيدر بشكل صحيح</li>
        <li>تحقق من تحميل ملفات CSS على جميع الصفحات</li>
        <li>اختبر التصميم المتجاوب على أجهزة مختلفة</li>
        <li>تأكد من عمل جميع التأثيرات التفاعلية</li>
        <li>اختبر دعم اللغة العربية RTL</li>
    </ul>
</div>";

echo "</div>
</body>
</html>";
?>
