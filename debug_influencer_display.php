<?php
require_once 'config/config.php';

echo "=== تشخيص مشكلة عرض المؤثرين في لوحة التحكم ===\n";

try {
    echo "\n=== 1. فحص البيانات في قاعدة البيانات ===\n";
    
    // إحصائيات أساسية
    $stats = [
        'total' => fetchOne("SELECT COUNT(*) as count FROM influencers_content")['count'] ?? 0,
        'published' => fetchOne("SELECT COUNT(*) as count FROM influencers_content WHERE status = 'published'")['count'] ?? 0,
        'draft' => fetchOne("SELECT COUNT(*) as count FROM influencers_content WHERE status = 'draft'")['count'] ?? 0,
        'featured' => fetchOne("SELECT COUNT(*) as count FROM influencers_content WHERE is_featured = 1")['count'] ?? 0
    ];
    
    echo "الإحصائيات:\n";
    echo "- إجمالي المحتوى: " . $stats['total'] . "\n";
    echo "- منشور: " . $stats['published'] . "\n";
    echo "- مسودة: " . $stats['draft'] . "\n";
    echo "- مميز: " . $stats['featured'] . "\n";
    
    if ($stats['total'] == 0) {
        echo "⚠ لا توجد بيانات في قاعدة البيانات\n";
        exit;
    }
    
    echo "\n=== 2. فحص البيانات الخام ===\n";
    $rawData = fetchAll("SELECT * FROM influencers_content ORDER BY created_at DESC");
    
    echo "البيانات الموجودة:\n";
    foreach ($rawData as $index => $row) {
        echo "السجل " . ($index + 1) . ":\n";
        echo "  - ID: " . $row['id'] . "\n";
        echo "  - اسم المؤثر: " . $row['influencer_name'] . "\n";
        echo "  - نوع المحتوى: " . $row['content_type'] . "\n";
        echo "  - الحالة: " . $row['status'] . "\n";
        echo "  - مميز: " . ($row['is_featured'] ? 'نعم' : 'لا') . "\n";
        echo "  - تاريخ الإنشاء: " . $row['created_at'] . "\n";
        echo "  - نص المحتوى: " . mb_substr($row['content_text'], 0, 50) . "...\n";
        echo "\n";
    }
    
    echo "\n=== 3. محاكاة استعلام لوحة التحكم ===\n";
    
    // محاكاة معاملات البحث (نفس المنطق في admin/influencers.php)
    $search = '';
    $status = '';
    $contentType = '';
    $category = 0;
    
    // بناء استعلام البحث
    $whereConditions = ["1=1"];
    $params = [];
    
    if (!empty($search)) {
        $whereConditions[] = "(ic.influencer_name LIKE ? OR ic.content_title LIKE ?)";
        $searchTerm = "%{$search}%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    if (!empty($status)) {
        $whereConditions[] = "ic.status = ?";
        $params[] = $status;
    }
    
    if (!empty($contentType)) {
        $whereConditions[] = "ic.content_type = ?";
        $params[] = $contentType;
    }
    
    if ($category > 0) {
        $whereConditions[] = "ic.category_id = ?";
        $params[] = $category;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    echo "WHERE clause: {$whereClause}\n";
    echo "Parameters: " . json_encode($params, JSON_UNESCAPED_UNICODE) . "\n";
    
    // التصفح
    $page = 1;
    $perPage = 20;
    $offset = ($page - 1) * $perPage;
    
    // عدد المحتويات الإجمالي
    $countSql = "SELECT COUNT(*) as total FROM influencers_content ic WHERE {$whereClause}";
    echo "\nCount SQL: {$countSql}\n";
    
    $totalResult = fetchOne($countSql, $params);
    $totalContent = $totalResult['total'] ?? 0;
    $totalPages = ceil($totalContent / $perPage);
    
    echo "إجمالي المحتوى المطابق للفلتر: {$totalContent}\n";
    echo "إجمالي الصفحات: {$totalPages}\n";
    
    // جلب المحتوى (نفس الاستعلام في admin/influencers.php)
    $sql = "
        SELECT ic.*, cc.name_ar as category_name, p.name as product_name,
               COALESCE(cv.view_count, 0) as total_views
        FROM influencers_content ic
        LEFT JOIN content_categories cc ON ic.category_id = cc.id
        LEFT JOIN products p ON ic.product_id = p.id
        LEFT JOIN (
            SELECT content_id, COUNT(*) as view_count 
            FROM content_views 
            WHERE content_type = 'influencer' 
            GROUP BY content_id
        ) cv ON ic.id = cv.content_id
        WHERE {$whereClause}
        ORDER BY ic.sort_order ASC, ic.created_at DESC
        LIMIT {$perPage} OFFSET {$offset}
    ";
    
    echo "\nMain SQL:\n{$sql}\n";
    
    try {
        $contents = fetchAll($sql, $params);
        echo "\nعدد النتائج المسترجعة: " . count($contents) . "\n";
        
        if (empty($contents)) {
            echo "⚠ الاستعلام لم يرجع أي نتائج!\n";
            
            // اختبار استعلام مبسط
            echo "\n=== 4. اختبار استعلام مبسط ===\n";
            $simpleQuery = "SELECT * FROM influencers_content ORDER BY created_at DESC LIMIT 5";
            $simpleResults = fetchAll($simpleQuery);
            
            echo "نتائج الاستعلام المبسط: " . count($simpleResults) . "\n";
            if (!empty($simpleResults)) {
                foreach ($simpleResults as $result) {
                    echo "- " . $result['influencer_name'] . " (" . $result['content_type'] . ")\n";
                }
            }
            
            // فحص الجداول المرتبطة
            echo "\n=== 5. فحص الجداول المرتبطة ===\n";
            
            // فحص content_categories
            try {
                $categoriesCount = $pdo->query("SELECT COUNT(*) FROM content_categories")->fetchColumn();
                echo "جدول content_categories: {$categoriesCount} سجل\n";
            } catch (Exception $e) {
                echo "مشكلة في جدول content_categories: " . $e->getMessage() . "\n";
            }
            
            // فحص products
            try {
                $productsCount = $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn();
                echo "جدول products: {$productsCount} سجل\n";
            } catch (Exception $e) {
                echo "مشكلة في جدول products: " . $e->getMessage() . "\n";
            }
            
            // فحص content_views
            try {
                $viewsCount = $pdo->query("SELECT COUNT(*) FROM content_views")->fetchColumn();
                echo "جدول content_views: {$viewsCount} سجل\n";
            } catch (Exception $e) {
                echo "جدول content_views غير موجود أو به مشكلة: " . $e->getMessage() . "\n";
                echo "هذا قد يكون سبب المشكلة!\n";
            }
            
        } else {
            echo "\n=== 4. تفاصيل النتائج المسترجعة ===\n";
            foreach ($contents as $index => $content) {
                echo "النتيجة " . ($index + 1) . ":\n";
                echo "  - ID: " . $content['id'] . "\n";
                echo "  - اسم المؤثر: " . $content['influencer_name'] . "\n";
                echo "  - عنوان المحتوى: " . ($content['content_title'] ?? 'بدون عنوان') . "\n";
                echo "  - نوع المحتوى: " . $content['content_type'] . "\n";
                echo "  - الحالة: " . $content['status'] . "\n";
                echo "  - التصنيف: " . ($content['category_name'] ?? 'بدون تصنيف') . "\n";
                echo "  - المنتج: " . ($content['product_name'] ?? 'بدون منتج') . "\n";
                echo "  - المشاهدات: " . $content['total_views'] . "\n";
                echo "  - مميز: " . ($content['is_featured'] ? 'نعم' : 'لا') . "\n";
                echo "\n";
            }
        }
        
    } catch (Exception $e) {
        echo "✗ خطأ في تنفيذ الاستعلام الرئيسي: " . $e->getMessage() . "\n";
        echo "كود الخطأ: " . $e->getCode() . "\n";
    }
    
    echo "\n=== 6. اختبار إنشاء جدول content_views ===\n";
    
    // محاولة إنشاء جدول content_views إذا لم يكن موجوداً
    try {
        $createViewsTable = "
        CREATE TABLE IF NOT EXISTS content_views (
            id INT AUTO_INCREMENT PRIMARY KEY,
            content_id INT NOT NULL,
            content_type ENUM('guideline', 'influencer') NOT NULL,
            user_ip VARCHAR(45),
            user_agent TEXT,
            viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            INDEX idx_content (content_id, content_type),
            INDEX idx_viewed_at (viewed_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createViewsTable);
        echo "✅ تم إنشاء/التحقق من جدول content_views\n";
        
        // إعادة اختبار الاستعلام
        echo "\n=== 7. إعادة اختبار الاستعلام بعد إنشاء الجدول ===\n";
        $contents = fetchAll($sql, $params);
        echo "عدد النتائج بعد إنشاء الجدول: " . count($contents) . "\n";
        
        if (!empty($contents)) {
            echo "✅ الاستعلام يعمل الآن!\n";
            foreach ($contents as $content) {
                echo "- " . $content['influencer_name'] . " (" . $content['content_type'] . ")\n";
            }
        }
        
    } catch (Exception $e) {
        echo "✗ خطأ في إنشاء جدول content_views: " . $e->getMessage() . "\n";
    }

} catch (Exception $e) {
    echo "✗ خطأ عام: " . $e->getMessage() . "\n";
}

echo "\n=== انتهاء التشخيص ===\n";
?>
