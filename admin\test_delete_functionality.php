<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'اختبار وظيفة الحذف';

echo "<h2>اختبار وظيفة حذف المنتجات</h2>";

// Test 1: Create a test product for deletion
echo "<h3>1. إنشاء منتج تجريبي للحذف</h3>";

$testProduct = [
    'name' => 'منتج اختبار الحذف ' . date('Y-m-d H:i:s'),
    'description' => 'منتج مؤقت لاختبار وظيفة الحذف',
    'price' => 1.00,
    'stock' => 1,
    'status' => 'inactive'
];

try {
    $testId = insertData('products', $testProduct);
    if ($testId) {
        echo "<p style='color: green;'>✅ تم إنشاء منتج تجريبي بنجاح (ID: $testId)</p>";
        
        // Test 2: Verify the product exists
        echo "<h3>2. التحقق من وجود المنتج</h3>";
        $createdProduct = fetchOne("SELECT * FROM products WHERE id = ?", [$testId]);
        if ($createdProduct) {
            echo "<p style='color: green;'>✅ المنتج موجود في قاعدة البيانات</p>";
            echo "<p><strong>اسم المنتج:</strong> " . htmlspecialchars($createdProduct['name']) . "</p>";
            echo "<p><strong>الحالة:</strong> " . $createdProduct['status'] . "</p>";
            
            // Test 3: Test the delete function directly
            echo "<h3>3. اختبار دالة الحذف مباشرة</h3>";
            
            echo "<h4>أ. اختبار deleteData function:</h4>";
            $deleteResult = deleteData('products', 'id = ?', [$testId]);
            
            echo "<p><strong>نتيجة الحذف:</strong> " . var_export($deleteResult, true) . "</p>";
            
            if ($deleteResult !== false && $deleteResult > 0) {
                echo "<p style='color: green;'>✅ دالة الحذف تعمل بشكل صحيح (صفوف محذوفة: $deleteResult)</p>";
                
                // Verify deletion
                $deletedProduct = fetchOne("SELECT id FROM products WHERE id = ?", [$testId]);
                if (!$deletedProduct) {
                    echo "<p style='color: green;'>✅ تم التحقق من الحذف - المنتج لم يعد موجود</p>";
                } else {
                    echo "<p style='color: red;'>❌ المنتج ما زال موجود بعد الحذف!</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ دالة الحذف لا تعمل (النتيجة: " . var_export($deleteResult, true) . ")</p>";
                
                // Try manual deletion for comparison
                echo "<h4>ب. اختبار الحذف اليدوي:</h4>";
                try {
                    global $pdo;
                    $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
                    $manualResult = $stmt->execute([$testId]);
                    $manualRowCount = $stmt->rowCount();
                    
                    echo "<p><strong>الحذف اليدوي:</strong> " . ($manualResult ? 'نجح' : 'فشل') . "</p>";
                    echo "<p><strong>صفوف محذوفة:</strong> $manualRowCount</p>";
                    
                    if ($manualResult && $manualRowCount > 0) {
                        echo "<p style='color: green;'>✅ الحذف اليدوي نجح - المشكلة في دالة deleteData</p>";
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ خطأ في الحذف اليدوي: " . $e->getMessage() . "</p>";
                }
            }
            
        } else {
            echo "<p style='color: red;'>❌ المنتج غير موجود بعد الإنشاء!</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء منتج تجريبي</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في إنشاء المنتج: " . $e->getMessage() . "</p>";
}

// Test 4: Test the complete delete process like in products.php
echo "<h3>4. اختبار العملية الكاملة للحذف</h3>";

$testProduct2 = [
    'name' => 'منتج اختبار العملية الكاملة ' . date('H:i:s'),
    'description' => 'منتج لاختبار العملية الكاملة للحذف',
    'price' => 2.00,
    'stock' => 2,
    'status' => 'inactive'
];

try {
    $testId2 = insertData('products', $testProduct2);
    if ($testId2) {
        echo "<p>تم إنشاء منتج ثاني للاختبار (ID: $testId2)</p>";
        
        // Simulate the exact process from products.php
        $product = fetchOne("SELECT name FROM products WHERE id = ?", [$testId2]);
        
        if ($product) {
            echo "<p>تم العثور على المنتج: " . htmlspecialchars($product['name']) . "</p>";
            
            try {
                // حذف صور المنتج الإضافية (if any)
                $images = fetchAll("SELECT image_path FROM product_images WHERE product_id = ?", [$testId2]);
                echo "<p>صور إضافية موجودة: " . count($images) . "</p>";
                
                foreach ($images as $image) {
                    $imagePath = UPLOAD_PATH . '/products/' . $image['image_path'];
                    if (file_exists($imagePath)) {
                        unlink($imagePath);
                    }
                }
                
                // حذف المنتج من قاعدة البيانات
                $deleteImages = deleteData('product_images', 'product_id = ?', [$testId2]);
                $deleteProduct = deleteData('products', 'id = ?', [$testId2]);
                
                echo "<p><strong>نتيجة حذف الصور:</strong> " . var_export($deleteImages, true) . "</p>";
                echo "<p><strong>نتيجة حذف المنتج:</strong> " . var_export($deleteProduct, true) . "</p>";
                
                if ($deleteProduct !== false && $deleteProduct > 0) {
                    echo "<p style='color: green;'>✅ العملية الكاملة للحذف نجحت!</p>";
                    
                    // Verify
                    $verifyProduct = fetchOne("SELECT id FROM products WHERE id = ?", [$testId2]);
                    if (!$verifyProduct) {
                        echo "<p style='color: green;'>✅ تم التحقق - المنتج محذوف تماماً</p>";
                    } else {
                        echo "<p style='color: red;'>❌ المنتج ما زال موجود!</p>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ العملية الكاملة للحذف فشلت</p>";
                    echo "<p>تفاصيل الخطأ: لم يتم حذف أي صف</p>";
                }
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ خطأ في العملية الكاملة: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ لم يتم العثور على المنتج الثاني</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار العملية الكاملة: " . $e->getMessage() . "</p>";
}

// Test 5: Check database function implementation
echo "<h3>5. فحص تنفيذ دالة deleteData</h3>";

echo "<h4>معلومات دالة deleteData:</h4>";
if (function_exists('deleteData')) {
    echo "<p style='color: green;'>✅ دالة deleteData موجودة</p>";
    
    // Test with a simple query
    echo "<h4>اختبار بسيط:</h4>";
    try {
        // Create a simple test record
        $simpleTest = insertData('products', [
            'name' => 'اختبار بسيط',
            'description' => 'اختبار',
            'price' => 1,
            'stock' => 1,
            'status' => 'inactive'
        ]);
        
        if ($simpleTest) {
            echo "<p>تم إنشاء سجل بسيط (ID: $simpleTest)</p>";
            
            $simpleDelete = deleteData('products', 'id = ?', [$simpleTest]);
            echo "<p><strong>نتيجة الحذف البسيط:</strong> " . var_export($simpleDelete, true) . "</p>";
            
            if ($simpleDelete !== false && $simpleDelete > 0) {
                echo "<p style='color: green;'>✅ الحذف البسيط نجح</p>";
            } else {
                echo "<p style='color: red;'>❌ الحذف البسيط فشل</p>";
            }
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في الاختبار البسيط: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ دالة deleteData غير موجودة!</p>";
}

echo "<hr>";
echo "<h3>الخلاصة:</h3>";
echo "<p>إذا كانت جميع الاختبارات تنجح، فإن وظيفة الحذف تعمل بشكل صحيح.</p>";
echo "<p>إذا فشلت بعض الاختبارات، فهناك مشكلة في دالة deleteData أو في معاملات الاستدعاء.</p>";

echo "<hr>";
echo "<p><a href='products.php'>العودة إلى إدارة المنتجات</a></p>";
echo "<p><a href='test_fixed_functions.php'>تشغيل اختبار شامل لدوال قاعدة البيانات</a></p>";
?>
