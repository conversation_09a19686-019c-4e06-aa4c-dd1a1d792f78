<?php
require_once '../../config/config.php';
requireAdminLogin();

header('Content-Type: application/json');

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف الإرشاد غير صحيح']);
    exit();
}

$id = (int)$_GET['id'];
$mode = $_GET['mode'] ?? 'edit';

try {
    $guideline = fetchOne("SELECT * FROM guidelines WHERE id = ?", [$id]);
    
    if (!$guideline) {
        echo json_encode(['success' => false, 'message' => 'الإرشاد غير موجود']);
        exit();
    }
    
    if ($mode === 'preview') {
        // إنشاء HTML للمعاينة
        $html = generatePreviewHTML($guideline);
        echo json_encode([
            'success' => true,
            'html' => $html
        ]);
    } else {
        // إرجاع البيانات للتعديل
        echo json_encode([
            'success' => true,
            'guideline' => $guideline
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في جلب البيانات'
    ]);
}

function generatePreviewHTML($guideline) {
    $html = '<div class="guideline-preview">';
    
    // Header
    $html .= '<div class="d-flex justify-content-between align-items-start mb-4">';
    $html .= '<div>';
    $html .= '<h2>' . htmlspecialchars($guideline['title']) . '</h2>';
    if ($guideline['short_description']) {
        $html .= '<p class="text-muted lead">' . htmlspecialchars($guideline['short_description']) . '</p>';
    }
    $html .= '</div>';
    
    // Badges
    $html .= '<div class="text-end">';
    
    // Importance badge
    $importanceColors = ['critical' => 'danger', 'important' => 'warning', 'normal' => 'info'];
    $importanceNames = ['critical' => 'حرج', 'important' => 'مهم', 'normal' => 'عادي'];
    $html .= '<span class="badge bg-' . $importanceColors[$guideline['importance_level']] . ' me-2">';
    $html .= $importanceNames[$guideline['importance_level']] . '</span>';
    
    // Difficulty badge
    $difficultyColors = ['easy' => 'success', 'medium' => 'warning', 'hard' => 'danger'];
    $difficultyNames = ['easy' => 'سهل', 'medium' => 'متوسط', 'hard' => 'صعب'];
    $html .= '<span class="badge bg-' . $difficultyColors[$guideline['difficulty_level']] . '">';
    $html .= $difficultyNames[$guideline['difficulty_level']] . '</span>';
    
    $html .= '</div>';
    $html .= '</div>';
    
    // Content
    if ($guideline['content']) {
        $html .= '<div class="mb-4">';
        $html .= '<h5>المحتوى:</h5>';
        $html .= '<div class="border rounded p-3 bg-light">';
        $html .= nl2br(htmlspecialchars($guideline['content']));
        $html .= '</div>';
        $html .= '</div>';
    }
    
    // Steps
    if ($guideline['steps']) {
        $steps = json_decode($guideline['steps'], true);
        if (is_array($steps) && !empty($steps)) {
            $html .= '<div class="mb-4">';
            $html .= '<h5><i class="bi bi-list-ol text-primary"></i> الخطوات:</h5>';
            $html .= '<ol class="list-group list-group-numbered">';
            foreach ($steps as $step) {
                if (!empty(trim($step))) {
                    $html .= '<li class="list-group-item">' . htmlspecialchars($step) . '</li>';
                }
            }
            $html .= '</ol>';
            $html .= '</div>';
        }
    }
    
    // Tips
    if ($guideline['tips']) {
        $tips = json_decode($guideline['tips'], true);
        if (is_array($tips) && !empty($tips)) {
            $html .= '<div class="mb-4">';
            $html .= '<h5><i class="bi bi-lightbulb text-success"></i> النصائح المفيدة:</h5>';
            $html .= '<div class="row">';
            foreach ($tips as $tip) {
                if (!empty(trim($tip))) {
                    $html .= '<div class="col-md-6 mb-2">';
                    $html .= '<div class="alert alert-success d-flex align-items-center">';
                    $html .= '<i class="bi bi-lightbulb-fill me-2"></i>';
                    $html .= htmlspecialchars($tip);
                    $html .= '</div>';
                    $html .= '</div>';
                }
            }
            $html .= '</div>';
            $html .= '</div>';
        }
    }
    
    // Warnings
    if ($guideline['warnings']) {
        $warnings = json_decode($guideline['warnings'], true);
        if (is_array($warnings) && !empty($warnings)) {
            $html .= '<div class="mb-4">';
            $html .= '<h5><i class="bi bi-exclamation-triangle text-danger"></i> تحذيرات مهمة:</h5>';
            foreach ($warnings as $warning) {
                if (!empty(trim($warning))) {
                    $html .= '<div class="alert alert-danger d-flex align-items-center mb-2">';
                    $html .= '<i class="bi bi-exclamation-triangle-fill me-2"></i>';
                    $html .= htmlspecialchars($warning);
                    $html .= '</div>';
                }
            }
            $html .= '</div>';
        }
    }
    
    // Video
    if ($guideline['video_url']) {
        $html .= '<div class="mb-4">';
        $html .= '<h5><i class="bi bi-play-circle text-danger"></i> فيديو تعليمي:</h5>';
        $html .= '<div class="ratio ratio-16x9">';
        
        $videoUrl = $guideline['video_url'];
        $platform = $guideline['video_platform'] ?? 'youtube';
        
        if ($platform === 'youtube' && (strpos($videoUrl, 'youtube.com') !== false || strpos($videoUrl, 'youtu.be') !== false)) {
            // Extract YouTube video ID
            preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $videoUrl, $matches);
            if (isset($matches[1])) {
                $videoId = $matches[1];
                $html .= '<iframe src="https://www.youtube.com/embed/' . $videoId . '" allowfullscreen></iframe>';
            } else {
                $html .= '<p class="text-muted">رابط فيديو غير صحيح</p>';
            }
        } else {
            $html .= '<p class="text-muted">رابط الفيديو: <a href="' . htmlspecialchars($videoUrl) . '" target="_blank">' . htmlspecialchars($videoUrl) . '</a></p>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
    }
    
    // Meta information
    $html .= '<div class="border-top pt-3 mt-4">';
    $html .= '<div class="row text-muted small">';
    $html .= '<div class="col-md-6">';
    $html .= '<strong>مرحلة الاستخدام:</strong> ';
    $usageStages = [
        'general' => 'عام',
        'pre_purchase' => 'قبل الشراء',
        'post_purchase' => 'بعد الشراء',
        'maintenance' => 'الصيانة'
    ];
    $html .= $usageStages[$guideline['usage_stage']] ?? 'غير محدد';
    $html .= '</div>';
    $html .= '<div class="col-md-6 text-end">';
    $html .= '<strong>تاريخ الإنشاء:</strong> ' . date('Y/m/d H:i', strtotime($guideline['created_at']));
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</div>';
    
    $html .= '</div>';
    
    return $html;
}
?>
