<?php
/**
 * Test script to verify formatPrice() function fixes
 * Tests various input formats including string values with commas
 */

require_once 'config/config.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار إصلاح دالة formatPrice</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f8f9fa; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".test-case { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }";
echo ".success { border-left-color: #28a745; background: #d4edda; }";
echo ".error { border-left-color: #dc3545; background: #f8d7da; }";
echo ".warning { border-left-color: #ffc107; background: #fff3cd; }";
echo "h1, h2 { color: #333; }";
echo "code { background: #e9ecef; padding: 2px 4px; border-radius: 3px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🧪 اختبار إصلاح دالة formatPrice()</h1>";
echo "<p>هذا الاختبار يتحقق من أن دالة formatPrice() تعمل بشكل صحيح مع جميع أنواع المدخلات</p>";

// Test cases with various input formats
$testCases = [
    // Numeric inputs
    ['input' => 1000, 'description' => 'رقم صحيح عادي'],
    ['input' => 1500.75, 'description' => 'رقم عشري'],
    ['input' => 0, 'description' => 'صفر'],
    ['input' => 0.99, 'description' => 'رقم أقل من واحد'],
    
    // String inputs (the problematic ones)
    ['input' => '200,000', 'description' => 'نص يحتوي على فواصل (المشكلة الأصلية)'],
    ['input' => '1,500.75', 'description' => 'نص يحتوي على فواصل ونقطة عشرية'],
    ['input' => '200.00', 'description' => 'نص رقمي عادي'],
    ['input' => '25.00', 'description' => 'نص رقمي صغير'],
    
    // Edge cases
    ['input' => '200,000.99', 'description' => 'نص معقد بفواصل ونقطة عشرية'],
    ['input' => 'abc123', 'description' => 'نص يحتوي على أحرف (اختبار خطأ)'],
    ['input' => '', 'description' => 'نص فارغ'],
    ['input' => null, 'description' => 'قيمة null'],
    
    // Real-world scenarios from database
    ['input' => getSetting('free_delivery_threshold'), 'description' => 'قيمة من قاعدة البيانات (free_delivery_threshold)'],
    ['input' => getSetting('delivery_price'), 'description' => 'قيمة من قاعدة البيانات (delivery_price)'],
];

echo "<h2>📋 نتائج الاختبارات</h2>";

$passedTests = 0;
$totalTests = count($testCases);

foreach ($testCases as $index => $testCase) {
    $input = $testCase['input'];
    $description = $testCase['description'];
    
    echo "<div class='test-case'>";
    echo "<h3>اختبار " . ($index + 1) . ": $description</h3>";
    echo "<p><strong>المدخل:</strong> <code>" . var_export($input, true) . "</code></p>";
    
    try {
        $result = formatPrice($input);
        echo "<p><strong>النتيجة:</strong> <code>$result</code></p>";
        
        // Validate result format
        if (strpos($result, 'دينار عراقي') !== false && is_numeric(str_replace([',', ' دينار عراقي'], '', $result))) {
            echo "<p style='color: #28a745;'>✅ <strong>نجح:</strong> النتيجة بالتنسيق الصحيح</p>";
            $passedTests++;
            echo "</div>";
        } else {
            echo "<p style='color: #ffc107;'>⚠️ <strong>تحذير:</strong> النتيجة قد لا تكون بالتنسيق المتوقع</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: #dc3545;'>❌ <strong>خطأ:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
    } catch (Error $e) {
        echo "<p style='color: #dc3545;'>❌ <strong>خطأ فادح:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

echo "<h2>📊 ملخص النتائج</h2>";
$successRate = ($passedTests / $totalTests) * 100;

if ($successRate >= 90) {
    $statusClass = 'success';
    $statusIcon = '✅';
    $statusText = 'ممتاز';
} elseif ($successRate >= 70) {
    $statusClass = 'warning';
    $statusIcon = '⚠️';
    $statusText = 'جيد';
} else {
    $statusClass = 'error';
    $statusIcon = '❌';
    $statusText = 'يحتاج إصلاح';
}

echo "<div class='test-case $statusClass'>";
echo "<h3>$statusIcon النتيجة النهائية: $statusText</h3>";
echo "<p><strong>الاختبارات الناجحة:</strong> $passedTests من $totalTests</p>";
echo "<p><strong>معدل النجاح:</strong> " . number_format($successRate, 1) . "%</p>";
echo "</div>";

// Test the specific error scenario
echo "<h2>🔍 اختبار السيناريو المحدد</h2>";
echo "<div class='test-case'>";
echo "<h3>اختبار السيناريو الذي كان يسبب الخطأ الأصلي</h3>";
echo "<p>هذا يحاكي الاستدعاء من index.php السطر 84:</p>";
echo "<code>formatPrice(getSetting('free_delivery_threshold'))</code>";

try {
    $thresholdValue = getSetting('free_delivery_threshold');
    echo "<p><strong>قيمة free_delivery_threshold:</strong> <code>" . var_export($thresholdValue, true) . "</code></p>";
    echo "<p><strong>نوع البيانات:</strong> <code>" . gettype($thresholdValue) . "</code></p>";
    
    $formattedResult = formatPrice($thresholdValue);
    echo "<p><strong>النتيجة المنسقة:</strong> <code>$formattedResult</code></p>";
    echo "<p style='color: #28a745;'>✅ <strong>نجح:</strong> لا يوجد خطأ TypeError</p>";
    
} catch (Exception $e) {
    echo "<p style='color: #dc3545;'>❌ <strong>خطأ:</strong> " . $e->getMessage() . "</p>";
} catch (Error $e) {
    echo "<p style='color: #dc3545;'>❌ <strong>خطأ فادح:</strong> " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<h2>🔧 معلومات إضافية</h2>";
echo "<div class='test-case'>";
echo "<h3>معلومات النظام</h3>";
echo "<p><strong>إصدار PHP:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>المنطقة الزمنية:</strong> " . date_default_timezone_get() . "</p>";
echo "<p><strong>الترميز:</strong> " . mb_internal_encoding() . "</p>";

// Test database connection
try {
    $settingsCount = fetchOne("SELECT COUNT(*) as count FROM site_settings")['count'];
    echo "<p><strong>عدد الإعدادات في قاعدة البيانات:</strong> $settingsCount</p>";
    echo "<p style='color: #28a745;'>✅ الاتصال بقاعدة البيانات يعمل</p>";
} catch (Exception $e) {
    echo "<p style='color: #dc3545;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<div style='margin-top: 30px; padding: 20px; background: #e3f2fd; border-radius: 5px;'>";
echo "<h3>📝 ملاحظات مهمة</h3>";
echo "<ul>";
echo "<li>تم إصلاح دالة formatPrice() للتعامل مع النصوص التي تحتوي على فواصل</li>";
echo "<li>الدالة الآن تنظف المدخل من أي أحرف غير رقمية قبل المعالجة</li>";
echo "<li>تم إصلاح مشكلة TypeError التي كانت تحدث مع round() function</li>";
echo "<li>جميع الأسعار تُعرض الآن بالدينار العراقي بدون فواصل عشرية</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
