/**
 * Professional Cart Notification System
 * Modern Toast/Popup Design with Arabic RTL Support
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

/* ==========================================================================
   Cart Notification Container
   ========================================================================== */

.cart-notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    pointer-events: none;
    max-width: 400px;
    width: 100%;
}

[dir="rtl"] .cart-notification-container {
    right: auto;
    left: 20px;
}

/* ==========================================================================
   Professional Cart Toast Notification
   ========================================================================== */

.cart-toast {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(40, 167, 69, 0.2);
    border-left: 4px solid #28a745;
    padding: 0;
    margin-bottom: 1rem;
    pointer-events: auto;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
    position: relative;
}

[dir="rtl"] .cart-toast {
    border-left: none;
    border-right: 4px solid #28a745;
    transform: translateX(-100%);
}

.cart-toast.show {
    transform: translateX(0);
    opacity: 1;
}

.cart-toast.hide {
    transform: translateX(100%);
    opacity: 0;
}

[dir="rtl"] .cart-toast.hide {
    transform: translateX(-100%);
}

/* Success Animation Overlay */
.cart-toast::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(40, 167, 69, 0.1), transparent);
    transition: left 0.6s ease;
    z-index: 1;
}

.cart-toast.show::before {
    left: 100%;
}

/* ==========================================================================
   Toast Header
   ========================================================================== */

.cart-toast-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
    z-index: 2;
}

.cart-toast-icon {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.cart-toast-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
    flex-grow: 1;
}

.cart-toast-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    opacity: 0.8;
}

.cart-toast-close:hover {
    background: rgba(255, 255, 255, 0.2);
    opacity: 1;
}

/* ==========================================================================
   Toast Body
   ========================================================================== */

.cart-toast-body {
    padding: 1.5rem;
    position: relative;
    z-index: 2;
}

.cart-product-info {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.cart-product-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-product-image i {
    font-size: 1.5rem;
    color: #6c757d;
}

.cart-product-details {
    flex-grow: 1;
    min-width: 0;
}

.cart-product-name {
    font-size: 0.95rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cart-product-meta {
    font-size: 0.85rem;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cart-product-quantity {
    background: #e9ecef;
    color: #495057;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
}

.cart-product-price {
    color: #28a745;
    font-weight: 600;
}

/* ==========================================================================
   Toast Actions
   ========================================================================== */

.cart-toast-actions {
    display: flex;
    gap: 0.75rem;
}

.cart-action-btn {
    flex: 1;
    padding: 0.75rem 1rem;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    border: none;
    cursor: pointer;
}

.cart-action-btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.cart-action-btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.cart-action-btn-outline {
    background: transparent;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.cart-action-btn-outline:hover {
    background: #f8f9fa;
    color: #495057;
    border-color: #adb5bd;
}

/* ==========================================================================
   Progress Bar
   ========================================================================== */

.cart-toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 0 0 16px 16px;
    transform-origin: left;
    animation: progressBar 5s linear forwards;
}

@keyframes progressBar {
    from { width: 100%; }
    to { width: 0%; }
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .cart-notification-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    [dir="rtl"] .cart-notification-container {
        left: 10px;
        right: 10px;
    }
    
    .cart-toast-header {
        padding: 0.75rem 1rem;
    }
    
    .cart-toast-body {
        padding: 1rem;
    }
    
    .cart-product-info {
        margin-bottom: 1rem;
    }
    
    .cart-product-image {
        width: 50px;
        height: 50px;
    }
    
    .cart-toast-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .cart-action-btn {
        padding: 0.6rem 0.8rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 576px) {
    .cart-notification-container {
        top: 5px;
        right: 5px;
        left: 5px;
    }
    
    .cart-toast-header {
        padding: 0.6rem 0.8rem;
    }
    
    .cart-toast-title {
        font-size: 0.9rem;
    }
    
    .cart-toast-body {
        padding: 0.8rem;
    }
    
    .cart-product-name {
        font-size: 0.9rem;
    }
    
    .cart-product-meta {
        font-size: 0.8rem;
    }
}

/* ==========================================================================
   Accessibility & Performance
   ========================================================================== */

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .cart-toast,
    .cart-action-btn,
    .cart-toast::before {
        transition: none !important;
        animation: none !important;
    }
    
    .cart-toast-progress {
        animation: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .cart-toast {
        border: 2px solid #000;
    }
    
    .cart-action-btn-outline {
        border: 2px solid #000;
    }
}

/* Focus states for accessibility */
.cart-toast-close:focus,
.cart-action-btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}
