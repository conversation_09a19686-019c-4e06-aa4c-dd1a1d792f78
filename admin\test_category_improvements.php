<?php
/**
 * اختبار تحسينات إدارة التصنيفات
 * Category Management Improvements Test
 */

require_once '../config/config.php';
require_once '../config/database.php';

// التحقق من الجلسة
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$testResults = [];

// Test 1: Currency Formatting
try {
    $testPrice = 1500.75;
    $formattedPrice = formatPrice($testPrice);
    
    if (strpos($formattedPrice, 'دينار عراقي') !== false && strpos($formattedPrice, '.') === false) {
        $testResults['currency'] = '✅ تنسيق العملة: ' . $formattedPrice;
    } else {
        $testResults['currency'] = '❌ خطأ في تنسيق العملة: ' . $formattedPrice;
    }
} catch (Exception $e) {
    $testResults['currency'] = '❌ خطأ في اختبار العملة: ' . $e->getMessage();
}

// Test 2: Category CRUD Operations
try {
    // Test category creation
    $testCategoryData = [
        'name' => 'تصنيف اختبار ' . time(),
        'description' => 'وصف تصنيف الاختبار',
        'image' => 'https://via.placeholder.com/150x150/007bff/ffffff?text=Test',
        'status' => 'active'
    ];
    
    $categoryId = insertData('categories', $testCategoryData);
    
    if ($categoryId) {
        $testResults['create'] = '✅ إنشاء التصنيف: نجح';
        
        // Test category update
        $updateData = ['description' => 'وصف محدث ' . time()];
        $updateResult = updateData('categories', $updateData, 'id = ?', [$categoryId]);
        
        if ($updateResult !== false && $updateResult > 0) {
            $testResults['update'] = '✅ تحديث التصنيف: نجح';
        } else {
            $testResults['update'] = '❌ تحديث التصنيف: فشل';
        }
        
        // Test status toggle
        $toggleResult = updateData('categories', ['status' => 'inactive'], 'id = ?', [$categoryId]);
        
        if ($toggleResult !== false && $toggleResult > 0) {
            $testResults['toggle'] = '✅ تغيير الحالة: نجح';
        } else {
            $testResults['toggle'] = '❌ تغيير الحالة: فشل';
        }
        
        // Test category deletion
        $deleteResult = deleteData('categories', 'id = ?', [$categoryId]);
        
        if ($deleteResult) {
            $testResults['delete'] = '✅ حذف التصنيف: نجح';
        } else {
            $testResults['delete'] = '❌ حذف التصنيف: فشل';
        }
        
    } else {
        $testResults['create'] = '❌ إنشاء التصنيف: فشل';
        $testResults['update'] = '⏭️ تم تخطي اختبار التحديث';
        $testResults['toggle'] = '⏭️ تم تخطي اختبار تغيير الحالة';
        $testResults['delete'] = '⏭️ تم تخطي اختبار الحذف';
    }
    
} catch (Exception $e) {
    $testResults['crud'] = '❌ خطأ في اختبار عمليات CRUD: ' . $e->getMessage();
}

// Test 3: Image URL Validation
try {
    $validUrls = [
        'https://example.com/image.jpg',
        'https://example.com/image.png',
        'https://example.com/image.gif',
        'https://example.com/image.webp'
    ];
    
    $invalidUrls = [
        'not-a-url',
        'https://example.com/file.pdf',
        'ftp://example.com/image.jpg'
    ];
    
    $validCount = 0;
    $invalidCount = 0;
    
    foreach ($validUrls as $url) {
        if (filter_var($url, FILTER_VALIDATE_URL) && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $url)) {
            $validCount++;
        }
    }
    
    foreach ($invalidUrls as $url) {
        if (!filter_var($url, FILTER_VALIDATE_URL) || !preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $url)) {
            $invalidCount++;
        }
    }
    
    if ($validCount == count($validUrls) && $invalidCount == count($invalidUrls)) {
        $testResults['image_validation'] = '✅ التحقق من صحة روابط الصور: نجح';
    } else {
        $testResults['image_validation'] = '❌ التحقق من صحة روابط الصور: فشل';
    }
    
} catch (Exception $e) {
    $testResults['image_validation'] = '❌ خطأ في اختبار التحقق من الصور: ' . $e->getMessage();
}

// Test 4: Database Functions
try {
    $categories = fetchAll("SELECT * FROM categories LIMIT 5");
    $categoriesCount = fetchOne("SELECT COUNT(*) as count FROM categories");
    
    if (is_array($categories) && isset($categoriesCount['count'])) {
        $testResults['database'] = '✅ دوال قاعدة البيانات: تعمل بشكل صحيح';
    } else {
        $testResults['database'] = '❌ دوال قاعدة البيانات: خطأ في الاستعلام';
    }
    
} catch (Exception $e) {
    $testResults['database'] = '❌ خطأ في اختبار قاعدة البيانات: ' . $e->getMessage();
}

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-professional fade-in-professional">
                <div class="card-header">
                    <h5 class="mb-0 text-professional-dark">
                        <i class="bi bi-check2-square text-professional-success"></i> 
                        اختبار تحسينات إدارة التصنيفات
                    </h5>
                </div>
                
                <div class="card-body">
                    <div class="alert-professional alert-info slide-up-professional">
                        <i class="bi bi-info-circle-fill alert-icon"></i>
                        <div class="alert-content">
                            <strong>معلومات الاختبار:</strong><br>
                            هذه الصفحة تختبر جميع التحسينات المطبقة على نظام إدارة التصنيفات
                        </div>
                    </div>
                    
                    <div class="row">
                        <?php foreach ($testResults as $testName => $result): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <?php 
                                            $titles = [
                                                'currency' => 'تنسيق العملة',
                                                'create' => 'إنشاء التصنيف',
                                                'update' => 'تحديث التصنيف',
                                                'toggle' => 'تغيير حالة التصنيف',
                                                'delete' => 'حذف التصنيف',
                                                'image_validation' => 'التحقق من روابط الصور',
                                                'database' => 'دوال قاعدة البيانات',
                                                'crud' => 'عمليات CRUD'
                                            ];
                                            echo $titles[$testName] ?? $testName;
                                            ?>
                                        </h6>
                                        <p class="card-text"><?php echo $result; ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <hr class="border-professional">
                    
                    <div class="text-center">
                        <h5 class="text-professional-dark mb-3">الخطوات التالية</h5>
                        <div class="btn-group-professional">
                            <a href="categories.php" class="btn-professional btn-primary hover-lift-professional">
                                <i class="bi bi-tags"></i> إدارة التصنيفات
                            </a>
                            <a href="products.php" class="btn-professional btn-outline-primary hover-lift-professional">
                                <i class="bi bi-box-seam"></i> إدارة المنتجات
                            </a>
                            <a href="dashboard.php" class="btn-professional btn-outline-secondary hover-lift-professional">
                                <i class="bi bi-speedometer2"></i> لوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations to test result cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.3s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
