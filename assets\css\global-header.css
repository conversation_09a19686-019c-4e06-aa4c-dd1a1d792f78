/**
 * Global Header Styles - Consistent Across All Pages
 * Professional, Modern, and Standardized Design
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

/* ==========================================================================
   Base Typography and Layout
   ========================================================================== */

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
}

/* ==========================================================================
   Navbar Brand Styling
   ========================================================================== */

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: #2c3e50 !important;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

/* ==========================================================================
   Navbar Base Styling
   ========================================================================== */

.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    padding: 0.5rem 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

/* ==========================================================================
   Navigation Links Styling
   ========================================================================== */

.navbar-nav .nav-link {
    color: white !important;
    font-weight: 500;
    margin: 0 8px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 0.75rem 1rem !important;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.1);
    transition: left 0.3s ease;
    z-index: 0;
}

.navbar-nav .nav-link:hover::before {
    left: 0;
}

.navbar-nav .nav-link i {
    font-size: 1.2rem;
    margin-bottom: 4px;
    display: block;
    position: relative;
    z-index: 1;
}

.navbar-nav .nav-link span {
    position: relative;
    z-index: 1;
}

.navbar-nav .nav-link:hover {
    color: #ffd700 !important;
    background-color: rgba(255,255,255,0.1);
    transform: translateY(-2px);
}

.navbar-nav .nav-link.active {
    color: #ffd700 !important;
    background-color: rgba(255,215,0,0.2);
}

/* ==========================================================================
   Cart Button Styling
   ========================================================================== */

.cart-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.cart-icon {
    position: relative;
    display: inline-block;
}

.cart-btn {
    background: none !important;
    border: 2px solid rgba(255,255,255,0.3) !important;
    color: white !important;
    padding: 0.5rem 1rem !important;
    border-radius: 8px !important;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    font-weight: 500;
}

.cart-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255,215,0,0.1);
    transition: left 0.3s ease;
}

.cart-btn:hover::before {
    left: 0;
}

.cart-btn:hover {
    border-color: #ffd700 !important;
    color: #ffd700 !important;
    background-color: rgba(255,215,0,0.1) !important;
    transform: translateY(-1px);
}

.cart-btn i {
    font-size: 1.1rem;
    position: relative;
    z-index: 1;
}

.cart-btn span {
    position: relative;
    z-index: 1;
}

/* ==========================================================================
   Search Form Styling
   ========================================================================== */

.search-form {
    max-width: 320px;
}

.search-form .input-group {
    min-width: 250px;
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.search-form .input-group:focus-within {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    transform: translateY(-1px);
}

.search-form .form-control {
    border: none;
    background-color: rgba(255,255,255,0.95);
    color: #333;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.search-form .form-control:focus {
    background-color: white;
    box-shadow: none;
    border: none;
}

.search-form .form-control::placeholder {
    color: #666;
}

.search-form .btn {
    border: none;
    background-color: rgba(255,255,255,0.2);
    color: white;
    padding: 0.75rem 1.2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.search-form .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255,215,0,0.3);
    transition: left 0.3s ease;
}

.search-form .btn:hover::before {
    left: 0;
}

.search-form .btn:hover {
    background-color: rgba(255,215,0,0.3);
    color: #ffd700;
    transform: scale(1.05);
}

.search-form .btn i {
    position: relative;
    z-index: 1;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .search-form {
        max-width: 100%;
        margin-top: 10px;
    }
    
    .navbar-nav .nav-link {
        flex-direction: row;
        justify-content: flex-start;
        text-align: right;
        padding: 0.5rem 1rem !important;
    }
    
    .navbar-nav .nav-link i {
        margin-bottom: 0;
        margin-left: 8px;
        font-size: 1rem;
    }
    
    .cart-btn {
        padding: 0.4rem 0.8rem !important;
    }
    
    .cart-btn .d-none {
        display: none !important;
    }
}

@media (max-width: 576px) {
    .navbar {
        padding: 0.5rem 0;
    }
    
    .navbar-brand {
        font-size: 1.1rem;
    }
    
    .search-form .input-group {
        min-width: 200px;
    }
    
    .navbar-nav .nav-link {
        padding: 0.4rem 0.8rem !important;
        margin: 0 4px;
    }
}

/* ==========================================================================
   RTL Support
   ========================================================================== */

[dir="rtl"] .navbar-nav .nav-link i {
    margin-left: 0;
    margin-right: 8px;
}

[dir="rtl"] .cart-badge {
    right: auto;
    left: -5px;
}

[dir="rtl"] .search-form .form-control {
    text-align: right;
}

/* ==========================================================================
   Accessibility Enhancements
   ========================================================================== */

.navbar-nav .nav-link:focus {
    outline: 2px solid #ffd700;
    outline-offset: 2px;
}

.cart-btn:focus {
    outline: 2px solid #ffd700;
    outline-offset: 2px;
}

.search-form .btn:focus {
    outline: 2px solid #ffd700;
    outline-offset: 2px;
}

/* ==========================================================================
   Enhanced Interactive Effects
   ========================================================================== */

/* Active page highlighting */
.navbar-nav .nav-link.active-page {
    background-color: rgba(255,215,0,0.2);
    color: #ffd700 !important;
}

/* Scroll effects for navbar */
.navbar.navbar-scrolled {
    padding: 0.5rem 0;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    backdrop-filter: blur(10px);
}

/* Enhanced ripple effect for nav links */
.navbar-nav .nav-link {
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,215,0,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.navbar-nav .nav-link:active::after {
    width: 100px;
    height: 100px;
}

/* Enhanced cart button animations */
.cart-btn:hover .cart-icon {
    animation: cartBounce 0.6s ease;
}

@keyframes cartBounce {
    0%, 20%, 60%, 100% { transform: translateY(0); }
    40% { transform: translateY(-3px); }
    80% { transform: translateY(-1px); }
}

/* Search form focus effects */
.search-form .form-control:focus {
    transform: scale(1.02);
}

/* Navbar brand hover effect */
.navbar-brand:hover {
    text-shadow: 0 0 10px rgba(255,215,0,0.5);
}

/* ==========================================================================
   Loading States and Micro-interactions
   ========================================================================== */

/* Loading state for search button */
.search-form .btn.loading {
    pointer-events: none;
}

.search-form .btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Cart badge pulse animation */
.cart-badge {
    animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Hover state for entire navbar */
.navbar:hover {
    box-shadow: 0 6px 25px rgba(0,0,0,0.15);
}

/* ==========================================================================
   Reduce motion for users who prefer it
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
    .navbar-nav .nav-link,
    .cart-btn,
    .search-form .btn,
    .navbar-brand {
        transition: none !important;
        animation: none !important;
    }

    .navbar-nav .nav-link:hover,
    .cart-btn:hover,
    .search-form .btn:hover {
        transform: none !important;
    }

    .cart-badge {
        animation: none !important;
    }

    .navbar-nav .nav-link::after {
        display: none;
    }
}
