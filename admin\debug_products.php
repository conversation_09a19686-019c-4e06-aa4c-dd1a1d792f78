<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'تشخيص مشاكل المنتجات';

echo "<h2>تشخيص مشاكل إدارة المنتجات</h2>";

// Test database connection
echo "<h3>1. اختبار الاتصال بقاعدة البيانات</h3>";
try {
    $testQuery = fetchOne("SELECT 1 as test");
    if ($testQuery) {
        echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح</p>";
    } else {
        echo "<p style='color: red;'>❌ مشكلة في الاتصال بقاعدة البيانات</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</p>";
}

// Check products table structure
echo "<h3>2. فحص هيكل جدول المنتجات</h3>";
try {
    $columns = fetchAll("SHOW COLUMNS FROM products");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $requiredColumns = ['ingredients', 'usage_instructions', 'image_url_1', 'image_url_2', 'image_url_3', 'video_url'];
    $existingColumns = [];
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
        
        $existingColumns[] = $column['Field'];
    }
    echo "</table>";
    
    echo "<h4>فحص الأعمدة الجديدة:</h4>";
    foreach ($requiredColumns as $col) {
        if (in_array($col, $existingColumns)) {
            echo "<p style='color: green;'>✅ العمود '$col' موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ العمود '$col' غير موجود - يحتاج إلى إضافة</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في فحص الجدول: " . $e->getMessage() . "</p>";
}

// Test updateData function
echo "<h3>3. اختبار دالة updateData</h3>";
try {
    // Get a test product
    $testProduct = fetchOne("SELECT id, name, status FROM products LIMIT 1");
    if ($testProduct) {
        echo "<p>منتج الاختبار: " . htmlspecialchars($testProduct['name']) . " (ID: " . $testProduct['id'] . ")</p>";
        echo "<p>الحالة الحالية: " . $testProduct['status'] . "</p>";
        
        // Try to update with a simple field that definitely exists
        $result = updateData('products', ['status' => $testProduct['status']], 'id = ?', [$testProduct['id']]);
        
        if ($result !== false) {
            echo "<p style='color: green;'>✅ دالة updateData تعمل بشكل صحيح</p>";
        } else {
            echo "<p style='color: red;'>❌ دالة updateData لا تعمل</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد منتجات للاختبار</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار updateData: " . $e->getMessage() . "</p>";
}

// Test deleteData function
echo "<h3>4. اختبار دالة deleteData</h3>";
try {
    // Create a test record first
    $testData = [
        'name' => 'منتج اختبار للحذف',
        'description' => 'منتج مؤقت للاختبار',
        'price' => 1.00,
        'stock' => 0,
        'status' => 'inactive'
    ];
    
    $testId = insertData('products', $testData);
    if ($testId) {
        echo "<p>تم إنشاء منتج اختبار (ID: $testId)</p>";
        
        // Now try to delete it
        $deleteResult = deleteData('products', 'id = ?', [$testId]);
        if ($deleteResult !== false && $deleteResult > 0) {
            echo "<p style='color: green;'>✅ دالة deleteData تعمل بشكل صحيح</p>";
        } else {
            echo "<p style='color: red;'>❌ دالة deleteData لا تعمل (النتيجة: $deleteResult)</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء منتج اختبار</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار deleteData: " . $e->getMessage() . "</p>";
}

// Check error logs
echo "<h3>5. فحص سجلات الأخطاء</h3>";
$errorLogPath = ini_get('error_log');
if ($errorLogPath && file_exists($errorLogPath)) {
    $errorLines = file($errorLogPath);
    $recentErrors = array_slice($errorLines, -10); // Last 10 lines
    
    echo "<h4>آخر 10 أخطاء:</h4>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #ddd;'>";
    foreach ($recentErrors as $error) {
        if (strpos($error, 'Update Error') !== false || strpos($error, 'Delete Error') !== false) {
            echo "<span style='color: red;'>" . htmlspecialchars($error) . "</span>";
        } else {
            echo htmlspecialchars($error);
        }
    }
    echo "</pre>";
} else {
    echo "<p>لم يتم العثور على ملف سجل الأخطاء</p>";
}

echo "<hr>";
echo "<p><a href='products.php'>العودة إلى إدارة المنتجات</a></p>";
echo "<p><a href='migrate_products_table.php'>تشغيل تحديث قاعدة البيانات</a></p>";
?>
