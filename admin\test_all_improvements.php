<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'اختبار جميع التحسينات';

// Test 1: Persistent Notifications System
$testResults = [];

try {
    // Setup notifications table
    $createTable = "
        CREATE TABLE IF NOT EXISTS admin_notification_reads (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT NOT NULL DEFAULT 1,
            notification_type ENUM('order', 'review') NOT NULL,
            reference_id INT NOT NULL,
            read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_read (admin_id, notification_type, reference_id),
            INDEX idx_admin_type (admin_id, notification_type),
            INDEX idx_reference (reference_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTable);
    $testResults['notifications_table'] = '✅ تم إنشاء جدول الإشعارات بنجاح';
    
    // Test unread notifications count
    $unreadOrdersResult = fetchOne("
        SELECT COUNT(*) as count 
        FROM orders o
        LEFT JOIN admin_notification_reads anr ON (anr.notification_type = 'order' AND anr.reference_id = o.id)
        WHERE o.status = 'pending' AND anr.id IS NULL
    ");
    $unreadOrders = ($unreadOrdersResult && isset($unreadOrdersResult['count'])) ? $unreadOrdersResult['count'] : 0;
    
    $unreadReviewsResult = fetchOne("
        SELECT COUNT(*) as count 
        FROM reviews r
        LEFT JOIN admin_notification_reads anr ON (anr.notification_type = 'review' AND anr.reference_id = r.id)
        WHERE r.status = 'pending' AND anr.id IS NULL
    ");
    $unreadReviews = ($unreadReviewsResult && isset($unreadReviewsResult['count'])) ? $unreadReviewsResult['count'] : 0;
    
    $testResults['unread_notifications'] = "✅ الإشعارات غير المقروءة: $unreadOrders طلب، $unreadReviews تقييم";
    
} catch (Exception $e) {
    $testResults['notifications_table'] = '❌ خطأ في إعداد الإشعارات: ' . $e->getMessage();
}

// Test 2: Currency Update
try {
    $currentCurrency = getSetting('currency');
    if ($currentCurrency === 'دينار عراقي') {
        $testResults['currency'] = '✅ العملة محدثة بنجاح إلى: ' . $currentCurrency;
    } else {
        $testResults['currency'] = '⚠️ العملة الحالية: ' . $currentCurrency . ' (يجب أن تكون: دينار عراقي)';
    }
    
    // Test formatPrice function
    $samplePrice = formatPrice(1000);
    $testResults['price_format'] = '✅ تنسيق السعر: ' . $samplePrice;
    
} catch (Exception $e) {
    $testResults['currency'] = '❌ خطأ في اختبار العملة: ' . $e->getMessage();
}

// Test 3: AJAX Endpoints
$ajaxTests = [
    'dashboard_stats.php' => 'إحصائيات لوحة التحكم',
    'notifications.php' => 'الإشعارات',
    'mark_notifications_read.php' => 'تحديد الإشعارات كمقروءة'
];

foreach ($ajaxTests as $file => $description) {
    if (file_exists("ajax/$file")) {
        $testResults["ajax_$file"] = "✅ ملف $description موجود";
    } else {
        $testResults["ajax_$file"] = "❌ ملف $description غير موجود";
    }
}

// Test 4: Notifications Page
if (file_exists('notifications.php')) {
    $testResults['notifications_page'] = '✅ صفحة الإشعارات موجودة';
} else {
    $testResults['notifications_page'] = '❌ صفحة الإشعارات غير موجودة';
}

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-success text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">اختبار جميع التحسينات</h2>
                            <p class="mb-0 opacity-75">
                                فحص شامل لجميع التحسينات المطبقة على النظام
                            </p>
                        </div>
                        <div class="col-md-4 text-center">
                            <i class="bi bi-check-circle display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Test Results -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-clipboard-check"></i> نتائج الاختبارات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($testResults as $test => $result): ?>
                            <div class="col-md-6 mb-3">
                                <div class="alert <?php echo strpos($result, '✅') !== false ? 'alert-success' : (strpos($result, '⚠️') !== false ? 'alert-warning' : 'alert-danger'); ?> mb-0">
                                    <strong><?php echo ucfirst(str_replace('_', ' ', $test)); ?>:</strong><br>
                                    <?php echo $result; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Interactive Tests -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-play-circle"></i> اختبارات تفاعلية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-primary w-100" onclick="testNotificationsAjax()">
                                <i class="bi bi-bell"></i> اختبار AJAX الإشعارات
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-success w-100" onclick="testStatsAjax()">
                                <i class="bi bi-graph-up"></i> اختبار AJAX الإحصائيات
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-warning w-100" onclick="testMarkAllRead()">
                                <i class="bi bi-check-all"></i> اختبار تحديد الكل كمقروء
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-info w-100" onclick="testSidebarToggle()">
                                <i class="bi bi-layout-sidebar"></i> اختبار الشريط الجانبي
                            </button>
                        </div>
                    </div>
                    
                    <div id="interactiveResults" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Feature Checklist -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-list-check"></i> قائمة المراجعة النهائية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>الإشعارات المستمرة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success"></i> جدول قراءة الإشعارات</li>
                                <li><i class="bi bi-check-circle text-success"></i> تتبع الإشعارات المقروءة</li>
                                <li><i class="bi bi-check-circle text-success"></i> صفحة الإشعارات الحقيقية</li>
                                <li><i class="bi bi-check-circle text-success"></i> تحديد فردي ومجمع</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>تحسينات أخرى:</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success"></i> إخفاء/إظهار الشريط الجانبي</li>
                                <li><i class="bi bi-check-circle text-success"></i> استبدال العملة بالكامل</li>
                                <li><i class="bi bi-check-circle text-success"></i> معالجة أخطاء JSON</li>
                                <li><i class="bi bi-check-circle text-success"></i> تحسين تجربة المستخدم</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Navigation -->
    <div class="row mt-4">
        <div class="col-12 text-center">
            <a href="dashboard.php" class="btn btn-primary me-2">
                <i class="bi bi-arrow-left"></i> العودة إلى لوحة التحكم
            </a>
            <a href="notifications.php" class="btn btn-success me-2">
                <i class="bi bi-bell"></i> صفحة الإشعارات
            </a>
            <a href="setup_notifications_table.php" class="btn btn-info">
                <i class="bi bi-gear"></i> إعداد قاعدة البيانات
            </a>
        </div>
    </div>
</div>

<script>
function testNotificationsAjax() {
    const resultsDiv = document.getElementById('interactiveResults');
    resultsDiv.innerHTML = '<div class="alert alert-info">جاري اختبار AJAX الإشعارات...</div>';
    
    fetch('ajax/notifications.php')
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                resultsDiv.innerHTML = '<div class="alert alert-success">✅ AJAX الإشعارات يعمل بنجاح<br><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
            } catch (parseError) {
                resultsDiv.innerHTML = '<div class="alert alert-danger">❌ خطأ في تحليل JSON: ' + parseError.message + '<br><strong>الاستجابة:</strong><pre>' + text + '</pre></div>';
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = '<div class="alert alert-danger">❌ خطأ في الاتصال: ' + error.message + '</div>';
        });
}

function testStatsAjax() {
    const resultsDiv = document.getElementById('interactiveResults');
    resultsDiv.innerHTML = '<div class="alert alert-info">جاري اختبار AJAX الإحصائيات...</div>';
    
    fetch('ajax/dashboard_stats.php')
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                resultsDiv.innerHTML = '<div class="alert alert-success">✅ AJAX الإحصائيات يعمل بنجاح<br><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
            } catch (parseError) {
                resultsDiv.innerHTML = '<div class="alert alert-danger">❌ خطأ في تحليل JSON: ' + parseError.message + '<br><strong>الاستجابة:</strong><pre>' + text + '</pre></div>';
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = '<div class="alert alert-danger">❌ خطأ في الاتصال: ' + error.message + '</div>';
        });
}

function testMarkAllRead() {
    const resultsDiv = document.getElementById('interactiveResults');
    resultsDiv.innerHTML = '<div class="alert alert-info">جاري اختبار تحديد الكل كمقروء...</div>';
    
    fetch('ajax/mark_notifications_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.text())
    .then(text => {
        try {
            const data = JSON.parse(text);
            if (data.success) {
                resultsDiv.innerHTML = '<div class="alert alert-success">✅ تحديد الكل كمقروء يعمل بنجاح<br><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
            } else {
                resultsDiv.innerHTML = '<div class="alert alert-warning">⚠️ ' + data.message + '</div>';
            }
        } catch (parseError) {
            resultsDiv.innerHTML = '<div class="alert alert-danger">❌ خطأ في تحليل JSON: ' + parseError.message + '<br><strong>الاستجابة:</strong><pre>' + text + '</pre></div>';
        }
    })
    .catch(error => {
        resultsDiv.innerHTML = '<div class="alert alert-danger">❌ خطأ في الاتصال: ' + error.message + '</div>';
    });
}

function testSidebarToggle() {
    const resultsDiv = document.getElementById('interactiveResults');
    const sidebar = document.getElementById('sidebar');
    
    if (sidebar) {
        const wasHidden = sidebar.classList.contains('hidden');
        toggleSidebarDesktop();
        const isNowHidden = sidebar.classList.contains('hidden');
        
        resultsDiv.innerHTML = '<div class="alert alert-success">✅ الشريط الجانبي يعمل بنجاح<br>' +
            'الحالة السابقة: ' + (wasHidden ? 'مخفي' : 'ظاهر') + '<br>' +
            'الحالة الحالية: ' + (isNowHidden ? 'مخفي' : 'ظاهر') + '</div>';
    } else {
        resultsDiv.innerHTML = '<div class="alert alert-danger">❌ لم يتم العثور على الشريط الجانبي</div>';
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
