<?php
/**
 * Migration script to add new fields to products table
 * Run this once to update existing database
 */

require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'تحديث جدول المنتجات';

// Check if migration is needed
function checkMigrationNeeded() {
    global $pdo;
    try {
        $result = $pdo->query("SHOW COLUMNS FROM products LIKE 'ingredients'");
        return $result->rowCount() == 0;
    } catch (PDOException $e) {
        return true;
    }
}

$migrationNeeded = checkMigrationNeeded();
$migrationResult = null;

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['run_migration'])) {
    try {
        // Add new columns to products table
        $migrations = [
            "ALTER TABLE products ADD COLUMN ingredients TEXT DEFAULT NULL AFTER short_description",
            "ALTER TABLE products ADD COLUMN usage_instructions TEXT DEFAULT NULL AFTER ingredients",
            "ALTER TABLE products ADD COLUMN image_url_1 VARCHAR(500) DEFAULT NULL AFTER image",
            "ALTER TABLE products ADD COLUMN image_url_2 VARCHAR(500) DEFAULT NULL AFTER image_url_1",
            "ALTER TABLE products ADD COLUMN image_url_3 VARCHAR(500) DEFAULT NULL AFTER image_url_2",
            "ALTER TABLE products ADD COLUMN video_url VARCHAR(500) DEFAULT NULL AFTER image_url_3"
        ];
        
        foreach ($migrations as $sql) {
            try {
                $pdo->exec($sql);
            } catch (PDOException $e) {
                // Ignore if column already exists
                if ($e->getCode() != '42S21') { // Duplicate column name
                    throw $e;
                }
            }
        }
        
        $migrationResult = 'success';
        $migrationNeeded = false;
        
    } catch (PDOException $e) {
        $migrationResult = 'error';
        $errorMessage = $e->getMessage();
    }
}

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">تحديث جدول المنتجات</h5>
                </div>
                
                <div class="card-body">
                    <?php if ($migrationResult == 'success'): ?>
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i>
                            تم تحديث جدول المنتجات بنجاح! تمت إضافة الحقول الجديدة:
                            <ul class="mt-2 mb-0">
                                <li>المكونات (ingredients)</li>
                                <li>الاستخدام (usage_instructions)</li>
                                <li>رابط الصورة الأولى (image_url_1)</li>
                                <li>رابط الصورة الثانية (image_url_2)</li>
                                <li>رابط الصورة الثالثة (image_url_3)</li>
                                <li>رابط الفيديو (video_url)</li>
                            </ul>
                        </div>
                        <a href="products.php" class="btn btn-primary">العودة إلى إدارة المنتجات</a>
                        
                    <?php elseif ($migrationResult == 'error'): ?>
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            حدث خطأ أثناء التحديث: <?php echo htmlspecialchars($errorMessage); ?>
                        </div>
                        
                    <?php elseif ($migrationNeeded): ?>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            يحتاج جدول المنتجات إلى تحديث لإضافة الحقول الجديدة التالية:
                            <ul class="mt-2 mb-0">
                                <li><strong>المكونات:</strong> حقل نصي لوصف مكونات المنتج</li>
                                <li><strong>الاستخدام:</strong> حقل نصي لتعليمات استخدام المنتج</li>
                                <li><strong>روابط الصور:</strong> 3 حقول لروابط صور المنتج الخارجية</li>
                                <li><strong>رابط الفيديو:</strong> حقل لرابط فيديو المنتج</li>
                            </ul>
                        </div>
                        
                        <form method="POST">
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                                <strong>تنبيه:</strong> تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل التحديث.
                            </div>
                            
                            <button type="submit" name="run_migration" class="btn btn-primary" 
                                    onclick="return confirm('هل أنت متأكد من تحديث جدول المنتجات؟')">
                                <i class="bi bi-database-up"></i> تحديث الجدول
                            </button>
                            <a href="products.php" class="btn btn-secondary">إلغاء</a>
                        </form>
                        
                    <?php else: ?>
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i>
                            جدول المنتجات محدث ولا يحتاج إلى تعديل.
                        </div>
                        <a href="products.php" class="btn btn-primary">العودة إلى إدارة المنتجات</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
