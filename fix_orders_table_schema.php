<?php
/**
 * Fix orders table schema to match checkout data structure
 */

require_once 'config/config.php';

echo "<h1>Orders Table Schema Fix</h1>";

// 1. Check current table structure
echo "<h2>1. Current Table Structure</h2>";
try {
    $currentColumns = $pdo->query("DESCRIBE orders")->fetchAll();
    echo "<h3>Current orders table columns:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Default</th></tr>";
    foreach ($currentColumns as $column) {
        echo "<tr>";
        echo "<td><strong>" . $column['Field'] . "</strong></td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "❌ Error getting current table structure: " . $e->getMessage() . "<br>";
    die();
}

// 2. Define the correct structure based on checkout.php data
$requiredColumns = [
    'id' => 'int(11) NOT NULL AUTO_INCREMENT',
    'customer_name' => 'varchar(255) NOT NULL',
    'customer_phone' => 'varchar(20) NOT NULL',
    'customer_email' => 'varchar(100) DEFAULT NULL',
    'address' => 'text NOT NULL',
    'province' => 'varchar(100) NOT NULL',
    'city' => 'varchar(50) DEFAULT NULL',
    'postal_code' => 'varchar(10) DEFAULT NULL',
    'subtotal' => 'decimal(10,2) NOT NULL DEFAULT 0.00',
    'delivery_price' => 'decimal(10,2) NOT NULL DEFAULT 0.00',
    'discount_amount' => 'decimal(10,2) NOT NULL DEFAULT 0.00',
    'total_price' => 'decimal(10,2) NOT NULL',
    'payment_method' => "enum('cash_on_delivery','bank_transfer') NOT NULL DEFAULT 'cash_on_delivery'",
    'status' => "enum('pending','confirmed','processing','shipped','delivered','cancelled') NOT NULL DEFAULT 'pending'",
    'notes' => 'text',
    'created_at' => 'timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP',
    'updated_at' => 'timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
];

echo "<h2>2. Required Structure Analysis</h2>";
$currentColumnNames = array_column($currentColumns, 'Field');
$requiredColumnNames = array_keys($requiredColumns);

echo "<h3>Missing columns:</h3>";
$missingColumns = array_diff($requiredColumnNames, $currentColumnNames);
if (empty($missingColumns)) {
    echo "✅ No missing columns<br>";
} else {
    foreach ($missingColumns as $missing) {
        echo "❌ Missing: $missing<br>";
    }
}

echo "<h3>Extra columns:</h3>";
$extraColumns = array_diff($currentColumnNames, $requiredColumnNames);
if (empty($extraColumns)) {
    echo "✅ No extra columns<br>";
} else {
    foreach ($extraColumns as $extra) {
        echo "⚠️ Extra: $extra<br>";
    }
}

// 3. Apply fixes if needed
if (!empty($missingColumns)) {
    echo "<h2>3. Applying Schema Fixes</h2>";
    
    foreach ($missingColumns as $columnName) {
        $columnDef = $requiredColumns[$columnName];
        $sql = "ALTER TABLE orders ADD COLUMN `$columnName` $columnDef";
        
        try {
            $pdo->exec($sql);
            echo "✅ Added column: $columnName<br>";
        } catch (Exception $e) {
            echo "❌ Failed to add column $columnName: " . $e->getMessage() . "<br>";
        }
    }
} else {
    echo "<h2>3. Schema Check Complete</h2>";
    echo "✅ All required columns exist<br>";
}

// 4. Test insertion with correct data structure
echo "<h2>4. Test Order Insertion</h2>";

$testOrderData = [
    'customer_name' => 'Schema Fix Test Customer',
    'customer_phone' => '07123456789',
    'address' => 'Test Address, Baghdad, Iraq',
    'province' => 'بغداد',
    'subtotal' => 50000.00,
    'delivery_price' => 5000.00,
    'discount_amount' => 0.00,
    'total_price' => 55000.00,
    'payment_method' => 'cash_on_delivery',
    'status' => 'pending',
    'notes' => 'Schema fix test order'
];

echo "<h3>Testing with data:</h3>";
echo "<pre>" . json_encode($testOrderData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

try {
    $orderId = insertData('orders', $testOrderData);
    
    if ($orderId) {
        echo "✅ Order insertion successful - Order ID: $orderId<br>";
        
        // Test order item insertion
        $testItemData = [
            'order_id' => $orderId,
            'product_id' => 1,
            'product_name' => 'Test Product',
            'quantity' => 2,
            'price' => 25000.00,
            'total' => 50000.00
        ];
        
        $itemId = insertData('order_items', $testItemData);
        if ($itemId) {
            echo "✅ Order item insertion successful - Item ID: $itemId<br>";
        } else {
            echo "❌ Order item insertion failed<br>";
        }
        
        // Verify the order exists and can be retrieved
        $verifyOrder = fetchOne("SELECT * FROM orders WHERE id = ?", [$orderId]);
        if ($verifyOrder) {
            echo "✅ Order verification successful<br>";
            echo "<h4>Order Details:</h4>";
            echo "<ul>";
            echo "<li>Customer: " . $verifyOrder['customer_name'] . "</li>";
            echo "<li>Phone: " . $verifyOrder['customer_phone'] . "</li>";
            echo "<li>Total: " . number_format($verifyOrder['total_price']) . " IQD</li>";
            echo "<li>Status: " . $verifyOrder['status'] . "</li>";
            echo "<li>Created: " . $verifyOrder['created_at'] . "</li>";
            echo "</ul>";
        } else {
            echo "❌ Order verification failed<br>";
        }
        
        // Clean up test data
        $pdo->prepare("DELETE FROM order_items WHERE order_id = ?")->execute([$orderId]);
        $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$orderId]);
        echo "✅ Test data cleaned up<br>";
        
    } else {
        echo "❌ Order insertion failed<br>";
        
        // Try to get more specific error information
        $errorInfo = $pdo->errorInfo();
        if ($errorInfo[0] !== '00000') {
            echo "Database Error: " . json_encode($errorInfo) . "<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Exception during insertion: " . $e->getMessage() . "<br>";
    echo "Error Code: " . $e->getCode() . "<br>";
}

// 5. Final verification
echo "<h2>5. Final System Status</h2>";

if ($orderId ?? false) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Schema Fix Successful!</h3>";
    echo "<p>The orders table schema has been corrected and order insertion is now working.</p>";
    echo "<ul>";
    echo "<li>✅ All required columns exist</li>";
    echo "<li>✅ Order insertion working</li>";
    echo "<li>✅ Order item insertion working</li>";
    echo "<li>✅ Data retrieval working</li>";
    echo "</ul>";
    echo "<p><strong>The checkout process should now work correctly!</strong></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Issues Still Exist</h3>";
    echo "<p>The schema fix did not resolve the insertion problem. Further investigation needed.</p>";
    echo "</div>";
}

echo "<h2>6. Next Steps</h2>";
echo "<ul>";
echo "<li><a href='verify_checkout_fix.php' target='_blank'>Run Complete Checkout Verification</a></li>";
echo "<li><a href='checkout.php' target='_blank'>Test Checkout Process</a></li>";
echo "<li><a href='admin/orders.php' target='_blank'>Check Admin Orders Page</a></li>";
echo "</ul>";
?>
