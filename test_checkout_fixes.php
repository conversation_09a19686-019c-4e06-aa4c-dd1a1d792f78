<?php
/**
 * Comprehensive test for checkout fixes
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Checkout Fixes - Comprehensive Test</h1>";

try {
    require_once 'config/config.php';
    
    echo "<h2>1. Phone Number Validation Test</h2>";
    
    // Test Iraqi phone number patterns
    $testPhones = [
        '07123456789' => 'Standard Iraqi mobile',
        '+9647123456789' => 'International format with +',
        '9647123456789' => 'International format without +',
        '************' => 'With spaces',
        '************' => 'With dashes',
        '079(123)4567' => 'With parentheses',
        '+964 77 123 4567' => 'International with spaces',
        '07712345678' => 'Network 077',
        '07812345678' => 'Network 078',
        '07912345678' => 'Network 079',
        '0612345678' => 'Invalid - not mobile',
        '123456789' => 'Invalid - too short',
        '+1234567890' => 'Invalid - wrong country code'
    ];
    
    echo "<h3>JavaScript Validation Test (Client-side)</h3>";
    echo "<script>";
    echo "function testPhoneValidation() {";
    echo "    const phonePatterns = [";
    echo "        /^07[0-9]{8}$/,";
    echo "        /^\+9647[0-9]{8}$/,";
    echo "        /^9647[0-9]{8}$/,";
    echo "        /^07[7-9][0-9]{7}$/,";
    echo "        /^\+96477[0-9]{7}$/,";
    echo "        /^\+96478[0-9]{7}$/,";
    echo "        /^\+96479[0-9]{7}$/";
    echo "    ];";
    echo "    ";
    echo "    const testPhones = " . json_encode($testPhones) . ";";
    echo "    ";
    echo "    for (let phone in testPhones) {";
    echo "        const cleanPhone = phone.replace(/[\\s\\-\\(\\)]/g, '');";
    echo "        let isValid = false;";
    echo "        for (let pattern of phonePatterns) {";
    echo "            if (pattern.test(cleanPhone)) {";
    echo "                isValid = true;";
    echo "                break;";
    echo "            }";
    echo "        }";
    echo "        const status = isValid ? '✅' : '❌';";
    echo "        console.log(status + ' ' + phone + ' (' + testPhones[phone] + ')');";
    echo "        document.write('<div>' + status + ' ' + phone + ' (' + testPhones[phone] + ')</div>');";
    echo "    }";
    echo "}";
    echo "testPhoneValidation();";
    echo "</script>";
    
    echo "<h3>PHP Validation Test (Server-side)</h3>";
    
    foreach ($testPhones as $phone => $description) {
        // Clean phone number
        $cleanPhone = preg_replace('/[\s\-\(\)]/', '', $phone);
        
        // Test patterns
        $phonePatterns = [
            '/^07[0-9]{8}$/',
            '/^\+9647[0-9]{8}$/',
            '/^9647[0-9]{8}$/',
            '/^07[7-9][0-9]{7}$/',
            '/^\+96477[0-9]{7}$/',
            '/^\+96478[0-9]{7}$/',
            '/^\+96479[0-9]{7}$/'
        ];
        
        $isValid = false;
        foreach ($phonePatterns as $pattern) {
            if (preg_match($pattern, $cleanPhone)) {
                $isValid = true;
                break;
            }
        }
        
        $status = $isValid ? '✅' : '❌';
        echo "<div>{$status} {$phone} ({$description}) - Clean: {$cleanPhone}</div>";
    }
    
    echo "<h2>2. Database Connection and Insert Test</h2>";
    
    if (!$pdo) {
        echo "❌ PDO connection not available<br>";
    } else {
        echo "✅ PDO connection available<br>";
        
        // Test basic query
        try {
            $result = $pdo->query("SELECT 1 as test");
            echo "✅ Basic database query works<br>";
        } catch (Exception $e) {
            echo "❌ Basic database query failed: " . $e->getMessage() . "<br>";
        }
        
        // Check orders table
        try {
            $tableCheck = $pdo->query("SHOW TABLES LIKE 'orders'");
            if ($tableCheck->rowCount() > 0) {
                echo "✅ Orders table exists<br>";
            } else {
                echo "❌ Orders table does not exist<br>";
            }
        } catch (Exception $e) {
            echo "❌ Table check failed: " . $e->getMessage() . "<br>";
        }
        
        // Test insertData function
        if (function_exists('insertData')) {
            echo "✅ insertData function exists<br>";
            
            $testOrderData = [
                'customer_name' => 'Test Customer',
                'customer_phone' => '07123456789',
                'address' => 'Test Address, Baghdad',
                'province' => 'بغداد',
                'subtotal' => 50000.00,
                'delivery_price' => 5000.00,
                'discount_amount' => 0.00,
                'total_price' => 55000.00,
                'payment_method' => 'cash_on_delivery',
                'status' => 'pending'
            ];
            
            echo "Testing insertData with: " . json_encode($testOrderData, JSON_UNESCAPED_UNICODE) . "<br>";
            
            $testOrderId = insertData('orders', $testOrderData);
            
            if ($testOrderId) {
                echo "✅ insertData succeeded - Order ID: {$testOrderId}<br>";
                
                // Verify the order exists
                $verifyOrder = fetchOne("SELECT * FROM orders WHERE id = ?", [$testOrderId]);
                if ($verifyOrder) {
                    echo "✅ Order verified in database<br>";
                    echo "Customer: " . $verifyOrder['customer_name'] . "<br>";
                    echo "Phone: " . $verifyOrder['customer_phone'] . "<br>";
                    echo "Total: " . number_format($verifyOrder['total_price']) . " IQD<br>";
                    
                    // Test order items insertion
                    $testItemData = [
                        'order_id' => $testOrderId,
                        'product_id' => 1,
                        'product_name' => 'Test Product',
                        'quantity' => 2,
                        'price' => 25000.00,
                        'total' => 50000.00
                    ];
                    
                    $testItemId = insertData('order_items', $testItemData);
                    if ($testItemId) {
                        echo "✅ Order item inserted successfully - Item ID: {$testItemId}<br>";
                    } else {
                        echo "❌ Order item insertion failed<br>";
                    }
                    
                    // Clean up test data
                    $pdo->prepare("DELETE FROM order_items WHERE order_id = ?")->execute([$testOrderId]);
                    $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$testOrderId]);
                    echo "✅ Test data cleaned up<br>";
                    
                } else {
                    echo "❌ Order not found in database after insertion<br>";
                }
            } else {
                echo "❌ insertData failed<br>";
            }
        } else {
            echo "❌ insertData function not found<br>";
        }
    }
    
    echo "<h2>3. Complete Order Flow Test</h2>";
    echo "<p>To test the complete order flow:</p>";
    echo "<ol>";
    echo "<li>Add products to cart</li>";
    echo "<li>Go to checkout page</li>";
    echo "<li>Fill in customer information with various phone formats</li>";
    echo "<li>Select a province</li>";
    echo "<li>Submit the order</li>";
    echo "<li>Check admin panel for the new order</li>";
    echo "</ol>";
    
    echo "<h2>4. Admin Panel Check</h2>";
    echo "<p>Recent orders in database:</p>";
    
    try {
        $recentOrders = fetchAll("SELECT * FROM orders ORDER BY created_at DESC LIMIT 5");
        if (count($recentOrders) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Customer</th><th>Phone</th><th>Province</th><th>Total</th><th>Status</th><th>Created</th></tr>";
            foreach ($recentOrders as $order) {
                echo "<tr>";
                echo "<td>" . $order['id'] . "</td>";
                echo "<td>" . htmlspecialchars($order['customer_name']) . "</td>";
                echo "<td>" . htmlspecialchars($order['customer_phone']) . "</td>";
                echo "<td>" . htmlspecialchars($order['province']) . "</td>";
                echo "<td>" . number_format($order['total_price']) . " IQD</td>";
                echo "<td>" . $order['status'] . "</td>";
                echo "<td>" . $order['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No orders found in database.</p>";
        }
    } catch (Exception $e) {
        echo "❌ Error fetching orders: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>Test Summary</h2>";
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ Fixes Applied:</h3>";
    echo "<ul>";
    echo "<li><strong>Phone Validation:</strong> Fixed regex patterns to accept all Iraqi phone formats</li>";
    echo "<li><strong>Database Insert:</strong> Enhanced error logging and validation</li>";
    echo "<li><strong>Order Processing:</strong> Added verification and better error handling</li>";
    echo "</ul>";
    echo "<h3>🧪 Next Steps:</h3>";
    echo "<ul>";
    echo "<li>Test the checkout page with various phone number formats</li>";
    echo "<li>Verify orders appear in admin panel</li>";
    echo "<li>Check error logs for any remaining issues</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Fatal Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<p><a href='checkout.php'>Go to Checkout</a> | <a href='admin/orders.php'>Check Admin Orders</a></p>";
?>
