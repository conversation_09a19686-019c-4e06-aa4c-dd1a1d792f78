<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'إدارة المؤثرين والمراجعات';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'add':
            case 'edit':
                $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
                $influencerName = sanitizeInput($_POST['influencer_name'] ?? '');
                $contentType = sanitizeInput($_POST['content_type'] ?? '');
                $contentTitle = sanitizeInput($_POST['content_title'] ?? '');
                $contentText = $_POST['content_text'] ?? ''; // Rich text, will be sanitized differently
                $rating = isset($_POST['rating']) && $_POST['rating'] !== '' ? (int)$_POST['rating'] : null;
                $productId = isset($_POST['product_id']) && $_POST['product_id'] ? sanitizeInput($_POST['product_id']) : null;
                $videoUrl = sanitizeInput($_POST['video_url'] ?? '');
                $videoPlatform = sanitizeInput($_POST['video_platform'] ?? '');
                $categoryId = isset($_POST['category_id']) && $_POST['category_id'] ? (int)$_POST['category_id'] : null;
                $status = sanitizeInput($_POST['status'] ?? 'published');
                $isFeatured = isset($_POST['is_featured']) ? 1 : 0;
                $sortOrder = (int)($_POST['sort_order'] ?? 0);

                // التحقق من الحقول المطلوبة والقيود
                $errors = [];
                if (empty($influencerName)) {
                    $errors[] = 'اسم المؤثر مطلوب';
                }
                if (empty($contentType)) {
                    $errors[] = 'نوع المحتوى مطلوب';
                } elseif (!in_array($contentType, ['video', 'post', 'review'])) {
                    $errors[] = 'نوع المحتوى غير صحيح';
                }
                if (empty($contentText)) {
                    $errors[] = 'نص المحتوى مطلوب';
                }
                if (!in_array($status, ['draft', 'published', 'archived'])) {
                    $errors[] = 'حالة النشر غير صحيحة';
                }
                if ($rating !== null && ($rating < 1 || $rating > 5)) {
                    $errors[] = 'التقييم يجب أن يكون بين 1 و 5';
                }
                if (strlen($influencerName) > 255) {
                    $errors[] = 'اسم المؤثر طويل جداً (الحد الأقصى 255 حرف)';
                }
                if (!empty($contentTitle) && strlen($contentTitle) > 500) {
                    $errors[] = 'عنوان المحتوى طويل جداً (الحد الأقصى 500 حرف)';
                }
                if (!empty($videoUrl) && strlen($videoUrl) > 1000) {
                    $errors[] = 'رابط الفيديو طويل جداً (الحد الأقصى 1000 حرف)';
                }
                if (!empty($videoPlatform) && !in_array($videoPlatform, ['youtube', 'instagram', 'tiktok', 'other'])) {
                    $errors[] = 'منصة الفيديو غير صحيحة';
                }

                if (!empty($errors)) {
                    throw new Exception(implode(', ', $errors));
                }
                
                // معالجة الصورة (رابط خارجي فقط)
                $imagePath = sanitizeInput($_POST['image_url'] ?? '');
                if (!empty($imagePath) && !filter_var($imagePath, FILTER_VALIDATE_URL)) {
                    throw new Exception('رابط الصورة غير صحيح');
                }

                // إذا لم يتم تحديد صورة، استخدم صورة افتراضية
                if (empty($imagePath) && $imageType === 'url') {
                    $imagePath = 'https://via.placeholder.com/150x150/007bff/ffffff?text=' . urlencode('مؤثر');
                }
                
                if ($action === 'add') {
                    // تنظيف وتحضير البيانات للإدراج
                    $cleanVideoUrl = !empty($videoUrl) ? $videoUrl : null;
                    $cleanVideoPlatform = !empty($videoPlatform) ? $videoPlatform : null;
                    $cleanContentTitle = !empty($contentTitle) ? $contentTitle : null;
                    $cleanImagePath = !empty($imagePath) ? $imagePath : null;

                    $sql = "INSERT INTO influencers_content (
                        influencer_name, influencer_image, influencer_image_type, content_type,
                        content_title, content_text, rating, product_id, video_url, video_platform,
                        category_id, status, is_featured, sort_order, created_by, published_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                    $publishedAt = $status === 'published' ? date('Y-m-d H:i:s') : null;

                    $params = [
                        $influencerName, $cleanImagePath, 'url', $contentType,
                        $cleanContentTitle, $contentText, $rating, $productId, $cleanVideoUrl, $cleanVideoPlatform,
                        $categoryId, $status, $isFeatured, $sortOrder, $_SESSION['admin_id'], $publishedAt
                    ];

                    // تسجيل معلومات التشخيص مفصلة
                    error_log("Influencer Insert - Parameters count: " . count($params));
                    error_log("Influencer Insert - SQL placeholders: " . substr_count($sql, '?'));
                    error_log("Influencer Insert - Data: " . json_encode($params, JSON_UNESCAPED_UNICODE));

                    try {
                        $result = executeQuery($sql, $params);

                        if ($result) {
                            $insertedId = $pdo->lastInsertId();
                            $_SESSION['success_message'] = 'تم إضافة المحتوى بنجاح (ID: ' . $insertedId . ')';
                            error_log("Influencer Insert Success - ID: " . $insertedId);
                        } else {
                            $_SESSION['error_message'] = 'فشل في إضافة المحتوى - خطأ في قاعدة البيانات';
                            error_log("Influencer Insert Failed - executeQuery returned false");
                        }
                    } catch (Exception $e) {
                        $_SESSION['error_message'] = 'فشل في إضافة المحتوى: ' . $e->getMessage();
                        error_log("Influencer Insert Exception: " . $e->getMessage());
                        error_log("Stack trace: " . $e->getTraceAsString());
                    }
                } else {
                    // التحقق من وجود المحتوى
                    $existing = fetchOne("SELECT * FROM influencers_content WHERE id = ?", [$id]);
                    if (!$existing) {
                        throw new Exception('المحتوى غير موجود');
                    }
                    
                    // إذا لم يتم إدخال رابط صورة جديد، احتفظ بالصورة القديمة
                    if (empty($imagePath)) {
                        $imagePath = $existing['influencer_image'];
                    }

                    $sql = "UPDATE influencers_content SET
                        influencer_name = ?, influencer_image = ?, influencer_image_type = ?,
                        content_type = ?, content_title = ?, content_text = ?, rating = ?,
                        product_id = ?, video_url = ?, video_platform = ?, category_id = ?,
                        status = ?, is_featured = ?, sort_order = ?, updated_by = ?, updated_at = NOW(),
                        published_at = CASE WHEN status != 'published' AND ? = 'published' THEN NOW() ELSE published_at END
                        WHERE id = ?";

                    $result = executeQuery($sql, [
                        $influencerName, $imagePath, 'url', $contentType,
                        $contentTitle, $contentText, $rating, $productId, $videoUrl, $videoPlatform,
                        $categoryId, $status, $isFeatured, $sortOrder, $_SESSION['admin_id'], $status, $id
                    ]);
                    
                    if ($result) {
                        $_SESSION['success_message'] = 'تم تحديث المحتوى بنجاح';
                    }
                }
                break;
                
            case 'delete':
                $id = (int)$_POST['id'];
                $content = fetchOne("SELECT * FROM influencers_content WHERE id = ?", [$id]);
                
                if ($content) {
                    // لا حاجة لحذف الصور المرفوعة لأننا نستخدم الروابط الخارجية فقط
                    
                    $result = executeQuery("DELETE FROM influencers_content WHERE id = ?", [$id]);
                    if ($result) {
                        $_SESSION['success_message'] = 'تم حذف المحتوى بنجاح';
                    }
                } else {
                    throw new Exception('المحتوى غير موجود');
                }
                break;
                
            case 'toggle_status':
                $id = (int)$_POST['id'];
                $newStatus = sanitizeInput($_POST['new_status']);
                
                $publishedAt = $newStatus === 'published' ? ', published_at = NOW()' : '';
                $sql = "UPDATE influencers_content SET status = ?, updated_by = ?, updated_at = NOW() {$publishedAt} WHERE id = ?";
                
                $result = executeQuery($sql, [$newStatus, $_SESSION['admin_id'], $id]);
                if ($result) {
                    $_SESSION['success_message'] = 'تم تحديث حالة المحتوى بنجاح';
                }
                break;
        }
    } catch (Exception $e) {
        $errorMessage = 'حدث خطأ: ' . $e->getMessage();
        $_SESSION['error_message'] = $errorMessage;
        error_log("Influencer Operation Error: " . $errorMessage);
        error_log("Stack trace: " . $e->getTraceAsString());
    }
    
    header('Location: influencers.php');
    exit();
}

// معاملات البحث والفلترة
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$status = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
$contentType = isset($_GET['content_type']) ? sanitizeInput($_GET['content_type']) : '';
$category = isset($_GET['category']) ? (int)$_GET['category'] : 0;

// بناء استعلام البحث
$whereConditions = ["1=1"];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(ic.influencer_name LIKE ? OR ic.content_title LIKE ?)";
    $searchTerm = "%{$search}%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if (!empty($status)) {
    $whereConditions[] = "ic.status = ?";
    $params[] = $status;
}

if (!empty($contentType)) {
    $whereConditions[] = "ic.content_type = ?";
    $params[] = $contentType;
}

if ($category > 0) {
    $whereConditions[] = "ic.category_id = ?";
    $params[] = $category;
}

$whereClause = implode(' AND ', $whereConditions);

// التصفح
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 20;
$offset = ($page - 1) * $perPage;

// عدد المحتويات الإجمالي
$countSql = "SELECT COUNT(*) as total FROM influencers_content ic WHERE {$whereClause}";
$totalResult = fetchOne($countSql, $params);
$totalContent = $totalResult['total'] ?? 0;
$totalPages = ceil($totalContent / $perPage);

// جلب المحتوى
$sql = "
    SELECT ic.*, cc.name_ar as category_name, p.name as product_name,
           COALESCE(cv.view_count, 0) as total_views
    FROM influencers_content ic
    LEFT JOIN content_categories cc ON ic.category_id = cc.id
    LEFT JOIN products p ON ic.product_id = p.id
    LEFT JOIN (
        SELECT content_id, COUNT(*) as view_count 
        FROM content_views 
        WHERE content_type = 'influencer' 
        GROUP BY content_id
    ) cv ON ic.id = cv.content_id
    WHERE {$whereClause}
    ORDER BY ic.sort_order ASC, ic.created_at DESC
    LIMIT {$perPage} OFFSET {$offset}
";

$contents = fetchAll($sql, $params);

// جلب البيانات المساعدة
$categories = fetchAll("SELECT id, name_ar as name FROM content_categories WHERE type IN ('influencer', 'both') AND status = 'active' ORDER BY name_ar");
$products = fetchAll("SELECT id, name FROM products WHERE status = 'active' ORDER BY name");

// إضافة فئات المنتجات إلى قائمة المنتجات
$productCategories = [
    ['id' => 'cat_skincare', 'name' => 'منتجات البشرة', 'type' => 'category'],
    ['id' => 'cat_makeup', 'name' => 'المكياج', 'type' => 'category'],
    ['id' => 'cat_perfumes', 'name' => 'العطور', 'type' => 'category'],
    ['id' => 'cat_others', 'name' => 'اخرى', 'type' => 'category']
];

// دمج المنتجات والفئات
$allProductOptions = array_merge($products, $productCategories);

// دالة للحصول على اسم المنتج أو الفئة
function getProductDisplayName($productId, $products, $productCategories) {
    if (empty($productId)) {
        return '';
    }

    // البحث في المنتجات أولاً
    foreach ($products as $product) {
        if ($product['id'] == $productId) {
            return $product['name'];
        }
    }

    // البحث في فئات المنتجات
    foreach ($productCategories as $category) {
        if ($category['id'] == $productId) {
            return $category['name'];
        }
    }

    return '';
}

// إحصائيات
$stats = [
    'total' => fetchOne("SELECT COUNT(*) as count FROM influencers_content")['count'] ?? 0,
    'published' => fetchOne("SELECT COUNT(*) as count FROM influencers_content WHERE status = 'published'")['count'] ?? 0,
    'draft' => fetchOne("SELECT COUNT(*) as count FROM influencers_content WHERE status = 'draft'")['count'] ?? 0,
    'featured' => fetchOne("SELECT COUNT(*) as count FROM influencers_content WHERE is_featured = 1")['count'] ?? 0
];

require_once 'includes/header.php';
?>

<div class="container-fluid px-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إدارة المؤثرين والمراجعات</h1>
            <p class="text-muted">إدارة محتوى المؤثرين والمراجعات والمنشورات</p>
        </div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#contentModal" onclick="openAddModal()">
            <i class="bi bi-plus-circle"></i> إضافة محتوى جديد
        </button>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-light border mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="small text-primary fw-semibold">إجمالي المحتوى</div>
                            <div class="text-lg fw-bold text-dark"><?php echo $stats['total']; ?></div>
                        </div>
                        <i class="bi bi-collection fs-2 text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-light border mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="small text-success fw-semibold">منشور</div>
                            <div class="text-lg fw-bold text-dark"><?php echo $stats['published']; ?></div>
                        </div>
                        <i class="bi bi-check-circle fs-2 text-success"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-light border mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="small text-warning fw-semibold">مسودة</div>
                            <div class="text-lg fw-bold text-dark"><?php echo $stats['draft']; ?></div>
                        </div>
                        <i class="bi bi-file-earmark-text fs-2 text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-light border mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="small text-info fw-semibold">مميز</div>
                            <div class="text-lg fw-bold text-dark"><?php echo $stats['featured']; ?></div>
                        </div>
                        <i class="bi bi-star-fill fs-2 text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="ابحث في اسم المؤثر أو العنوان...">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="published" <?php echo $status === 'published' ? 'selected' : ''; ?>>منشور</option>
                        <option value="draft" <?php echo $status === 'draft' ? 'selected' : ''; ?>>مسودة</option>
                        <option value="archived" <?php echo $status === 'archived' ? 'selected' : ''; ?>>مؤرشف</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="content_type" class="form-label">نوع المحتوى</label>
                    <select class="form-select" id="content_type" name="content_type">
                        <option value="">جميع الأنواع</option>
                        <option value="video" <?php echo $contentType === 'video' ? 'selected' : ''; ?>>فيديو</option>
                        <option value="review" <?php echo $contentType === 'review' ? 'selected' : ''; ?>>مراجعة</option>
                        <option value="post" <?php echo $contentType === 'post' ? 'selected' : ''; ?>>منشور</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">التصنيف</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">جميع التصنيفات</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo $cat['id']; ?>" <?php echo $category === $cat['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($cat['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="bi bi-funnel"></i> فلترة
                    </button>
                    <a href="influencers.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Content Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">قائمة المحتوى (<?php echo $totalContent; ?> عنصر)</h5>
        </div>
        <div class="card-body p-0">
            <?php if (empty($contents)): ?>
                <div class="text-center py-5">
                    <i class="bi bi-inbox display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">لا يوجد محتوى</h4>
                    <p class="text-muted">لم يتم العثور على محتوى يطابق معايير البحث</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0" style="color: #000000 !important;">
                        <thead class="table-light">
                            <tr>
                                <th>المؤثر</th>
                                <th>المحتوى</th>
                                <th>النوع</th>
                                <th>التقييم</th>
                                <th>المنتج</th>
                                <th>الحالة</th>
                                <th>المشاهدات</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($contents as $content): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php
                                            $imageUrl = '';
                                            if ($content['influencer_image']) {
                                                $imageUrl = $content['influencer_image'];
                                            } else {
                                                $imageUrl = 'https://via.placeholder.com/40x40/f8f9fa/6c757d?text=' . urlencode('صورة');
                                            }
                                            ?>
                                            <img src="<?php echo $imageUrl; ?>" 
                                                 alt="<?php echo htmlspecialchars($content['influencer_name']); ?>"
                                                 class="rounded-circle me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                            <div>
                                                <div class="fw-bold text-dark"><?php echo htmlspecialchars($content['influencer_name']); ?></div>
                                                <?php if ($content['category_name']): ?>
                                                    <small class="text-muted"><?php echo htmlspecialchars($content['category_name']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <?php if ($content['content_title']): ?>
                                                <div class="fw-bold text-dark"><?php echo htmlspecialchars(mb_substr($content['content_title'], 0, 50) . (mb_strlen($content['content_title']) > 50 ? '...' : '')); ?></div>
                                            <?php endif; ?>
                                            <small class="text-dark" style="opacity: 0.7;">
                                                <?php echo htmlspecialchars(mb_substr(strip_tags($content['content_text']), 0, 80) . (mb_strlen(strip_tags($content['content_text'])) > 80 ? '...' : '')); ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                        $typeColors = ['video' => 'danger', 'review' => 'warning', 'post' => 'info'];
                                        $typeNames = ['video' => 'فيديو', 'review' => 'مراجعة', 'post' => 'منشور'];
                                        ?>
                                        <span class="badge bg-<?php echo $typeColors[$content['content_type']]; ?>">
                                            <?php echo $typeNames[$content['content_type']]; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($content['rating']): ?>
                                            <div class="d-flex align-items-center">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="bi bi-star<?php echo $i <= $content['rating'] ? '-fill text-warning' : ' text-muted'; ?>" style="font-size: 0.8em;"></i>
                                                <?php endfor; ?>
                                                <span class="ms-1 small">(<?php echo $content['rating']; ?>)</span>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $productDisplayName = getProductDisplayName($content['product_id'], $products, $productCategories);
                                        if ($productDisplayName): ?>
                                            <span class="badge bg-light text-dark"><?php echo htmlspecialchars($productDisplayName); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $statusColors = ['published' => 'success', 'draft' => 'warning', 'archived' => 'secondary'];
                                        $statusNames = ['published' => 'منشور', 'draft' => 'مسودة', 'archived' => 'مؤرشف'];
                                        ?>
                                        <span class="badge bg-<?php echo $statusColors[$content['status']]; ?>">
                                            <?php echo $statusNames[$content['status']]; ?>
                                        </span>
                                        <?php if ($content['is_featured']): ?>
                                            <i class="bi bi-star-fill text-warning ms-1" title="مميز"></i>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo number_format($content['total_views']); ?></span>
                                    </td>
                                    <td>
                                        <small class="text-dark"><?php echo date('Y/m/d H:i', strtotime($content['created_at'])); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="editContent(<?php echo $content['id']; ?>)" title="تعديل">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-success" 
                                                    onclick="toggleStatus(<?php echo $content['id']; ?>, '<?php echo $content['status']; ?>')" title="تغيير الحالة">
                                                <i class="bi bi-arrow-repeat"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteContent(<?php echo $content['id']; ?>)" title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="card-footer">
                        <nav aria-label="تصفح المحتوى">
                            <ul class="pagination justify-content-center mb-0">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                            <i class="bi bi-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                            <i class="bi bi-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Content Modal -->
<div class="modal fade" id="contentModal" tabindex="-1" aria-labelledby="contentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contentModalLabel">إضافة محتوى جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form id="contentForm" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" id="formAction" value="add">
                    <input type="hidden" name="id" id="contentId" value="">

                    <div class="row">
                        <!-- معلومات المؤثر -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">معلومات المؤثر</h6>

                            <div class="mb-3">
                                <label for="influencer_name" class="form-label">اسم المؤثر <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="influencer_name" name="influencer_name" required>
                            </div>

                            <div class="mb-3">
                                <label for="image_url_input" class="form-label">صورة المؤثر</label>
                                <input type="url" class="form-control" id="image_url_input" name="image_url" placeholder="https://example.com/image.jpg">
                                <small class="form-text text-muted">أدخل رابط الصورة الخارجي (مثل: https://example.com/image.jpg)</small>
                            </div>
                        </div>

                        <!-- معلومات المحتوى -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">معلومات المحتوى</h6>

                            <div class="mb-3">
                                <label for="content_type" class="form-label">نوع المحتوى <span class="text-danger">*</span></label>
                                <select class="form-select" id="content_type" name="content_type" required>
                                    <option value="">اختر نوع المحتوى</option>
                                    <option value="video">فيديو</option>
                                    <option value="review">مراجعة</option>
                                    <option value="post">منشور</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="content_title" class="form-label">عنوان المحتوى</label>
                                <input type="text" class="form-control" id="content_title" name="content_title">
                            </div>

                            <div class="mb-3">
                                <label for="category_id" class="form-label">التصنيف</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">بدون تصنيف</option>
                                    <?php foreach ($categories as $cat): ?>
                                        <option value="<?php echo $cat['id']; ?>"><?php echo htmlspecialchars($cat['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- نص المحتوى -->
                    <div class="mb-3">
                        <label for="content_text" class="form-label">نص المحتوى <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="content_text" name="content_text" rows="6" required></textarea>
                    </div>

                    <div class="row">
                        <!-- التقييم والمنتج -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="rating" class="form-label">التقييم</label>
                                <select class="form-select" id="rating" name="rating">
                                    <option value="">بدون تقييم</option>
                                    <option value="5">5 نجوم - ممتاز</option>
                                    <option value="4">4 نجوم - جيد جداً</option>
                                    <option value="3">3 نجوم - جيد</option>
                                    <option value="2">2 نجوم - مقبول</option>
                                    <option value="1">1 نجمة - ضعيف</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="product_id" class="form-label">المنتج المرتبط</label>
                                <select class="form-select" id="product_id" name="product_id">
                                    <option value="">بدون منتج</option>

                                    <optgroup label="المنتجات">
                                        <?php foreach ($products as $product): ?>
                                            <option value="<?php echo $product['id']; ?>"><?php echo htmlspecialchars($product['name']); ?></option>
                                        <?php endforeach; ?>
                                    </optgroup>

                                    <optgroup label="فئات المنتجات">
                                        <?php foreach ($productCategories as $category): ?>
                                            <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                                        <?php endforeach; ?>
                                    </optgroup>
                                </select>
                            </div>
                        </div>

                        <!-- الفيديو والإعدادات -->
                        <div class="col-md-6">
                            <div class="mb-3" id="video_section" style="display: none;">
                                <label for="video_url" class="form-label">رابط الفيديو</label>
                                <input type="url" class="form-control" id="video_url" name="video_url" placeholder="https://youtube.com/watch?v=...">
                                <small class="form-text text-muted">يدعم YouTube, Instagram, TikTok</small>
                            </div>

                            <div class="mb-3" id="video_platform_section" style="display: none;">
                                <label for="video_platform" class="form-label">منصة الفيديو</label>
                                <select class="form-select" id="video_platform" name="video_platform">
                                    <option value="youtube">YouTube</option>
                                    <option value="instagram">Instagram</option>
                                    <option value="tiktok">TikTok</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="sort_order" class="form-label">ترتيب العرض</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" value="0" min="0">
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات النشر -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">حالة النشر</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="published">منشور</option>
                                    <option value="draft">مسودة</option>
                                    <option value="archived">مؤرشف</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured">
                                    <label class="form-check-label" for="is_featured">
                                        محتوى مميز
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> حفظ المحتوى
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Status Toggle Modal -->
<div class="modal fade" id="statusModal" tabindex="-1" aria-labelledby="statusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalLabel">تغيير حالة المحتوى</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form id="statusForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="toggle_status">
                    <input type="hidden" name="id" id="statusContentId">

                    <p>اختر الحالة الجديدة للمحتوى:</p>

                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="new_status" id="status_published" value="published">
                        <label class="form-check-label" for="status_published">
                            <i class="bi bi-check-circle text-success"></i> منشور
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="new_status" id="status_draft" value="draft">
                        <label class="form-check-label" for="status_draft">
                            <i class="bi bi-file-earmark-text text-warning"></i> مسودة
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="new_status" id="status_archived" value="archived">
                        <label class="form-check-label" for="status_archived">
                            <i class="bi bi-archive text-secondary"></i> مؤرشف
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تحديث الحالة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form id="deleteForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="deleteContentId">

                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                        <h4 class="mt-3">هل أنت متأكد؟</h4>
                        <p class="text-muted">سيتم حذف هذا المحتوى نهائياً ولا يمكن التراجع عن هذا الإجراء.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash"></i> حذف نهائياً
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// لا حاجة لإدارة نوع الصورة لأننا نستخدم الروابط الخارجية فقط

// إدارة نوع المحتوى
document.getElementById('content_type').addEventListener('change', function() {
    const videoSection = document.getElementById('video_section');
    const videoPlatformSection = document.getElementById('video_platform_section');

    if (this.value === 'video') {
        videoSection.style.display = 'block';
        videoPlatformSection.style.display = 'block';
    } else {
        videoSection.style.display = 'none';
        videoPlatformSection.style.display = 'none';
        document.getElementById('video_url').value = '';
    }
});

// فتح نموذج الإضافة
function openAddModal() {
    document.getElementById('contentModalLabel').textContent = 'إضافة محتوى جديد';
    document.getElementById('formAction').value = 'add';
    document.getElementById('contentId').value = '';
    document.getElementById('contentForm').reset();

    // إعادة تعيين حقل الصورة
    document.getElementById('image_url_input').value = '';

    // إخفاء قسم الفيديو
    document.getElementById('video_section').style.display = 'none';
    document.getElementById('video_platform_section').style.display = 'none';
}

// تعديل المحتوى
function editContent(id) {
    fetch(`ajax/get_influencer_content.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const content = data.content;

                document.getElementById('contentModalLabel').textContent = 'تعديل المحتوى';
                document.getElementById('formAction').value = 'edit';
                document.getElementById('contentId').value = content.id;

                // ملء البيانات
                document.getElementById('influencer_name').value = content.influencer_name;
                document.getElementById('content_title').value = content.content_title || '';
                document.getElementById('content_text').value = content.content_text;
                document.getElementById('rating').value = content.rating || '';
                document.getElementById('product_id').value = content.product_id || '';
                document.getElementById('video_url').value = content.video_url || '';
                document.getElementById('video_platform').value = content.video_platform || 'youtube';
                document.getElementById('category_id').value = content.category_id || '';
                document.getElementById('status').value = content.status;
                document.getElementById('is_featured').checked = content.is_featured == 1;
                document.getElementById('sort_order').value = content.sort_order;

                // تعيين نوع المحتوى مع التأكد من التحديث الصحيح
                const contentTypeSelect = document.getElementById('content_type');
                contentTypeSelect.value = content.content_type;

                // إطلاق حدث التغيير يدوياً للتأكد من تحديث واجهة المستخدم
                contentTypeSelect.dispatchEvent(new Event('change'));

                // إعداد رابط الصورة
                document.getElementById('image_url_input').value = content.influencer_image || '';

                // إعداد قسم الفيديو بناءً على نوع المحتوى
                const videoSection = document.getElementById('video_section');
                const videoPlatformSection = document.getElementById('video_platform_section');

                if (content.content_type === 'video') {
                    videoSection.style.display = 'block';
                    videoPlatformSection.style.display = 'block';
                } else {
                    videoSection.style.display = 'none';
                    videoPlatformSection.style.display = 'none';
                }

                // فتح النموذج
                new bootstrap.Modal(document.getElementById('contentModal')).show();
            } else {
                alert('حدث خطأ في جلب بيانات المحتوى');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
}

// تغيير الحالة
function toggleStatus(id, currentStatus) {
    document.getElementById('statusContentId').value = id;

    // إلغاء تحديد جميع الخيارات
    document.querySelectorAll('input[name="new_status"]').forEach(radio => {
        radio.checked = false;
    });

    // تحديد الحالة الحالية
    if (currentStatus !== 'published') {
        document.getElementById('status_published').checked = true;
    } else {
        document.getElementById('status_draft').checked = true;
    }

    new bootstrap.Modal(document.getElementById('statusModal')).show();
}

// حذف المحتوى
function deleteContent(id) {
    document.getElementById('deleteContentId').value = id;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// معالجة الرسائل
<?php if (isset($_SESSION['success_message'])): ?>
    showAlert('success', '<?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>');
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    showAlert('error', '<?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>');
<?php endif; ?>

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
        </div>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
</script>

<?php require_once 'includes/footer.php'; ?>
