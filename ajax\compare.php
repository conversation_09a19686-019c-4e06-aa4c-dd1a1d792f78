<?php
/**
 * Product Compare AJAX Handler
 * Professional Arabic E-commerce Product Comparison
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json; charset=utf-8');

try {
    if (!isset($_POST['action']) || !isset($_POST['product_id'])) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
        exit;
    }

    $action = $_POST['action'];
    $productId = (int)$_POST['product_id'];

    // Validate product exists
    $product = fetchOne("SELECT id, name FROM products WHERE id = ? AND status = 'active'", [$productId]);
    if (!$product) {
        echo json_encode(['success' => false, 'message' => 'المنتج غير موجود']);
        exit;
    }

    // Initialize compare session
    if (!isset($_SESSION['compare'])) {
        $_SESSION['compare'] = [];
    }

    switch ($action) {
        case 'add':
            // Limit compare to 4 products maximum
            if (count($_SESSION['compare']) >= 4) {
                echo json_encode([
                    'success' => false,
                    'message' => 'يمكنك مقارنة 4 منتجات كحد أقصى'
                ]);
                break;
            }

            if (!in_array($productId, $_SESSION['compare'])) {
                $_SESSION['compare'][] = $productId;
                
                echo json_encode([
                    'success' => true,
                    'message' => 'تم إضافة المنتج للمقارنة بنجاح!',
                    'compare_count' => count($_SESSION['compare']),
                    'product_name' => $product['name']
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'المنتج موجود بالفعل في المقارنة'
                ]);
            }
            break;

        case 'remove':
            $key = array_search($productId, $_SESSION['compare']);
            if ($key !== false) {
                unset($_SESSION['compare'][$key]);
                $_SESSION['compare'] = array_values($_SESSION['compare']); // Reindex array
                
                echo json_encode([
                    'success' => true,
                    'message' => 'تم إزالة المنتج من المقارنة',
                    'compare_count' => count($_SESSION['compare'])
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'المنتج غير موجود في المقارنة'
                ]);
            }
            break;

        case 'get_count':
            echo json_encode([
                'success' => true,
                'compare_count' => count($_SESSION['compare'])
            ]);
            break;

        case 'get_items':
            if (empty($_SESSION['compare'])) {
                echo json_encode([
                    'success' => true,
                    'items' => [],
                    'count' => 0
                ]);
                break;
            }

            $placeholders = str_repeat('?,', count($_SESSION['compare']) - 1) . '?';
            $compareProducts = fetchAll("
                SELECT 
                    p.id, p.name, p.price, p.discount, p.image, p.image_url_1,
                    p.short_description, p.description, p.specifications,
                    c.name as category_name
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.id IN ($placeholders) AND p.status = 'active'
                ORDER BY FIELD(p.id, " . implode(',', $_SESSION['compare']) . ")
            ", $_SESSION['compare']);

            echo json_encode([
                'success' => true,
                'items' => $compareProducts,
                'count' => count($compareProducts)
            ]);
            break;

        case 'clear':
            $_SESSION['compare'] = [];
            echo json_encode([
                'success' => true,
                'message' => 'تم مسح جميع المنتجات من المقارنة',
                'compare_count' => 0
            ]);
            break;

        default:
            echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
            break;
    }

} catch (Exception $e) {
    error_log("Compare error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ، يرجى المحاولة مرة أخرى'
    ]);
}
?>
