<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'اختبار تحسينات لوحة القيادة';

// Test currency update
try {
    $newCurrency = 'دينار عراقي';
    
    // Check if currency setting exists
    $existing = fetchOne("SELECT id FROM site_settings WHERE setting_key = 'currency'");
    
    if ($existing) {
        // Update existing setting
        updateData('site_settings', ['setting_value' => $newCurrency], 'setting_key = ?', ['currency']);
        $currencyUpdateStatus = "✅ تم تحديث العملة في قاعدة البيانات";
    } else {
        // Insert new setting
        insertData('site_settings', [
            'setting_key' => 'currency',
            'setting_value' => $newCurrency
        ]);
        $currencyUpdateStatus = "✅ تم إضافة إعداد العملة الجديد";
    }
} catch (Exception $e) {
    $currencyUpdateStatus = "❌ خطأ في تحديث العملة: " . $e->getMessage();
}

// Test stats retrieval
$stats = [
    'products' => 0,
    'orders' => 0,
    'pending_orders' => 0,
    'reviews' => 0
];

try {
    $productsCount = fetchOne("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
    $ordersCount = fetchOne("SELECT COUNT(*) as count FROM orders");
    $pendingOrdersCount = fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'");
    $reviewsCount = fetchOne("SELECT COUNT(*) as count FROM reviews WHERE status = 'approved'");
    
    $stats = [
        'products' => ($productsCount && isset($productsCount['count'])) ? $productsCount['count'] : 0,
        'orders' => ($ordersCount && isset($ordersCount['count'])) ? $ordersCount['count'] : 0,
        'pending_orders' => ($pendingOrdersCount && isset($pendingOrdersCount['count'])) ? $pendingOrdersCount['count'] : 0,
        'reviews' => ($reviewsCount && isset($reviewsCount['count'])) ? $reviewsCount['count'] : 0
    ];
    $statsStatus = "✅ تم جلب الإحصائيات بنجاح";
} catch (Exception $e) {
    $statsStatus = "❌ خطأ في جلب الإحصائيات: " . $e->getMessage();
}

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">اختبار تحسينات لوحة القيادة</h4>
                </div>
                <div class="card-body">
                    <h5>1. اختبار تحديث العملة:</h5>
                    <p><?php echo $currencyUpdateStatus; ?></p>
                    <p><strong>العملة الحالية:</strong> <?php echo getSetting('currency', 'غير محدد'); ?></p>
                    <p><strong>مثال على تنسيق السعر:</strong> <?php echo formatPrice(1000); ?></p>
                    
                    <hr>
                    
                    <h5>2. اختبار الإحصائيات:</h5>
                    <p><?php echo $statsStatus; ?></p>
                    <ul>
                        <li>المنتجات النشطة: <?php echo number_format($stats['products']); ?></li>
                        <li>إجمالي الطلبات: <?php echo number_format($stats['orders']); ?></li>
                        <li>الطلبات المعلقة: <?php echo number_format($stats['pending_orders']); ?></li>
                        <li>التقييمات: <?php echo number_format($stats['reviews']); ?></li>
                    </ul>
                    
                    <hr>
                    
                    <h5>3. اختبار البطاقات المحسنة:</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card stats-card">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="stats-icon-container primary">
                                                <i class="bi bi-box text-white fs-4"></i>
                                            </div>
                                        </div>
                                        <div class="ms-3">
                                            <h3 class="stats-number">123</h3>
                                            <p class="stats-label mb-0">مثال على البطاقة</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h5>4. اختبار وظائف JavaScript:</h5>
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <button class="btn btn-primary w-100" onclick="testStatsRefresh()">اختبار تحديث الإحصائيات</button>
                        </div>
                        <div class="col-md-4 mb-2">
                            <button class="btn btn-success w-100" onclick="testNotifications()">اختبار الإشعارات</button>
                        </div>
                        <div class="col-md-4 mb-2">
                            <button class="btn btn-info w-100" onclick="testSidebarToggle()">اختبار تبديل الشريط الجانبي</button>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-4 mb-2">
                            <button class="btn btn-warning w-100" onclick="testMarkAllRead()">اختبار تحديد الكل كمقروء</button>
                        </div>
                        <div class="col-md-4 mb-2">
                            <button class="btn btn-secondary w-100" onclick="testCurrencyUpdate()">اختبار تحديث العملة</button>
                        </div>
                        <div class="col-md-4 mb-2">
                            <button class="btn btn-danger w-100" onclick="clearTestResults()">مسح النتائج</button>
                        </div>
                    </div>

                    <div id="testResults" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testStatsRefresh() {
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = '<div class="alert alert-info">جاري اختبار تحديث الإحصائيات...</div>';

    fetch('ajax/dashboard_stats.php')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.error) {
                    resultsDiv.innerHTML = '<div class="alert alert-warning">⚠️ خطأ من الخادم: ' + data.message + '</div>';
                } else {
                    resultsDiv.innerHTML = '<div class="alert alert-success">✅ تم اختبار تحديث الإحصائيات بنجاح<br><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                }
            } catch (parseError) {
                resultsDiv.innerHTML = '<div class="alert alert-danger">❌ خطأ في تحليل JSON: ' + parseError.message + '<br><strong>الاستجابة:</strong><pre>' + text + '</pre></div>';
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = '<div class="alert alert-danger">❌ خطأ في اختبار الإحصائيات: ' + error.message + '</div>';
        });
}

function testNotifications() {
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = '<div class="alert alert-info">جاري اختبار الإشعارات...</div>';

    fetch('ajax/notifications.php')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.error) {
                    resultsDiv.innerHTML = '<div class="alert alert-warning">⚠️ خطأ من الخادم: ' + data.message + '</div>';
                } else {
                    resultsDiv.innerHTML = '<div class="alert alert-success">✅ تم اختبار الإشعارات بنجاح<br><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                }
            } catch (parseError) {
                resultsDiv.innerHTML = '<div class="alert alert-danger">❌ خطأ في تحليل JSON: ' + parseError.message + '<br><strong>الاستجابة:</strong><pre>' + text + '</pre></div>';
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = '<div class="alert alert-danger">❌ خطأ في اختبار الإشعارات: ' + error.message + '</div>';
        });
}

function testSidebarToggle() {
    const resultsDiv = document.getElementById('testResults');
    const sidebar = document.getElementById('sidebar');
    const toggleBtn = document.getElementById('sidebarToggleBtn');

    if (sidebar && toggleBtn) {
        const wasCollapsed = sidebar.classList.contains('collapsed');
        toggleSidebarDesktop();
        const isNowCollapsed = sidebar.classList.contains('collapsed');

        resultsDiv.innerHTML = '<div class="alert alert-success">✅ تم اختبار تبديل الشريط الجانبي بنجاح<br>' +
            'الحالة السابقة: ' + (wasCollapsed ? 'مطوي' : 'مفتوح') + '<br>' +
            'الحالة الحالية: ' + (isNowCollapsed ? 'مطوي' : 'مفتوح') + '</div>';
    } else {
        resultsDiv.innerHTML = '<div class="alert alert-danger">❌ لم يتم العثور على الشريط الجانبي أو زر التبديل</div>';
    }
}

function testMarkAllRead() {
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = '<div class="alert alert-info">جاري اختبار تحديد الكل كمقروء...</div>';

    fetch('ajax/mark_notifications_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
    })
    .then(text => {
        try {
            const data = JSON.parse(text);
            if (data.success) {
                resultsDiv.innerHTML = '<div class="alert alert-success">✅ تم اختبار تحديد الكل كمقروء بنجاح<br><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
            } else {
                resultsDiv.innerHTML = '<div class="alert alert-warning">⚠️ ' + data.message + '</div>';
            }
        } catch (parseError) {
            resultsDiv.innerHTML = '<div class="alert alert-danger">❌ خطأ في تحليل JSON: ' + parseError.message + '<br><strong>الاستجابة:</strong><pre>' + text + '</pre></div>';
        }
    })
    .catch(error => {
        resultsDiv.innerHTML = '<div class="alert alert-danger">❌ خطأ في اختبار تحديد الكل كمقروء: ' + error.message + '</div>';
    });
}

function testCurrencyUpdate() {
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = '<div class="alert alert-info">جاري اختبار تحديث العملة...</div>';

    // Test the getSetting function and formatPrice
    const currentCurrency = '<?php echo getSetting('currency'); ?>';
    const samplePrice = <?php echo formatPrice(1000); ?>;

    resultsDiv.innerHTML = '<div class="alert alert-success">✅ تم اختبار العملة بنجاح<br>' +
        'العملة الحالية: ' + currentCurrency + '<br>' +
        'مثال على السعر: ' + samplePrice + '</div>';
}

function clearTestResults() {
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = '<div class="alert alert-secondary">تم مسح النتائج</div>';
    setTimeout(() => {
        resultsDiv.innerHTML = '';
    }, 1000);
}
</script>

<?php require_once 'includes/footer.php'; ?>
