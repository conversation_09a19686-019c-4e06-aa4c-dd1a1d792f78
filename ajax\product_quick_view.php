<?php
/**
 * Product Quick View AJAX Handler
 * Professional Arabic E-commerce Quick Product Preview
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

try {
    if (!isset($_GET['id']) || empty($_GET['id'])) {
        echo '<div class="alert alert-danger">معرف المنتج مطلوب</div>';
        exit;
    }

    $productId = (int)$_GET['id'];

    // Fetch product details
    $product = fetchOne("
        SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.id = ? AND p.status = 'active'
    ", [$productId]);

    if (!$product) {
        echo '<div class="alert alert-danger">المنتج غير موجود</div>';
        exit;
    }

    // Get product images
    $images = [];
    for ($i = 1; $i <= 5; $i++) {
        if (!empty($product['image_url_' . $i])) {
            $images[] = $product['image_url_' . $i];
        }
    }
    if (!empty($product['image'])) {
        $images[] = UPLOAD_URL . '/' . $product['image'];
    }
    if (empty($images)) {
        $images[] = 'https://via.placeholder.com/400x400/f8f9fa/6c757d?text=' . urlencode('صورة المنتج');
    }

    // Calculate discounted price
    $originalPrice = $product['price'];
    $discountedPrice = $originalPrice;
    if ($product['discount'] > 0) {
        $discountedPrice = $originalPrice - ($originalPrice * $product['discount'] / 100);
    }
?>

<div class="quick-view-content">
    <div class="row">
        <!-- Product Images -->
        <div class="col-md-6">
            <div class="product-images">
                <div class="main-image mb-3">
                    <img src="<?php echo $images[0]; ?>" 
                         class="img-fluid rounded" 
                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                         id="quickViewMainImage">
                </div>
                
                <?php if (count($images) > 1): ?>
                <div class="image-thumbnails">
                    <div class="row g-2">
                        <?php foreach ($images as $index => $image): ?>
                            <div class="col-3">
                                <img src="<?php echo $image; ?>" 
                                     class="img-fluid rounded thumbnail-image <?php echo $index === 0 ? 'active' : ''; ?>" 
                                     alt="صورة <?php echo $index + 1; ?>"
                                     onclick="changeQuickViewImage('<?php echo $image; ?>', this)">
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Product Details -->
        <div class="col-md-6">
            <div class="product-details">
                <h4 class="product-title mb-3"><?php echo htmlspecialchars($product['name']); ?></h4>
                
                <div class="product-category mb-2">
                    <small class="text-muted">
                        <i class="bi bi-tag"></i> <?php echo htmlspecialchars($product['category_name']); ?>
                    </small>
                </div>

                <div class="product-price mb-3">
                    <?php if ($product['discount'] > 0): ?>
                        <span class="current-price h4 text-primary"><?php echo formatPrice($discountedPrice); ?></span>
                        <span class="original-price text-muted text-decoration-line-through ms-2"><?php echo formatPrice($originalPrice); ?></span>
                        <span class="discount-badge bg-danger text-white px-2 py-1 rounded ms-2">
                            خصم <?php echo $product['discount']; ?>%
                        </span>
                    <?php else: ?>
                        <span class="current-price h4 text-primary"><?php echo formatPrice($originalPrice); ?></span>
                    <?php endif; ?>
                </div>

                <div class="product-description mb-4">
                    <p><?php echo htmlspecialchars($product['short_description']); ?></p>
                </div>

                <?php if (!empty($product['specifications'])): ?>
                <div class="product-specs mb-4">
                    <h6>المواصفات:</h6>
                    <div class="specs-content">
                        <?php echo nl2br(htmlspecialchars($product['specifications'])); ?>
                    </div>
                </div>
                <?php endif; ?>

                <div class="product-actions">
                    <div class="row g-2">
                        <div class="col-md-8">
                            <button class="btn btn-primary w-100" 
                                    onclick="addToCart(<?php echo $product['id']; ?>, 1)">
                                <i class="bi bi-cart-plus"></i> أضف للسلة
                            </button>
                        </div>
                        <div class="col-md-4">
                            <a href="<?php echo SITE_URL; ?>/product.php?id=<?php echo $product['id']; ?>" 
                               class="btn btn-outline-primary w-100">
                                <i class="bi bi-eye"></i> عرض كامل
                            </a>
                        </div>
                    </div>
                    
                    <div class="row g-2 mt-2">
                        <div class="col-6">
                            <button class="btn btn-outline-secondary w-100" 
                                    onclick="addToWishlist(<?php echo $product['id']; ?>)">
                                <i class="bi bi-heart"></i> المفضلة
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="btn btn-outline-secondary w-100" 
                                    onclick="addToCompare(<?php echo $product['id']; ?>)">
                                <i class="bi bi-arrow-left-right"></i> مقارنة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.quick-view-content {
    padding: 1rem;
}

.thumbnail-image {
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
    border: 2px solid transparent;
}

.thumbnail-image:hover,
.thumbnail-image.active {
    opacity: 1;
    border-color: #667eea;
}

.product-title {
    color: #333;
    font-weight: 600;
}

.current-price {
    font-weight: 700;
}

.original-price {
    font-size: 1rem;
}

.discount-badge {
    font-size: 0.8rem;
    font-weight: 600;
}

.specs-content {
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.9rem;
}

.product-actions .btn {
    border-radius: 0.5rem;
    font-weight: 500;
}
</style>

<script>
function changeQuickViewImage(imageSrc, thumbnail) {
    document.getElementById('quickViewMainImage').src = imageSrc;
    
    // Update active thumbnail
    document.querySelectorAll('.thumbnail-image').forEach(img => {
        img.classList.remove('active');
    });
    thumbnail.classList.add('active');
}
</script>

<?php
} catch (Exception $e) {
    error_log("Quick view error: " . $e->getMessage());
    echo '<div class="alert alert-danger">حدث خطأ في تحميل المنتج</div>';
}
?>
