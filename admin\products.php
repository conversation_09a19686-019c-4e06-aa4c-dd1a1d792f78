<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'إدارة المنتجات';

// معالجة الحذف (POST method - more secure)
if (isset($_POST['delete_product']) && is_numeric($_POST['delete_product'])) {
    $productId = (int)$_POST['delete_product'];

    // تسجيل محاولة الحذف
    error_log("DELETE REQUEST (POST): Attempting to delete product ID $productId");

    // التحقق من وجود المنتج
    $product = fetchOne("SELECT name FROM products WHERE id = ?", [$productId]);

    if ($product) {
        $productName = $product['name'];
        error_log("DELETE REQUEST (POST): Product found - " . $productName);

        try {
            // حذف صور المنتج الإضافية
            $images = fetchAll("SELECT image_path FROM product_images WHERE product_id = ?", [$productId]);
            error_log("DELETE REQUEST (POST): Found " . count($images) . " related images");

            foreach ($images as $image) {
                $imagePath = UPLOAD_PATH . '/products/' . $image['image_path'];
                if (file_exists($imagePath)) {
                    unlink($imagePath);
                    error_log("DELETE REQUEST (POST): Deleted image file - " . $imagePath);
                }
            }

            // حذف المنتج من قاعدة البيانات
            $deleteImages = deleteData('product_images', 'product_id = ?', [$productId]);
            error_log("DELETE REQUEST (POST): Delete images result - " . var_export($deleteImages, true));

            $deleteProduct = deleteData('products', 'id = ?', [$productId]);
            error_log("DELETE REQUEST (POST): Delete product result - " . var_export($deleteProduct, true));

            // التحقق من نجاح الحذف
            if ($deleteProduct !== false && $deleteProduct > 0) {
                $_SESSION['success'] = 'تم حذف المنتج "' . htmlspecialchars($productName) . '" بنجاح';
                error_log("DELETE SUCCESS (POST): Product ID $productId deleted successfully");
            } else {
                // محاولة إضافية للتحقق
                $checkProduct = fetchOne("SELECT id, name FROM products WHERE id = ?", [$productId]);
                if (!$checkProduct) {
                    // المنتج لم يعد موجود، إذن الحذف نجح
                    $_SESSION['success'] = 'تم حذف المنتج "' . htmlspecialchars($productName) . '" بنجاح';
                    error_log("DELETE SUCCESS (POST): Product ID $productId confirmed deleted (verification check)");
                } else {
                    $_SESSION['error'] = 'حدث خطأ أثناء حذف المنتج - المنتج ما زال موجود';
                    error_log("DELETE FAILED (POST): Product ID $productId still exists after delete attempt");
                }
            }

        } catch (Exception $e) {
            $_SESSION['error'] = 'حدث خطأ أثناء حذف المنتج: ' . $e->getMessage();
            error_log("DELETE ERROR (POST): Exception - " . $e->getMessage());
        }
    } else {
        $_SESSION['error'] = 'المنتج غير موجود';
        error_log("DELETE ERROR (POST): Product ID $productId not found");
    }

    // إعادة التوجيه مع تأكيد
    error_log("DELETE REQUEST (POST): Redirecting to products.php");
    header('Location: products.php');
    exit();
}



// معالجة تغيير الحالة
if (isset($_GET['toggle_status']) && is_numeric($_GET['toggle_status'])) {
    $productId = (int)$_GET['toggle_status'];
    $product = fetchOne("SELECT status FROM products WHERE id = ?", [$productId]);

    if ($product) {
        $newStatus = $product['status'] == 'active' ? 'inactive' : 'active';

        try {
            $result = updateData('products', ['status' => $newStatus], 'id = ?', [$productId]);

            if ($result !== false && $result > 0) {
                $_SESSION['success'] = 'تم تحديث حالة المنتج بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء تحديث حالة المنتج (لم يتم تحديث أي صف)';
                error_log("Status toggle failed for product ID $productId. Result: " . var_export($result, true));
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'حدث خطأ أثناء تحديث حالة المنتج: ' . $e->getMessage();
            error_log("Status toggle exception for product ID $productId: " . $e->getMessage());
        }
    } else {
        $_SESSION['error'] = 'المنتج غير موجود';
    }

    header('Location: products.php');
    exit();
}

// الفلترة والبحث
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$category = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$status = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';

$whereClause = "1=1";
$params = [];

if (!empty($search)) {
    $whereClause .= " AND (p.name LIKE ? OR p.description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($category > 0) {
    $whereClause .= " AND p.category_id = ?";
    $params[] = $category;
}

if (!empty($status)) {
    $whereClause .= " AND p.status = ?";
    $params[] = $status;
}

// الترقيم
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 20;
$offset = ($page - 1) * $perPage;

// عدد المنتجات الإجمالي
$countResult = fetchOne("SELECT COUNT(*) as total FROM products p WHERE $whereClause", $params);
$totalProducts = ($countResult && isset($countResult['total'])) ? $countResult['total'] : 0;
$totalPages = ceil($totalProducts / $perPage);

// جلب المنتجات
$products = fetchAll("
    SELECT p.*, c.name as category_name 
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    WHERE $whereClause 
    ORDER BY p.created_at DESC 
    LIMIT $perPage OFFSET $offset
", $params);

// جلب التصنيفات للفلتر
$categories = fetchAll("SELECT id, name FROM categories ORDER BY name");
if (!$categories) {
    $categories = [];
}

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-professional fade-in-professional">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0 text-professional-dark">
                                <i class="bi bi-box-seam text-professional-primary"></i> إدارة المنتجات
                            </h5>
                            <small class="text-professional-muted">إجمالي المنتجات: <?php echo $totalProducts; ?></small>
                        </div>
                        <div class="btn-group-professional">
                            <a href="migrate_products_table.php" class="btn-professional btn-outline-secondary btn-sm hover-lift-professional" title="تحديث قاعدة البيانات">
                                <i class="bi bi-database-up"></i>
                                تحديث قاعدة البيانات
                            </a>
                            <a href="add_product.php" class="btn-professional btn-primary hover-lift-professional">
                                <i class="bi bi-plus-circle"></i> إضافة منتج جديد
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- رسائل النجاح والخطأ -->
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert-professional alert-success slide-up-professional" role="alert">
                            <i class="bi bi-check-circle-fill alert-icon"></i>
                            <div class="alert-content">
                                <strong>تم بنجاح!</strong><br>
                                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert-professional alert-danger slide-up-professional" role="alert">
                            <i class="bi bi-exclamation-triangle-fill alert-icon"></i>
                            <div class="alert-content">
                                <strong>حدث خطأ!</strong><br>
                                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
                        </div>
                    <?php endif; ?>

                    <!-- فلاتر البحث -->
                    <div class="search-filter-professional slide-up-professional">
                        <form method="GET" class="form-row-professional">
                            <div class="form-group-professional">
                                <label class="form-label">البحث في المنتجات</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-professional-light border-professional">
                                        <i class="bi bi-search text-professional-primary"></i>
                                    </span>
                                    <input type="text" class="form-control focus-professional" name="search"
                                           placeholder="اسم المنتج أو الوصف..." value="<?php echo htmlspecialchars($search); ?>">
                                </div>
                            </div>
                            <div class="form-group-professional">
                                <label class="form-label">التصنيف</label>
                                <select name="category" class="form-select focus-professional">
                                    <option value="">جميع التصنيفات</option>
                                    <?php foreach ($categories as $cat): ?>
                                        <option value="<?php echo $cat['id']; ?>"
                                                <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($cat['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="form-group-professional">
                                <label class="form-label">الحالة</label>
                                <select name="status" class="form-select focus-professional">
                                    <option value="">جميع الحالات</option>
                                    <option value="active" <?php echo $status == 'active' ? 'selected' : ''; ?>>نشط</option>
                                    <option value="inactive" <?php echo $status == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                                </select>
                            </div>
                            <div class="form-group-professional">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn-professional btn-primary hover-lift-professional" style="width: 100%;">
                                    <i class="bi bi-funnel"></i> فلترة
                                </button>
                            </div>
                        </form>

                        <?php if (!empty($search) || !empty($category) || !empty($status)): ?>
                            <div class="mt-3 text-center">
                                <a href="products.php" class="btn-professional btn-outline-secondary btn-sm hover-lift-professional">
                                    <i class="bi bi-x-circle"></i> مسح الفلاتر
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- جدول المنتجات -->
                    <?php if (!empty($products)): ?>
                        <div class="table-responsive">
                            <table class="table-professional slide-up-professional">
                                <thead>
                                    <tr>
                                        <th width="80">الصورة</th>
                                        <th>اسم المنتج</th>
                                        <th width="120">التصنيف</th>
                                        <th width="100">السعر</th>
                                        <th width="80">الخصم</th>
                                        <th width="80">المخزون</th>
                                        <th width="100">الحالة</th>
                                        <th width="60">مميز</th>
                                        <th width="140">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($products as $product): ?>
                                        <tr>
                                            <td>
                                                <?php
                                                $imageUrl = null;
                                                if (!empty($product['image_url_1'])) {
                                                    $imageUrl = $product['image_url_1'];
                                                } elseif (!empty($product['image'])) {
                                                    $imageUrl = UPLOAD_URL . '/products/' . $product['image'];
                                                }
                                                ?>

                                                <?php if ($imageUrl): ?>
                                                    <img src="<?php echo $imageUrl; ?>"
                                                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                         style="width: 60px; height: 60px; object-fit: cover;"
                                                         class="rounded shadow-sm">
                                                <?php else: ?>
                                                    <div class="bg-light d-flex align-items-center justify-content-center rounded shadow-sm"
                                                         style="width: 60px; height: 60px;">
                                                        <i class="bi bi-image text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="fw-bold text-dark mb-1">
                                                    <?php echo htmlspecialchars($product['name']); ?>
                                                </div>
                                                <?php if (!empty($product['short_description'])): ?>
                                                    <small class="text-muted d-block">
                                                        <?php echo htmlspecialchars(mb_substr($product['short_description'], 0, 60)); ?>
                                                        <?php echo mb_strlen($product['short_description']) > 60 ? '...' : ''; ?>
                                                    </small>
                                                <?php endif; ?>
                                                <div class="mt-1">
                                                    <?php
                                                    // عد الصور المتاحة
                                                    $imageCount = 0;
                                                    for ($i = 1; $i <= 5; $i++) {
                                                        if (!empty($product['image_url_' . $i])) $imageCount++;
                                                    }
                                                    if (!empty($product['image'])) $imageCount++;

                                                    // عد الفيديوهات المتاحة
                                                    $videoCount = 0;
                                                    if (!empty($product['video_url'])) $videoCount++;
                                                    if (!empty($product['video_url_1'])) $videoCount++;
                                                    if (!empty($product['video_url_2'])) $videoCount++;
                                                    if (!empty($product['video_url_3'])) $videoCount++;
                                                    ?>

                                                    <?php if ($imageCount > 0): ?>
                                                        <span class="badge-professional badge-info me-1" title="<?php echo $imageCount; ?> صور">
                                                            <i class="bi bi-images"></i> <?php echo $imageCount; ?>
                                                        </span>
                                                    <?php endif; ?>

                                                    <?php if ($videoCount > 0): ?>
                                                        <span class="badge-professional badge-danger me-1" title="<?php echo $videoCount; ?> فيديو">
                                                            <i class="bi bi-play-circle"></i> <?php echo $videoCount; ?>
                                                        </span>
                                                    <?php endif; ?>

                                                    <?php if (!empty($product['ingredients'])): ?>
                                                        <span class="badge-professional badge-secondary me-1" title="يحتوي على مكونات">
                                                            <i class="bi bi-list-ul"></i> مكونات
                                                        </span>
                                                    <?php endif; ?>

                                                    <?php if (!empty($product['usage_instructions'])): ?>
                                                        <span class="badge-professional badge-success me-1" title="يحتوي على تعليمات الاستخدام">
                                                            <i class="bi bi-info-circle"></i> استخدام
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?></td>
                                            <td><?php echo formatPrice($product['price']); ?></td>
                                            <td>
                                                <?php if ($product['discount'] > 0): ?>
                                                    <span class="badge-professional badge-warning"><?php echo $product['discount']; ?>%</span>
                                                <?php else: ?>
                                                    <span class="text-professional-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge-professional <?php echo $product['stock'] <= 5 ? 'badge-warning' : 'badge-success'; ?>">
                                                    <?php echo $product['stock']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="status-indicator-professional <?php echo $product['status'] == 'active' ? 'active' : 'inactive'; ?>">
                                                    <?php echo $product['status'] == 'active' ? 'نشط' : 'غير نشط'; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($product['is_featured']): ?>
                                                    <i class="bi bi-star-fill text-professional-warning" style="font-size: 18px;"></i>
                                                <?php else: ?>
                                                    <i class="bi bi-star text-professional-muted" style="font-size: 18px;"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group-professional">
                                                    <a href="edit_product.php?id=<?php echo $product['id']; ?>"
                                                       class="btn-professional btn-primary btn-sm hover-lift-professional" title="تعديل المنتج">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <a href="?toggle_status=<?php echo $product['id']; ?>"
                                                       class="btn-professional btn-sm <?php echo $product['status'] == 'active' ? 'btn-warning' : 'btn-success'; ?> hover-lift-professional"
                                                       title="<?php echo $product['status'] == 'active' ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                        <i class="bi bi-toggle-<?php echo $product['status'] == 'active' ? 'on' : 'off'; ?>"></i>
                                                    </a>
                                                    <!-- Delete Product -->
                                                    <form method="POST" style="display: inline-block;"
                                                          onsubmit="return handleDeleteProduct(event, '<?php echo htmlspecialchars($product['name'], ENT_QUOTES); ?>');">
                                                        <input type="hidden" name="delete_product" value="<?php echo $product['id']; ?>">
                                                        <button type="submit" class="btn-professional btn-danger btn-sm hover-lift-professional" title="حذف المنتج">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- الترقيم -->
                        <?php if ($totalPages > 1): ?>
                            <div class="pagination-professional">
                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <a class="page-link <?php echo $i == $page ? 'active' : ''; ?>"
                                       href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo $category; ?>&status=<?php echo $status; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endfor; ?>
                            </div>
                        <?php endif; ?>
                        
                    <?php else: ?>
                        <div class="text-center py-5 fade-in-professional">
                            <div class="mb-4">
                                <i class="bi bi-box-seam display-1 text-professional-muted"></i>
                            </div>
                            <h4 class="text-professional-dark mb-3">لا توجد منتجات</h4>
                            <?php if (!empty($search) || !empty($category) || !empty($status)): ?>
                                <p class="text-professional-muted mb-4">لم يتم العثور على منتجات تطابق معايير البحث المحددة</p>
                                <div class="btn-group-professional justify-content-center">
                                    <a href="products.php" class="btn-professional btn-outline-secondary hover-lift-professional">
                                        <i class="bi bi-arrow-clockwise"></i> مسح الفلاتر
                                    </a>
                                    <a href="add_product.php" class="btn-professional btn-primary hover-lift-professional">
                                        <i class="bi bi-plus-circle"></i> إضافة منتج جديد
                                    </a>
                                </div>
                            <?php else: ?>
                                <p class="text-professional-muted mb-4">ابدأ بإضافة منتجك الأول لمتجرك الإلكتروني</p>
                                <a href="add_product.php" class="btn-professional btn-primary btn-lg hover-lift-professional">
                                    <i class="bi bi-plus-circle"></i> إضافة منتج جديد
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
// Professional delete confirmation handler
async function handleDeleteProduct(event, productName) {
    event.preventDefault();

    const confirmed = await confirmDelete(
        'هل أنت متأكد من حذف هذا المنتج؟',
        'تأكيد حذف المنتج',
        productName
    );

    if (confirmed) {
        event.target.closest('form').submit();
    }

    return false;
}

// إخفاء الرسائل تلقائياً بعد 5 ثوان
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert-professional');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            alert.style.opacity = '0';
            alert.style.transform = 'translateY(-20px)';
            setTimeout(() => alert.remove(), 300);
        }, 5000);
    });

    // Add smooth animations to table rows
    const tableRows = document.querySelectorAll('.table-professional tbody tr');
    tableRows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
