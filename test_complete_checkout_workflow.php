<?php
/**
 * Test complete checkout workflow
 */

require_once 'config/config.php';

echo "<h1>🎯 Complete Checkout Workflow Test</h1>";
echo "<p>This test verifies the entire checkout process from cart to admin dashboard.</p>";

// Start session
if (!isset($_SESSION)) {
    session_start();
}

// Step 1: Setup test environment
echo "<h2>Step 1: Test Environment Setup</h2>";

// Clear any existing cart
clearCart();
echo "✅ Cleared existing cart<br>";

// Check if we have products
$products = fetchAll("SELECT id, name, price, stock FROM products WHERE status = 'active' LIMIT 3");
if (count($products) === 0) {
    echo "❌ No products found. Creating test products...<br>";
    
    // Create test products
    $testProducts = [
        ['name' => 'منتج اختبار 1', 'description' => 'منتج للاختبار', 'price' => 25000, 'stock' => 100, 'status' => 'active', 'category_id' => 1],
        ['name' => 'منتج اختبار 2', 'description' => 'منتج آخر للاختبار', 'price' => 35000, 'stock' => 50, 'status' => 'active', 'category_id' => 1]
    ];
    
    foreach ($testProducts as $product) {
        $productId = insertData('products', $product);
        if ($productId) {
            echo "✅ Created test product: {$product['name']} (ID: $productId)<br>";
            $products[] = ['id' => $productId, 'name' => $product['name'], 'price' => $product['price'], 'stock' => $product['stock']];
        }
    }
}

echo "Available products: " . count($products) . "<br>";

// Step 2: Add products to cart
echo "<h2>Step 2: Cart Setup</h2>";
$cartProducts = array_slice($products, 0, 2); // Take first 2 products
foreach ($cartProducts as $product) {
    addToCart($product['id'], 2);
    echo "✅ Added product {$product['id']} ({$product['name']}) to cart - Quantity: 2<br>";
}

$cart = getCart();
echo "Cart contents: " . json_encode($cart) . "<br>";
echo "Cart item count: " . getCartItemCount() . "<br>";

if (empty($cart)) {
    echo "❌ Cart is empty - cannot proceed with checkout test<br>";
    exit;
}

// Step 3: Test checkout form submission
echo "<h2>Step 3: Checkout Form Submission Test</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_complete_checkout'])) {
    echo "<h3>Processing Checkout Form...</h3>";
    
    // Simulate the checkout process
    $_POST['place_order'] = '1';
    $_POST['customer_name'] = 'Complete Workflow Test Customer';
    $_POST['customer_phone'] = '07123456789';
    $_POST['address'] = 'Complete Test Address, Baghdad, Iraq';
    $_POST['province'] = 'بغداد';
    $_POST['payment_method'] = 'cash_on_delivery';
    $_POST['notes'] = 'Complete workflow test order - should appear in admin panel';
    
    // Capture output from checkout processing
    ob_start();
    
    // Include checkout processing (simulate)
    $customerName = sanitizeInput($_POST['customer_name']);
    $customerPhone = sanitizeInput($_POST['customer_phone']);
    $address = sanitizeInput($_POST['address']);
    $province = sanitizeInput($_POST['province']);
    $paymentMethod = sanitizeInput($_POST['payment_method']);
    $notes = sanitizeInput($_POST['notes']);
    
    echo "Form data processed:<br>";
    echo "- Customer: $customerName<br>";
    echo "- Phone: $customerPhone<br>";
    echo "- Province: $province<br>";
    
    // Get cart and calculate totals
    $cart = getCart();
    if (empty($cart)) {
        echo "❌ Cart is empty during checkout!<br>";
        exit;
    }
    
    // Get product details
    $productIds = array_keys($cart);
    $placeholders = str_repeat('?,', count($productIds) - 1) . '?';
    
    $products = fetchAll("
        SELECT id, name, price, discount, image, stock 
        FROM products 
        WHERE id IN ($placeholders) AND status = 'active'
    ", $productIds);
    
    if (empty($products)) {
        echo "❌ No products found for cart items<br>";
        exit;
    }
    
    // Calculate cart items and totals
    $cartItems = [];
    $subtotal = 0;
    
    foreach ($products as $product) {
        $quantity = $cart[$product['id']];
        $price = $product['price'];
        
        if ($product['discount'] > 0) {
            $price = $price - ($price * $product['discount'] / 100);
        }
        
        $total = $price * $quantity;
        $subtotal += $total;
        
        $cartItems[] = [
            'product' => $product,
            'quantity' => $quantity,
            'price' => $price,
            'total' => $total
        ];
    }
    
    echo "✅ Cart calculations completed - Subtotal: " . number_format($subtotal) . " IQD<br>";
    
    // Calculate delivery and final total
    $deliveryPrice = 5000; // Default for Baghdad
    $discountAmount = 0;
    $finalTotal = $subtotal - $discountAmount + $deliveryPrice;
    
    echo "✅ Final calculations - Delivery: " . number_format($deliveryPrice) . " IQD, Total: " . number_format($finalTotal) . " IQD<br>";
    
    // Create order
    try {
        $pdo->beginTransaction();
        
        $orderData = [
            'customer_name' => $customerName,
            'customer_phone' => $customerPhone,
            'address' => $address,
            'province' => $province,
            'subtotal' => $subtotal,
            'delivery_price' => $deliveryPrice,
            'discount_amount' => $discountAmount,
            'total_price' => $finalTotal,
            'payment_method' => $paymentMethod,
            'notes' => $notes,
            'status' => 'pending'
        ];
        
        echo "<h4>Creating Order...</h4>";
        $orderId = insertData('orders', $orderData);
        
        if ($orderId) {
            echo "✅ Order created successfully - ID: $orderId<br>";
            
            // Insert order items
            echo "<h4>Creating Order Items...</h4>";
            $itemsCreated = 0;
            foreach ($cartItems as $item) {
                $orderItemData = [
                    'order_id' => $orderId,
                    'product_id' => $item['product']['id'],
                    'product_name' => $item['product']['name'],
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                    'total' => $item['total']
                ];
                
                $itemId = insertData('order_items', $orderItemData);
                if ($itemId) {
                    $itemsCreated++;
                    echo "✅ Order item created - ID: $itemId<br>";
                }
            }
            
            echo "Total order items created: $itemsCreated<br>";
            
            // Commit transaction
            $pdo->commit();
            echo "✅ Transaction committed<br>";
            
            // Verify order exists
            $verifyOrder = fetchOne("SELECT * FROM orders WHERE id = ?", [$orderId]);
            $verifyItems = fetchAll("SELECT * FROM order_items WHERE order_id = ?", [$orderId]);
            
            if ($verifyOrder && count($verifyItems) > 0) {
                echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
                echo "<h2>🎉 CHECKOUT WORKFLOW SUCCESSFUL!</h2>";
                echo "<h3>Order Details:</h3>";
                echo "<ul>";
                echo "<li><strong>Order ID:</strong> " . $verifyOrder['id'] . "</li>";
                echo "<li><strong>Customer:</strong> " . $verifyOrder['customer_name'] . "</li>";
                echo "<li><strong>Phone:</strong> " . $verifyOrder['customer_phone'] . "</li>";
                echo "<li><strong>Total:</strong> " . number_format($verifyOrder['total_price']) . " IQD</li>";
                echo "<li><strong>Status:</strong> " . $verifyOrder['status'] . "</li>";
                echo "<li><strong>Items:</strong> " . count($verifyItems) . "</li>";
                echo "<li><strong>Created:</strong> " . $verifyOrder['created_at'] . "</li>";
                echo "</ul>";
                echo "</div>";
                
                echo "<h3>🔗 Verification Links:</h3>";
                echo "<ul>";
                echo "<li><a href='admin/orders.php' target='_blank'><strong>Check Admin Orders Panel</strong></a></li>";
                echo "<li><a href='order-success.php?order=$orderId' target='_blank'><strong>View Order Success Page</strong></a></li>";
                echo "</ul>";
                
                // Test cart clearing
                echo "<h4>Testing Cart Clearing...</h4>";
                clearCart();
                $clearedCart = getCart();
                if (empty($clearedCart)) {
                    echo "✅ Cart cleared successfully<br>";
                } else {
                    echo "❌ Cart clearing failed<br>";
                }
                
            } else {
                echo "❌ Order verification failed<br>";
            }
            
        } else {
            echo "❌ Order creation failed<br>";
            $pdo->rollBack();
        }
        
    } catch (Exception $e) {
        echo "❌ Exception during checkout: " . $e->getMessage() . "<br>";
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
    }
    
    $output = ob_get_clean();
    echo $output;
    
} else {
    // Show test form
    echo "<h3>Submit Test Checkout Form</h3>";
    echo "<p>This will test the complete checkout workflow:</p>";
    
    echo '<form method="POST" style="max-width: 600px; border: 2px solid #007bff; padding: 20px; border-radius: 10px; background: #f8f9fa;">';
    echo '<input type="hidden" name="test_complete_checkout" value="1">';
    
    echo '<h4 style="color: #007bff; margin-bottom: 20px;">🛒 Complete Checkout Test</h4>';
    
    echo '<div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin-bottom: 20px;">';
    echo '<h5>What this test will do:</h5>';
    echo '<ul>';
    echo '<li>✅ Process checkout form with test data</li>';
    echo '<li>✅ Create order in database</li>';
    echo '<li>✅ Create order items</li>';
    echo '<li>✅ Clear shopping cart</li>';
    echo '<li>✅ Verify order appears in admin panel</li>';
    echo '</ul>';
    echo '</div>';
    
    echo '<button type="submit" style="background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 18px; cursor: pointer; width: 100%;">';
    echo '<strong>🚀 Test Complete Checkout Workflow</strong>';
    echo '</button>';
    
    echo '</form>';
}

echo "<h2>Current System Status</h2>";
$currentOrders = fetchAll("SELECT COUNT(*) as count FROM orders");
$orderCount = $currentOrders[0]['count'] ?? 0;
echo "Total orders in database: <strong>$orderCount</strong><br>";

if ($orderCount > 0) {
    $recentOrders = fetchAll("SELECT id, customer_name, total_price, status, created_at FROM orders ORDER BY created_at DESC LIMIT 3");
    echo "<h4>Recent Orders:</h4>";
    echo "<ul>";
    foreach ($recentOrders as $order) {
        echo "<li>ID: {$order['id']} - {$order['customer_name']} - " . number_format($order['total_price']) . " IQD - {$order['status']} - {$order['created_at']}</li>";
    }
    echo "</ul>";
}

echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
