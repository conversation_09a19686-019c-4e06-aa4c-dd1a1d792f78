<?php
/**
 * PHP Configuration Check
 * Displays PHP configuration and checks for common issues
 */

// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>فحص إعدادات PHP - PHP Configuration Check</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background: #f8f9fa; }";
echo "h1, h2, h3 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".config-section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }";
echo "table { width: 100%; border-collapse: collapse; margin: 10px 0; }";
echo "th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }";
echo "th { background-color: #f2f2f2; }";
echo ".btn { display: inline-block; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 3px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 فحص إعدادات PHP</h1>";
echo "<p class='info'>فحص شامل لإعدادات PHP والمتطلبات اللازمة للموقع</p>";

// Basic PHP Info
echo "<div class='config-section'>";
echo "<h2>معلومات PHP الأساسية</h2>";
echo "<table>";
echo "<tr><th>الإعداد</th><th>القيمة</th><th>الحالة</th></tr>";

$phpVersion = PHP_VERSION;
$versionOk = version_compare($phpVersion, '7.4.0', '>=');
echo "<tr><td><strong>إصدار PHP</strong></td><td>$phpVersion</td><td>" . ($versionOk ? '<span style="color: green;">✅ جيد</span>' : '<span style="color: red;">❌ قديم</span>') . "</td></tr>";

echo "<tr><td><strong>نظام التشغيل</strong></td><td>" . PHP_OS . "</td><td><span style='color: green;'>✅</span></td></tr>";
echo "<tr><td><strong>معمارية النظام</strong></td><td>" . php_uname('m') . "</td><td><span style='color: green;'>✅</span></td></tr>";
echo "<tr><td><strong>خادم الويب</strong></td><td>" . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد') . "</td><td><span style='color: green;'>✅</span></td></tr>";

$maxExecutionTime = ini_get('max_execution_time');
$maxExecutionOk = $maxExecutionTime >= 30;
echo "<tr><td><strong>أقصى وقت تنفيذ</strong></td><td>{$maxExecutionTime} ثانية</td><td>" . ($maxExecutionOk ? '<span style="color: green;">✅ جيد</span>' : '<span style="color: orange;">⚠️ قصير</span>') . "</td></tr>";

$memoryLimit = ini_get('memory_limit');
echo "<tr><td><strong>حد الذاكرة</strong></td><td>$memoryLimit</td><td><span style='color: green;'>✅</span></td></tr>";

$uploadMaxFilesize = ini_get('upload_max_filesize');
echo "<tr><td><strong>أقصى حجم ملف</strong></td><td>$uploadMaxFilesize</td><td><span style='color: green;'>✅</span></td></tr>";

$postMaxSize = ini_get('post_max_size');
echo "<tr><td><strong>أقصى حجم POST</strong></td><td>$postMaxSize</td><td><span style='color: green;'>✅</span></td></tr>";

echo "</table>";
echo "</div>";

// Required Extensions
echo "<div class='config-section'>";
echo "<h2>الإضافات المطلوبة</h2>";
echo "<table>";
echo "<tr><th>الإضافة</th><th>الحالة</th><th>الإصدار</th><th>الوصف</th></tr>";

$requiredExtensions = [
    'pdo' => 'قاعدة البيانات PDO',
    'pdo_mysql' => 'MySQL لـ PDO',
    'mysqli' => 'MySQL التقليدي',
    'gd' => 'معالجة الصور',
    'session' => 'إدارة الجلسات',
    'json' => 'معالجة JSON',
    'mbstring' => 'النصوص متعددة البايت',
    'curl' => 'طلبات HTTP',
    'openssl' => 'التشفير',
    'zip' => 'ضغط الملفات'
];

$missingExtensions = [];
foreach ($requiredExtensions as $ext => $desc) {
    $loaded = extension_loaded($ext);
    $version = $loaded ? phpversion($ext) : 'غير متوفر';
    $status = $loaded ? '<span style="color: green;">✅ متوفر</span>' : '<span style="color: red;">❌ مفقود</span>';
    
    echo "<tr><td><strong>$ext</strong></td><td>$status</td><td>$version</td><td>$desc</td></tr>";
    
    if (!$loaded) {
        $missingExtensions[] = $ext;
    }
}

echo "</table>";

if (!empty($missingExtensions)) {
    echo "<div class='error'>";
    echo "<h3>❌ إضافات مفقودة</h3>";
    echo "<p>الإضافات التالية مطلوبة لعمل الموقع بشكل صحيح:</p>";
    echo "<ul>";
    foreach ($missingExtensions as $ext) {
        echo "<li><strong>$ext:</strong> " . $requiredExtensions[$ext] . "</li>";
    }
    echo "</ul>";
    echo "<p><strong>الحل:</strong> تأكد من تفعيل هذه الإضافات في ملف php.ini</p>";
    echo "</div>";
} else {
    echo "<div class='success'>✅ جميع الإضافات المطلوبة متوفرة</div>";
}

echo "</div>";

// Database Connection Test
echo "<div class='config-section'>";
echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

try {
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $database = 'shop_db';
    
    // Test MySQL connection
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>✅ الاتصال بخادم MySQL نجح</div>";
    
    // Check if database exists
    $databases = $pdo->query("SHOW DATABASES")->fetchAll(PDO::FETCH_COLUMN);
    $dbExists = in_array($database, $databases);
    
    if ($dbExists) {
        echo "<div class='success'>✅ قاعدة البيانات '$database' موجودة</div>";
        
        // Connect to database and check tables
        $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($tables)) {
            echo "<div class='success'>✅ قاعدة البيانات تحتوي على " . count($tables) . " جدول</div>";
            echo "<p><strong>الجداول الموجودة:</strong> " . implode(', ', $tables) . "</p>";
        } else {
            echo "<div class='warning'>⚠️ قاعدة البيانات فارغة</div>";
        }
        
    } else {
        echo "<div class='warning'>⚠️ قاعدة البيانات '$database' غير موجودة</div>";
    }
    
} catch (PDOException $e) {
    echo "<div class='error'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
}

echo "</div>";

// File Permissions
echo "<div class='config-section'>";
echo "<h2>صلاحيات الملفات</h2>";

$checkDirs = [
    'uploads' => 'مجلد الرفع',
    'uploads/products' => 'صور المنتجات',
    'uploads/categories' => 'صور التصنيفات',
    'config' => 'ملفات التكوين'
];

echo "<table>";
echo "<tr><th>المجلد</th><th>الوجود</th><th>القراءة</th><th>الكتابة</th><th>الحالة</th></tr>";

foreach ($checkDirs as $dir => $desc) {
    $exists = is_dir($dir);
    $readable = $exists ? is_readable($dir) : false;
    $writable = $exists ? is_writable($dir) : false;
    
    $existsStatus = $exists ? '<span style="color: green;">✅</span>' : '<span style="color: red;">❌</span>';
    $readableStatus = $readable ? '<span style="color: green;">✅</span>' : '<span style="color: red;">❌</span>';
    $writableStatus = $writable ? '<span style="color: green;">✅</span>' : '<span style="color: red;">❌</span>';
    
    $overallStatus = ($exists && $readable && $writable) ? 
        '<span style="color: green;">✅ جيد</span>' : 
        '<span style="color: red;">❌ مشكلة</span>';
    
    echo "<tr><td><strong>$dir</strong><br><small>$desc</small></td><td>$existsStatus</td><td>$readableStatus</td><td>$writableStatus</td><td>$overallStatus</td></tr>";
}

echo "</table>";
echo "</div>";

// Error Reporting
echo "<div class='config-section'>";
echo "<h2>إعدادات الأخطاء</h2>";

$displayErrors = ini_get('display_errors');
$logErrors = ini_get('log_errors');
$errorReporting = error_reporting();

echo "<table>";
echo "<tr><th>الإعداد</th><th>القيمة الحالية</th><th>التوصية</th></tr>";
echo "<tr><td><strong>عرض الأخطاء</strong></td><td>" . ($displayErrors ? 'مفعل' : 'معطل') . "</td><td>معطل في الإنتاج</td></tr>";
echo "<tr><td><strong>تسجيل الأخطاء</strong></td><td>" . ($logErrors ? 'مفعل' : 'معطل') . "</td><td>مفعل</td></tr>";
echo "<tr><td><strong>مستوى الأخطاء</strong></td><td>$errorReporting</td><td>E_ALL في التطوير</td></tr>";
echo "</table>";

echo "</div>";

// Quick Actions
echo "<div class='config-section'>";
echo "<h2>إجراءات سريعة</h2>";
echo "<div style='text-align: center;'>";
echo "<a href='fix_website_issues.php' class='btn' style='background: #dc3545;'>🔧 إصلاح المشاكل</a>";
echo "<a href='setup_database.php' class='btn' style='background: #ffc107; color: #212529;'>🗄️ إعداد قاعدة البيانات</a>";
echo "<a href='test_website_functionality.php' class='btn' style='background: #28a745;'>🧪 اختبار الوظائف</a>";
echo "<a href='test_connection.php' class='btn'>🔍 اختبار الاتصال</a>";
echo "<a href='index.php' class='btn' style='background: #17a2b8;'>🏠 الصفحة الرئيسية</a>";
echo "</div>";
echo "</div>";

// Full PHP Info (collapsible)
echo "<div class='config-section'>";
echo "<h2>معلومات PHP الكاملة</h2>";
echo "<p><button onclick=\"document.getElementById('phpinfo').style.display = document.getElementById('phpinfo').style.display === 'none' ? 'block' : 'none';\">عرض/إخفاء معلومات PHP الكاملة</button></p>";
echo "<div id='phpinfo' style='display: none; max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;'>";
ob_start();
phpinfo();
$phpinfo = ob_get_clean();
// Remove HTML tags and keep only the content
$phpinfo = preg_replace('/<style[^>]*>.*?<\/style>/si', '', $phpinfo);
$phpinfo = preg_replace('/<script[^>]*>.*?<\/script>/si', '', $phpinfo);
echo $phpinfo;
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
