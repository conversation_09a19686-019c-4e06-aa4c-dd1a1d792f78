<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'إدارة أكواد الخصم';

// معالجة الحذف
if (isset($_POST['delete_discount_code']) && is_numeric($_POST['delete_discount_code'])) {
    $codeId = (int)$_POST['delete_discount_code'];

    // جلب بيانات كود الخصم للتأكد من وجوده
    $discountCode = fetchOne("SELECT * FROM discount_codes WHERE id = ?", [$codeId]);

    if ($discountCode) {
        // حذف كود الخصم من قاعدة البيانات
        $result = deleteData('discount_codes', 'id = ?', [$codeId]);

        if ($result) {
            $_SESSION['success'] = 'تم حذف كود الخصم "' . htmlspecialchars($discountCode['code']) . '" بنجاح';
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء حذف كود الخصم. يرجى المحاولة مرة أخرى.';
        }
    } else {
        $_SESSION['error'] = 'كود الخصم المطلوب حذفه غير موجود.';
    }

    header('Location: discount_codes.php');
    exit();
}

// معالجة تغيير الحالة
if (isset($_GET['toggle_status']) && is_numeric($_GET['toggle_status'])) {
    $codeId = (int)$_GET['toggle_status'];
    $code = fetchOne("SELECT * FROM discount_codes WHERE id = ?", [$codeId]);

    if ($code) {
        $newStatus = $code['status'] == 'active' ? 'inactive' : 'active';
        $result = updateData('discount_codes', ['status' => $newStatus], 'id = ?', [$codeId]);

        if ($result !== false && $result > 0) {
            $statusText = $newStatus == 'active' ? 'تم تفعيل' : 'تم إلغاء تفعيل';
            $_SESSION['success'] = $statusText . ' كود الخصم "' . htmlspecialchars($code['code']) . '" بنجاح';
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء تحديث حالة كود الخصم. يرجى المحاولة مرة أخرى.';
        }
    } else {
        $_SESSION['error'] = 'كود الخصم المطلوب تحديثه غير موجود.';
    }

    header('Location: discount_codes.php');
    exit();
}

// معالجة إضافة/تعديل كود الخصم
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $code = strtoupper(sanitizeInput($_POST['code']));
    $type = sanitizeInput($_POST['type']);
    $amount = (float)$_POST['amount'];
    $minOrderAmount = (float)($_POST['min_order_amount'] ?? 0);
    $usageLimit = !empty($_POST['usage_limit']) ? (int)$_POST['usage_limit'] : null;
    $expirationDate = !empty($_POST['expiration_date']) ? sanitizeInput($_POST['expiration_date']) : null;
    $status = sanitizeInput($_POST['status']);
    $codeId = isset($_POST['code_id']) ? (int)$_POST['code_id'] : 0;
    
    $errors = [];
    
    if (empty($code)) {
        $errors[] = 'كود الخصم مطلوب';
    } elseif (!preg_match('/^[A-Z0-9]+$/', $code)) {
        $errors[] = 'كود الخصم يجب أن يحتوي على أحرف إنجليزية وأرقام فقط';
    }
    
    if (!in_array($type, ['percentage', 'fixed'])) {
        $errors[] = 'نوع الخصم غير صحيح';
    }
    
    if ($amount <= 0) {
        $errors[] = 'قيمة الخصم يجب أن تكون أكبر من صفر';
    }
    
    if ($type == 'percentage' && $amount > 100) {
        $errors[] = 'نسبة الخصم لا يمكن أن تزيد عن 100%';
    }
    
    // التحقق من عدم تكرار الكود
    $existingCode = fetchOne("SELECT id FROM discount_codes WHERE code = ? AND id != ?", [$code, $codeId]);
    if ($existingCode) {
        $errors[] = 'كود الخصم موجود مسبقاً';
    }
    
    if (empty($errors)) {
        $codeData = [
            'code' => $code,
            'type' => $type,
            'amount' => $amount,
            'min_order_amount' => $minOrderAmount,
            'usage_limit' => $usageLimit,
            'expiration_date' => $expirationDate,
            'status' => $status
        ];
        
        if ($codeId > 0) {
            // تعديل
            $result = updateData('discount_codes', $codeData, 'id = ?', [$codeId]);
            if ($result !== false && $result > 0) {
                $_SESSION['success'] = 'تم تحديث كود الخصم "' . htmlspecialchars($code) . '" بنجاح';
            } else {
                $errors[] = 'حدث خطأ أثناء تحديث كود الخصم. يرجى التأكد من صحة البيانات والمحاولة مرة أخرى.';
            }
        } else {
            // إضافة
            $result = insertData('discount_codes', $codeData);
            if ($result) {
                $_SESSION['success'] = 'تم إضافة كود الخصم "' . htmlspecialchars($code) . '" بنجاح';
            } else {
                $errors[] = 'حدث خطأ أثناء إضافة كود الخصم. يرجى التأكد من صحة البيانات والمحاولة مرة أخرى.';
            }
        }

        // إعادة التوجيه فقط إذا لم تكن هناك أخطاء
        if (empty($errors)) {
            header('Location: discount_codes.php');
            exit();
        }
    }
}

// جلب أكواد الخصم
$discountCodes = fetchAll("
    SELECT * FROM discount_codes 
    ORDER BY created_at DESC
");

// جلب بيانات كود الخصم للتعديل
$editCode = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $editCode = fetchOne("SELECT * FROM discount_codes WHERE id = ?", [(int)$_GET['edit']]);
}

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- نموذج إضافة/تعديل كود الخصم -->
        <div class="col-md-4">
            <div class="card-professional slide-up-professional">
                <div class="card-header">
                    <h5 class="mb-0 text-professional-dark">
                        <i class="bi bi-<?php echo $editCode ? 'pencil-square' : 'plus-circle'; ?> text-professional-primary"></i>
                        <?php echo $editCode ? 'تعديل كود الخصم' : 'إضافة كود خصم جديد'; ?>
                    </h5>
                </div>

                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert-professional alert-danger slide-up-professional">
                            <i class="bi bi-exclamation-triangle-fill alert-icon"></i>
                            <div class="alert-content">
                                <strong>يرجى تصحيح الأخطاء التالية:</strong>
                                <ul class="mb-0 mt-2">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo $error; ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="form-professional">
                        <?php if ($editCode): ?>
                            <input type="hidden" name="code_id" value="<?php echo $editCode['id']; ?>">
                        <?php endif; ?>

                        <div class="form-group-professional">
                            <label for="code" class="form-label">كود الخصم *</label>
                            <input type="text" class="form-control focus-professional" id="code" name="code"
                                   value="<?php echo htmlspecialchars($editCode['code'] ?? $_POST['code'] ?? ''); ?>"
                                   style="text-transform: uppercase;" required placeholder="أدخل كود الخصم">
                            <div class="form-text text-professional-muted">أحرف إنجليزية وأرقام فقط</div>
                        </div>

                        <div class="form-group-professional">
                            <label for="type" class="form-label">نوع الخصم *</label>
                            <select class="form-select focus-professional" id="type" name="type" required>
                                <option value="percentage" <?php echo ($editCode['type'] ?? 'percentage') == 'percentage' ? 'selected' : ''; ?>>نسبة مئوية</option>
                                <option value="fixed" <?php echo ($editCode['type'] ?? '') == 'fixed' ? 'selected' : ''; ?>>مبلغ ثابت</option>
                            </select>
                        </div>
                        
                        <div class="form-group-professional">
                            <label for="amount" class="form-label">قيمة الخصم *</label>
                            <div class="input-group">
                                <input type="number" class="form-control focus-professional" id="amount" name="amount"
                                       step="0.01" min="0" value="<?php echo $editCode['amount'] ?? $_POST['amount'] ?? ''; ?>" required>
                                <span class="input-group-text bg-professional-light border-professional" id="amount-unit">%</span>
                            </div>
                        </div>

                        <div class="form-group-professional">
                            <label for="min_order_amount" class="form-label">الحد الأدنى للطلب</label>
                            <div class="input-group">
                                <input type="number" class="form-control focus-professional" id="min_order_amount" name="min_order_amount"
                                       step="0.01" min="0" value="<?php echo $editCode['min_order_amount'] ?? $_POST['min_order_amount'] ?? '0'; ?>"
                                       placeholder="0">
                                <span class="input-group-text bg-professional-light border-professional">دينار عراقي</span>
                            </div>
                        </div>

                        <div class="form-group-professional">
                            <label for="usage_limit" class="form-label">حد الاستخدام</label>
                            <input type="number" class="form-control focus-professional" id="usage_limit" name="usage_limit"
                                   min="1" value="<?php echo $editCode['usage_limit'] ?? $_POST['usage_limit'] ?? ''; ?>"
                                   placeholder="غير محدود">
                            <div class="form-text text-professional-muted">اتركه فارغاً للاستخدام غير المحدود</div>
                        </div>

                        <div class="form-group-professional">
                            <label for="expiration_date" class="form-label">تاريخ الانتهاء</label>
                            <input type="date" class="form-control focus-professional" id="expiration_date" name="expiration_date"
                                   value="<?php echo $editCode['expiration_date'] ?? $_POST['expiration_date'] ?? ''; ?>">
                            <div class="form-text text-professional-muted">اتركه فارغاً إذا لم يكن له تاريخ انتهاء</div>
                        </div>

                        <div class="form-group-professional">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select focus-professional" id="status" name="status">
                                <option value="active" <?php echo ($editCode['status'] ?? 'active') == 'active' ? 'selected' : ''; ?>>نشط</option>
                                <option value="inactive" <?php echo ($editCode['status'] ?? '') == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                        
                        <div class="btn-group-professional" style="flex-direction: column; gap: 12px;">
                            <button type="submit" class="btn-professional btn-primary hover-lift-professional">
                                <i class="bi bi-check-circle"></i>
                                <?php echo $editCode ? 'تحديث الكود' : 'إضافة الكود'; ?>
                            </button>
                            <?php if ($editCode): ?>
                                <a href="discount_codes.php" class="btn-professional btn-outline-secondary hover-lift-professional">
                                    <i class="bi bi-x-circle"></i> إلغاء التعديل
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- قائمة أكواد الخصم -->
        <div class="col-md-8">
            <div class="card-professional slide-up-professional">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-professional-dark">
                            <i class="bi bi-percent text-professional-primary"></i> قائمة أكواد الخصم
                        </h5>
                        <span class="badge-professional badge-info">إجمالي الأكواد: <?php echo count($discountCodes); ?></span>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- رسائل النجاح والخطأ -->
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert-professional alert-success slide-up-professional" role="alert">
                            <i class="bi bi-check-circle-fill alert-icon"></i>
                            <div class="alert-content">
                                <strong>تم بنجاح!</strong><br>
                                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert-professional alert-danger slide-up-professional" role="alert">
                            <i class="bi bi-exclamation-triangle-fill alert-icon"></i>
                            <div class="alert-content">
                                <strong>حدث خطأ!</strong><br>
                                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($discountCodes)): ?>
                        <div class="table-responsive">
                            <table class="table-professional">
                                <thead>
                                    <tr>
                                        <th>الكود</th>
                                        <th>النوع</th>
                                        <th>القيمة</th>
                                        <th>الحد الأدنى</th>
                                        <th>الاستخدام</th>
                                        <th>تاريخ الانتهاء</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($discountCodes as $discountCode): ?>
                                        <tr>
                                            <td>
                                                <strong class="font-monospace"><?php echo htmlspecialchars($discountCode['code']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge-professional <?php echo $discountCode['type'] == 'percentage' ? 'badge-info' : 'badge-success'; ?>">
                                                    <?php echo $discountCode['type'] == 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                if ($discountCode['type'] == 'percentage') {
                                                    echo (int)$discountCode['amount'] . '%';
                                                } else {
                                                    // عرض المبلغ الثابت بتنسيق العملة العراقية
                                                    echo formatPrice($discountCode['amount']);
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <?php echo $discountCode['min_order_amount'] > 0 ? formatPrice($discountCode['min_order_amount']) : '-'; ?>
                                            </td>
                                            <td>
                                                <?php if ($discountCode['usage_limit']): ?>
                                                    <?php echo $discountCode['used_count']; ?> / <?php echo $discountCode['usage_limit']; ?>
                                                    <?php if ($discountCode['used_count'] >= $discountCode['usage_limit']): ?>
                                                        <span class="badge-professional badge-danger ms-1">منتهي</span>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <?php echo $discountCode['used_count']; ?> / غير محدود
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($discountCode['expiration_date']): ?>
                                                    <?php 
                                                    $expirationDate = $discountCode['expiration_date'];
                                                    $isExpired = strtotime($expirationDate) < time();
                                                    ?>
                                                    <span class="<?php echo $isExpired ? 'text-danger' : ''; ?>">
                                                        <?php echo date('Y-m-d', strtotime($expirationDate)); ?>
                                                    </span>
                                                    <?php if ($isExpired): ?>
                                                        <br><span class="badge-professional badge-danger">منتهي</span>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-professional-muted">غير محدد</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="status-indicator-professional <?php echo $discountCode['status'] == 'active' ? 'active' : 'inactive'; ?>">
                                                    <?php echo $discountCode['status'] == 'active' ? 'نشط' : 'غير نشط'; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group-professional">
                                                    <a href="?edit=<?php echo $discountCode['id']; ?>"
                                                       class="btn-professional btn-primary btn-sm hover-lift-professional" title="تعديل كود الخصم">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <a href="?toggle_status=<?php echo $discountCode['id']; ?>"
                                                       class="btn-professional btn-sm <?php echo $discountCode['status'] == 'active' ? 'btn-warning' : 'btn-success'; ?> hover-lift-professional"
                                                       title="<?php echo $discountCode['status'] == 'active' ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                        <i class="bi bi-toggle-<?php echo $discountCode['status'] == 'active' ? 'on' : 'off'; ?>"></i>
                                                    </a>
                                                    <form method="POST" style="display: inline-block;"
                                                          onsubmit="return handleDeleteDiscountCode(event, '<?php echo htmlspecialchars($discountCode['code'], ENT_QUOTES); ?>');">
                                                        <input type="hidden" name="delete_discount_code" value="<?php echo $discountCode['id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف كود الخصم">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5 fade-in-professional">
                            <i class="bi bi-percent display-1 text-professional-muted"></i>
                            <h4 class="mt-3 text-professional-dark">لا توجد أكواد خصم</h4>
                            <p class="text-professional-muted">ابدأ بإضافة كود خصم جديد من النموذج على اليسار</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Professional delete confirmation handler
async function handleDeleteDiscountCode(event, discountCode) {
    event.preventDefault();

    const confirmed = await confirmDelete(
        'هل أنت متأكد من حذف هذا الكود؟',
        'تأكيد حذف كود الخصم',
        discountCode
    );

    if (confirmed) {
        event.target.closest('form').submit();
    }

    return false;
}

// تغيير وحدة القياس حسب نوع الخصم
document.getElementById('type').addEventListener('change', function() {
    const amountUnit = document.getElementById('amount-unit');
    const amountInput = document.getElementById('amount');

    if (this.value === 'percentage') {
        amountUnit.textContent = '%';
        amountInput.max = '100';
    } else {
        amountUnit.textContent = 'دينار عراقي';
        amountInput.removeAttribute('max');
    }
});

// تحويل النص إلى أحرف كبيرة
document.getElementById('code').addEventListener('input', function() {
    this.value = this.value.toUpperCase();
});
</script>

<?php require_once 'includes/footer.php'; ?>
