<?php
/**
 * إنشاء الجداول المفقودة
 * Create Missing Tables
 */

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>إنشاء الجداول المفقودة</h2>";

try {
    // تضمين ملفات التكوين
    require_once 'config/config.php';
    
    echo "<p style='color: green;'>✅ تم تضمين ملفات التكوين بنجاح</p>";
    
    // الحصول على اتصال قاعدة البيانات
    $database = new Database();
    $pdo = $database->getConnection();
    
    if (!$pdo) {
        throw new Exception("لا يمكن الاتصال بقاعدة البيانات");
    }
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // قائمة الجداول المطلوبة مع استعلامات الإنشاء
    $tables = [
        'categories' => "
            CREATE TABLE `categories` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(100) NOT NULL,
                `description` text,
                `image` varchar(255) DEFAULT NULL,
                `status` enum('active','inactive') NOT NULL DEFAULT 'active',
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `name` (`name`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ",
        
        'products' => "
            CREATE TABLE `products` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(200) NOT NULL,
                `description` text,
                `short_description` varchar(500) DEFAULT NULL,
                `price` decimal(10,2) NOT NULL,
                `discount` decimal(5,2) DEFAULT '0.00',
                `stock` int(11) NOT NULL DEFAULT '0',
                `category_id` int(11) DEFAULT NULL,
                `image` varchar(255) DEFAULT NULL,
                `is_featured` tinyint(1) NOT NULL DEFAULT '0',
                `status` enum('active','inactive') NOT NULL DEFAULT 'active',
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `category_id` (`category_id`),
                KEY `status` (`status`),
                KEY `is_featured` (`is_featured`),
                CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ",
        
        'orders' => "
            CREATE TABLE `orders` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `customer_name` varchar(100) NOT NULL,
                `customer_phone` varchar(20) NOT NULL,
                `customer_email` varchar(100) DEFAULT NULL,
                `customer_address` text NOT NULL,
                `total_price` decimal(10,2) NOT NULL,
                `delivery_price` decimal(10,2) DEFAULT '0.00',
                `discount_amount` decimal(10,2) DEFAULT '0.00',
                `discount_code` varchar(50) DEFAULT NULL,
                `payment_method` enum('cash_on_delivery','bank_transfer') NOT NULL DEFAULT 'cash_on_delivery',
                `status` enum('pending','confirmed','processing','shipped','delivered','cancelled') NOT NULL DEFAULT 'pending',
                `notes` text,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `status` (`status`),
                KEY `created_at` (`created_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ",
        
        'order_items' => "
            CREATE TABLE `order_items` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `order_id` int(11) NOT NULL,
                `product_id` int(11) NOT NULL,
                `product_name` varchar(200) NOT NULL,
                `product_price` decimal(10,2) NOT NULL,
                `quantity` int(11) NOT NULL,
                `total_price` decimal(10,2) NOT NULL,
                PRIMARY KEY (`id`),
                KEY `order_id` (`order_id`),
                KEY `product_id` (`product_id`),
                CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
                CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ",
        
        'reviews' => "
            CREATE TABLE `reviews` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `product_id` int(11) NOT NULL,
                `customer_name` varchar(100) NOT NULL,
                `customer_email` varchar(100) DEFAULT NULL,
                `rating` tinyint(1) NOT NULL CHECK (`rating` >= 1 AND `rating` <= 5),
                `comment` text,
                `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending',
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `product_id` (`product_id`),
                KEY `status` (`status`),
                CONSTRAINT `reviews_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ",
        
        'discount_codes' => "
            CREATE TABLE `discount_codes` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `code` varchar(50) NOT NULL,
                `type` enum('percentage','fixed') NOT NULL,
                `amount` decimal(10,2) NOT NULL,
                `min_order_amount` decimal(10,2) DEFAULT '0.00',
                `usage_limit` int(11) DEFAULT NULL,
                `used_count` int(11) NOT NULL DEFAULT '0',
                `expiration_date` date DEFAULT NULL,
                `status` enum('active','inactive') NOT NULL DEFAULT 'active',
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `code` (`code`),
                KEY `status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ",
        
        'newsletter' => "
            CREATE TABLE `newsletter` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `email` varchar(100) NOT NULL,
                `status` enum('active','inactive') NOT NULL DEFAULT 'active',
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `email` (`email`),
                KEY `status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ",
        
        'site_settings' => "
            CREATE TABLE `site_settings` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `setting_key` varchar(100) NOT NULL,
                `setting_value` text,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `setting_key` (`setting_key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        "
    ];
    
    echo "<h3>إنشاء الجداول:</h3>";
    
    foreach ($tables as $tableName => $createSQL) {
        echo "<p>إنشاء جدول <strong>$tableName</strong>...</p>";
        
        try {
            // التحقق من وجود الجدول
            $checkTable = $pdo->query("SHOW TABLES LIKE '$tableName'");
            
            if ($checkTable->rowCount() > 0) {
                echo "<p style='color: orange;'>⚠️ الجدول $tableName موجود مسبقاً</p>";
            } else {
                // إنشاء الجدول
                $pdo->exec($createSQL);
                echo "<p style='color: green;'>✅ تم إنشاء جدول $tableName بنجاح</p>";
            }
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ خطأ في إنشاء جدول $tableName: " . $e->getMessage() . "</p>";
        }
    }
    
    // إدراج بيانات افتراضية
    echo "<h3>إدراج البيانات الافتراضية:</h3>";
    
    // إدراج المدير الافتراضي إذا لم يكن موجوداً
    $adminExists = fetchOne("SELECT id FROM admins WHERE username = 'admin'");
    if (!$adminExists) {
        $adminPassword = password_hash('password', PASSWORD_DEFAULT);
        $pdo->exec("INSERT INTO admins (username, password, email) VALUES ('admin', '$adminPassword', '<EMAIL>')");
        echo "<p style='color: green;'>✅ تم إنشاء المدير الافتراضي</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ المدير الافتراضي موجود مسبقاً</p>";
    }
    
    // إدراج تصنيفات افتراضية
    $defaultCategories = [
        ['name' => 'إلكترونيات', 'description' => 'أجهزة إلكترونية متنوعة'],
        ['name' => 'ملابس', 'description' => 'ملابس رجالية ونسائية'],
        ['name' => 'كتب', 'description' => 'كتب ومراجع متنوعة'],
        ['name' => 'رياضة', 'description' => 'معدات وأدوات رياضية']
    ];
    
    foreach ($defaultCategories as $category) {
        $existing = fetchOne("SELECT id FROM categories WHERE name = ?", [$category['name']]);
        if (!$existing) {
            $pdo->prepare("INSERT INTO categories (name, description, status) VALUES (?, ?, 'active')")
                ->execute([$category['name'], $category['description']]);
            echo "<p style='color: green;'>✅ تم إضافة تصنيف: " . $category['name'] . "</p>";
        }
    }
    
    // إدراج إعدادات افتراضية
    $defaultSettings = [
        'site_name' => 'متجري الإلكتروني',
        'site_description' => 'متجر إلكتروني شامل لجميع احتياجاتك',
        'delivery_price' => '25.00',
        'free_delivery_threshold' => '200.00',
        'currency' => 'دينار عراقي',
        'contact_phone' => '',
        'contact_email' => '',
        'whatsapp_number' => ''
    ];
    
    foreach ($defaultSettings as $key => $value) {
        $existing = fetchOne("SELECT id FROM site_settings WHERE setting_key = ?", [$key]);
        if (!$existing) {
            $pdo->prepare("INSERT INTO site_settings (setting_key, setting_value) VALUES (?, ?)")
                ->execute([$key, $value]);
            echo "<p style='color: green;'>✅ تم إضافة إعداد: $key</p>";
        }
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ تم إنشاء جميع الجداول بنجاح!</h4>";
    echo "<p>يمكنك الآن استخدام جميع ميزات لوحة التحكم.</p>";
    echo "<p><strong>الخطوات التالية:</strong></p>";
    echo "<ul>";
    echo "<li><a href='admin/login.php'>تسجيل الدخول إلى لوحة التحكم</a></li>";
    echo "<li><a href='admin/categories.php'>إدارة التصنيفات</a></li>";
    echo "<li><a href='admin/products.php'>إدارة المنتجات</a></li>";
    echo "</ul>";
    echo "</div>";
    
    // عرض ملخص الجداول
    echo "<h3>ملخص الجداول المنشأة:</h3>";
    $allTables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<ul>";
    foreach ($allTables as $table) {
        $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
        echo "<li style='color: green;'>✅ <strong>$table:</strong> $count سجل</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ في إنشاء الجداول</h4>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
    
    echo "<h4>خطوات حل المشكلة:</h4>";
    echo "<ol>";
    echo "<li>تأكد من تشغيل MySQL في XAMPP</li>";
    echo "<li>تأكد من وجود قاعدة البيانات shop_db</li>";
    echo "<li>تحقق من صلاحيات إنشاء الجداول</li>";
    echo "<li>جرب تشغيل setup_database.php</li>";
    echo "</ol>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    line-height: 1.6;
}

h2, h3, h4 {
    color: #333;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 10px 0;
}
</style>
