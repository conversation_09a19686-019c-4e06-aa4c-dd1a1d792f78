/**
 * Professional Product Carousel JavaScript
 * Modern, Accessible, and RTL-Supported Implementation
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

class ProductCarousel {
    constructor(element) {
        this.container = element;
        this.track = this.container.querySelector('.product-carousel-track');
        this.slides = this.container.querySelectorAll('.product-carousel-slide');
        this.indicators = this.container.querySelectorAll('.product-carousel-indicator');
        this.prevBtn = this.container.querySelector('.product-carousel-nav.prev');
        this.nextBtn = this.container.querySelector('.product-carousel-nav.next');
        
        this.currentSlide = 0;
        this.totalSlides = this.slides.length;
        this.isRTL = document.documentElement.dir === 'rtl';
        this.autoAdvanceTime = 5000; // 5 seconds
        this.autoAdvanceTimer = null;
        this.isPlaying = true;
        
        // Touch/swipe properties
        this.touchStartX = 0;
        this.touchEndX = 0;
        this.touchThreshold = 50;
        
        this.init();
    }
    
    init() {
        if (this.totalSlides <= 1) {
            this.hideNavigation();
            return;
        }
        
        this.setupEventListeners();
        this.setupImageLoading();
        this.startAutoAdvance();
        this.updateAriaAttributes();
        
        console.log('Product Carousel initialized with', this.totalSlides, 'slides');
    }
    
    setupEventListeners() {
        // Navigation buttons
        if (this.prevBtn) {
            this.prevBtn.addEventListener('click', () => this.previousSlide());
        }
        
        if (this.nextBtn) {
            this.nextBtn.addEventListener('click', () => this.nextSlide());
        }
        
        // Indicators
        this.indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => this.goToSlide(index));
        });
        
        // Touch/swipe support
        this.container.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: true });
        this.container.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: true });
        
        // Keyboard navigation
        this.container.addEventListener('keydown', (e) => this.handleKeyboard(e));
        
        // Pause on hover
        this.container.addEventListener('mouseenter', () => this.pauseAutoAdvance());
        this.container.addEventListener('mouseleave', () => this.resumeAutoAdvance());
        
        // Pause when tab is not active
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAutoAdvance();
            } else {
                this.resumeAutoAdvance();
            }
        });
    }
    
    setupImageLoading() {
        this.slides.forEach((slide, index) => {
            const img = slide.querySelector('img');
            const loading = slide.querySelector('.product-carousel-loading');
            
            if (img && loading) {
                img.addEventListener('load', () => {
                    loading.classList.add('hidden');
                    setTimeout(() => {
                        loading.style.display = 'none';
                    }, 300);
                });
                
                img.addEventListener('error', () => {
                    slide.classList.add('error');
                    slide.innerHTML = `
                        <i class="bi bi-image"></i>
                        <div class="error-text">فشل في تحميل الصورة</div>
                    `;
                });
            }
        });
    }
    
    nextSlide() {
        const nextIndex = (this.currentSlide + 1) % this.totalSlides;
        this.goToSlide(nextIndex);
    }
    
    previousSlide() {
        const prevIndex = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
        this.goToSlide(prevIndex);
    }
    
    goToSlide(index) {
        if (index === this.currentSlide || index < 0 || index >= this.totalSlides) return;
        
        // Update indicators
        this.indicators.forEach((indicator, i) => {
            indicator.classList.toggle('active', i === index);
            indicator.setAttribute('aria-selected', i === index ? 'true' : 'false');
        });
        
        // Calculate transform value
        const translateX = this.isRTL ? index * 100 : -index * 100;
        this.track.style.transform = `translateX(${translateX}%)`;
        
        this.currentSlide = index;
        this.updateAriaAttributes();
        this.restartAutoAdvance();
    }
    
    handleTouchStart(e) {
        this.touchStartX = e.touches[0].clientX;
        this.pauseAutoAdvance();
    }
    
    handleTouchEnd(e) {
        this.touchEndX = e.changedTouches[0].clientX;
        this.handleSwipe();
        this.resumeAutoAdvance();
    }
    
    handleSwipe() {
        const swipeDistance = this.touchStartX - this.touchEndX;
        const absSwipeDistance = Math.abs(swipeDistance);
        
        if (absSwipeDistance > this.touchThreshold) {
            if (this.isRTL) {
                // RTL: swipe right to go next, swipe left to go previous
                if (swipeDistance > 0) {
                    this.nextSlide();
                } else {
                    this.previousSlide();
                }
            } else {
                // LTR: swipe left to go next, swipe right to go previous
                if (swipeDistance > 0) {
                    this.nextSlide();
                } else {
                    this.previousSlide();
                }
            }
        }
    }
    
    handleKeyboard(e) {
        switch (e.key) {
            case 'ArrowLeft':
                e.preventDefault();
                this.isRTL ? this.nextSlide() : this.previousSlide();
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.isRTL ? this.previousSlide() : this.nextSlide();
                break;
            case 'Home':
                e.preventDefault();
                this.goToSlide(0);
                break;
            case 'End':
                e.preventDefault();
                this.goToSlide(this.totalSlides - 1);
                break;
        }
    }
    
    startAutoAdvance() {
        if (this.totalSlides <= 1) return;
        
        this.autoAdvanceTimer = setInterval(() => {
            if (this.isPlaying) {
                this.nextSlide();
            }
        }, this.autoAdvanceTime);
    }
    
    pauseAutoAdvance() {
        this.isPlaying = false;
    }
    
    resumeAutoAdvance() {
        this.isPlaying = true;
    }
    
    restartAutoAdvance() {
        if (this.autoAdvanceTimer) {
            clearInterval(this.autoAdvanceTimer);
        }
        this.startAutoAdvance();
    }
    
    updateAriaAttributes() {
        this.container.setAttribute('aria-label', `صورة ${this.currentSlide + 1} من ${this.totalSlides}`);
        
        if (this.prevBtn) {
            this.prevBtn.setAttribute('aria-label', 'الصورة السابقة');
        }
        
        if (this.nextBtn) {
            this.nextBtn.setAttribute('aria-label', 'الصورة التالية');
        }
    }
    
    hideNavigation() {
        if (this.prevBtn) this.prevBtn.style.display = 'none';
        if (this.nextBtn) this.nextBtn.style.display = 'none';
        if (this.indicators.length > 0) {
            this.container.querySelector('.product-carousel-indicators').style.display = 'none';
        }
    }
    
    destroy() {
        if (this.autoAdvanceTimer) {
            clearInterval(this.autoAdvanceTimer);
        }
        
        // Remove event listeners
        this.container.removeEventListener('touchstart', this.handleTouchStart);
        this.container.removeEventListener('touchend', this.handleTouchEnd);
        this.container.removeEventListener('keydown', this.handleKeyboard);
        this.container.removeEventListener('mouseenter', this.pauseAutoAdvance);
        this.container.removeEventListener('mouseleave', this.resumeAutoAdvance);
    }
}

// Initialize all product carousels when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    const carousels = document.querySelectorAll('.product-carousel-container');
    
    carousels.forEach(carousel => {
        new ProductCarousel(carousel);
    });
});

// Export for use in other scripts
window.ProductCarousel = ProductCarousel;
