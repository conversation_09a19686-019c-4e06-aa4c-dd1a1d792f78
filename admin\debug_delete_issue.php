<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'تشخيص مشكلة الحذف';

echo "<h2>تشخيص مشكلة حذف المنتجات</h2>";

// Test 1: Check if we can create and delete a test product
echo "<h3>1. اختبار إنشاء وحذف منتج تجريبي</h3>";

$testProduct = [
    'name' => 'منتج اختبار الحذف ' . date('Y-m-d H:i:s'),
    'description' => 'منتج مؤقت لاختبار الحذف',
    'price' => 1.00,
    'stock' => 1,
    'status' => 'inactive'
];

try {
    $testId = insertData('products', $testProduct);
    if ($testId) {
        echo "<p style='color: green;'>✅ تم إنشاء منتج تجريبي (ID: $testId)</p>";
        
        // Verify it exists
        $createdProduct = fetchOne("SELECT * FROM products WHERE id = ?", [$testId]);
        if ($createdProduct) {
            echo "<p style='color: green;'>✅ المنتج موجود في قاعدة البيانات</p>";
            echo "<p><strong>اسم المنتج:</strong> " . htmlspecialchars($createdProduct['name']) . "</p>";
            
            // Now test the exact delete process from products.php
            echo "<h4>اختبار عملية الحذف الكاملة:</h4>";
            
            // Step 1: Check for product (like in products.php)
            $product = fetchOne("SELECT name FROM products WHERE id = ?", [$testId]);
            if ($product) {
                echo "<p>✅ تم العثور على المنتج للحذف</p>";
                
                try {
                    // Step 2: Delete related images (like in products.php)
                    $images = fetchAll("SELECT image_path FROM product_images WHERE product_id = ?", [$testId]);
                    echo "<p>عدد الصور المرتبطة: " . count($images) . "</p>";
                    
                    // Step 3: Delete from product_images table
                    $deleteImages = deleteData('product_images', 'product_id = ?', [$testId]);
                    echo "<p><strong>نتيجة حذف الصور:</strong> " . var_export($deleteImages, true) . "</p>";
                    
                    // Step 4: Delete from products table
                    echo "<h5>حذف المنتج من جدول products:</h5>";
                    echo "<p><strong>SQL سيتم تنفيذه:</strong> DELETE FROM products WHERE id = :param_0</p>";
                    echo "<p><strong>المعاملات:</strong> [param_0 => $testId]</p>";
                    
                    $deleteProduct = deleteData('products', 'id = ?', [$testId]);
                    echo "<p><strong>نتيجة حذف المنتج:</strong> " . var_export($deleteProduct, true) . "</p>";
                    
                    if ($deleteProduct !== false && $deleteProduct > 0) {
                        echo "<p style='color: green;'>✅ تم حذف المنتج بنجاح (صفوف محذوفة: $deleteProduct)</p>";
                        
                        // Verify deletion
                        $checkDeleted = fetchOne("SELECT id FROM products WHERE id = ?", [$testId]);
                        if (!$checkDeleted) {
                            echo "<p style='color: green;'>✅ تم التحقق - المنتج لم يعد موجود</p>";
                        } else {
                            echo "<p style='color: red;'>❌ المنتج ما زال موجود بعد الحذف!</p>";
                            echo "<p>بيانات المنتج المتبقية: " . print_r($checkDeleted, true) . "</p>";
                        }
                    } else {
                        echo "<p style='color: red;'>❌ فشل في حذف المنتج</p>";
                        echo "<p>النتيجة المُرجعة: " . var_export($deleteProduct, true) . "</p>";
                        
                        // Try manual deletion for comparison
                        echo "<h5>محاولة الحذف اليدوي:</h5>";
                        global $pdo;
                        $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
                        $manualResult = $stmt->execute([$testId]);
                        $manualRowCount = $stmt->rowCount();
                        
                        echo "<p><strong>الحذف اليدوي:</strong> " . ($manualResult ? 'نجح' : 'فشل') . "</p>";
                        echo "<p><strong>صفوف محذوفة يدوياً:</strong> $manualRowCount</p>";
                    }
                    
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ خطأ في عملية الحذف: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ لم يتم العثور على المنتج للحذف</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ المنتج غير موجود بعد الإنشاء</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء منتج تجريبي</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاختبار: " . $e->getMessage() . "</p>";
}

// Test 2: Check if there are any existing products we can test with
echo "<h3>2. اختبار مع منتج موجود</h3>";

$existingProducts = fetchAll("SELECT id, name FROM products ORDER BY id DESC LIMIT 3");
if (!empty($existingProducts)) {
    echo "<p>المنتجات المتاحة للاختبار:</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>اختبار</th></tr>";
    
    foreach ($existingProducts as $prod) {
        echo "<tr>";
        echo "<td>" . $prod['id'] . "</td>";
        echo "<td>" . htmlspecialchars($prod['name']) . "</td>";
        echo "<td><a href='?test_delete=" . $prod['id'] . "'>اختبار الحذف</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد منتجات للاختبار</p>";
}

// Handle test delete request
if (isset($_GET['test_delete']) && is_numeric($_GET['test_delete'])) {
    $testDeleteId = (int)$_GET['test_delete'];
    echo "<h3>3. اختبار حذف المنتج ID: $testDeleteId</h3>";
    
    $productToDelete = fetchOne("SELECT name FROM products WHERE id = ?", [$testDeleteId]);
    if ($productToDelete) {
        echo "<p><strong>المنتج المراد حذفه:</strong> " . htmlspecialchars($productToDelete['name']) . "</p>";
        echo "<p><strong>تحذير:</strong> هذا سيحذف المنتج فعلياً من قاعدة البيانات!</p>";
        
        if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
            echo "<h4>تنفيذ الحذف...</h4>";
            
            try {
                // Execute the same delete logic as products.php
                $images = fetchAll("SELECT image_path FROM product_images WHERE product_id = ?", [$testDeleteId]);
                $deleteImages = deleteData('product_images', 'product_id = ?', [$testDeleteId]);
                $deleteProduct = deleteData('products', 'id = ?', [$testDeleteId]);
                
                echo "<p><strong>حذف الصور:</strong> " . var_export($deleteImages, true) . "</p>";
                echo "<p><strong>حذف المنتج:</strong> " . var_export($deleteProduct, true) . "</p>";
                
                if ($deleteProduct !== false && $deleteProduct > 0) {
                    echo "<p style='color: green;'>✅ تم حذف المنتج بنجاح!</p>";
                } else {
                    echo "<p style='color: red;'>❌ فشل في حذف المنتج</p>";
                }
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p><a href='?test_delete=$testDeleteId&confirm=yes' onclick='return confirm(\"هل أنت متأكد؟\")'>تأكيد الحذف</a></p>";
        }
    } else {
        echo "<p style='color: red;'>❌ المنتج غير موجود</p>";
    }
}

// Test 3: Check database connection and permissions
echo "<h3>4. فحص قاعدة البيانات</h3>";

try {
    global $pdo;
    
    // Test basic connection
    $testConnection = $pdo->query("SELECT 1");
    if ($testConnection) {
        echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات يعمل</p>";
    }
    
    // Test table structure
    $tableInfo = $pdo->query("DESCRIBE products");
    $columns = $tableInfo->fetchAll(PDO::FETCH_ASSOC);
    echo "<p><strong>أعمدة جدول products:</strong> " . count($columns) . " عمود</p>";
    
    // Test permissions
    $testInsert = $pdo->prepare("INSERT INTO products (name, description, price, stock, status) VALUES (?, ?, ?, ?, ?)");
    $testData = ['اختبار صلاحيات', 'اختبار', 1.00, 1, 'inactive'];
    $insertResult = $testInsert->execute($testData);
    
    if ($insertResult) {
        $insertId = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ صلاحية الإدراج تعمل (ID: $insertId)</p>";
        
        // Test delete permission
        $testDelete = $pdo->prepare("DELETE FROM products WHERE id = ?");
        $deleteResult = $testDelete->execute([$insertId]);
        $deletedRows = $testDelete->rowCount();
        
        if ($deleteResult && $deletedRows > 0) {
            echo "<p style='color: green;'>✅ صلاحية الحذف تعمل (صفوف محذوفة: $deletedRows)</p>";
        } else {
            echo "<p style='color: red;'>❌ مشكلة في صلاحية الحذف</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ مشكلة في صلاحية الإدراج</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في فحص قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>الخلاصة:</h3>";
echo "<p>إذا نجحت جميع الاختبارات أعلاه، فإن مشكلة الحذف قد تكون في:</p>";
echo "<ul>";
echo "<li>رابط الحذف في الواجهة لا يتم استدعاؤه بشكل صحيح</li>";
echo "<li>مشكلة في JavaScript أو Modal</li>";
echo "<li>مشكلة في معالجة GET parameter</li>";
echo "<li>مشكلة في إعادة التوجيه بعد الحذف</li>";
echo "</ul>";

echo "<p><a href='products.php'>العودة إلى إدارة المنتجات</a></p>";
?>
