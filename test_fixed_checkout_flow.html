<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fixed Checkout Flow</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 900px;
            margin: 30px auto;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .header-fix-badge {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px 25px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            font-weight: bold;
            font-size: 18px;
        }
        .test-section {
            background: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
        }
        .test-section:hover {
            border-color: #007bff;
            box-shadow: 0 4px 15px rgba(0,123,255,0.1);
        }
        .btn-test-checkout {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: bold;
            border-radius: 10px;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        .btn-test-checkout:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.3);
        }
        .success-indicator {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .warning-indicator {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .step-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <div class="header-fix-badge">
                🔧 Header Redirect Fix Applied - Ready for Testing
            </div>

            <div class="success-indicator">
                <h4>✅ Header Issues Fixed!</h4>
                <p class="mb-0">The "headers already sent" error has been resolved. Both checkout.php and order-success.php now handle redirects properly before any HTML output.</p>
            </div>

            <div class="test-section">
                <h3>🎯 Fixed Issues Summary</h3>
                <div class="row">
                    <div class="col-md-6">
                        <h5>✅ checkout.php Fixed:</h5>
                        <ul>
                            <li>Moved header include after redirect logic</li>
                            <li>Cart validation happens before output</li>
                            <li>Order creation redirects work properly</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>✅ order-success.php Fixed:</h5>
                        <ul>
                            <li>Order validation before any output</li>
                            <li>Redirects work without header errors</li>
                            <li>Proper order confirmation display</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h3>🧪 Complete Checkout Flow Test</h3>
                <p>Follow these steps to test the fixed checkout process:</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <span class="step-number">1</span>
                            <div>
                                <strong>Add Products to Cart</strong><br>
                                <small>Make sure you have items in your cart</small>
                            </div>
                        </div>
                        <a href="products.php" target="_blank" class="btn btn-outline-primary w-100 mb-3">
                            🛍️ Go to Products Page
                        </a>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <span class="step-number">2</span>
                            <div>
                                <strong>Review Your Cart</strong><br>
                                <small>Verify items are in your cart</small>
                            </div>
                        </div>
                        <a href="cart.php" target="_blank" class="btn btn-outline-secondary w-100 mb-3">
                            🛒 View Shopping Cart
                        </a>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <span class="step-number">3</span>
                            <div>
                                <strong>Test Checkout Process</strong><br>
                                <small>Should redirect properly now</small>
                            </div>
                        </div>
                        <a href="checkout.php" target="_blank" class="btn btn-test-checkout mb-3">
                            💳 Test Checkout Page
                        </a>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <span class="step-number">4</span>
                            <div>
                                <strong>Verify Admin Orders</strong><br>
                                <small>Check if orders appear</small>
                            </div>
                        </div>
                        <a href="admin/orders.php" target="_blank" class="btn btn-outline-warning w-100 mb-3">
                            📊 Check Admin Orders
                        </a>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h3>📋 Quick Test Form</h3>
                <p>Use this pre-filled form to quickly test the checkout process:</p>
                
                <div class="warning-indicator">
                    <strong>⚠️ Important:</strong> Make sure you have products in your cart before submitting this form!
                </div>

                <form action="checkout.php" method="POST" id="quickTestForm">
                    <input type="hidden" name="place_order" value="1">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label"><strong>اسم العميل *</strong></label>
                            <input type="text" class="form-control" name="customer_name" 
                                   value="Header Fix Test Customer" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label"><strong>رقم الهاتف *</strong></label>
                            <input type="text" class="form-control" name="customer_phone" 
                                   value="07123456789" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label"><strong>العنوان *</strong></label>
                        <textarea class="form-control" name="address" rows="2" required>Header Fix Test Address, Baghdad, Iraq</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label"><strong>المحافظة *</strong></label>
                            <select class="form-control" name="province" required>
                                <option value="بغداد" selected>بغداد</option>
                                <option value="البصرة">البصرة</option>
                                <option value="نينوى">نينوى</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label"><strong>طريقة الدفع</strong></label>
                            <select class="form-control" name="payment_method">
                                <option value="cash_on_delivery" selected>الدفع عند الاستلام</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label"><strong>ملاحظات</strong></label>
                        <textarea class="form-control" name="notes" rows="2">Header redirect fix test - should redirect to order success page without errors</textarea>
                    </div>
                    
                    <button type="submit" class="btn-test-checkout">
                        🚀 Test Fixed Checkout Process
                    </button>
                </form>
            </div>

            <div class="test-section">
                <h3>🔍 Expected Results</h3>
                <div class="row">
                    <div class="col-md-6">
                        <h5>✅ Success Indicators:</h5>
                        <ul>
                            <li>No "headers already sent" errors</li>
                            <li>Smooth redirect to order-success.php</li>
                            <li>Order confirmation page displays</li>
                            <li>Order appears in admin panel</li>
                            <li>Shopping cart is cleared</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>🚨 If Issues Persist:</h5>
                        <ul>
                            <li>Check browser console for errors</li>
                            <li>Verify products are in cart first</li>
                            <li>Check PHP error logs</li>
                            <li>Ensure database is accessible</li>
                            <li>Test with different browsers</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h3>🔧 Additional Testing Tools</h3>
                <div class="row">
                    <div class="col-md-4">
                        <a href="test_header_redirect_fix.php" target="_blank" class="btn btn-outline-info w-100 mb-2">
                            🔍 Header Fix Diagnostics
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="test_complete_checkout_workflow.php" target="_blank" class="btn btn-outline-success w-100 mb-2">
                            🎯 Complete Workflow Test
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="comprehensive_checkout_fix.php" target="_blank" class="btn btn-outline-warning w-100 mb-2">
                            🛠️ System Diagnostics
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('quickTestForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري المعالجة...';
            submitBtn.disabled = true;
            
            // Show processing message
            const alert = document.createElement('div');
            alert.className = 'alert alert-info mt-3';
            alert.innerHTML = '<strong>🔄 Processing...</strong><br>Testing the fixed checkout process. You should be redirected to the order success page without any header errors.';
            this.appendChild(alert);
        });
    </script>
</body>
</html>
