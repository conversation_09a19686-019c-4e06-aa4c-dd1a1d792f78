<?php
/**
 * Homepage Debug Script
 * Tests all homepage functions and database connections
 */

echo "<h1>🔍 Homepage Debug Script</h1>";

// Test 1: Basic includes
echo "<h2>1. Testing Basic Includes</h2>";
try {
    require_once 'config/config.php';
    echo "✅ Config loaded successfully<br>";
    
    if (isset($pdo)) {
        echo "✅ Database connection available<br>";
    } else {
        echo "❌ Database connection not available<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error loading config: " . $e->getMessage() . "<br>";
}

// Test 2: Check functions
echo "<h2>2. Testing Functions</h2>";

$functions = [
    'fetchOne',
    'fetchAll', 
    'getHomepageSettings',
    'getHomepageSectionSettings',
    'formatPrice',
    'formatArabicDate'
];

foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "✅ Function {$func} exists<br>";
    } else {
        echo "❌ Function {$func} missing<br>";
    }
}

// Test 3: Database tables
echo "<h2>3. Testing Database Tables</h2>";

$tables = [
    'homepage_settings',
    'products',
    'categories',
    'reviews'
];

foreach ($tables as $table) {
    try {
        $result = fetchOne("SHOW TABLES LIKE '$table'");
        if ($result) {
            echo "✅ Table {$table} exists<br>";
            
            // Count records
            $count = fetchOne("SELECT COUNT(*) as count FROM $table")['count'];
            echo "&nbsp;&nbsp;&nbsp;📊 Records: {$count}<br>";
        } else {
            echo "❌ Table {$table} missing<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error checking table {$table}: " . $e->getMessage() . "<br>";
    }
}

// Test 4: Homepage settings
echo "<h2>4. Testing Homepage Settings</h2>";

try {
    $sections = [
        'carousel',
        'featured_products', 
        'offers',
        'influencers',
        'customer_reviews',
        'success_story',
        'why_choose_us',
        'newsletter',
        'call_to_action'
    ];
    
    foreach ($sections as $section) {
        $settings = getHomepageSectionSettings($section);
        echo "✅ Section {$section}: " . count($settings) . " settings<br>";
        
        // Show first few settings
        $i = 0;
        foreach ($settings as $key => $value) {
            if ($i < 2) {
                echo "&nbsp;&nbsp;&nbsp;• {$key}: " . substr($value, 0, 50) . "...<br>";
                $i++;
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error testing homepage settings: " . $e->getMessage() . "<br>";
}

// Test 5: Sample data queries
echo "<h2>5. Testing Data Queries</h2>";

try {
    // Featured products
    $featuredProducts = fetchAll("
        SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.is_featured = 1 AND p.status = 'active' 
        LIMIT 3
    ");
    echo "✅ Featured products query: " . count($featuredProducts) . " results<br>";
    
    // Discounted products
    $discountedProducts = fetchAll("
        SELECT COUNT(*) as count FROM products 
        WHERE discount > 0 AND status = 'active'
    ");
    echo "✅ Discounted products: " . $discountedProducts[0]['count'] . " found<br>";
    
    // Reviews
    $reviews = fetchAll("
        SELECT COUNT(*) as count FROM reviews 
        WHERE status = 'approved'
    ");
    echo "✅ Approved reviews: " . $reviews[0]['count'] . " found<br>";
    
} catch (Exception $e) {
    echo "❌ Error testing data queries: " . $e->getMessage() . "<br>";
}

// Test 6: File existence
echo "<h2>6. Testing Required Files</h2>";

$files = [
    'index.php',
    'includes/header.php',
    'includes/footer.php',
    'includes/homepage_carousel.php',
    'assets/css/homepage.css',
    'ajax/cart.php',
    'ajax/newsletter.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ File {$file} exists<br>";
    } else {
        echo "❌ File {$file} missing<br>";
    }
}

// Test 7: Create homepage settings if missing
echo "<h2>7. Homepage Settings Setup</h2>";

try {
    $tableExists = fetchOne("SHOW TABLES LIKE 'homepage_settings'");
    if (!$tableExists) {
        echo "⚠️ Homepage settings table missing, creating...<br>";
        
        // Create table
        $createTableQuery = "
            CREATE TABLE `homepage_settings` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `section_name` varchar(100) NOT NULL,
                `setting_key` varchar(100) NOT NULL,
                `setting_value` text,
                `setting_type` enum('text','textarea','image','url','number','boolean') DEFAULT 'text',
                `sort_order` int(11) DEFAULT 0,
                `is_active` tinyint(1) DEFAULT 1,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_section_key` (`section_name`,`setting_key`),
                KEY `idx_section` (`section_name`),
                KEY `idx_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createTableQuery);
        echo "✅ Homepage settings table created<br>";
        
        // Insert default data
        $defaultSettings = [
            ['carousel', 'slide_1_image', 'https://via.placeholder.com/1200x600/667eea/ffffff?text=الشريحة+الأولى', 'image', 1],
            ['carousel', 'slide_1_title', 'مرحباً بك في متجرنا الإلكتروني', 'text', 2],
            ['carousel', 'slide_1_subtitle', 'اكتشف مجموعة واسعة من المنتجات عالية الجودة', 'text', 3],
            ['carousel', 'auto_advance_time', '10', 'number', 4],
            ['featured_products', 'show_section', '1', 'boolean', 1],
            ['featured_products', 'section_title', 'المنتجات المميزة', 'text', 2],
            ['offers', 'show_section', '1', 'boolean', 1],
            ['offers', 'section_title', 'العروض الخاصة', 'text', 2]
        ];
        
        $insertQuery = "INSERT INTO homepage_settings (section_name, setting_key, setting_value, setting_type, sort_order) VALUES (?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($insertQuery);
        
        foreach ($defaultSettings as $setting) {
            $stmt->execute($setting);
        }
        
        echo "✅ Default homepage settings inserted<br>";
    } else {
        echo "✅ Homepage settings table already exists<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error setting up homepage settings: " . $e->getMessage() . "<br>";
}

echo "<h2>🎯 Summary</h2>";
echo "<p>If all tests show ✅, the homepage should work properly.</p>";
echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Test Homepage</a></p>";
echo "<p><a href='admin/homepage_settings.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>⚙️ Admin Settings</a></p>";
?>
