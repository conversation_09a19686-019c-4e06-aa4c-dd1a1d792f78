<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إزالة التحقق من رقم الهاتف</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="tel"], input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        input.is-invalid {
            border-color: #dc3545;
            background-color: #fff8f8;
        }
        .validation-message {
            margin-top: 5px;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            display: none;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-results {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار إزالة التحقق من رقم الهاتف</h1>
        
        <div class="test-form">
            <h2>نموذج اختبار</h2>
            <form id="testForm">
                <div class="form-group">
                    <label for="customerName">اسم العميل *</label>
                    <input type="text" id="customerName" name="customer_name" required>
                </div>
                
                <div class="form-group">
                    <label for="customerPhone">رقم الهاتف *</label>
                    <input type="tel" id="customerPhone" name="customer_phone" required 
                           placeholder="أي تنسيق مقبول الآن">
                    <div id="phoneValidationMessage" class="validation-message"></div>
                </div>
                
                <div class="form-group">
                    <label for="address">العنوان *</label>
                    <input type="text" id="address" name="address" required>
                </div>
                
                <button type="submit">اختبار الإرسال</button>
                <button type="button" onclick="testVariousPhones()">اختبار أرقام مختلفة</button>
            </form>
        </div>
        
        <div class="test-results">
            <h2>نتائج الاختبار</h2>
            <div id="testResults"></div>
        </div>
    </div>

    <script>
        // Simulate the checkout form validation (without phone validation)
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const requiredFields = this.querySelectorAll('[required]');
            let isValid = true;
            let results = '<h3>نتائج التحقق من النموذج:</h3>';
            
            // Check required fields (but not phone format)
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                    results += `<div class="error">❌ ${field.previousElementSibling.textContent} مطلوب</div>`;
                } else {
                    field.classList.remove('is-invalid');
                    results += `<div class="success">✅ ${field.previousElementSibling.textContent} صحيح</div>`;
                }
            });
            
            // Phone field specific test
            const phoneField = document.getElementById('customerPhone');
            if (phoneField.value.trim()) {
                results += `<div class="success">✅ رقم الهاتف مقبول: "${phoneField.value}" (بدون تحقق من التنسيق)</div>`;
            }
            
            if (isValid) {
                results += '<div class="success"><strong>🎉 النموذج صالح! سيتم إرسال الطلب.</strong></div>';
            } else {
                results += '<div class="error"><strong>❌ يرجى ملء الحقول المطلوبة.</strong></div>';
            }
            
            document.getElementById('testResults').innerHTML = results;
        });
        
        function testVariousPhones() {
            const testPhones = [
                '07123456789',
                '+9647123456789', 
                '************',
                '(*************',
                'abc123def',
                '12345',
                '******-555-0123',
                'invalid-phone',
                '00000000000'
            ];
            
            let results = '<h3>اختبار أرقام هاتف مختلفة:</h3>';
            results += '<p>جميع الأرقام التالية يجب أن تكون مقبولة (بدون رسائل خطأ):</p>';
            
            testPhones.forEach(phone => {
                // Set the phone value
                document.getElementById('customerPhone').value = phone;
                
                // Simulate validation (should always pass now)
                const phoneField = document.getElementById('customerPhone');
                phoneField.classList.remove('is-invalid');
                
                results += `<div class="success">✅ "${phone}" - مقبول</div>`;
            });
            
            results += '<div class="success"><strong>✅ جميع أرقام الهاتف مقبولة بدون تحقق من التنسيق!</strong></div>';
            
            document.getElementById('testResults').innerHTML = results;
            
            // Reset form
            document.getElementById('customerPhone').value = '';
        }
        
        // Test on page load
        window.addEventListener('load', function() {
            let initialResults = '<h3>حالة التحقق من رقم الهاتف:</h3>';
            
            // Check if phone validation code exists
            const pageSource = document.documentElement.innerHTML;
            const hasPhoneValidation = pageSource.includes('phonePatterns') || 
                                     pageSource.includes('/^07[0-9]{8}$/') ||
                                     pageSource.includes('يرجى إدخال رقم هاتف عراقي صحيح');
            
            if (hasPhoneValidation) {
                initialResults += '<div class="error">❌ لا يزال يوجد كود التحقق من رقم الهاتف في الصفحة</div>';
            } else {
                initialResults += '<div class="success">✅ تم إزالة كود التحقق من رقم الهاتف بنجاح</div>';
            }
            
            initialResults += '<p>اختبر النموذج أعلاه بأرقام هاتف مختلفة للتأكد من عدم وجود تحقق من التنسيق.</p>';
            
            document.getElementById('testResults').innerHTML = initialResults;
        });
    </script>
</body>
</html>
