<?php
/**
 * Comprehensive database schema repair
 */

require_once 'config/config.php';

echo "<h1>Database Schema Repair Tool</h1>";
echo "<p>This tool will ensure all tables have the correct structure for the checkout process.</p>";

// 1. Drop and recreate orders table with correct structure
echo "<h2>1. Recreating Orders Table</h2>";

try {
    // First, check if there are any existing orders
    $existingOrders = fetchAll("SELECT COUNT(*) as count FROM orders");
    $orderCount = $existingOrders[0]['count'] ?? 0;
    
    if ($orderCount > 0) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0;'>";
        echo "⚠️ Warning: Found $orderCount existing orders. They will be preserved during schema update.";
        echo "</div>";
    }
    
    // Create backup table if orders exist
    if ($orderCount > 0) {
        $pdo->exec("CREATE TABLE orders_backup AS SELECT * FROM orders");
        echo "✅ Created backup of existing orders<br>";
    }
    
    // Drop existing table
    $pdo->exec("DROP TABLE IF EXISTS orders");
    echo "✅ Dropped existing orders table<br>";
    
    // Create new table with correct structure
    $createOrdersSQL = "
    CREATE TABLE `orders` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `customer_name` varchar(255) NOT NULL,
        `customer_phone` varchar(20) NOT NULL,
        `customer_email` varchar(100) DEFAULT NULL,
        `address` text NOT NULL,
        `province` varchar(100) NOT NULL,
        `city` varchar(50) DEFAULT NULL,
        `postal_code` varchar(10) DEFAULT NULL,
        `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
        `delivery_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_price` decimal(10,2) NOT NULL,
        `payment_method` enum('cash_on_delivery','bank_transfer') NOT NULL DEFAULT 'cash_on_delivery',
        `status` enum('pending','confirmed','processing','shipped','delivered','cancelled') NOT NULL DEFAULT 'pending',
        `notes` text,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `status` (`status`),
        KEY `created_at` (`created_at`),
        KEY `customer_phone` (`customer_phone`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($createOrdersSQL);
    echo "✅ Created new orders table with correct structure<br>";
    
    // Restore data if backup exists
    if ($orderCount > 0) {
        try {
            $pdo->exec("INSERT INTO orders SELECT * FROM orders_backup");
            echo "✅ Restored $orderCount existing orders<br>";
            $pdo->exec("DROP TABLE orders_backup");
            echo "✅ Cleaned up backup table<br>";
        } catch (Exception $e) {
            echo "⚠️ Could not restore all data: " . $e->getMessage() . "<br>";
            echo "Backup table 'orders_backup' preserved for manual recovery<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error recreating orders table: " . $e->getMessage() . "<br>";
}

// 2. Ensure order_items table exists with correct structure
echo "<h2>2. Checking Order Items Table</h2>";

try {
    $pdo->exec("DROP TABLE IF EXISTS order_items");
    
    $createOrderItemsSQL = "
    CREATE TABLE `order_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `order_id` int(11) NOT NULL,
        `product_id` int(11) NOT NULL,
        `product_name` varchar(255) NOT NULL,
        `quantity` int(11) NOT NULL,
        `price` decimal(10,2) NOT NULL,
        `total` decimal(10,2) NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `order_id` (`order_id`),
        KEY `product_id` (`product_id`),
        FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($createOrderItemsSQL);
    echo "✅ Created order_items table with correct structure<br>";
    
} catch (Exception $e) {
    echo "❌ Error creating order_items table: " . $e->getMessage() . "<br>";
}

// 3. Verify table structures
echo "<h2>3. Verifying Table Structures</h2>";

try {
    $ordersColumns = $pdo->query("DESCRIBE orders")->fetchAll();
    echo "<h3>Orders table columns:</h3>";
    echo "<ul>";
    foreach ($ordersColumns as $column) {
        echo "<li><strong>" . $column['Field'] . "</strong> - " . $column['Type'] . "</li>";
    }
    echo "</ul>";
    
    $itemsColumns = $pdo->query("DESCRIBE order_items")->fetchAll();
    echo "<h3>Order items table columns:</h3>";
    echo "<ul>";
    foreach ($itemsColumns as $column) {
        echo "<li><strong>" . $column['Field'] . "</strong> - " . $column['Type'] . "</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "❌ Error verifying table structures: " . $e->getMessage() . "<br>";
}

// 4. Test complete order insertion workflow
echo "<h2>4. Testing Complete Order Workflow</h2>";

$testOrderData = [
    'customer_name' => 'Database Repair Test Customer',
    'customer_phone' => '07123456789',
    'address' => 'Test Address, Baghdad, Iraq',
    'province' => 'بغداد',
    'subtotal' => 75000.00,
    'delivery_price' => 5000.00,
    'discount_amount' => 5000.00,
    'total_price' => 75000.00,
    'payment_method' => 'cash_on_delivery',
    'status' => 'pending',
    'notes' => 'Database repair test order'
];

echo "<h3>Step 1: Insert Order</h3>";
$orderId = insertData('orders', $testOrderData);

if ($orderId) {
    echo "✅ Order inserted successfully - ID: $orderId<br>";
    
    echo "<h3>Step 2: Insert Order Items</h3>";
    $testItems = [
        [
            'order_id' => $orderId,
            'product_id' => 1,
            'product_name' => 'Test Product 1',
            'quantity' => 2,
            'price' => 25000.00,
            'total' => 50000.00
        ],
        [
            'order_id' => $orderId,
            'product_id' => 2,
            'product_name' => 'Test Product 2',
            'quantity' => 1,
            'price' => 25000.00,
            'total' => 25000.00
        ]
    ];
    
    $itemsInserted = 0;
    foreach ($testItems as $item) {
        $itemId = insertData('order_items', $item);
        if ($itemId) {
            $itemsInserted++;
            echo "✅ Order item inserted - ID: $itemId<br>";
        } else {
            echo "❌ Failed to insert order item<br>";
        }
    }
    
    echo "<h3>Step 3: Verify Data</h3>";
    $verifyOrder = fetchOne("SELECT * FROM orders WHERE id = ?", [$orderId]);
    $verifyItems = fetchAll("SELECT * FROM order_items WHERE order_id = ?", [$orderId]);
    
    if ($verifyOrder && count($verifyItems) === $itemsInserted) {
        echo "✅ Data verification successful<br>";
        echo "<h4>Order Summary:</h4>";
        echo "<ul>";
        echo "<li>Customer: " . $verifyOrder['customer_name'] . "</li>";
        echo "<li>Total: " . number_format($verifyOrder['total_price']) . " IQD</li>";
        echo "<li>Items: " . count($verifyItems) . "</li>";
        echo "<li>Status: " . $verifyOrder['status'] . "</li>";
        echo "</ul>";
    } else {
        echo "❌ Data verification failed<br>";
    }
    
    // Clean up test data
    $pdo->prepare("DELETE FROM order_items WHERE order_id = ?")->execute([$orderId]);
    $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$orderId]);
    echo "✅ Test data cleaned up<br>";
    
} else {
    echo "❌ Order insertion failed<br>";
}

// 5. Final status
echo "<h2>5. Repair Complete</h2>";

if ($orderId ?? false) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 Database Schema Repair Successful!</h3>";
    echo "<p>The database has been repaired and the checkout process should now work correctly.</p>";
    echo "<ul>";
    echo "<li>✅ Orders table recreated with correct structure</li>";
    echo "<li>✅ Order items table recreated with correct structure</li>";
    echo "<li>✅ Foreign key relationships established</li>";
    echo "<li>✅ Order insertion tested and working</li>";
    echo "<li>✅ Order item insertion tested and working</li>";
    echo "<li>✅ Data retrieval tested and working</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ Repair Incomplete</h3>";
    echo "<p>Some issues remain. Please check the error messages above.</p>";
    echo "</div>";
}

echo "<h2>6. Test the Checkout Process</h2>";
echo "<p>Now test the checkout process to ensure everything works:</p>";
echo "<ul>";
echo "<li><a href='products.php' target='_blank'>Add products to cart</a></li>";
echo "<li><a href='checkout.php' target='_blank'>Test checkout process</a></li>";
echo "<li><a href='admin/orders.php' target='_blank'>Check admin orders page</a></li>";
echo "<li><a href='verify_checkout_fix.php' target='_blank'>Run verification test</a></li>";
echo "</ul>";
?>
