/**
 * Content Management System JavaScript
 * Handles interactions for influencers and guidelines pages
 */

class ContentManagement {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupVideoEmbeds();
        this.setupLazyLoading();
        this.setupSearchFilters();
    }

    setupEventListeners() {
        // Card click handlers
        document.addEventListener('click', (e) => {
            const influencerCard = e.target.closest('.influencer-card');
            const guidelineCard = e.target.closest('.guideline-card');

            if (influencerCard) {
                this.openInfluencerModal(influencerCard.dataset.id);
            } else if (guidelineCard) {
                this.openGuidelineModal(guidelineCard.dataset.id);
            }
        });

        // Filter form submission
        const filterForms = document.querySelectorAll('.filter-form');
        filterForms.forEach(form => {
            form.addEventListener('submit', (e) => {
                this.handleFilterSubmit(e);
            });
        });

        // Search input with debounce
        const searchInputs = document.querySelectorAll('.search-input');
        searchInputs.forEach(input => {
            let timeout;
            input.addEventListener('input', (e) => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    this.handleSearch(e.target.value);
                }, 500);
            });
        });

        // Rating interactions
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('rating-star')) {
                this.handleRatingClick(e);
            }
        });
    }

    setupVideoEmbeds() {
        const videoContainers = document.querySelectorAll('.video-embed');
        videoContainers.forEach(container => {
            const videoUrl = container.dataset.videoUrl;
            const platform = container.dataset.platform || 'youtube';
            
            if (videoUrl) {
                this.embedVideo(container, videoUrl, platform);
            }
        });
    }

    embedVideo(container, url, platform) {
        let embedHtml = '';

        switch (platform) {
            case 'youtube':
                const youtubeId = this.extractYouTubeId(url);
                if (youtubeId) {
                    embedHtml = `
                        <div class="ratio ratio-16x9">
                            <iframe src="https://www.youtube.com/embed/${youtubeId}" 
                                    allowfullscreen loading="lazy"></iframe>
                        </div>
                    `;
                }
                break;

            case 'instagram':
                embedHtml = `
                    <div class="instagram-embed">
                        <a href="${url}" target="_blank" class="btn btn-primary">
                            <i class="bi bi-instagram"></i> مشاهدة على Instagram
                        </a>
                    </div>
                `;
                break;

            case 'tiktok':
                embedHtml = `
                    <div class="tiktok-embed">
                        <a href="${url}" target="_blank" class="btn btn-dark">
                            <i class="bi bi-tiktok"></i> مشاهدة على TikTok
                        </a>
                    </div>
                `;
                break;

            default:
                embedHtml = `
                    <div class="video-link">
                        <a href="${url}" target="_blank" class="btn btn-outline-primary">
                            <i class="bi bi-play-circle"></i> مشاهدة الفيديو
                        </a>
                    </div>
                `;
        }

        container.innerHTML = embedHtml;
    }

    extractYouTubeId(url) {
        const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
        const match = url.match(regExp);
        return (match && match[2].length === 11) ? match[2] : null;
    }

    setupLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback for older browsers
            images.forEach(img => {
                img.src = img.dataset.src;
                img.classList.remove('lazy');
            });
        }
    }

    setupSearchFilters() {
        const filterInputs = document.querySelectorAll('.filter-input');
        filterInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.applyFilters();
            });
        });
    }

    openInfluencerModal(id) {
        const modal = new bootstrap.Modal(document.getElementById('influencerModal'));
        const modalBody = document.querySelector('#influencerModal .modal-body');
        
        // Show loading
        modalBody.innerHTML = this.getLoadingHTML();
        modal.show();

        // Track view
        this.trackView('influencer', id);

        // Fetch content
        fetch(`ajax/get_influencer_content.php?id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    modalBody.innerHTML = this.generateInfluencerHTML(data.content);
                    this.setupModalVideoEmbeds();
                } else {
                    modalBody.innerHTML = this.getErrorHTML('حدث خطأ في جلب المحتوى');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                modalBody.innerHTML = this.getErrorHTML('حدث خطأ في الاتصال');
            });
    }

    openGuidelineModal(id) {
        const modal = new bootstrap.Modal(document.getElementById('guidelineModal'));
        const modalBody = document.querySelector('#guidelineModal .modal-body');
        
        // Show loading
        modalBody.innerHTML = this.getLoadingHTML();
        modal.show();

        // Track view
        this.trackView('guideline', id);

        // Fetch content
        fetch(`ajax/get_guideline.php?id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    modalBody.innerHTML = data.html;
                    this.setupModalVideoEmbeds();
                } else {
                    modalBody.innerHTML = this.getErrorHTML('حدث خطأ في جلب الإرشاد');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                modalBody.innerHTML = this.getErrorHTML('حدث خطأ في الاتصال');
            });
    }

    generateInfluencerHTML(content) {
        let html = '<div class="influencer-detail">';
        
        // Header
        html += '<div class="d-flex align-items-center mb-4">';
        
        // Avatar
        let avatarUrl = 'https://via.placeholder.com/80x80/f8f9fa/6c757d?text=صورة';
        if (content.influencer_image_type === 'url' && content.influencer_image) {
            avatarUrl = content.influencer_image;
        } else if (content.influencer_image_type === 'upload' && content.influencer_image) {
            avatarUrl = `uploads/influencers/${content.influencer_image}`;
        }
        
        html += `<img src="${avatarUrl}" alt="${content.influencer_name}" class="rounded-circle me-3" style="width: 80px; height: 80px; object-fit: cover;">`;
        
        html += '<div>';
        html += `<h4>${content.influencer_name}</h4>`;
        
        // Rating
        if (content.rating) {
            html += '<div class="rating-display mb-2">';
            for (let i = 1; i <= 5; i++) {
                const starClass = i <= content.rating ? 'bi-star-fill text-warning' : 'bi-star text-muted';
                html += `<i class="bi ${starClass}"></i>`;
            }
            html += `<span class="ms-2">(${content.rating}/5)</span>`;
            html += '</div>';
        }
        
        // Content type badge
        const typeColors = { video: 'danger', review: 'warning', post: 'info' };
        const typeNames = { video: 'فيديو', review: 'مراجعة', post: 'منشور' };
        html += `<span class="badge bg-${typeColors[content.content_type]}">${typeNames[content.content_type]}</span>`;
        
        html += '</div>';
        html += '</div>';
        
        // Title
        if (content.content_title) {
            html += `<h5 class="mb-3">${content.content_title}</h5>`;
        }
        
        // Content
        html += `<div class="content-text mb-4">${content.content_text.replace(/\n/g, '<br>')}</div>`;
        
        // Video
        if (content.video_url) {
            html += '<div class="video-section mb-4">';
            html += '<h6 class="text-danger"><i class="bi bi-play-circle"></i> الفيديو</h6>';
            html += `<div class="video-embed" data-video-url="${content.video_url}" data-platform="${content.video_platform}"></div>`;
            html += '</div>';
        }
        
        // Product link
        if (content.product_name) {
            html += '<div class="product-section">';
            html += '<h6><i class="bi bi-box"></i> المنتج المرتبط</h6>';
            html += `<span class="badge bg-light text-dark">${content.product_name}</span>`;
            html += '</div>';
        }
        
        html += '</div>';
        
        return html;
    }

    setupModalVideoEmbeds() {
        const modalVideoEmbeds = document.querySelectorAll('.modal .video-embed');
        modalVideoEmbeds.forEach(container => {
            const videoUrl = container.dataset.videoUrl;
            const platform = container.dataset.platform || 'youtube';
            
            if (videoUrl) {
                this.embedVideo(container, videoUrl, platform);
            }
        });
    }

    trackView(type, id) {
        fetch('ajax/track_view.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `type=${type}&id=${id}`
        }).catch(error => {
            console.error('Error tracking view:', error);
        });
    }

    handleFilterSubmit(e) {
        e.preventDefault();
        const form = e.target;
        const formData = new FormData(form);
        const params = new URLSearchParams(formData);
        
        // Update URL without page reload
        const newUrl = `${window.location.pathname}?${params.toString()}`;
        window.history.pushState({}, '', newUrl);
        
        // Reload content
        this.loadFilteredContent(params);
    }

    handleSearch(query) {
        const currentParams = new URLSearchParams(window.location.search);
        
        if (query.trim()) {
            currentParams.set('search', query);
        } else {
            currentParams.delete('search');
        }
        
        currentParams.delete('page'); // Reset to first page
        
        const newUrl = `${window.location.pathname}?${currentParams.toString()}`;
        window.history.pushState({}, '', newUrl);
        
        this.loadFilteredContent(currentParams);
    }

    loadFilteredContent(params) {
        const contentContainer = document.querySelector('.content-grid');
        if (!contentContainer) return;
        
        // Show loading
        contentContainer.innerHTML = this.getLoadingHTML();
        
        // Fetch filtered content
        fetch(`ajax/filter_content.php?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    contentContainer.innerHTML = data.html;
                    this.setupVideoEmbeds();
                    this.setupLazyLoading();
                } else {
                    contentContainer.innerHTML = this.getErrorHTML('حدث خطأ في تحميل المحتوى');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                contentContainer.innerHTML = this.getErrorHTML('حدث خطأ في الاتصال');
            });
    }

    applyFilters() {
        const filterForm = document.querySelector('.filter-form');
        if (filterForm) {
            const formData = new FormData(filterForm);
            const params = new URLSearchParams(formData);
            this.loadFilteredContent(params);
        }
    }

    handleRatingClick(e) {
        const rating = parseInt(e.target.dataset.rating);
        const ratingContainer = e.target.closest('.rating-filter');
        
        if (ratingContainer) {
            const currentParams = new URLSearchParams(window.location.search);
            currentParams.set('rating', rating);
            currentParams.delete('page');
            
            const newUrl = `${window.location.pathname}?${currentParams.toString()}`;
            window.history.pushState({}, '', newUrl);
            
            this.loadFilteredContent(currentParams);
        }
    }

    getLoadingHTML() {
        return `
            <div class="text-center py-5">
                <div class="loading-spinner"></div>
                <p class="mt-3 text-muted">جاري التحميل...</p>
            </div>
        `;
    }

    getErrorHTML(message) {
        return `
            <div class="alert alert-danger text-center">
                <i class="bi bi-exclamation-triangle fs-1 mb-3"></i>
                <h5>حدث خطأ</h5>
                <p>${message}</p>
                <button class="btn btn-outline-danger" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }

    // Utility methods
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    showToast(message, type = 'info') {
        const toastContainer = document.querySelector('.toast-container') || this.createToastContainer();
        
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        toastContainer.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    createToastContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
        return container;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ContentManagement();
});

// Export for use in other scripts
window.ContentManagement = ContentManagement;
