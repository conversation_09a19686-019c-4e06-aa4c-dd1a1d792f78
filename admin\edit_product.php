<?php
require_once '../config/config.php';
requireAdminLogin();

// التحقق من وجود معرف المنتج
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = 'معرف المنتج غير صحيح';
    header('Location: products.php');
    exit();
}

$productId = (int)$_GET['id'];

// جلب بيانات المنتج
$product = fetchOne("SELECT * FROM products WHERE id = ?", [$productId]);

if (!$product) {
    $_SESSION['error'] = 'المنتج غير موجود';
    header('Location: products.php');
    exit();
}

$pageTitle = 'تعديل المنتج: ' . $product['name'];

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = sanitizeInput($_POST['name']);
    $description = sanitizeInput($_POST['description']);
    $short_description = sanitizeInput($_POST['short_description']);
    $ingredients = sanitizeInput($_POST['ingredients'] ?? '');
    $usage_instructions = sanitizeInput($_POST['usage_instructions'] ?? '');
    $price = (float)$_POST['price'];
    $discount = (float)($_POST['discount'] ?? 0);
    $stock = (int)$_POST['stock'];
    $category_id = (int)$_POST['category_id'];
    $image_url_1 = sanitizeInput($_POST['image_url_1'] ?? '');
    $image_url_2 = sanitizeInput($_POST['image_url_2'] ?? '');
    $image_url_3 = sanitizeInput($_POST['image_url_3'] ?? '');
    $image_url_4 = sanitizeInput($_POST['image_url_4'] ?? '');
    $image_url_5 = sanitizeInput($_POST['image_url_5'] ?? '');
    $video_url = sanitizeInput($_POST['video_url'] ?? '');
    $video_url_1 = sanitizeInput($_POST['video_url_1'] ?? '');
    $video_url_2 = sanitizeInput($_POST['video_url_2'] ?? '');
    $video_url_3 = sanitizeInput($_POST['video_url_3'] ?? '');
    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
    $status = sanitizeInput($_POST['status']);
    
    $errors = [];
    
    // التحقق من البيانات
    if (empty($name)) $errors[] = 'اسم المنتج مطلوب';
    if (empty($description)) $errors[] = 'وصف المنتج مطلوب';
    if ($price <= 0) $errors[] = 'السعر يجب أن يكون أكبر من صفر';
    if ($stock < 0) $errors[] = 'المخزون لا يمكن أن يكون سالباً';
    if ($category_id <= 0) $errors[] = 'يجب اختيار تصنيف';
    
    // التحقق من صحة روابط الصور
    $imageUrls = [$image_url_1, $image_url_2, $image_url_3, $image_url_4, $image_url_5];
    foreach ($imageUrls as $i => $url) {
        if (!empty($url) && !filter_var($url, FILTER_VALIDATE_URL)) {
            $errors[] = 'رابط الصورة ' . ($i + 1) . ' غير صحيح';
        }
    }

    // التحقق من صحة روابط الفيديوهات
    $videoUrls = [$video_url, $video_url_1, $video_url_2, $video_url_3];
    foreach ($videoUrls as $i => $url) {
        if (!empty($url) && !filter_var($url, FILTER_VALIDATE_URL)) {
            $videoLabel = $i == 0 ? 'الفيديو الرئيسي' : 'الفيديو ' . $i;
            $errors[] = 'رابط ' . $videoLabel . ' غير صحيح';
        }
    }
    
    if (empty($errors)) {
        $productData = [
            'name' => $name,
            'description' => $description,
            'short_description' => $short_description,
            'ingredients' => $ingredients,
            'usage_instructions' => $usage_instructions,
            'price' => $price,
            'discount' => $discount,
            'stock' => $stock,
            'category_id' => $category_id,
            'image_url_1' => $image_url_1,
            'image_url_2' => $image_url_2,
            'image_url_3' => $image_url_3,
            'image_url_4' => $image_url_4,
            'image_url_5' => $image_url_5,
            'video_url' => $video_url,
            'video_url_1' => $video_url_1,
            'video_url_2' => $video_url_2,
            'video_url_3' => $video_url_3,
            'is_featured' => $is_featured,
            'status' => $status
        ];
        
        try {
            $result = updateData('products', $productData, 'id = ?', [$productId]);

            if ($result !== false && $result > 0) {
                $_SESSION['success'] = 'تم تحديث المنتج بنجاح';
                header('Location: products.php');
                exit();
            } else {
                $errors[] = 'حدث خطأ أثناء تحديث المنتج (لم يتم تحديث أي صف)';
                error_log("Product update failed for ID $productId. Result: " . var_export($result, true));
                error_log("Product data: " . print_r($productData, true));
            }
        } catch (Exception $e) {
            $errors[] = 'حدث خطأ أثناء تحديث المنتج: ' . $e->getMessage();
            error_log("Product update exception for ID $productId: " . $e->getMessage());
        }
    }
}

// جلب التصنيفات
$categories = fetchAll("SELECT id, name FROM categories WHERE status = 'active' ORDER BY name");
if (!$categories) {
    $categories = [];
}

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-professional fade-in-professional">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-professional-dark">
                            <i class="bi bi-pencil-square text-professional-primary"></i> تعديل المنتج
                        </h5>
                        <a href="products.php" class="btn-professional btn-outline-secondary hover-lift-professional">
                            <i class="bi bi-arrow-right"></i> العودة للمنتجات
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert-professional alert-danger slide-up-professional">
                            <i class="bi bi-exclamation-triangle-fill alert-icon"></i>
                            <div class="alert-content">
                                <strong>يرجى تصحيح الأخطاء التالية:</strong>
                                <ul class="mb-0 mt-2">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo $error; ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="form-professional">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group-professional">
                                    <label for="name" class="form-label">اسم المنتج *</label>
                                    <input type="text" class="form-control focus-professional" id="name" name="name"
                                           value="<?php echo htmlspecialchars($_POST['name'] ?? $product['name']); ?>" required
                                           placeholder="أدخل اسم المنتج">
                                </div>

                                <div class="form-group-professional">
                                    <label for="short_description" class="form-label">وصف مختصر</label>
                                    <textarea class="form-control focus-professional" id="short_description" name="short_description"
                                              rows="2" maxlength="500" placeholder="وصف مختصر للمنتج"><?php echo htmlspecialchars($_POST['short_description'] ?? $product['short_description']); ?></textarea>
                                    <div class="form-text text-professional-muted">حد أقصى 500 حرف</div>
                                </div>

                                <div class="form-group-professional">
                                    <label for="description" class="form-label">وصف المنتج *</label>
                                    <textarea class="form-control focus-professional" id="description" name="description"
                                              rows="6" required placeholder="وصف تفصيلي للمنتج"><?php echo htmlspecialchars($_POST['description'] ?? $product['description']); ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="ingredients" class="form-label">المكونات</label>
                                    <textarea class="form-control" id="ingredients" name="ingredients" 
                                              rows="4" placeholder="اكتب مكونات المنتج..."><?php echo htmlspecialchars($_POST['ingredients'] ?? $product['ingredients']); ?></textarea>
                                    <div class="form-text">قائمة بمكونات المنتج (اختياري)</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="usage_instructions" class="form-label">الاستخدام</label>
                                    <textarea class="form-control" id="usage_instructions" name="usage_instructions" 
                                              rows="4" placeholder="اكتب تعليمات استخدام المنتج..."><?php echo htmlspecialchars($_POST['usage_instructions'] ?? $product['usage_instructions']); ?></textarea>
                                    <div class="form-text">تعليمات وطريقة استخدام المنتج (اختياري)</div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-images"></i> صور المنتج (5 صور كحد أقصى)
                                    </label>
                                    <div class="row g-2">
                                        <div class="col-12">
                                            <label for="image_url_1" class="form-label text-muted small">الصورة الرئيسية</label>
                                            <input type="url" class="form-control" id="image_url_1" name="image_url_1"
                                                   placeholder="https://example.com/image1.jpg"
                                                   value="<?php echo htmlspecialchars($_POST['image_url_1'] ?? $product['image_url_1']); ?>">
                                        </div>
                                        <div class="col-6">
                                            <label for="image_url_2" class="form-label text-muted small">الصورة الثانية</label>
                                            <input type="url" class="form-control" id="image_url_2" name="image_url_2"
                                                   placeholder="https://example.com/image2.jpg"
                                                   value="<?php echo htmlspecialchars($_POST['image_url_2'] ?? $product['image_url_2']); ?>">
                                        </div>
                                        <div class="col-6">
                                            <label for="image_url_3" class="form-label text-muted small">الصورة الثالثة</label>
                                            <input type="url" class="form-control" id="image_url_3" name="image_url_3"
                                                   placeholder="https://example.com/image3.jpg"
                                                   value="<?php echo htmlspecialchars($_POST['image_url_3'] ?? $product['image_url_3']); ?>">
                                        </div>
                                        <div class="col-6">
                                            <label for="image_url_4" class="form-label text-muted small">الصورة الرابعة</label>
                                            <input type="url" class="form-control" id="image_url_4" name="image_url_4"
                                                   placeholder="https://example.com/image4.jpg"
                                                   value="<?php echo htmlspecialchars($_POST['image_url_4'] ?? $product['image_url_4']); ?>">
                                        </div>
                                        <div class="col-6">
                                            <label for="image_url_5" class="form-label text-muted small">الصورة الخامسة</label>
                                            <input type="url" class="form-control" id="image_url_5" name="image_url_5"
                                                   placeholder="https://example.com/image5.jpg"
                                                   value="<?php echo htmlspecialchars($_POST['image_url_5'] ?? $product['image_url_5']); ?>">
                                        </div>
                                    </div>
                                    <div class="form-text">أدخل روابط الصور الخارجية (اختياري) - الصورة الأولى ستكون الصورة الرئيسية</div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-play-circle"></i> فيديوهات المنتج (3 فيديوهات كحد أقصى)
                                    </label>
                                    <div class="row g-2">
                                        <div class="col-12">
                                            <label for="video_url" class="form-label text-muted small">الفيديو الرئيسي</label>
                                            <input type="url" class="form-control" id="video_url" name="video_url"
                                                   placeholder="https://youtube.com/watch?v=..."
                                                   value="<?php echo htmlspecialchars($_POST['video_url'] ?? $product['video_url']); ?>">
                                        </div>
                                        <div class="col-6">
                                            <label for="video_url_1" class="form-label text-muted small">الفيديو الثاني</label>
                                            <input type="url" class="form-control" id="video_url_1" name="video_url_1"
                                                   placeholder="https://youtube.com/watch?v=..."
                                                   value="<?php echo htmlspecialchars($_POST['video_url_1'] ?? $product['video_url_1']); ?>">
                                        </div>
                                        <div class="col-6">
                                            <label for="video_url_2" class="form-label text-muted small">الفيديو الثالث</label>
                                            <input type="url" class="form-control" id="video_url_2" name="video_url_2"
                                                   placeholder="https://youtube.com/watch?v=..."
                                                   value="<?php echo htmlspecialchars($_POST['video_url_2'] ?? $product['video_url_2']); ?>">
                                        </div>
                                        <div class="col-12">
                                            <label for="video_url_3" class="form-label text-muted small">الفيديو الرابع</label>
                                            <input type="url" class="form-control" id="video_url_3" name="video_url_3"
                                                   placeholder="https://vimeo.com/..."
                                                   value="<?php echo htmlspecialchars($_POST['video_url_3'] ?? $product['video_url_3']); ?>">
                                        </div>
                                    </div>
                                    <div class="form-text">أدخل روابط الفيديوهات من YouTube، Vimeo أو مواقع أخرى (اختياري)</div>
                                </div>

                                <div class="mb-3">
                                    <label for="category_id" class="form-label">التصنيف *</label>
                                    <select class="form-select" id="category_id" name="category_id" required>
                                        <option value="">اختر التصنيف</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo $category['id']; ?>"
                                                    <?php echo ($_POST['category_id'] ?? $product['category_id']) == $category['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($category['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="row">
                                    <div class="col-6">
                                        <div class="mb-3">
                                            <label for="price" class="form-label">السعر *</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="price" name="price"
                                                       step="0.01" min="0" value="<?php echo $_POST['price'] ?? $product['price']; ?>" required>
                                                <span class="input-group-text">دينار عراقي</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="mb-3">
                                            <label for="discount" class="form-label">نسبة الخصم</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="discount" name="discount"
                                                       step="0.01" min="0" max="100" value="<?php echo $_POST['discount'] ?? $product['discount']; ?>">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="stock" class="form-label">المخزون *</label>
                                    <input type="number" class="form-control" id="stock" name="stock"
                                           min="0" value="<?php echo $_POST['stock'] ?? $product['stock']; ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="status" class="form-label">الحالة</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active" <?php echo ($_POST['status'] ?? $product['status']) == 'active' ? 'selected' : ''; ?>>نشط</option>
                                        <option value="inactive" <?php echo ($_POST['status'] ?? $product['status']) == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured"
                                               <?php echo ($_POST['is_featured'] ?? $product['is_featured']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_featured">
                                            منتج مميز
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr class="border-professional">

                        <div class="btn-group-professional justify-content-between">
                            <a href="products.php" class="btn-professional btn-outline-secondary hover-lift-professional">
                                <i class="bi bi-x-circle"></i> إلغاء
                            </a>
                            <button type="submit" class="btn-professional btn-primary hover-lift-professional">
                                <i class="bi bi-check-circle"></i> حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// معاينة الصور من الروابط
function previewImageFromUrl(inputId, previewId) {
    const input = document.getElementById(inputId);
    const url = input.value.trim();

    let preview = document.getElementById(previewId);
    if (!preview) {
        preview = document.createElement('img');
        preview.id = previewId;
        preview.className = 'img-thumbnail mt-2';
        preview.style.maxWidth = '150px';
        preview.style.maxHeight = '150px';
        preview.style.display = 'none';
        input.parentNode.appendChild(preview);
    }

    if (url && isValidImageUrl(url)) {
        preview.src = url;
        preview.style.display = 'block';
        preview.onerror = function() {
            this.style.display = 'none';
        };
    } else {
        preview.style.display = 'none';
    }
}

function isValidImageUrl(url) {
    return /\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i.test(url) || url.includes('imgur.com') || url.includes('cloudinary.com');
}

// إضافة مستمعات الأحداث لمعاينة الصور
for (let i = 1; i <= 5; i++) {
    const imageInput = document.getElementById('image_url_' + i);
    if (imageInput) {
        imageInput.addEventListener('input', function() {
            previewImageFromUrl('image_url_' + i, 'preview-' + i);
        });
    }
}

// إضافة مستمعات الأحداث للفيديوهات
const videoInputs = ['video_url', 'video_url_1', 'video_url_2', 'video_url_3'];
videoInputs.forEach(function(inputId) {
    const videoInput = document.getElementById(inputId);
    if (videoInput) {
        videoInput.addEventListener('input', function() {
            validateVideoUrl(inputId);
        });
    }
});

// دالة للتحقق من صحة رابط الفيديو
function validateVideoUrl(inputId) {
    const input = document.getElementById(inputId);
    const url = input.value.trim();

    // إزالة رسائل التحقق السابقة
    const existingFeedback = input.parentNode.querySelector('.video-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }

    if (url) {
        const isYoutube = url.includes('youtube.com') || url.includes('youtu.be');
        const isVimeo = url.includes('vimeo.com');
        const isValidUrl = /^https?:\/\/.+/.test(url);

        const feedback = document.createElement('div');
        feedback.className = 'video-feedback form-text mt-1';

        if (isYoutube) {
            feedback.innerHTML = '<i class="bi bi-youtube text-danger"></i> فيديو YouTube';
            feedback.className += ' text-success';
        } else if (isVimeo) {
            feedback.innerHTML = '<i class="bi bi-vimeo text-primary"></i> فيديو Vimeo';
            feedback.className += ' text-success';
        } else if (isValidUrl) {
            feedback.innerHTML = '<i class="bi bi-link-45deg"></i> رابط فيديو خارجي';
            feedback.className += ' text-info';
        } else {
            feedback.innerHTML = '<i class="bi bi-exclamation-triangle"></i> رابط غير صحيح';
            feedback.className += ' text-danger';
        }

        input.parentNode.appendChild(feedback);
    }
}

// حساب السعر بعد الخصم
function calculateDiscountedPrice() {
    const price = parseFloat(document.getElementById('price').value) || 0;
    const discount = parseFloat(document.getElementById('discount').value) || 0;
    const discountedPrice = price - (price * discount / 100);

    let priceInfo = document.getElementById('price-info');
    if (!priceInfo) {
        priceInfo = document.createElement('div');
        priceInfo.id = 'price-info';
        priceInfo.className = 'form-text';
        document.getElementById('discount').parentNode.appendChild(priceInfo);
    }

    if (discount > 0) {
        // عرض السعر كرقم صحيح بدون فواصل عشرية
        const wholePriceAfterDiscount = Math.round(discountedPrice);
        priceInfo.innerHTML = `السعر بعد الخصم: <strong>${wholePriceAfterDiscount.toLocaleString('ar-SA')} دينار عراقي</strong>`;
        priceInfo.className = 'form-text text-success';
    } else {
        priceInfo.innerHTML = '';
    }
}

document.getElementById('price').addEventListener('input', calculateDiscountedPrice);
document.getElementById('discount').addEventListener('input', calculateDiscountedPrice);

// تشغيل حساب السعر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    calculateDiscountedPrice();

    // معاينة الصور الموجودة
    for (let i = 1; i <= 5; i++) {
        previewImageFromUrl('image_url_' + i, 'preview-' + i);
    }

    // التحقق من الفيديوهات الموجودة
    const videoInputs = ['video_url', 'video_url_1', 'video_url_2', 'video_url_3'];
    videoInputs.forEach(function(inputId) {
        validateVideoUrl(inputId);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
