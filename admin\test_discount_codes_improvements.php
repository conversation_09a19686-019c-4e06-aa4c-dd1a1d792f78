<?php
/**
 * اختبار تحسينات إدارة أكواد الخصم
 * Discount Codes Management Improvements Test
 */

require_once '../config/config.php';
require_once '../config/database.php';

// التحقق من الجلسة
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$testResults = [];

// Test 1: Currency Formatting for Discount Values
try {
    $testAmount = 150.75;
    $formattedAmount = formatPrice($testAmount);
    
    if (strpos($formattedAmount, 'دينار عراقي') !== false && strpos($formattedAmount, '.') === false) {
        $testResults['currency'] = '✅ تنسيق العملة للخصومات: ' . $formattedAmount;
    } else {
        $testResults['currency'] = '❌ خطأ في تنسيق العملة: ' . $formattedAmount;
    }
} catch (Exception $e) {
    $testResults['currency'] = '❌ خطأ في اختبار العملة: ' . $e->getMessage();
}

// Test 2: Discount Code CRUD Operations
try {
    // Test discount code creation
    $testCodeData = [
        'code' => 'TEST' . time(),
        'type' => 'fixed',
        'amount' => 100,
        'min_order_amount' => 500,
        'usage_limit' => 10,
        'expiration_date' => date('Y-m-d', strtotime('+30 days')),
        'status' => 'active'
    ];
    
    $codeId = insertData('discount_codes', $testCodeData);
    
    if ($codeId) {
        $testResults['create'] = '✅ إنشاء كود الخصم: نجح';
        
        // Test discount code update
        $updateData = ['amount' => 150];
        $updateResult = updateData('discount_codes', $updateData, 'id = ?', [$codeId]);
        
        if ($updateResult !== false && $updateResult > 0) {
            $testResults['update'] = '✅ تحديث كود الخصم: نجح';
        } else {
            $testResults['update'] = '❌ تحديث كود الخصم: فشل';
        }
        
        // Test status toggle
        $toggleResult = updateData('discount_codes', ['status' => 'inactive'], 'id = ?', [$codeId]);
        
        if ($toggleResult !== false && $toggleResult > 0) {
            $testResults['toggle'] = '✅ تغيير الحالة: نجح';
        } else {
            $testResults['toggle'] = '❌ تغيير الحالة: فشل';
        }
        
        // Test discount code deletion
        $deleteResult = deleteData('discount_codes', 'id = ?', [$codeId]);
        
        if ($deleteResult) {
            $testResults['delete'] = '✅ حذف كود الخصم: نجح';
        } else {
            $testResults['delete'] = '❌ حذف كود الخصم: فشل';
        }
        
    } else {
        $testResults['create'] = '❌ إنشاء كود الخصم: فشل';
        $testResults['update'] = '⏭️ تم تخطي اختبار التحديث';
        $testResults['toggle'] = '⏭️ تم تخطي اختبار تغيير الحالة';
        $testResults['delete'] = '⏭️ تم تخطي اختبار الحذف';
    }
    
} catch (Exception $e) {
    $testResults['crud'] = '❌ خطأ في اختبار عمليات CRUD: ' . $e->getMessage();
}

// Test 3: Discount Code Validation
try {
    $validCodes = ['SAVE20', 'DISCOUNT50', 'WELCOME2024'];
    $invalidCodes = ['save20', 'discount-50', 'welcome@2024'];
    
    $validCount = 0;
    $invalidCount = 0;
    
    foreach ($validCodes as $code) {
        if (preg_match('/^[A-Z0-9]+$/', $code)) {
            $validCount++;
        }
    }
    
    foreach ($invalidCodes as $code) {
        if (!preg_match('/^[A-Z0-9]+$/', $code)) {
            $invalidCount++;
        }
    }
    
    if ($validCount == count($validCodes) && $invalidCount == count($invalidCodes)) {
        $testResults['validation'] = '✅ التحقق من صحة أكواد الخصم: نجح';
    } else {
        $testResults['validation'] = '❌ التحقق من صحة أكواد الخصم: فشل';
    }
    
} catch (Exception $e) {
    $testResults['validation'] = '❌ خطأ في اختبار التحقق من الأكواد: ' . $e->getMessage();
}

// Test 4: Database Functions
try {
    $discountCodes = fetchAll("SELECT * FROM discount_codes LIMIT 5");
    $codesCount = fetchOne("SELECT COUNT(*) as count FROM discount_codes");
    
    if (is_array($discountCodes) && isset($codesCount['count'])) {
        $testResults['database'] = '✅ دوال قاعدة البيانات: تعمل بشكل صحيح';
    } else {
        $testResults['database'] = '❌ دوال قاعدة البيانات: خطأ في الاستعلام';
    }
    
} catch (Exception $e) {
    $testResults['database'] = '❌ خطأ في اختبار قاعدة البيانات: ' . $e->getMessage();
}

// Test 5: Discount Type Validation
try {
    $percentageTest = ['type' => 'percentage', 'amount' => 25];
    $fixedTest = ['type' => 'fixed', 'amount' => 100];
    
    $percentageValid = in_array($percentageTest['type'], ['percentage', 'fixed']) && 
                      $percentageTest['amount'] > 0 && 
                      $percentageTest['amount'] <= 100;
                      
    $fixedValid = in_array($fixedTest['type'], ['percentage', 'fixed']) && 
                  $fixedTest['amount'] > 0;
    
    if ($percentageValid && $fixedValid) {
        $testResults['discount_types'] = '✅ التحقق من أنواع الخصم: نجح';
    } else {
        $testResults['discount_types'] = '❌ التحقق من أنواع الخصم: فشل';
    }
    
} catch (Exception $e) {
    $testResults['discount_types'] = '❌ خطأ في اختبار أنواع الخصم: ' . $e->getMessage();
}

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-professional fade-in-professional">
                <div class="card-header">
                    <h5 class="mb-0 text-professional-dark">
                        <i class="bi bi-check2-square text-professional-success"></i> 
                        اختبار تحسينات إدارة أكواد الخصم
                    </h5>
                </div>
                
                <div class="card-body">
                    <div class="alert-professional alert-info slide-up-professional">
                        <i class="bi bi-info-circle-fill alert-icon"></i>
                        <div class="alert-content">
                            <strong>معلومات الاختبار:</strong><br>
                            هذه الصفحة تختبر جميع التحسينات المطبقة على نظام إدارة أكواد الخصم
                        </div>
                    </div>
                    
                    <div class="row">
                        <?php foreach ($testResults as $testName => $result): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <?php 
                                            $titles = [
                                                'currency' => 'تنسيق العملة للخصومات',
                                                'create' => 'إنشاء كود الخصم',
                                                'update' => 'تحديث كود الخصم',
                                                'toggle' => 'تغيير حالة كود الخصم',
                                                'delete' => 'حذف كود الخصم',
                                                'validation' => 'التحقق من صحة الأكواد',
                                                'database' => 'دوال قاعدة البيانات',
                                                'discount_types' => 'التحقق من أنواع الخصم',
                                                'crud' => 'عمليات CRUD'
                                            ];
                                            echo $titles[$testName] ?? $testName;
                                            ?>
                                        </h6>
                                        <p class="card-text"><?php echo $result; ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <hr class="border-professional">
                    
                    <div class="text-center">
                        <h5 class="text-professional-dark mb-3">الخطوات التالية</h5>
                        <div class="btn-group-professional">
                            <a href="discount_codes.php" class="btn-professional btn-primary hover-lift-professional">
                                <i class="bi bi-percent"></i> إدارة أكواد الخصم
                            </a>
                            <a href="categories.php" class="btn-professional btn-outline-primary hover-lift-professional">
                                <i class="bi bi-tags"></i> إدارة التصنيفات
                            </a>
                            <a href="products.php" class="btn-professional btn-outline-primary hover-lift-professional">
                                <i class="bi bi-box-seam"></i> إدارة المنتجات
                            </a>
                            <a href="dashboard.php" class="btn-professional btn-outline-secondary hover-lift-professional">
                                <i class="bi bi-speedometer2"></i> لوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations to test result cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.3s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
