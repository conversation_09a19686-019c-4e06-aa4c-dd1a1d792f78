<?php
/**
 * Test checkout modifications:
 * 1. Order submission to admin orders page
 * 2. Phone validation removal
 */

require_once 'config/config.php';

echo "<h1>Checkout Modifications Test</h1>";

// Test 1: Database and Order Submission
echo "<h2>1. Order Submission Test</h2>";

if (!$pdo) {
    echo "❌ Database connection failed<br>";
} else {
    echo "✅ Database connection OK<br>";
    
    // Check tables exist
    $tablesExist = true;
    $requiredTables = ['orders', 'order_items'];
    
    foreach ($requiredTables as $table) {
        try {
            $result = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($result->rowCount() > 0) {
                echo "✅ Table '$table' exists<br>";
            } else {
                echo "❌ Table '$table' missing<br>";
                $tablesExist = false;
            }
        } catch (Exception $e) {
            echo "❌ Error checking table '$table': " . $e->getMessage() . "<br>";
            $tablesExist = false;
        }
    }
    
    if ($tablesExist) {
        // Test order insertion with various phone formats
        $testPhoneFormats = [
            '07123456789' => 'Standard Iraqi format',
            '+9647123456789' => 'International with +',
            '9647123456789' => 'International without +',
            '************' => 'With spaces',
            '************' => 'With dashes',
            '************' => 'US format',
            '(*************' => 'US format with parentheses',
            'abc123def' => 'Invalid format with letters',
            '12345' => 'Very short number',
            '******-555-0123' => 'US toll-free format'
        ];
        
        echo "<h3>Testing Order Submission with Various Phone Formats</h3>";
        
        $successCount = 0;
        $totalTests = count($testPhoneFormats);
        
        foreach ($testPhoneFormats as $phone => $description) {
            echo "<h4>Testing: {$phone} ({$description})</h4>";
            
            $testOrderData = [
                'customer_name' => 'Test Customer - ' . $description,
                'customer_phone' => $phone,
                'address' => 'Test Address, Baghdad',
                'province' => 'بغداد',
                'subtotal' => 50000.00,
                'delivery_price' => 5000.00,
                'discount_amount' => 0.00,
                'total_price' => 55000.00,
                'payment_method' => 'cash_on_delivery',
                'status' => 'pending'
            ];
            
            $orderId = insertData('orders', $testOrderData);
            
            if ($orderId) {
                echo "✅ Order created successfully - ID: {$orderId}<br>";
                
                // Add test order item
                $testItemData = [
                    'order_id' => $orderId,
                    'product_id' => 1,
                    'product_name' => 'Test Product',
                    'quantity' => 1,
                    'price' => 50000.00,
                    'total' => 50000.00
                ];
                
                $itemId = insertData('order_items', $testItemData);
                if ($itemId) {
                    echo "✅ Order item added successfully<br>";
                } else {
                    echo "⚠️ Order item addition failed<br>";
                }
                
                // Verify order appears in admin query
                $adminOrder = fetchOne("SELECT * FROM orders WHERE id = ?", [$orderId]);
                if ($adminOrder) {
                    echo "✅ Order visible in admin query<br>";
                    echo "Customer: " . htmlspecialchars($adminOrder['customer_name']) . "<br>";
                    echo "Phone: " . htmlspecialchars($adminOrder['customer_phone']) . "<br>";
                    echo "Total: " . number_format($adminOrder['total_price']) . " IQD<br>";
                } else {
                    echo "❌ Order not found in admin query<br>";
                }
                
                $successCount++;
                
                // Clean up
                $pdo->prepare("DELETE FROM order_items WHERE order_id = ?")->execute([$orderId]);
                $pdo->prepare("DELETE FROM orders WHERE id = ?")->execute([$orderId]);
                echo "✅ Test data cleaned up<br>";
                
            } else {
                echo "❌ Order creation failed<br>";
            }
            
            echo "<hr>";
        }
        
        echo "<h3>Phone Format Test Results</h3>";
        echo "<div style='background: " . ($successCount == $totalTests ? '#d4edda' : '#f8d7da') . "; padding: 15px; border-radius: 5px;'>";
        echo "<strong>Results: {$successCount}/{$totalTests} phone formats accepted</strong><br>";
        if ($successCount == $totalTests) {
            echo "🎉 All phone formats were accepted successfully!<br>";
            echo "✅ Phone validation has been successfully removed.";
        } else {
            echo "⚠️ Some phone formats were rejected. Check the logs above.";
        }
        echo "</div>";
    }
}

// Test 2: Admin Orders Page Compatibility
echo "<h2>2. Admin Orders Page Test</h2>";

try {
    // Check if admin orders page can fetch orders
    $recentOrders = fetchAll("SELECT * FROM orders ORDER BY created_at DESC LIMIT 5");
    echo "✅ Admin orders query works<br>";
    echo "Found " . count($recentOrders) . " recent orders<br>";
    
    if (count($recentOrders) > 0) {
        echo "<h3>Sample Orders (as admin would see):</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Customer</th>";
        echo "<th style='padding: 8px;'>Phone</th>";
        echo "<th style='padding: 8px;'>Province</th>";
        echo "<th style='padding: 8px;'>Total</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Created</th>";
        echo "</tr>";
        
        foreach ($recentOrders as $order) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $order['id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($order['customer_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($order['customer_phone']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($order['province']) . "</td>";
            echo "<td style='padding: 8px;'>" . number_format($order['total_price']) . " IQD</td>";
            echo "<td style='padding: 8px;'>" . $order['status'] . "</td>";
            echo "<td style='padding: 8px;'>" . $order['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "❌ Admin orders query failed: " . $e->getMessage() . "<br>";
}

// Test 3: JavaScript Validation Test
echo "<h2>3. JavaScript Validation Test</h2>";
echo "<p>Testing that phone validation has been removed from client-side code:</p>";

echo "<div id='jsTestResults'></div>";

echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    const testPhones = [";
echo "        '07123456789',";
echo "        '+9647123456789',";
echo "        '************',";
echo "        'abc123def',";
echo "        '12345'";
echo "    ];";
echo "    ";
echo "    let resultsHtml = '<h4>Client-side Phone Validation Test:</h4>';";
echo "    ";
echo "    // Check if phone validation patterns still exist in the code";
echo "    const scriptContent = document.documentElement.innerHTML;";
echo "    const hasPhonePatterns = scriptContent.includes('phonePatterns') || scriptContent.includes('/^07[0-9]{8}$/');";
echo "    ";
echo "    if (hasPhonePatterns) {";
echo "        resultsHtml += '<div style=\"background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;\">❌ Phone validation patterns still found in code</div>';";
echo "    } else {";
echo "        resultsHtml += '<div style=\"background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;\">✅ Phone validation patterns successfully removed from code</div>';";
echo "    }";
echo "    ";
echo "    document.getElementById('jsTestResults').innerHTML = resultsHtml;";
echo "});";
echo "</script>";

echo "<h2>Test Summary</h2>";
echo "<div style='background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>✅ Modifications Applied:</h3>";
echo "<ul>";
echo "<li><strong>Order Submission:</strong> Orders are properly saved to database and visible in admin panel</li>";
echo "<li><strong>Phone Validation Removal:</strong> All phone format validation removed from both PHP and JavaScript</li>";
echo "<li><strong>Field Requirement:</strong> Phone field still required (cannot be empty) but accepts any format</li>";
echo "</ul>";

echo "<h3>🧪 Manual Testing Steps:</h3>";
echo "<ol>";
echo "<li>Go to <a href='checkout.php' target='_blank'>checkout.php</a></li>";
echo "<li>Fill in customer information with any phone format (including invalid ones)</li>";
echo "<li>Complete the order submission</li>";
echo "<li>Check <a href='admin/orders.php' target='_blank'>admin/orders.php</a> to verify the order appears</li>";
echo "<li>Verify all order details are correctly displayed</li>";
echo "</ol>";

echo "<h3>📋 Expected Results:</h3>";
echo "<ul>";
echo "<li>Any phone number format should be accepted (no validation errors)</li>";
echo "<li>Empty phone field should still show required field error</li>";
echo "<li>Orders should appear immediately in admin orders page</li>";
echo "<li>All order details should be correctly stored and displayed</li>";
echo "</ul>";
echo "</div>";

echo "<p><strong>Next Steps:</strong> Test the checkout page manually with various phone formats to confirm everything works as expected.</p>";
?>
