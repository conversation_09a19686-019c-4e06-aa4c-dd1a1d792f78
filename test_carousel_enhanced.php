<?php
/**
 * Enhanced Carousel Test Page
 * Tests different carousel scenarios and configurations
 */

$pageTitle = 'اختبار الكاروسيل المحسن';
require_once 'config/config.php';

// Test scenarios
$testScenarios = [
    'empty' => 'حالة فارغة - بدون صور',
    'single' => 'شريحة واحدة فقط',
    'multiple' => 'شرائح متعددة',
    'mixed' => 'شرائح مختلطة (مع وبدون نص)'
];

$currentTest = $_GET['test'] ?? 'empty';

// Mock carousel settings for different test scenarios
switch ($currentTest) {
    case 'empty':
        $mockCarouselSettings = [
            'slide_1_image' => '',
            'slide_1_title' => 'مرحباً بك في متجرنا الإلكتروني',
            'slide_1_subtitle' => 'اكتشف مجموعة واسعة من المنتجات عالية الجودة بأفضل الأسعار',
            'slide_1_button_text' => 'تصفح المنتجات',
            'slide_1_button_url' => '/products.php',
            'slide_2_image' => '',
            'slide_3_image' => '',
            'slide_4_image' => '',
            'auto_advance_time' => '8',
            'show_indicators' => '1',
            'show_controls' => '1'
        ];
        break;
        
    case 'single':
        $mockCarouselSettings = [
            'slide_1_image' => 'https://picsum.photos/1200/600?random=1',
            'slide_1_title' => 'عرض خاص محدود',
            'slide_1_subtitle' => 'خصومات تصل إلى 50% على جميع المنتجات',
            'slide_1_button_text' => 'تسوق الآن',
            'slide_1_button_url' => '/products.php',
            'slide_2_image' => '',
            'slide_3_image' => '',
            'slide_4_image' => '',
            'auto_advance_time' => '8',
            'show_indicators' => '1',
            'show_controls' => '1'
        ];
        break;
        
    case 'multiple':
        $mockCarouselSettings = [
            'slide_1_image' => 'https://picsum.photos/1200/600?random=1',
            'slide_1_title' => 'مرحباً بك في متجرنا',
            'slide_1_subtitle' => 'اكتشف أفضل المنتجات',
            'slide_1_button_text' => 'تصفح المنتجات',
            'slide_1_button_url' => '/products.php',
            'slide_2_image' => 'https://picsum.photos/1200/600?random=2',
            'slide_2_title' => 'عروض حصرية',
            'slide_2_subtitle' => 'خصومات مذهلة لفترة محدودة',
            'slide_3_image' => 'https://picsum.photos/1200/600?random=3',
            'slide_3_title' => 'منتجات جديدة',
            'slide_3_subtitle' => 'اكتشف أحدث إضافاتنا',
            'slide_4_image' => 'https://picsum.photos/1200/600?random=4',
            'slide_4_title' => '',
            'slide_4_subtitle' => '',
            'auto_advance_time' => '5',
            'show_indicators' => '1',
            'show_controls' => '1'
        ];
        break;
        
    case 'mixed':
        $mockCarouselSettings = [
            'slide_1_image' => 'https://picsum.photos/1200/600?random=5',
            'slide_1_title' => 'شريحة مع نص كامل',
            'slide_1_subtitle' => 'هذه شريحة تحتوي على عنوان ووصف وزر',
            'slide_1_button_text' => 'اضغط هنا',
            'slide_1_button_url' => '/products.php',
            'slide_2_image' => 'https://picsum.photos/1200/600?random=6',
            'slide_2_title' => '',
            'slide_2_subtitle' => '',
            'slide_3_image' => 'https://picsum.photos/1200/600?random=7',
            'slide_3_title' => 'شريحة مع عنوان فقط',
            'slide_3_subtitle' => '',
            'slide_4_image' => '',
            'auto_advance_time' => '6',
            'show_indicators' => '1',
            'show_controls' => '1'
        ];
        break;
}

// Override the getHomepageSectionSettings function for testing
function getHomepageSectionSettings($section) {
    global $mockCarouselSettings;
    if ($section === 'carousel') {
        return $mockCarouselSettings;
    }
    return [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/homepage.css" rel="stylesheet">
    
    <style>
        body {
            direction: rtl;
            text-align: right;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .test-controls {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            border-right: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="test-controls">
                    <h2><i class="bi bi-gear-fill"></i> اختبار الكاروسيل المحسن</h2>
                    <p>اختر سيناريو الاختبار:</p>
                    
                    <div class="btn-group" role="group">
                        <?php foreach ($testScenarios as $key => $label): ?>
                            <a href="?test=<?php echo $key; ?>" 
                               class="btn <?php echo $currentTest === $key ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                <?php echo $label; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <div class="test-info">
                    <h5><i class="bi bi-info-circle"></i> السيناريو الحالي: <?php echo $testScenarios[$currentTest]; ?></h5>
                    <p>
                        <?php
                        switch ($currentTest) {
                            case 'empty':
                                echo 'يختبر هذا السيناريو الحالة الافتراضية عندما لا توجد صور مضافة للكاروسيل.';
                                break;
                            case 'single':
                                echo 'يختبر هذا السيناريو عرض شريحة واحدة فقط مع النص والأزرار.';
                                break;
                            case 'multiple':
                                echo 'يختبر هذا السيناريو عرض شرائح متعددة مع نصوص مختلفة.';
                                break;
                            case 'mixed':
                                echo 'يختبر هذا السيناريو مزيج من الشرائح - بعضها مع نص وبعضها بدون.';
                                break;
                        }
                        ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Include the enhanced carousel -->
    <?php include 'includes/homepage_carousel.php'; ?>

    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-check-circle"></i> نتائج الاختبار</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>الميزات المختبرة:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check text-success"></i> التصميم المتجاوب</li>
                                    <li><i class="bi bi-check text-success"></i> دعم اللغة العربية RTL</li>
                                    <li><i class="bi bi-check text-success"></i> الحالة الافتراضية</li>
                                    <li><i class="bi bi-check text-success"></i> تأثيرات الانتقال</li>
                                    <li><i class="bi bi-check text-success"></i> التحكم باللمس</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>الإعدادات الحالية:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>وقت التقدم التلقائي:</strong> <?php echo $mockCarouselSettings['auto_advance_time']; ?> ثواني</li>
                                    <li><strong>المؤشرات:</strong> <?php echo $mockCarouselSettings['show_indicators'] ? 'مفعلة' : 'معطلة'; ?></li>
                                    <li><strong>أزرار التحكم:</strong> <?php echo $mockCarouselSettings['show_controls'] ? 'مفعلة' : 'معطلة'; ?></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
