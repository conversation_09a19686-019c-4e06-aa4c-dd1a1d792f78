<?php
/**
 * Test the actual checkout.php file directly
 */

echo "<h1>🎯 Direct Checkout.php Test</h1>";
echo "<p>This will test the actual checkout.php file with real form submission.</p>";

// Start session
if (!isset($_SESSION)) {
    session_start();
}

// Set up test cart
$_SESSION['cart'] = [1 => 2, 2 => 1]; // Test products

echo "<h2>📋 Test Setup</h2>";
echo "Cart created with test products<br>";
echo "Cart contents: " . json_encode($_SESSION['cart']) . "<br>";

// Check if we have products in database
require_once 'config/config.php';

$productCheck = fetchAll("SELECT id, name, price FROM products WHERE status = 'active' LIMIT 5");
echo "Available products in database: " . count($productCheck) . "<br>";

if (count($productCheck) === 0) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>⚠️ No Products Found</h3>";
    echo "<p>You need to add some products to the database first.</p>";
    echo "<p><a href='admin/products.php' target='_blank'>Add Products in Admin Panel</a></p>";
    echo "</div>";
} else {
    echo "<h3>Available Products:</h3>";
    echo "<ul>";
    foreach ($productCheck as $product) {
        echo "<li>ID: {$product['id']} - {$product['name']} - " . number_format($product['price']) . " IQD</li>";
    }
    echo "</ul>";
}

echo "<h2>🧪 Direct Checkout Test</h2>";
echo "<p>This form will submit directly to checkout.php with proper debugging:</p>";

?>

<form action="checkout.php" method="POST" style="max-width: 600px; border: 1px solid #ddd; padding: 20px; border-radius: 5px; background: #f9f9f9;">
    <h3>معلومات العميل</h3>
    
    <div style="margin-bottom: 15px;">
        <label for="customer_name"><strong>اسم العميل *</strong></label><br>
        <input type="text" id="customer_name" name="customer_name" value="Direct Test Customer" required 
               style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 3px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="customer_phone"><strong>رقم الهاتف *</strong></label><br>
        <input type="text" id="customer_phone" name="customer_phone" value="07123456789" required 
               style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 3px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="address"><strong>العنوان *</strong></label><br>
        <textarea id="address" name="address" required 
                  style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 3px; height: 80px;">Direct Test Address, Baghdad, Iraq</textarea>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="province"><strong>المحافظة *</strong></label><br>
        <select id="province" name="province" required 
                style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 3px;">
            <option value="">اختر المحافظة</option>
            <option value="بغداد" selected>بغداد</option>
            <option value="البصرة">البصرة</option>
            <option value="نينوى">نينوى</option>
            <option value="أربيل">أربيل</option>
            <option value="النجف">النجف</option>
            <option value="كربلاء">كربلاء</option>
            <option value="الأنبار">الأنبار</option>
            <option value="ذي قار">ذي قار</option>
            <option value="بابل">بابل</option>
            <option value="كركوك">كركوك</option>
            <option value="ديالى">ديالى</option>
            <option value="المثنى">المثنى</option>
            <option value="القادسية">القادسية</option>
            <option value="ميسان">ميسان</option>
            <option value="واسط">واسط</option>
            <option value="دهوك">دهوك</option>
            <option value="السليمانية">السليمانية</option>
            <option value="صلاح الدين">صلاح الدين</option>
        </select>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="payment_method"><strong>طريقة الدفع</strong></label><br>
        <select id="payment_method" name="payment_method" 
                style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 3px;">
            <option value="cash_on_delivery" selected>الدفع عند الاستلام</option>
            <option value="bank_transfer">تحويل بنكي</option>
        </select>
    </div>
    
    <div style="margin-bottom: 20px;">
        <label for="notes"><strong>ملاحظات</strong></label><br>
        <textarea id="notes" name="notes" 
                  style="width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 3px; height: 60px;">Direct checkout test - please check if this order appears in admin panel</textarea>
    </div>
    
    <div style="text-align: center;">
        <button type="submit" name="place_order" value="1" 
                style="background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;">
            <strong>تأكيد الطلب - Direct Test</strong>
        </button>
    </div>
</form>

<div style="margin-top: 30px; padding: 15px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 5px;">
    <h3>📝 Test Instructions</h3>
    <ol>
        <li><strong>Submit the form above</strong> - It will go directly to checkout.php</li>
        <li><strong>Check what happens</strong> - Look for success/error messages</li>
        <li><strong>Check admin panel</strong> - <a href="admin/orders.php" target="_blank">View Orders in Admin</a></li>
        <li><strong>Check PHP error logs</strong> - Look for any error messages</li>
    </ol>
    
    <h4>Expected Results:</h4>
    <ul>
        <li>✅ Form should submit successfully</li>
        <li>✅ Order should be saved to database</li>
        <li>✅ Redirect to order success page</li>
        <li>✅ Order should appear in admin panel</li>
        <li>✅ Cart should be cleared</li>
    </ul>
</div>

<div style="margin-top: 20px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
    <h3>🔍 Debugging Tips</h3>
    <p>If the order doesn't work:</p>
    <ul>
        <li>Check browser developer console for JavaScript errors</li>
        <li>Look at the network tab to see if the form actually submits</li>
        <li>Check if you're redirected or if the page reloads</li>
        <li>Look for any error messages on the page</li>
    </ul>
    
    <p><strong>Quick Links:</strong></p>
    <ul>
        <li><a href="checkout.php" target="_blank">View Checkout Page</a></li>
        <li><a href="admin/orders.php" target="_blank">Admin Orders</a></li>
        <li><a href="trace_checkout_submission.php" target="_blank">Checkout Tracer</a></li>
    </ul>
</div>

<?php
echo "<h2>📊 Current System Status</h2>";

// Check current orders count
$currentOrders = fetchAll("SELECT COUNT(*) as count FROM orders");
$orderCount = $currentOrders[0]['count'] ?? 0;
echo "Current orders in database: <strong>$orderCount</strong><br>";

// Check recent orders
if ($orderCount > 0) {
    $recentOrders = fetchAll("SELECT id, customer_name, total_price, status, created_at FROM orders ORDER BY created_at DESC LIMIT 3");
    echo "<h4>Recent Orders:</h4>";
    echo "<ul>";
    foreach ($recentOrders as $order) {
        echo "<li>ID: {$order['id']} - {$order['customer_name']} - " . number_format($order['total_price']) . " IQD - {$order['status']} - {$order['created_at']}</li>";
    }
    echo "</ul>";
}

echo "<p><em>Test page loaded at " . date('Y-m-d H:i:s') . "</em></p>";
?>
