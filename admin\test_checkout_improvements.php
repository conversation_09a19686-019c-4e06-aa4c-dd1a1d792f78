<?php
/**
 * اختبار تحسينات نظام الدفع والطلبات
 * Checkout System Improvements Test
 */

require_once '../config/config.php';
require_once '../config/database.php';

// التحقق من الجلسة
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$testResults = [];

// Test 1: Delivery Pricing Table
try {
    $tableExists = false;
    $result = $pdo->query("SHOW TABLES LIKE 'delivery_pricing'");
    $tableExists = $result->rowCount() > 0;
    
    if ($tableExists) {
        $provincesCount = fetchOne("SELECT COUNT(*) as count FROM delivery_pricing");
        if ($provincesCount && $provincesCount['count'] > 0) {
            $testResults['delivery_table'] = '✅ جدول أسعار التوصيل: موجود ومُعد (' . $provincesCount['count'] . ' محافظة)';
        } else {
            $testResults['delivery_table'] = '⚠️ جدول أسعار التوصيل: موجود لكن فارغ';
        }
    } else {
        $testResults['delivery_table'] = '❌ جدول أسعار التوصيل: غير موجود';
    }
} catch (Exception $e) {
    $testResults['delivery_table'] = '❌ خطأ في اختبار جدول أسعار التوصيل: ' . $e->getMessage();
}

// Test 2: Iraqi Provinces Data
try {
    if ($tableExists) {
        $iraqiProvinces = [
            'بغداد', 'البصرة', 'نينوى', 'أربيل', 'النجف', 'كربلاء', 
            'الأنبار', 'ذي قار', 'بابل', 'كركوك', 'ديالى', 'المثنى', 
            'القادسية', 'ميسان', 'واسط', 'دهوك', 'السليمانية', 'صلاح الدين'
        ];
        
        $foundProvinces = 0;
        foreach ($iraqiProvinces as $province) {
            $exists = fetchOne("SELECT COUNT(*) as count FROM delivery_pricing WHERE province = ?", [$province]);
            if ($exists && $exists['count'] > 0) {
                $foundProvinces++;
            }
        }
        
        if ($foundProvinces == count($iraqiProvinces)) {
            $testResults['iraqi_provinces'] = '✅ المحافظات العراقية: جميع المحافظات موجودة (' . $foundProvinces . '/18)';
        } else {
            $testResults['iraqi_provinces'] = '⚠️ المحافظات العراقية: ناقصة (' . $foundProvinces . '/18)';
        }
    } else {
        $testResults['iraqi_provinces'] = '⏭️ تم تخطي اختبار المحافظات العراقية';
    }
} catch (Exception $e) {
    $testResults['iraqi_provinces'] = '❌ خطأ في اختبار المحافظات العراقية: ' . $e->getMessage();
}

// Test 3: Delivery Price Calculation
try {
    if ($tableExists) {
        $testProvince = fetchOne("SELECT province, price FROM delivery_pricing LIMIT 1");
        if ($testProvince) {
            $calculatedPrice = (float)$testProvince['price'];
            if ($calculatedPrice > 0) {
                $testResults['price_calculation'] = '✅ حساب أسعار التوصيل: يعمل بشكل صحيح (' . formatPrice($calculatedPrice) . ' لـ' . $testProvince['province'] . ')';
            } else {
                $testResults['price_calculation'] = '⚠️ حساب أسعار التوصيل: السعر صفر';
            }
        } else {
            $testResults['price_calculation'] = '❌ حساب أسعار التوصيل: لا توجد بيانات للاختبار';
        }
    } else {
        $testResults['price_calculation'] = '⏭️ تم تخطي اختبار حساب الأسعار';
    }
} catch (Exception $e) {
    $testResults['price_calculation'] = '❌ خطأ في اختبار حساب الأسعار: ' . $e->getMessage();
}

// Test 4: AJAX Endpoint
try {
    $ajaxFile = '../get_delivery_price.php';
    if (file_exists($ajaxFile)) {
        $testResults['ajax_endpoint'] = '✅ نقطة AJAX: الملف موجود ومُعد';
    } else {
        $testResults['ajax_endpoint'] = '❌ نقطة AJAX: الملف غير موجود';
    }
} catch (Exception $e) {
    $testResults['ajax_endpoint'] = '❌ خطأ في اختبار نقطة AJAX: ' . $e->getMessage();
}

// Test 5: Order Processing
try {
    // Test if orders table exists and can handle new structure
    $orderTableExists = false;
    $result = $pdo->query("SHOW TABLES LIKE 'orders'");
    $orderTableExists = $result->rowCount() > 0;
    
    if ($orderTableExists) {
        // Check if required columns exist
        $columns = $pdo->query("DESCRIBE orders")->fetchAll(PDO::FETCH_COLUMN);
        $requiredColumns = ['customer_name', 'customer_phone', 'address', 'province', 'delivery_price', 'total_price'];
        $missingColumns = array_diff($requiredColumns, $columns);
        
        if (empty($missingColumns)) {
            $testResults['order_processing'] = '✅ معالجة الطلبات: جدول الطلبات مُعد بشكل صحيح';
        } else {
            $testResults['order_processing'] = '⚠️ معالجة الطلبات: أعمدة ناقصة (' . implode(', ', $missingColumns) . ')';
        }
    } else {
        $testResults['order_processing'] = '❌ معالجة الطلبات: جدول الطلبات غير موجود';
    }
} catch (Exception $e) {
    $testResults['order_processing'] = '❌ خطأ في اختبار معالجة الطلبات: ' . $e->getMessage();
}

// Test 6: PHP Warnings Fix
try {
    $checkoutFile = '../checkout.php';
    if (file_exists($checkoutFile)) {
        $checkoutContent = file_get_contents($checkoutFile);

        // Check if PHP warnings are fixed
        $hasEmailReference = strpos($checkoutContent, '$customerEmail') !== false;
        $hasCityReference = strpos($checkoutContent, '$city') !== false;
        $hasPostalReference = strpos($checkoutContent, '$postalCode') !== false;

        // Check if professional design is applied
        $hasProfessionalDesign = strpos($checkoutContent, 'card-professional') !== false;
        $hasProgressIndicator = strpos($checkoutContent, 'progress-step-professional') !== false;
        $hasLoadingState = strpos($checkoutContent, 'btn-loading') !== false;

        $phpTests = [];
        $phpTests[] = $hasEmailReference ? '❌ مرجع البريد الإلكتروني: لم يتم حذفه' : '✅ مرجع البريد الإلكتروني: تم حذفه';
        $phpTests[] = $hasCityReference ? '❌ مرجع المدينة: لم يتم حذفه' : '✅ مرجع المدينة: تم حذفه';
        $phpTests[] = $hasPostalReference ? '❌ مرجع الرمز البريدي: لم يتم حذفه' : '✅ مرجع الرمز البريدي: تم حذفه';
        $phpTests[] = $hasProfessionalDesign ? '✅ التصميم المهني: مطبق' : '❌ التصميم المهني: غير مطبق';
        $phpTests[] = $hasProgressIndicator ? '✅ مؤشر التقدم: موجود' : '❌ مؤشر التقدم: غير موجود';
        $phpTests[] = $hasLoadingState ? '✅ حالة التحميل: مطبقة' : '❌ حالة التحميل: غير مطبقة';

        $testResults['php_fixes'] = implode('<br>', $phpTests);
    } else {
        $testResults['php_fixes'] = '❌ ملف الدفع: غير موجود';
    }
} catch (Exception $e) {
    $testResults['php_fixes'] = '❌ خطأ في اختبار إصلاحات PHP: ' . $e->getMessage();
}

// Test 7: Order Processing Enhancement
try {
    // Test if order data structure is correct
    $orderTableExists = false;
    $result = $pdo->query("SHOW TABLES LIKE 'orders'");
    $orderTableExists = $result->rowCount() > 0;

    if ($orderTableExists) {
        // Check if required columns exist for new structure
        $columns = $pdo->query("DESCRIBE orders")->fetchAll(PDO::FETCH_COLUMN);
        $requiredColumns = ['customer_name', 'customer_phone', 'address', 'province', 'delivery_price', 'total_price'];
        $missingColumns = array_diff($requiredColumns, $columns);

        // Check if removed columns are not referenced
        $removedColumns = ['customer_email', 'city', 'postal_code'];
        $presentRemovedColumns = array_intersect($removedColumns, $columns);

        if (empty($missingColumns) && empty($presentRemovedColumns)) {
            $testResults['order_structure'] = '✅ هيكل الطلبات: محدث بشكل صحيح';
        } else {
            $issues = [];
            if (!empty($missingColumns)) {
                $issues[] = 'أعمدة ناقصة: ' . implode(', ', $missingColumns);
            }
            if (!empty($presentRemovedColumns)) {
                $issues[] = 'أعمدة يجب حذفها: ' . implode(', ', $presentRemovedColumns);
            }
            $testResults['order_structure'] = '⚠️ هيكل الطلبات: ' . implode(' | ', $issues);
        }
    } else {
        $testResults['order_structure'] = '❌ جدول الطلبات: غير موجود';
    }
} catch (Exception $e) {
    $testResults['order_structure'] = '❌ خطأ في اختبار هيكل الطلبات: ' . $e->getMessage();
}

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card-professional fade-in-professional">
                <div class="card-header">
                    <h5 class="mb-0 text-professional-dark">
                        <i class="bi bi-check2-square text-professional-success"></i> 
                        اختبار تحسينات نظام الدفع والطلبات
                    </h5>
                </div>
                
                <div class="card-body">
                    <div class="alert-professional alert-info slide-up-professional">
                        <i class="bi bi-info-circle-fill alert-icon"></i>
                        <div class="alert-content">
                            <strong>معلومات الاختبار:</strong><br>
                            هذه الصفحة تختبر جميع التحسينات المطبقة على نظام الدفع والطلبات
                        </div>
                    </div>
                    
                    <div class="row">
                        <?php foreach ($testResults as $testName => $result): ?>
                            <div class="col-md-6 mb-3">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <?php 
                                            $titles = [
                                                'delivery_table' => 'جدول أسعار التوصيل',
                                                'iraqi_provinces' => 'المحافظات العراقية',
                                                'price_calculation' => 'حساب أسعار التوصيل',
                                                'ajax_endpoint' => 'نقطة AJAX للأسعار',
                                                'order_processing' => 'معالجة الطلبات',
                                                'php_fixes' => 'إصلاحات PHP والتصميم',
                                                'order_structure' => 'هيكل جدول الطلبات'
                                            ];
                                            echo $titles[$testName] ?? $testName;
                                            ?>
                                        </h6>
                                        <div class="card-text"><?php echo $result; ?></div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <hr class="border-professional">
                    
                    <div class="text-center">
                        <h5 class="text-professional-dark mb-3">الخطوات التالية</h5>
                        <div class="btn-group-professional">
                            <?php if (!$tableExists): ?>
                                <a href="create_delivery_pricing_table.php" class="btn-professional btn-warning hover-lift-professional">
                                    <i class="bi bi-plus-circle"></i> إنشاء جدول أسعار التوصيل
                                </a>
                            <?php else: ?>
                                <a href="delivery_pricing.php" class="btn-professional btn-primary hover-lift-professional">
                                    <i class="bi bi-gear"></i> إدارة أسعار التوصيل
                                </a>
                            <?php endif; ?>
                            <a href="../checkout.php" class="btn-professional btn-outline-primary hover-lift-professional">
                                <i class="bi bi-cart-check"></i> اختبار صفحة الدفع
                            </a>
                            <a href="reviews.php" class="btn-professional btn-outline-primary hover-lift-professional">
                                <i class="bi bi-star"></i> إدارة التقييمات
                            </a>
                            <a href="dashboard.php" class="btn-professional btn-outline-secondary hover-lift-professional">
                                <i class="bi bi-speedometer2"></i> لوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations to test result cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.3s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
