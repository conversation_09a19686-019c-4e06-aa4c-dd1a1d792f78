<?php
require_once '../config/config.php';

// التحقق من تسجيل دخول المدير (استخدام النظام الموجود)
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

// دالة احتياطية لجلب إعدادات الصفحة الرئيسية
if (!function_exists('getHomepageSettings')) {
    function getHomepageSettings($section = null) {
        global $pdo;
        try {
            if ($section) {
                $query = "SELECT setting_key, setting_value, setting_type FROM homepage_settings WHERE section_name = ? AND is_active = 1 ORDER BY sort_order";
                $stmt = $pdo->prepare($query);
                $stmt->execute([$section]);
            } else {
                $query = "SELECT section_name, setting_key, setting_value, setting_type FROM homepage_settings WHERE is_active = 1 ORDER BY section_name, sort_order";
                $stmt = $pdo->prepare($query);
                $stmt->execute();
            }

            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $settings = [];

            if ($section) {
                foreach ($results as $row) {
                    $settings[$row['setting_key']] = [
                        'value' => $row['setting_value'],
                        'type' => $row['setting_type']
                    ];
                }
            } else {
                foreach ($results as $row) {
                    $settings[$row['section_name']][$row['setting_key']] = [
                        'value' => $row['setting_value'],
                        'type' => $row['setting_type']
                    ];
                }
            }

            return $settings;
        } catch (Exception $e) {
            return [];
        }
    }
}

if (!function_exists('updateHomepageSetting')) {
    function updateHomepageSetting($section, $key, $value, $type = 'text') {
        global $pdo;
        try {
            $query = "INSERT INTO homepage_settings (section_name, setting_key, setting_value, setting_type)
                      VALUES (?, ?, ?, ?)
                      ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), setting_type = VALUES(setting_type)";
            $stmt = $pdo->prepare($query);
            return $stmt->execute([$section, $key, $value, $type]);
        } catch (Exception $e) {
            return false;
        }
    }
}

// التحقق من وجود جدول إعدادات الصفحة الرئيسية وإنشاؤه إذا لم يكن موجوداً
try {
    $tableExists = fetchOne("SHOW TABLES LIKE 'homepage_settings'");
    if (!$tableExists) {
        // إنشاء الجدول
        $createTableQuery = "
            CREATE TABLE `homepage_settings` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `section_name` varchar(100) NOT NULL,
                `setting_key` varchar(100) NOT NULL,
                `setting_value` text,
                `setting_type` enum('text','textarea','image','url','number','boolean') DEFAULT 'text',
                `sort_order` int(11) DEFAULT 0,
                `is_active` tinyint(1) DEFAULT 1,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_section_key` (`section_name`,`setting_key`),
                KEY `idx_section` (`section_name`),
                KEY `idx_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $pdo->exec($createTableQuery);

        // إدراج البيانات الافتراضية الأساسية
        $defaultSettings = [
            ['carousel', 'slide_1_image', 'https://via.placeholder.com/1200x600/667eea/ffffff?text=الشريحة+الأولى', 'image', 1],
            ['carousel', 'slide_1_title', 'مرحباً بك في متجرنا الإلكتروني', 'text', 2],
            ['carousel', 'slide_1_subtitle', 'اكتشف مجموعة واسعة من المنتجات عالية الجودة', 'text', 3],
            ['carousel', 'auto_advance_time', '10', 'number', 4],
            ['featured_products', 'show_section', '1', 'boolean', 1],
            ['featured_products', 'section_title', 'المنتجات المميزة', 'text', 2],
            ['offers', 'show_section', '1', 'boolean', 1],
            ['offers', 'section_title', 'العروض الخاصة', 'text', 2]
        ];

        $insertQuery = "INSERT INTO homepage_settings (section_name, setting_key, setting_value, setting_type, sort_order) VALUES (?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($insertQuery);

        foreach ($defaultSettings as $setting) {
            $stmt->execute($setting);
        }
    }
} catch (Exception $e) {
    error_log("Homepage settings table creation error: " . $e->getMessage());
}

// معالجة تحديث الإعدادات (قبل أي إخراج)
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $sections = ['carousel', 'featured_products', 'offers', 'influencers', 'customer_reviews', 'success_story', 'why_choose_us', 'newsletter', 'call_to_action'];
        
        foreach ($sections as $section) {
            foreach ($_POST as $key => $value) {
                if (strpos($key, $section . '_') === 0) {
                    $settingKey = substr($key, strlen($section . '_'));
                    $settingType = 'text';
                    
                    // تحديد نوع الإعداد
                    if (strpos($settingKey, 'image') !== false || strpos($settingKey, 'url') !== false) {
                        $settingType = 'url';
                    } elseif (strpos($settingKey, 'content') !== false || strpos($settingKey, 'description') !== false) {
                        $settingType = 'textarea';
                    } elseif (strpos($settingKey, 'limit') !== false || strpos($settingKey, 'time') !== false) {
                        $settingType = 'number';
                    } elseif (strpos($settingKey, 'show_') === 0) {
                        $settingType = 'boolean';
                        $value = isset($_POST[$key]) ? '1' : '0';
                    }
                    
                    updateHomepageSetting($section, $settingKey, sanitizeInput($value), $settingType);
                }
            }
        }
        
        $_SESSION['success'] = 'تم تحديث إعدادات الصفحة الرئيسية بنجاح';
        header('Location: homepage_settings.php');
        exit();
        
    } catch (Exception $e) {
        $_SESSION['error'] = 'حدث خطأ: ' . $e->getMessage();
    }
}

// تضمين الهيدر بعد معالجة النموذج
$pageTitle = 'إعدادات الصفحة الرئيسية';
require_once 'includes/header.php';

// جلب الإعدادات الحالية
$homepageSettings = getHomepageSettings();

// القيم الافتراضية
$defaults = [
    'carousel' => [
        'slide_1_title' => 'مرحباً بك في متجرنا الإلكتروني',
        'slide_1_subtitle' => 'اكتشف مجموعة واسعة من المنتجات عالية الجودة',
        'auto_advance_time' => '10',
        'show_indicators' => '1',
        'show_controls' => '1'
    ],
    'featured_products' => [
        'section_title' => 'المنتجات المميزة',
        'section_subtitle' => 'اكتشف أفضل منتجاتنا المختارة بعناية',
        'products_limit' => '6',
        'show_section' => '1'
    ],
    'offers' => [
        'section_title' => 'العروض الخاصة',
        'section_subtitle' => 'لا تفوت عروضنا المحدودة والحصرية',
        'show_section' => '1'
    ]
];

function getSettingValue($section, $key, $defaults) {
    global $homepageSettings;
    if (isset($homepageSettings[$section][$key])) {
        return $homepageSettings[$section][$key]['value'];
    }
    return isset($defaults[$section][$key]) ? $defaults[$section][$key] : '';
}
?>

<div class="container-fluid px-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">إعدادات الصفحة الرئيسية</h1>
            <p class="text-muted">إدارة محتوى وإعدادات الصفحة الرئيسية</p>
        </div>
        <div>
            <a href="../index.php" class="btn btn-outline-primary" target="_blank">
                <i class="bi bi-eye"></i> معاينة الصفحة الرئيسية
            </a>
        </div>
    </div>

    <!-- Messages -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="bi bi-check-circle"></i> <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="bi bi-exclamation-triangle"></i> <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <form method="POST" class="needs-validation" novalidate>
        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="carousel-tab" data-bs-toggle="tab" data-bs-target="#carousel" type="button">
                    <i class="bi bi-images"></i> الكاروسيل
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="products-tab" data-bs-toggle="tab" data-bs-target="#products" type="button">
                    <i class="bi bi-box"></i> المنتجات المميزة
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="offers-tab" data-bs-toggle="tab" data-bs-target="#offers" type="button">
                    <i class="bi bi-percent"></i> العروض
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="influencers-tab" data-bs-toggle="tab" data-bs-target="#influencers" type="button">
                    <i class="bi bi-people"></i> المؤثرين
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button">
                    <i class="bi bi-star"></i> آراء العملاء
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="story-tab" data-bs-toggle="tab" data-bs-target="#story" type="button">
                    <i class="bi bi-trophy"></i> قصة النجاح
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="features-tab" data-bs-toggle="tab" data-bs-target="#features" type="button">
                    <i class="bi bi-award"></i> لماذا تختارنا
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="newsletter-tab" data-bs-toggle="tab" data-bs-target="#newsletter" type="button">
                    <i class="bi bi-envelope"></i> النشرة البريدية
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="cta-tab" data-bs-toggle="tab" data-bs-target="#cta" type="button">
                    <i class="bi bi-arrow-right-circle"></i> ابدأ التسوق
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="settingsTabContent">
            <!-- Carousel Settings -->
            <div class="tab-pane fade show active" id="carousel" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-images"></i> إعدادات الكاروسيل</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Slide 1 -->
                            <div class="col-md-6 mb-4">
                                <h6 class="text-primary">الشريحة الأولى (مع النص)</h6>
                                <div class="mb-3">
                                    <label class="form-label">رابط الصورة</label>
                                    <input type="url" class="form-control" name="carousel_slide_1_image" 
                                           value="<?php echo htmlspecialchars(getSettingValue('carousel', 'slide_1_image', $defaults)); ?>" 
                                           placeholder="https://example.com/image.jpg">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان الرئيسي</label>
                                    <input type="text" class="form-control" name="carousel_slide_1_title" 
                                           value="<?php echo htmlspecialchars(getSettingValue('carousel', 'slide_1_title', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان الفرعي</label>
                                    <textarea class="form-control" name="carousel_slide_1_subtitle" rows="2"><?php echo htmlspecialchars(getSettingValue('carousel', 'slide_1_subtitle', $defaults)); ?></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">نص الزر</label>
                                    <input type="text" class="form-control" name="carousel_slide_1_button_text" 
                                           value="<?php echo htmlspecialchars(getSettingValue('carousel', 'slide_1_button_text', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رابط الزر</label>
                                    <input type="text" class="form-control" name="carousel_slide_1_button_url" 
                                           value="<?php echo htmlspecialchars(getSettingValue('carousel', 'slide_1_button_url', $defaults)); ?>">
                                </div>
                            </div>

                            <!-- Other Slides -->
                            <div class="col-md-6 mb-4">
                                <h6 class="text-primary">الشرائح الأخرى (صور فقط)</h6>
                                <div class="mb-3">
                                    <label class="form-label">الشريحة الثانية - رابط الصورة</label>
                                    <input type="url" class="form-control" name="carousel_slide_2_image" 
                                           value="<?php echo htmlspecialchars(getSettingValue('carousel', 'slide_2_image', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الشريحة الثالثة - رابط الصورة</label>
                                    <input type="url" class="form-control" name="carousel_slide_3_image" 
                                           value="<?php echo htmlspecialchars(getSettingValue('carousel', 'slide_3_image', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الشريحة الرابعة - رابط الصورة</label>
                                    <input type="url" class="form-control" name="carousel_slide_4_image" 
                                           value="<?php echo htmlspecialchars(getSettingValue('carousel', 'slide_4_image', $defaults)); ?>">
                                </div>
                                
                                <h6 class="text-primary mt-4">إعدادات الكاروسيل</h6>
                                <div class="mb-3">
                                    <label class="form-label">وقت التقدم التلقائي (بالثواني)</label>
                                    <input type="number" class="form-control" name="carousel_auto_advance_time" 
                                           value="<?php echo htmlspecialchars(getSettingValue('carousel', 'auto_advance_time', $defaults)); ?>" 
                                           min="5" max="30">
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="carousel_show_indicators" 
                                           <?php echo getSettingValue('carousel', 'show_indicators', $defaults) ? 'checked' : ''; ?>>
                                    <label class="form-check-label">إظهار النقاط السفلية</label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="carousel_show_controls" 
                                           <?php echo getSettingValue('carousel', 'show_controls', $defaults) ? 'checked' : ''; ?>>
                                    <label class="form-check-label">إظهار أسهم التنقل</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Featured Products Settings -->
            <div class="tab-pane fade" id="products" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-box"></i> إعدادات المنتجات المميزة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عنوان القسم</label>
                                    <input type="text" class="form-control" name="featured_products_section_title"
                                           value="<?php echo htmlspecialchars(getSettingValue('featured_products', 'section_title', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان الفرعي</label>
                                    <textarea class="form-control" name="featured_products_section_subtitle" rows="2"><?php echo htmlspecialchars(getSettingValue('featured_products', 'section_subtitle', $defaults)); ?></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عدد المنتجات المعروضة</label>
                                    <input type="number" class="form-control" name="featured_products_products_limit"
                                           value="<?php echo htmlspecialchars(getSettingValue('featured_products', 'products_limit', $defaults)); ?>"
                                           min="3" max="12">
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="featured_products_show_section"
                                           <?php echo getSettingValue('featured_products', 'show_section', $defaults) ? 'checked' : ''; ?>>
                                    <label class="form-check-label">إظهار هذا القسم</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Offers Settings -->
            <div class="tab-pane fade" id="offers" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-percent"></i> إعدادات العروض</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عنوان القسم</label>
                                    <input type="text" class="form-control" name="offers_section_title"
                                           value="<?php echo htmlspecialchars(getSettingValue('offers', 'section_title', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان الفرعي</label>
                                    <textarea class="form-control" name="offers_section_subtitle" rows="2"><?php echo htmlspecialchars(getSettingValue('offers', 'section_subtitle', $defaults)); ?></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رابط صورة البانر</label>
                                    <input type="url" class="form-control" name="offers_banner_image"
                                           value="<?php echo htmlspecialchars(getSettingValue('offers', 'banner_image', $defaults)); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عنوان البانر</label>
                                    <input type="text" class="form-control" name="offers_banner_title"
                                           value="<?php echo htmlspecialchars(getSettingValue('offers', 'banner_title', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">عنوان البانر الفرعي</label>
                                    <input type="text" class="form-control" name="offers_banner_subtitle"
                                           value="<?php echo htmlspecialchars(getSettingValue('offers', 'banner_subtitle', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">نص زر البانر</label>
                                    <input type="text" class="form-control" name="offers_banner_button_text"
                                           value="<?php echo htmlspecialchars(getSettingValue('offers', 'banner_button_text', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رابط زر البانر</label>
                                    <input type="text" class="form-control" name="offers_banner_button_url"
                                           value="<?php echo htmlspecialchars(getSettingValue('offers', 'banner_button_url', $defaults)); ?>">
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="offers_show_section"
                                           <?php echo getSettingValue('offers', 'show_section', $defaults) ? 'checked' : ''; ?>>
                                    <label class="form-check-label">إظهار هذا القسم</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Influencers Settings -->
            <div class="tab-pane fade" id="influencers" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-people"></i> إعدادات المؤثرين</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عنوان القسم</label>
                                    <input type="text" class="form-control" name="influencers_section_title"
                                           value="<?php echo htmlspecialchars(getSettingValue('influencers', 'section_title', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان الفرعي</label>
                                    <textarea class="form-control" name="influencers_section_subtitle" rows="2"><?php echo htmlspecialchars(getSettingValue('influencers', 'section_subtitle', $defaults)); ?></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عدد المحتوى المعروض</label>
                                    <input type="number" class="form-control" name="influencers_content_limit"
                                           value="<?php echo htmlspecialchars(getSettingValue('influencers', 'content_limit', $defaults)); ?>"
                                           min="3" max="12">
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="influencers_show_section"
                                           <?php echo getSettingValue('influencers', 'show_section', $defaults) ? 'checked' : ''; ?>>
                                    <label class="form-check-label">إظهار هذا القسم</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Reviews Settings -->
            <div class="tab-pane fade" id="reviews" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-star"></i> إعدادات آراء العملاء</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عنوان القسم</label>
                                    <input type="text" class="form-control" name="customer_reviews_section_title"
                                           value="<?php echo htmlspecialchars(getSettingValue('customer_reviews', 'section_title', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان الفرعي</label>
                                    <textarea class="form-control" name="customer_reviews_section_subtitle" rows="2"><?php echo htmlspecialchars(getSettingValue('customer_reviews', 'section_subtitle', $defaults)); ?></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عدد التقييمات المعروضة</label>
                                    <input type="number" class="form-control" name="customer_reviews_reviews_limit"
                                           value="<?php echo htmlspecialchars(getSettingValue('customer_reviews', 'reviews_limit', $defaults)); ?>"
                                           min="3" max="12">
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="customer_reviews_show_section"
                                           <?php echo getSettingValue('customer_reviews', 'show_section', $defaults) ? 'checked' : ''; ?>>
                                    <label class="form-check-label">إظهار هذا القسم</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Success Story Settings -->
            <div class="tab-pane fade" id="story" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-trophy"></i> إعدادات قصة النجاح</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عنوان القسم</label>
                                    <input type="text" class="form-control" name="success_story_section_title"
                                           value="<?php echo htmlspecialchars(getSettingValue('success_story', 'section_title', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">محتوى القصة</label>
                                    <textarea class="form-control" name="success_story_story_content" rows="4"><?php echo htmlspecialchars(getSettingValue('success_story', 'story_content', $defaults)); ?></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رابط صورة القصة</label>
                                    <input type="url" class="form-control" name="success_story_story_image"
                                           value="<?php echo htmlspecialchars(getSettingValue('success_story', 'story_image', $defaults)); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">الإنجازات</h6>
                                <div class="mb-3">
                                    <label class="form-label">عدد العملاء</label>
                                    <input type="text" class="form-control" name="success_story_achievements_customers"
                                           value="<?php echo htmlspecialchars(getSettingValue('success_story', 'achievements_customers', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">عدد المنتجات</label>
                                    <input type="text" class="form-control" name="success_story_achievements_products"
                                           value="<?php echo htmlspecialchars(getSettingValue('success_story', 'achievements_products', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">سنوات الخبرة</label>
                                    <input type="text" class="form-control" name="success_story_achievements_years"
                                           value="<?php echo htmlspecialchars(getSettingValue('success_story', 'achievements_years', $defaults)); ?>">
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="success_story_show_section"
                                           <?php echo getSettingValue('success_story', 'show_section', $defaults) ? 'checked' : ''; ?>>
                                    <label class="form-check-label">إظهار هذا القسم</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Why Choose Us Settings -->
            <div class="tab-pane fade" id="features" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-award"></i> إعدادات لماذا تختارنا</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عنوان القسم</label>
                                    <input type="text" class="form-control" name="why_choose_us_section_title"
                                           value="<?php echo htmlspecialchars(getSettingValue('why_choose_us', 'section_title', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان الفرعي</label>
                                    <textarea class="form-control" name="why_choose_us_section_subtitle" rows="2"><?php echo htmlspecialchars(getSettingValue('why_choose_us', 'section_subtitle', $defaults)); ?></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="why_choose_us_show_section"
                                           <?php echo getSettingValue('why_choose_us', 'show_section', $defaults) ? 'checked' : ''; ?>>
                                    <label class="form-check-label">إظهار هذا القسم</label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Feature 1 -->
                            <div class="col-md-6 mb-4">
                                <h6 class="text-primary">الميزة الأولى</h6>
                                <div class="mb-3">
                                    <label class="form-label">أيقونة Bootstrap (مثل: bi-award)</label>
                                    <input type="text" class="form-control" name="why_choose_us_feature_1_icon"
                                           value="<?php echo htmlspecialchars(getSettingValue('why_choose_us', 'feature_1_icon', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <input type="text" class="form-control" name="why_choose_us_feature_1_title"
                                           value="<?php echo htmlspecialchars(getSettingValue('why_choose_us', 'feature_1_title', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="why_choose_us_feature_1_description" rows="2"><?php echo htmlspecialchars(getSettingValue('why_choose_us', 'feature_1_description', $defaults)); ?></textarea>
                                </div>
                            </div>

                            <!-- Feature 2 -->
                            <div class="col-md-6 mb-4">
                                <h6 class="text-primary">الميزة الثانية</h6>
                                <div class="mb-3">
                                    <label class="form-label">أيقونة Bootstrap (مثل: bi-truck)</label>
                                    <input type="text" class="form-control" name="why_choose_us_feature_2_icon"
                                           value="<?php echo htmlspecialchars(getSettingValue('why_choose_us', 'feature_2_icon', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <input type="text" class="form-control" name="why_choose_us_feature_2_title"
                                           value="<?php echo htmlspecialchars(getSettingValue('why_choose_us', 'feature_2_title', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea class="form-control" name="why_choose_us_feature_2_description" rows="2"><?php echo htmlspecialchars(getSettingValue('why_choose_us', 'feature_2_description', $defaults)); ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Newsletter Settings -->
            <div class="tab-pane fade" id="newsletter" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-envelope"></i> إعدادات النشرة البريدية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عنوان القسم</label>
                                    <input type="text" class="form-control" name="newsletter_section_title"
                                           value="<?php echo htmlspecialchars(getSettingValue('newsletter', 'section_title', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان الفرعي</label>
                                    <textarea class="form-control" name="newsletter_section_subtitle" rows="2"><?php echo htmlspecialchars(getSettingValue('newsletter', 'section_subtitle', $defaults)); ?></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رابط صورة الخلفية</label>
                                    <input type="url" class="form-control" name="newsletter_background_image"
                                           value="<?php echo htmlspecialchars(getSettingValue('newsletter', 'background_image', $defaults)); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نص الزر</label>
                                    <input type="text" class="form-control" name="newsletter_button_text"
                                           value="<?php echo htmlspecialchars(getSettingValue('newsletter', 'button_text', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">نص حقل الإدخال</label>
                                    <input type="text" class="form-control" name="newsletter_placeholder_text"
                                           value="<?php echo htmlspecialchars(getSettingValue('newsletter', 'placeholder_text', $defaults)); ?>">
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="newsletter_show_section"
                                           <?php echo getSettingValue('newsletter', 'show_section', $defaults) ? 'checked' : ''; ?>>
                                    <label class="form-check-label">إظهار هذا القسم</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Call to Action Settings -->
            <div class="tab-pane fade" id="cta" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-arrow-right-circle"></i> إعدادات ابدأ التسوق</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">عنوان القسم</label>
                                    <input type="text" class="form-control" name="call_to_action_section_title"
                                           value="<?php echo htmlspecialchars(getSettingValue('call_to_action', 'section_title', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">العنوان الفرعي</label>
                                    <textarea class="form-control" name="call_to_action_section_subtitle" rows="2"><?php echo htmlspecialchars(getSettingValue('call_to_action', 'section_subtitle', $defaults)); ?></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">لون الخلفية</label>
                                    <input type="color" class="form-control form-control-color" name="call_to_action_background_color"
                                           value="<?php echo htmlspecialchars(getSettingValue('call_to_action', 'background_color', $defaults)); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نص الزر الأساسي</label>
                                    <input type="text" class="form-control" name="call_to_action_primary_button_text"
                                           value="<?php echo htmlspecialchars(getSettingValue('call_to_action', 'primary_button_text', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رابط الزر الأساسي</label>
                                    <input type="text" class="form-control" name="call_to_action_primary_button_url"
                                           value="<?php echo htmlspecialchars(getSettingValue('call_to_action', 'primary_button_url', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">نص الزر الثانوي</label>
                                    <input type="text" class="form-control" name="call_to_action_secondary_button_text"
                                           value="<?php echo htmlspecialchars(getSettingValue('call_to_action', 'secondary_button_text', $defaults)); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رابط الزر الثانوي</label>
                                    <input type="text" class="form-control" name="call_to_action_secondary_button_url"
                                           value="<?php echo htmlspecialchars(getSettingValue('call_to_action', 'secondary_button_url', $defaults)); ?>">
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="call_to_action_show_section"
                                           <?php echo getSettingValue('call_to_action', 'show_section', $defaults) ? 'checked' : ''; ?>>
                                    <label class="form-check-label">إظهار هذا القسم</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="bi bi-check-circle"></i> حفظ جميع الإعدادات
                </button>
                <a href="dashboard.php" class="btn btn-secondary btn-lg ms-3">
                    <i class="bi bi-arrow-left"></i> العودة للوحة التحكم
                </a>
            </div>
        </div>
    </form>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php require_once 'includes/footer.php'; ?>
