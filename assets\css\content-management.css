/* Content Management System Styles */

/* General Styles */
.content-management-system {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Card Hover Effects */
.influencer-card,
.guideline-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.influencer-card:hover,
.guideline-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    cursor: pointer;
}

/* Influencer Cards */
.influencer-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

.influencer-card .influencer-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(255,255,255,0.3);
}

.influencer-card .rating-stars {
    color: #ffc107;
}

.influencer-card .content-preview {
    max-height: 100px;
    overflow: hidden;
    position: relative;
}

.influencer-card .content-preview::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(transparent, white);
}

/* Guideline Cards */
.guideline-card .card-header {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
    border-bottom: none;
}

.guideline-card .difficulty-badge {
    font-size: 0.75em;
    padding: 0.25em 0.5em;
}

.guideline-card .importance-badge {
    font-size: 0.75em;
    padding: 0.25em 0.5em;
}

/* Video Embed Styles */
.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 8px;
}

/* Filter Section */
.filter-section {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.filter-section .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.filter-section .form-control,
.filter-section .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.filter-section .form-control:focus,
.filter-section .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Search Input */
.search-input-group {
    position: relative;
}

.search-input-group .form-control {
    padding-left: 3rem;
}

.search-input-group .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 10;
}

/* Pagination */
.pagination .page-link {
    border-radius: 10px;
    margin: 0 2px;
    border: none;
    color: #667eea;
    font-weight: 500;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.pagination .page-link:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* Modal Styles */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 2px solid #f8f9fa;
    border-radius: 15px 15px 0 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Form Styles */
.form-floating > label {
    color: #6c757d;
    font-weight: 500;
}

.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Steps, Tips, and Warnings */
.step-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid #667eea;
}

.step-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
}

.tip-item {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-left: 4px solid #28a745;
}

.warning-item {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-left: 4px solid #dc3545;
}

/* Admin Table Styles */
.admin-table {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.admin-table .table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
}

.admin-table .table tbody tr {
    transition: all 0.3s ease;
}

.admin-table .table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

/* Status Badges */
.status-badge {
    font-size: 0.75em;
    padding: 0.5em 1em;
    border-radius: 20px;
    font-weight: 600;
}

.status-published {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.status-draft {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
}

.status-archived {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
}

/* Action Buttons */
.action-btn {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    margin: 0 2px;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.action-btn-edit {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.action-btn-delete {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.action-btn-view {
    background: linear-gradient(135deg, #28a745 0%, #218838 100%);
    color: white;
}

/* Statistics Cards */
.stats-card {
    border-radius: 15px;
    border: none;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.stats-card-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stats-card-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.stats-card-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.stats-card-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border: 0.25em solid rgba(102, 126, 234, 0.25);
    border-right-color: #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .filter-section {
        padding: 1rem;
    }
    
    .influencer-card,
    .guideline-card {
        margin-bottom: 1rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .admin-table .table {
        font-size: 0.875rem;
    }
    
    .action-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* RTL Support */
[dir="rtl"] .search-input-group .search-icon {
    left: auto;
    right: 1rem;
}

[dir="rtl"] .step-item {
    border-left: none;
    border-right: 4px solid #667eea;
}

[dir="rtl"] .tip-item {
    border-left: none;
    border-right: 4px solid #28a745;
}

[dir="rtl"] .warning-item {
    border-left: none;
    border-right: 4px solid #dc3545;
}

[dir="rtl"] .step-number {
    margin-right: 0;
    margin-left: 1rem;
}

/* Print Styles */
@media print {
    .filter-section,
    .pagination,
    .action-btn,
    .modal {
        display: none !important;
    }
    
    .influencer-card,
    .guideline-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .content-management-system {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .influencer-card,
    .guideline-card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .filter-section {
        background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
    }
    
    .form-control,
    .form-select {
        background-color: #3d3d3d;
        border-color: #555555;
        color: #ffffff;
    }
    
    .modal-content {
        background-color: #2d2d2d;
        color: #ffffff;
    }
}
