<?php
// تفعيل عرض الأخطاء للتشخيص
ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'إدارة التصنيفات';

// معالجة الحذف
if (isset($_POST['delete_category']) && is_numeric($_POST['delete_category'])) {
    $categoryId = (int)$_POST['delete_category'];

    // التحقق من وجود منتجات في هذا التصنيف
    $productsCount = fetchOne("SELECT COUNT(*) as count FROM products WHERE category_id = ?", [$categoryId]);
    $count = ($productsCount && isset($productsCount['count'])) ? $productsCount['count'] : 0;

    if ($count > 0) {
        $_SESSION['error'] = "لا يمكن حذف التصنيف لأنه يحتوي على $count منتج. يرجى نقل المنتجات إلى تصنيف آخر أولاً.";
    } else {
        // جلب بيانات التصنيف للتأكد من وجوده
        $category = fetchOne("SELECT * FROM categories WHERE id = ?", [$categoryId]);

        if ($category) {
            // حذف صورة التصنيف إذا كانت موجودة
            if ($category['image']) {
                $imagePath = UPLOAD_PATH . '/categories/' . $category['image'];
                if (file_exists($imagePath)) {
                    unlink($imagePath);
                }
            }

            // حذف التصنيف من قاعدة البيانات
            $result = deleteData('categories', 'id = ?', [$categoryId]);

            if ($result) {
                $_SESSION['success'] = 'تم حذف التصنيف "' . htmlspecialchars($category['name']) . '" بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء حذف التصنيف. يرجى المحاولة مرة أخرى.';
            }
        } else {
            $_SESSION['error'] = 'التصنيف المطلوب حذفه غير موجود.';
        }
    }

    header('Location: categories.php');
    exit();
}

// معالجة تغيير الحالة
if (isset($_GET['toggle_status']) && is_numeric($_GET['toggle_status'])) {
    $categoryId = (int)$_GET['toggle_status'];
    $category = fetchOne("SELECT * FROM categories WHERE id = ?", [$categoryId]);

    if ($category) {
        $newStatus = $category['status'] == 'active' ? 'inactive' : 'active';
        $result = updateData('categories', ['status' => $newStatus], 'id = ?', [$categoryId]);

        if ($result !== false && $result > 0) {
            $statusText = $newStatus == 'active' ? 'تم تفعيل' : 'تم إلغاء تفعيل';
            $_SESSION['success'] = $statusText . ' التصنيف "' . htmlspecialchars($category['name']) . '" بنجاح';
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء تحديث حالة التصنيف. يرجى المحاولة مرة أخرى.';
        }
    } else {
        $_SESSION['error'] = 'التصنيف المطلوب تحديثه غير موجود.';
    }

    header('Location: categories.php');
    exit();
}

// معالجة إضافة/تعديل التصنيف
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = sanitizeInput($_POST['name']);
    $description = sanitizeInput($_POST['description']);
    $status = sanitizeInput($_POST['status']);
    $categoryId = isset($_POST['category_id']) ? (int)$_POST['category_id'] : 0;
    
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'اسم التصنيف مطلوب';
    }
    
    // التحقق من عدم تكرار الاسم
    $existingCategory = fetchOne("SELECT id FROM categories WHERE name = ? AND id != ?", [$name, $categoryId]);
    if ($existingCategory) {
        $errors[] = 'اسم التصنيف موجود مسبقاً';
    }
    
    // معالجة رابط الصورة
    $imageUrl = sanitizeInput($_POST['image_url'] ?? '');

    // التحقق من صحة رابط الصورة إذا تم إدخاله
    if (!empty($imageUrl)) {
        if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
            $errors[] = 'رابط الصورة غير صحيح. يرجى إدخال رابط صحيح.';
        } elseif (!preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $imageUrl)) {
            $errors[] = 'رابط الصورة يجب أن ينتهي بامتداد صورة صحيح (jpg, jpeg, png, gif, webp).';
        }
    }

    if (empty($errors)) {
        $categoryData = [
            'name' => $name,
            'description' => $description,
            'status' => $status
        ];

        // إضافة رابط الصورة إذا تم إدخاله
        if (!empty($imageUrl)) {
            $categoryData['image'] = $imageUrl;
        } elseif ($categoryId == 0) {
            // للتصنيفات الجديدة، تعيين قيمة فارغة إذا لم يتم إدخال رابط
            $categoryData['image'] = null;
        }

        if ($categoryId > 0) {
            // تعديل
            $result = updateData('categories', $categoryData, 'id = ?', [$categoryId]);
            if ($result !== false && $result > 0) {
                $_SESSION['success'] = 'تم تحديث التصنيف "' . htmlspecialchars($name) . '" بنجاح';
            } else {
                $errors[] = 'حدث خطأ أثناء تحديث التصنيف. يرجى التأكد من صحة البيانات والمحاولة مرة أخرى.';
            }
        } else {
            // إضافة
            $result = insertData('categories', $categoryData);
            if ($result) {
                $_SESSION['success'] = 'تم إضافة التصنيف "' . htmlspecialchars($name) . '" بنجاح';
            } else {
                $errors[] = 'حدث خطأ أثناء إضافة التصنيف. يرجى التأكد من صحة البيانات والمحاولة مرة أخرى.';
            }
        }

        if (empty($errors)) {
            header('Location: categories.php');
            exit();
        }
    }
}

// جلب التصنيفات
$categories = fetchAll("
    SELECT c.*, 
           COUNT(p.id) as products_count 
    FROM categories c 
    LEFT JOIN products p ON c.id = p.category_id 
    GROUP BY c.id 
    ORDER BY c.created_at DESC
");

// جلب بيانات التصنيف للتعديل
$editCategory = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $editCategory = fetchOne("SELECT * FROM categories WHERE id = ?", [(int)$_GET['edit']]);
}

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- نموذج إضافة/تعديل التصنيف -->
        <div class="col-md-4">
            <div class="card-professional slide-up-professional">
                <div class="card-header">
                    <h5 class="mb-0 text-professional-dark">
                        <i class="bi bi-<?php echo $editCategory ? 'pencil-square' : 'plus-circle'; ?> text-professional-primary"></i>
                        <?php echo $editCategory ? 'تعديل التصنيف' : 'إضافة تصنيف جديد'; ?>
                    </h5>
                </div>

                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert-professional alert-danger slide-up-professional">
                            <i class="bi bi-exclamation-triangle-fill alert-icon"></i>
                            <div class="alert-content">
                                <strong>يرجى تصحيح الأخطاء التالية:</strong>
                                <ul class="mb-0 mt-2">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo $error; ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="form-professional">
                        <?php if ($editCategory): ?>
                            <input type="hidden" name="category_id" value="<?php echo $editCategory['id']; ?>">
                        <?php endif; ?>

                        <div class="form-group-professional">
                            <label for="name" class="form-label">اسم التصنيف *</label>
                            <input type="text" class="form-control focus-professional" id="name" name="name"
                                   value="<?php echo htmlspecialchars($editCategory['name'] ?? $_POST['name'] ?? ''); ?>"
                                   required placeholder="أدخل اسم التصنيف">
                        </div>

                        <div class="form-group-professional">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control focus-professional" id="description" name="description"
                                      rows="3" placeholder="وصف مختصر للتصنيف (اختياري)"><?php echo htmlspecialchars($editCategory['description'] ?? $_POST['description'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="form-group-professional">
                            <label for="image_url" class="form-label">رابط صورة التصنيف</label>
                            <input type="url" class="form-control focus-professional" id="image_url" name="image_url"
                                   placeholder="https://example.com/image.jpg"
                                   value="<?php echo htmlspecialchars($editCategory['image'] ?? $_POST['image_url'] ?? ''); ?>">
                            <div class="form-text text-professional-muted">أدخل رابط صورة خارجية للتصنيف (اختياري)</div>

                            <!-- معاينة الصورة -->
                            <div id="image-preview-container" class="mt-2" style="display: none;">
                                <img id="image-preview" src="" alt="معاينة الصورة"
                                     style="width: 100px; height: 100px; object-fit: cover;"
                                     class="rounded-professional border-professional">
                            </div>

                            <?php if ($editCategory && $editCategory['image']): ?>
                                <div class="mt-2">
                                    <label class="form-text text-professional-muted">الصورة الحالية:</label><br>
                                    <img src="<?php echo htmlspecialchars($editCategory['image']); ?>"
                                         alt="<?php echo htmlspecialchars($editCategory['name']); ?>"
                                         style="width: 100px; height: 100px; object-fit: cover;"
                                         class="rounded-professional border-professional">
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="form-group-professional">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select focus-professional" id="status" name="status">
                                <option value="active" <?php echo ($editCategory['status'] ?? 'active') == 'active' ? 'selected' : ''; ?>>نشط</option>
                                <option value="inactive" <?php echo ($editCategory['status'] ?? '') == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                        
                        <div class="btn-group-professional" style="flex-direction: column; gap: 12px;">
                            <button type="submit" class="btn-professional btn-primary hover-lift-professional">
                                <i class="bi bi-check-circle"></i>
                                <?php echo $editCategory ? 'تحديث التصنيف' : 'إضافة التصنيف'; ?>
                            </button>
                            <?php if ($editCategory): ?>
                                <a href="categories.php" class="btn-professional btn-outline-secondary hover-lift-professional">
                                    <i class="bi bi-x-circle"></i> إلغاء التعديل
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- قائمة التصنيفات -->
        <div class="col-md-8">
            <div class="card-professional slide-up-professional">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-professional-dark">
                            <i class="bi bi-list-ul text-professional-primary"></i> قائمة التصنيفات
                        </h5>
                        <span class="badge-professional badge-info">إجمالي التصنيفات: <?php echo count($categories); ?></span>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- رسائل النجاح والخطأ -->
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert-professional alert-success slide-up-professional" role="alert">
                            <i class="bi bi-check-circle-fill alert-icon"></i>
                            <div class="alert-content">
                                <strong>تم بنجاح!</strong><br>
                                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert-professional alert-danger slide-up-professional" role="alert">
                            <i class="bi bi-exclamation-triangle-fill alert-icon"></i>
                            <div class="alert-content">
                                <strong>حدث خطأ!</strong><br>
                                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($categories)): ?>
                        <div class="table-responsive">
                            <table class="table-professional">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>اسم التصنيف</th>
                                        <th>الوصف</th>
                                        <th>عدد المنتجات</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($categories as $category): ?>
                                        <tr>
                                            <td>
                                                <?php if ($category['image']): ?>
                                                    <img src="<?php echo htmlspecialchars($category['image']); ?>"
                                                         alt="<?php echo htmlspecialchars($category['name']); ?>"
                                                         style="width: 50px; height: 50px; object-fit: cover;"
                                                         class="rounded border"
                                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                    <div class="bg-light d-flex align-items-center justify-content-center rounded"
                                                         style="width: 50px; height: 50px; display: none;">
                                                        <i class="bi bi-image-fill text-muted"></i>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="bg-light d-flex align-items-center justify-content-center rounded"
                                                         style="width: 50px; height: 50px;">
                                                        <i class="bi bi-image text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td><strong><?php echo htmlspecialchars($category['name']); ?></strong></td>
                                            <td>
                                                <?php 
                                                $desc = $category['description'] ?? '';
                                                echo htmlspecialchars(strlen($desc) > 50 ? substr($desc, 0, 50) . '...' : $desc); 
                                                ?>
                                            </td>
                                            <td>
                                                <span class="badge-professional badge-info"><?php echo $category['products_count']; ?></span>
                                            </td>
                                            <td>
                                                <div class="status-indicator-professional <?php echo $category['status'] == 'active' ? 'active' : 'inactive'; ?>">
                                                    <?php echo $category['status'] == 'active' ? 'نشط' : 'غير نشط'; ?>
                                                </div>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($category['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group-professional">
                                                    <a href="?edit=<?php echo $category['id']; ?>"
                                                       class="btn-professional btn-primary btn-sm hover-lift-professional" title="تعديل التصنيف">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <a href="?toggle_status=<?php echo $category['id']; ?>"
                                                       class="btn-professional btn-sm <?php echo $category['status'] == 'active' ? 'btn-warning' : 'btn-success'; ?> hover-lift-professional"
                                                       title="<?php echo $category['status'] == 'active' ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                        <i class="bi bi-toggle-<?php echo $category['status'] == 'active' ? 'on' : 'off'; ?>"></i>
                                                    </a>
                                                    <?php if ($category['products_count'] == 0): ?>
                                                        <form method="POST" style="display: inline-block;"
                                                              onsubmit="return handleDeleteCategory(event, '<?php echo htmlspecialchars($category['name'], ENT_QUOTES); ?>');">
                                                            <input type="hidden" name="delete_category" value="<?php echo $category['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف التصنيف">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        </form>
                                                    <?php else: ?>
                                                        <button class="btn btn-sm btn-outline-danger"
                                                                title="لا يمكن الحذف - يحتوي على <?php echo $category['products_count']; ?> منتج" disabled>
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5 fade-in-professional">
                            <i class="bi bi-tags display-1 text-professional-muted"></i>
                            <h4 class="mt-3 text-professional-dark">لا توجد تصنيفات</h4>
                            <p class="text-professional-muted">ابدأ بإضافة تصنيف جديد من النموذج على اليسار</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Professional delete confirmation handler
async function handleDeleteCategory(event, categoryName) {
    event.preventDefault();

    const confirmed = await confirmDelete(
        'هل أنت متأكد من حذف هذا التصنيف؟',
        'تأكيد حذف التصنيف',
        categoryName
    );

    if (confirmed) {
        event.target.closest('form').submit();
    }

    return false;
}

// معاينة الصورة من الرابط
document.getElementById('image_url').addEventListener('input', function(e) {
    const url = e.target.value.trim();
    const previewContainer = document.getElementById('image-preview-container');
    const preview = document.getElementById('image-preview');

    if (url && isValidImageUrl(url)) {
        preview.src = url;
        preview.onload = function() {
            previewContainer.style.display = 'block';
        };
        preview.onerror = function() {
            previewContainer.style.display = 'none';
        };
    } else {
        previewContainer.style.display = 'none';
    }
});

// التحقق من صحة رابط الصورة
function isValidImageUrl(url) {
    try {
        new URL(url);
        return /\.(jpg|jpeg|png|gif|webp)$/i.test(url);
    } catch {
        return false;
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
