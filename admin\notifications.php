<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'الإشعارات';

// Handle marking individual notification as read
if (isset($_POST['mark_read'])) {
    $type = sanitizeInput($_POST['type']);
    $referenceId = (int)$_POST['reference_id'];
    
    if (in_array($type, ['order', 'review']) && $referenceId > 0) {
        try {
            $insertRead = "INSERT IGNORE INTO admin_notification_reads (admin_id, notification_type, reference_id) VALUES (1, ?, ?)";
            $stmt = $pdo->prepare($insertRead);
            $stmt->execute([$type, $referenceId]);
            
            $_SESSION['message'] = [
                'text' => 'تم تحديد الإشعار كمقروء',
                'type' => 'success'
            ];
        } catch (Exception $e) {
            $_SESSION['message'] = [
                'text' => 'حدث خطأ أثناء تحديث الإشعار',
                'type' => 'error'
            ];
        }
    }
    
    header('Location: notifications.php');
    exit();
}

// Create the notification reads table if it doesn't exist
try {
    $createTable = "
        CREATE TABLE IF NOT EXISTS admin_notification_reads (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT NOT NULL DEFAULT 1,
            notification_type ENUM('order', 'review') NOT NULL,
            reference_id INT NOT NULL,
            read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_read (admin_id, notification_type, reference_id),
            INDEX idx_admin_type (admin_id, notification_type),
            INDEX idx_reference (reference_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTable);
} catch (Exception $e) {
    // Table might already exist, continue
}

// Get unread order notifications
$unreadOrders = fetchAll("
    SELECT 
        o.id,
        o.customer_name,
        o.customer_phone,
        o.total_price,
        o.created_at,
        o.status
    FROM orders o
    LEFT JOIN admin_notification_reads anr ON (anr.notification_type = 'order' AND anr.reference_id = o.id)
    WHERE o.status = 'pending' AND anr.id IS NULL
    ORDER BY o.created_at DESC
");

// Get unread review notifications
$unreadReviews = fetchAll("
    SELECT 
        r.id,
        r.customer_name,
        r.rating,
        r.comment,
        r.created_at,
        p.name as product_name,
        p.id as product_id
    FROM reviews r
    JOIN products p ON r.product_id = p.id
    LEFT JOIN admin_notification_reads anr ON (anr.notification_type = 'review' AND anr.reference_id = r.id)
    WHERE r.status = 'pending' AND anr.id IS NULL
    ORDER BY r.created_at DESC
");

// Get recent read notifications for history
$recentReadNotifications = fetchAll("
    SELECT 
        anr.notification_type,
        anr.reference_id,
        anr.read_at,
        CASE 
            WHEN anr.notification_type = 'order' THEN o.customer_name
            WHEN anr.notification_type = 'review' THEN r.customer_name
        END as customer_name,
        CASE 
            WHEN anr.notification_type = 'order' THEN CONCAT('طلب #', o.id)
            WHEN anr.notification_type = 'review' THEN CONCAT('تقييم للمنتج: ', p.name)
        END as title
    FROM admin_notification_reads anr
    LEFT JOIN orders o ON (anr.notification_type = 'order' AND anr.reference_id = o.id)
    LEFT JOIN reviews r ON (anr.notification_type = 'review' AND anr.reference_id = r.id)
    LEFT JOIN products p ON (r.product_id = p.id)
    WHERE anr.read_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    ORDER BY anr.read_at DESC
    LIMIT 10
");

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card welcome-section text-white border-0">
                <div class="card-body py-5">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="mb-3 fw-bold">
                                <i class="bi bi-bell-fill me-3"></i>
                                مركز الإشعارات
                            </h1>
                            <p class="mb-3 opacity-90 fs-5">
                                إدارة جميع الإشعارات والتنبيهات الخاصة بمتجرك
                            </p>
                            <div class="d-flex align-items-center">
                                <div class="me-4">
                                    <small class="opacity-75 d-block">آخر تحديث</small>
                                    <small class="fw-bold" id="lastUpdate"><?php echo date('Y-m-d H:i'); ?></small>
                                </div>
                                <div>
                                    <small class="opacity-75 d-block">حالة الإشعارات</small>
                                    <small class="fw-bold text-success">
                                        <i class="bi bi-check-circle me-1"></i>
                                        نشطة
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="position-relative">
                                <i class="bi bi-bell display-1 opacity-75"></i>
                                <div class="position-absolute top-50 start-50 translate-middle">
                                    <div class="bg-white bg-opacity-25 rounded-circle p-3">
                                        <i class="bi bi-exclamation text-white fs-3"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics -->
    <div class="row mb-5">
        <div class="col-md-4 mb-4">
            <div class="card stats-card border-0 h-100">
                <div class="card-body text-center p-4">
                    <div class="stats-icon-container warning mx-auto mb-4">
                        <i class="bi bi-cart-check text-white fs-3"></i>
                    </div>
                    <h3 class="stats-number mb-2"><?php echo is_array($unreadOrders) ? count($unreadOrders) : 0; ?></h3>
                    <p class="stats-label mb-2">طلبات غير مقروءة</p>
                    <small class="text-muted">
                        <i class="bi bi-clock me-1"></i>
                        تحتاج إلى مراجعة
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card stats-card border-0 h-100">
                <div class="card-body text-center p-4">
                    <div class="stats-icon-container info mx-auto mb-4">
                        <i class="bi bi-star text-white fs-3"></i>
                    </div>
                    <h3 class="stats-number mb-2"><?php echo is_array($unreadReviews) ? count($unreadReviews) : 0; ?></h3>
                    <p class="stats-label mb-2">تقييمات غير مقروءة</p>
                    <small class="text-muted">
                        <i class="bi bi-star-half me-1"></i>
                        في انتظار الموافقة
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card stats-card border-0 h-100">
                <div class="card-body text-center p-4">
                    <div class="stats-icon-container success mx-auto mb-4">
                        <i class="bi bi-check-all text-white fs-3"></i>
                    </div>
                    <h3 class="stats-number mb-2"><?php echo is_array($recentReadNotifications) ? count($recentReadNotifications) : 0; ?></h3>
                    <p class="stats-label mb-2">مقروءة مؤخراً</p>
                    <small class="text-muted">
                        <i class="bi bi-check-circle text-success me-1"></i>
                        آخر 7 أيام
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Unread Orders -->
    <?php if (is_array($unreadOrders) && !empty($unreadOrders)): ?>
    <div class="row mb-5">
        <div class="col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-transparent border-0 pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-1 fw-bold">
                                <i class="bi bi-cart-check text-warning me-2"></i>
                                طلبات جديدة
                            </h4>
                            <small class="text-muted">طلبات تحتاج إلى مراجعة ومعالجة</small>
                        </div>
                        <button class="btn btn-primary btn-sm px-4" onclick="markAllOrdersRead()">
                            <i class="bi bi-check-all me-2"></i>
                            تحديد الكل كمقروء
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th class="ps-4">رقم الطلب</th>
                                    <th>اسم العميل</th>
                                    <th>رقم الهاتف</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                    <th class="pe-4">إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($unreadOrders as $order): ?>
                                    <tr class="notification-row">
                                        <td class="ps-4">
                                            <div class="d-flex align-items-center">
                                                <div class="notification-indicator bg-warning me-3"></div>
                                                <strong class="text-primary">#<?php echo $order['id']; ?></strong>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                                    <i class="bi bi-person text-primary"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-semibold"><?php echo htmlspecialchars($order['customer_name']); ?></div>
                                                    <small class="text-muted">عميل جديد</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-light text-dark">
                                                <i class="bi bi-telephone me-1"></i>
                                                <?php echo htmlspecialchars($order['customer_phone']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success fs-6"><?php echo formatPrice($order['total_price']); ?></span>
                                        </td>
                                        <td>
                                            <div>
                                                <div class="fw-semibold"><?php echo formatDate($order['created_at']); ?></div>
                                                <small class="text-muted">
                                                    <i class="bi bi-clock me-1"></i>
                                                    <?php echo date('H:i', strtotime($order['created_at'])); ?>
                                                </small>
                                            </div>
                                        </td>
                                        <td class="pe-4">
                                            <div class="btn-group" role="group">
                                                <a href="order-details.php?id=<?php echo $order['id']; ?>"
                                                   class="btn btn-outline-primary btn-sm">
                                                    <i class="bi bi-eye me-1"></i>عرض
                                                </a>
                                                <form method="post" class="d-inline">
                                                    <input type="hidden" name="type" value="order">
                                                    <input type="hidden" name="reference_id" value="<?php echo $order['id']; ?>">
                                                    <button type="submit" name="mark_read" class="btn btn-outline-success btn-sm">
                                                        <i class="bi bi-check me-1"></i>مقروء
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Unread Reviews -->
    <?php if (is_array($unreadReviews) && !empty($unreadReviews)): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-star text-info"></i> تقييمات جديدة
                    </h5>
                    <button class="btn btn-outline-primary btn-sm" onclick="markAllReviewsRead()">
                        <i class="bi bi-check-all me-1"></i>
                        تحديد جميع التقييمات كمقروءة
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>المنتج</th>
                                    <th>اسم العميل</th>
                                    <th>التقييم</th>
                                    <th>التعليق</th>
                                    <th>التاريخ</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($unreadReviews as $review): ?>
                                    <tr>
                                        <td>
                                            <a href="../product.php?id=<?php echo $review['product_id']; ?>" target="_blank">
                                                <?php echo htmlspecialchars($review['product_name']); ?>
                                            </a>
                                        </td>
                                        <td><?php echo htmlspecialchars($review['customer_name']); ?></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="bi bi-star<?php echo $i <= $review['rating'] ? '-fill text-warning' : ' text-muted'; ?>"></i>
                                                <?php endfor; ?>
                                                <span class="ms-2">(<?php echo $review['rating']; ?>/5)</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                                <?php echo htmlspecialchars(substr($review['comment'], 0, 100)); ?>
                                                <?php if (strlen($review['comment']) > 100): ?>...<?php endif; ?>
                                            </div>
                                        </td>
                                        <td><?php echo formatDate($review['created_at']); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="reviews.php?id=<?php echo $review['id']; ?>"
                                                   class="btn btn-outline-primary btn-sm">
                                                    <i class="bi bi-eye"></i> عرض
                                                </a>
                                                <form method="post" class="d-inline">
                                                    <input type="hidden" name="type" value="review">
                                                    <input type="hidden" name="reference_id" value="<?php echo $review['id']; ?>">
                                                    <button type="submit" name="mark_read" class="btn btn-outline-success btn-sm">
                                                        <i class="bi bi-check"></i> تحديد كمقروء
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- No Unread Notifications -->
    <?php if ((!is_array($unreadOrders) || empty($unreadOrders)) && (!is_array($unreadReviews) || empty($unreadReviews))): ?>
    <div class="row mb-5">
        <div class="col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-body no-notifications">
                    <div class="content">
                        <div class="mb-4">
                            <i class="bi bi-check-circle display-1 text-success mb-3"></i>
                        </div>
                        <h3 class="fw-bold text-dark mb-3">🎉 رائع! لا توجد إشعارات جديدة</h3>
                        <p class="text-muted fs-5 mb-4">
                            جميع الإشعارات تم قراءتها ومعالجتها بنجاح
                        </p>
                        <div class="d-flex justify-content-center gap-3">
                            <a href="dashboard.php" class="btn btn-primary px-4">
                                <i class="bi bi-speedometer2 me-2"></i>
                                لوحة التحكم
                            </a>
                            <a href="orders.php" class="btn btn-outline-primary px-4">
                                <i class="bi bi-cart-check me-2"></i>
                                الطلبات
                            </a>
                            <a href="reviews.php" class="btn btn-outline-primary px-4">
                                <i class="bi bi-star me-2"></i>
                                التقييمات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Recent Read Notifications -->
    <?php if (is_array($recentReadNotifications) && !empty($recentReadNotifications)): ?>
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history"></i> الإشعارات المقروءة مؤخراً
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <?php foreach ($recentReadNotifications as $notification): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1"><?php echo htmlspecialchars($notification['title']); ?></h6>
                                    <small class="text-muted">
                                        العميل: <?php echo htmlspecialchars($notification['customer_name']); ?>
                                    </small>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted d-block">تم القراءة</small>
                                    <small class="text-muted"><?php echo formatDate($notification['read_at']); ?></small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
// Mark all orders as read
function markAllOrdersRead() {
    if (!confirm('هل أنت متأكد من تحديد جميع الطلبات كمقروءة؟')) {
        return;
    }

    const orderIds = <?php echo json_encode(is_array($unreadOrders) ? array_column($unreadOrders, 'id') : []); ?>;

    fetch('ajax/mark_specific_notifications_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            type: 'order',
            ids: orderIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ في الاتصال', 'error');
    });
}

// Mark all reviews as read
function markAllReviewsRead() {
    if (!confirm('هل أنت متأكد من تحديد جميع التقييمات كمقروءة؟')) {
        return;
    }

    const reviewIds = <?php echo json_encode(is_array($unreadReviews) ? array_column($unreadReviews, 'id') : []); ?>;

    fetch('ajax/mark_specific_notifications_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            type: 'review',
            ids: reviewIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('حدث خطأ في الاتصال', 'error');
    });
}
</script>

<?php require_once 'includes/footer.php'; ?>
