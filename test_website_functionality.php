<?php
/**
 * Website Functionality Test Script
 * Tests all major website components and pages
 */

// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار وظائف الموقع - Website Functionality Test</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background: #f8f9fa; }";
echo "h1, h2, h3 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".test-section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }";
echo ".btn { display: inline-block; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 3px; font-size: 14px; }";
echo ".btn:hover { background: #0056b3; }";
echo ".test-result { margin: 10px 0; padding: 8px; border-radius: 4px; }";
echo ".pass { background: #d4edda; color: #155724; }";
echo ".fail { background: #f8d7da; color: #721c24; }";
echo ".skip { background: #fff3cd; color: #856404; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🧪 اختبار وظائف الموقع الإلكتروني</h1>";
echo "<p class='info'>هذا السكريبت سيختبر جميع الصفحات والوظائف الرئيسية للموقع</p>";

$testResults = [];
$totalTests = 0;
$passedTests = 0;

function runTest($testName, $testFunction) {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    echo "<div class='test-section'>";
    echo "<h3>🔍 $testName</h3>";
    
    try {
        $result = $testFunction();
        if ($result['status'] === 'pass') {
            echo "<div class='test-result pass'>✅ نجح: " . $result['message'] . "</div>";
            $passedTests++;
        } elseif ($result['status'] === 'fail') {
            echo "<div class='test-result fail'>❌ فشل: " . $result['message'] . "</div>";
        } else {
            echo "<div class='test-result skip'>⏭️ تم التخطي: " . $result['message'] . "</div>";
        }
        
        if (isset($result['details'])) {
            echo "<div style='margin-top: 10px; font-size: 14px; color: #666;'>" . $result['details'] . "</div>";
        }
        
        $testResults[] = [
            'name' => $testName,
            'status' => $result['status'],
            'message' => $result['message']
        ];
        
    } catch (Exception $e) {
        echo "<div class='test-result fail'>❌ خطأ: " . $e->getMessage() . "</div>";
        $testResults[] = [
            'name' => $testName,
            'status' => 'fail',
            'message' => $e->getMessage()
        ];
    }
    
    echo "</div>";
}

// Test 1: Database Connection
runTest("اتصال قاعدة البيانات", function() {
    try {
        require_once 'config/config.php';
        
        if (!isset($pdo) || !$pdo) {
            return ['status' => 'fail', 'message' => 'متغير PDO غير متوفر'];
        }
        
        // Test a simple query
        $result = $pdo->query("SELECT 1")->fetchColumn();
        if ($result == 1) {
            return ['status' => 'pass', 'message' => 'الاتصال بقاعدة البيانات يعمل بشكل صحيح'];
        } else {
            return ['status' => 'fail', 'message' => 'فشل في تنفيذ استعلام بسيط'];
        }
        
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'خطأ في الاتصال: ' . $e->getMessage()];
    }
});

// Test 2: Required Tables
runTest("فحص الجداول المطلوبة", function() {
    try {
        require_once 'config/config.php';
        
        $requiredTables = ['admins', 'categories', 'products', 'orders', 'site_settings'];
        $existingTables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        $missingTables = array_diff($requiredTables, $existingTables);
        
        if (empty($missingTables)) {
            return [
                'status' => 'pass', 
                'message' => 'جميع الجداول المطلوبة موجودة',
                'details' => 'الجداول الموجودة: ' . implode(', ', $existingTables)
            ];
        } else {
            return [
                'status' => 'fail', 
                'message' => 'جداول مفقودة: ' . implode(', ', $missingTables),
                'details' => 'الجداول الموجودة: ' . implode(', ', $existingTables)
            ];
        }
        
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'خطأ في فحص الجداول: ' . $e->getMessage()];
    }
});

// Test 3: Homepage Loading
runTest("تحميل الصفحة الرئيسية", function() {
    try {
        ob_start();
        $error = false;
        
        set_error_handler(function($severity, $message, $file, $line) use (&$error) {
            $error = "خطأ PHP: $message في $file على السطر $line";
            return true;
        });
        
        include 'index.php';
        restore_error_handler();
        
        $output = ob_get_clean();
        
        if ($error) {
            return ['status' => 'fail', 'message' => $error];
        }
        
        if (strlen($output) > 1000) {
            return [
                'status' => 'pass', 
                'message' => 'تم تحميل الصفحة الرئيسية بنجاح',
                'details' => 'حجم المحتوى: ' . number_format(strlen($output)) . ' حرف'
            ];
        } else {
            return ['status' => 'fail', 'message' => 'محتوى الصفحة الرئيسية قصير جداً أو فارغ'];
        }
        
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'خطأ في تحميل الصفحة الرئيسية: ' . $e->getMessage()];
    }
});

// Test 4: Products Page
runTest("صفحة المنتجات", function() {
    try {
        if (!file_exists('products.php')) {
            return ['status' => 'fail', 'message' => 'ملف products.php غير موجود'];
        }
        
        ob_start();
        $error = false;
        
        set_error_handler(function($severity, $message, $file, $line) use (&$error) {
            $error = "خطأ PHP: $message";
            return true;
        });
        
        include 'products.php';
        restore_error_handler();
        
        $output = ob_get_clean();
        
        if ($error) {
            return ['status' => 'fail', 'message' => $error];
        }
        
        return ['status' => 'pass', 'message' => 'صفحة المنتجات تعمل بشكل صحيح'];
        
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'خطأ في صفحة المنتجات: ' . $e->getMessage()];
    }
});

// Test 5: Admin Login Page
runTest("صفحة تسجيل دخول الإدارة", function() {
    try {
        if (!file_exists('admin/login.php')) {
            return ['status' => 'fail', 'message' => 'ملف admin/login.php غير موجود'];
        }
        
        ob_start();
        $error = false;
        
        set_error_handler(function($severity, $message, $file, $line) use (&$error) {
            $error = "خطأ PHP: $message";
            return true;
        });
        
        include 'admin/login.php';
        restore_error_handler();
        
        $output = ob_get_clean();
        
        if ($error) {
            return ['status' => 'fail', 'message' => $error];
        }
        
        return ['status' => 'pass', 'message' => 'صفحة تسجيل دخول الإدارة تعمل بشكل صحيح'];
        
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'خطأ في صفحة تسجيل دخول الإدارة: ' . $e->getMessage()];
    }
});

// Test 6: Cart Functionality
runTest("وظائف سلة التسوق", function() {
    try {
        if (!file_exists('cart.php')) {
            return ['status' => 'fail', 'message' => 'ملف cart.php غير موجود'];
        }
        
        ob_start();
        $error = false;
        
        set_error_handler(function($severity, $message, $file, $line) use (&$error) {
            $error = "خطأ PHP: $message";
            return true;
        });
        
        include 'cart.php';
        restore_error_handler();
        
        $output = ob_get_clean();
        
        if ($error) {
            return ['status' => 'fail', 'message' => $error];
        }
        
        return ['status' => 'pass', 'message' => 'صفحة سلة التسوق تعمل بشكل صحيح'];
        
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'خطأ في سلة التسوق: ' . $e->getMessage()];
    }
});

// Test 7: AJAX Endpoints
runTest("نقاط AJAX", function() {
    try {
        $ajaxFiles = ['ajax/cart.php', 'ajax/search_suggestions.php', 'ajax/newsletter.php'];
        $workingFiles = [];
        $failedFiles = [];
        
        foreach ($ajaxFiles as $file) {
            if (file_exists($file)) {
                $workingFiles[] = $file;
            } else {
                $failedFiles[] = $file;
            }
        }
        
        if (empty($failedFiles)) {
            return [
                'status' => 'pass', 
                'message' => 'جميع ملفات AJAX موجودة',
                'details' => 'الملفات: ' . implode(', ', $workingFiles)
            ];
        } else {
            return [
                'status' => 'fail', 
                'message' => 'ملفات AJAX مفقودة: ' . implode(', ', $failedFiles),
                'details' => 'الملفات الموجودة: ' . implode(', ', $workingFiles)
            ];
        }
        
    } catch (Exception $e) {
        return ['status' => 'fail', 'message' => 'خطأ في فحص ملفات AJAX: ' . $e->getMessage()];
    }
});

// Summary
echo "<div class='test-section'>";
echo "<h2>📊 ملخص النتائج</h2>";

$successRate = $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 1) : 0;

if ($successRate >= 80) {
    echo "<div class='success'>";
    echo "<h3>🎉 ممتاز! الموقع يعمل بشكل جيد</h3>";
} elseif ($successRate >= 60) {
    echo "<div class='warning'>";
    echo "<h3>⚠️ الموقع يعمل مع بعض المشاكل</h3>";
} else {
    echo "<div class='error'>";
    echo "<h3>❌ الموقع يحتاج إلى إصلاحات</h3>";
}

echo "<p><strong>معدل النجاح:</strong> $successRate% ($passedTests من $totalTests اختبار)</p>";
echo "</div>";

echo "<h3>تفاصيل النتائج:</h3>";
echo "<ul>";
foreach ($testResults as $result) {
    $icon = $result['status'] === 'pass' ? '✅' : ($result['status'] === 'fail' ? '❌' : '⏭️');
    echo "<li>$icon <strong>" . $result['name'] . ":</strong> " . $result['message'] . "</li>";
}
echo "</ul>";

echo "<h3>الخطوات التالية:</h3>";
echo "<div style='text-align: center; margin: 20px 0;'>";

if ($successRate >= 80) {
    echo "<a href='index.php' class='btn' style='background: #28a745;'>🏠 زيارة الموقع</a>";
    echo "<a href='admin/login.php' class='btn' style='background: #17a2b8;'>🔐 لوحة التحكم</a>";
} else {
    echo "<a href='fix_website_issues.php' class='btn' style='background: #dc3545;'>🔧 إصلاح المشاكل</a>";
    echo "<a href='setup_database.php' class='btn' style='background: #ffc107; color: #212529;'>🗄️ إعداد قاعدة البيانات</a>";
}

echo "<a href='test_connection.php' class='btn'>🔍 اختبار الاتصال</a>";
echo "</div>";

echo "</div>";

echo "</body>";
echo "</html>";
?>
