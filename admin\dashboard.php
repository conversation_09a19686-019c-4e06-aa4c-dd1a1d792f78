<?php
require_once '../config/config.php';
requireAdminLogin();

$pageTitle = 'لوحة القيادة';

// جلب الإحصائيات
$productsCount = fetchOne("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
$ordersCount = fetchOne("SELECT COUNT(*) as count FROM orders");
$pendingOrdersCount = fetchOne("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'");
$reviewsCount = fetchOne("SELECT COUNT(*) as count FROM reviews WHERE status = 'approved'");
$newsletterCount = fetchOne("SELECT COUNT(*) as count FROM newsletter WHERE status = 'active'");

$stats = [
    'products' => ($productsCount && isset($productsCount['count'])) ? $productsCount['count'] : 0,
    'orders' => ($ordersCount && isset($ordersCount['count'])) ? $ordersCount['count'] : 0,
    'pending_orders' => ($pendingOrdersCount && isset($pendingOrdersCount['count'])) ? $pendingOrdersCount['count'] : 0,
    'reviews' => ($reviewsCount && isset($reviewsCount['count'])) ? $reviewsCount['count'] : 0,
    'newsletter' => ($newsletterCount && isset($newsletterCount['count'])) ? $newsletterCount['count'] : 0
];

// إحصائيات المبيعات
$salesStats = fetchOne("
    SELECT 
        SUM(total_price) as total_sales,
        AVG(total_price) as avg_order_value,
        COUNT(*) as total_orders
    FROM orders 
    WHERE status != 'cancelled'
");

// الطلبات الحديثة
$recentOrders = fetchAll("
    SELECT id, customer_name, total_price, status, created_at 
    FROM orders 
    ORDER BY created_at DESC 
    LIMIT 5
");

// المنتجات منخفضة المخزون
$lowStockProducts = fetchAll("
    SELECT id, name, stock 
    FROM products 
    WHERE stock <= 5 AND status = 'active' 
    ORDER BY stock ASC 
    LIMIT 5
");

// إحصائيات شهرية
$monthlyStats = fetchAll("
    SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        COUNT(*) as orders_count,
        SUM(total_price) as total_sales
    FROM orders 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
    GROUP BY DATE_FORMAT(created_at, '%Y-%m')
    ORDER BY month DESC
");

require_once 'includes/header.php';
?>

<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card welcome-section text-white border-0">
                <div class="card-body py-5">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="mb-3 fw-bold">مرحباً، <?php echo htmlspecialchars($_SESSION['admin_username']); ?>! 👋</h1>
                            <p class="mb-3 opacity-90 fs-5">
                                إليك نظرة سريعة على أداء متجرك اليوم
                            </p>
                            <div class="d-flex align-items-center">
                                <div class="me-4">
                                    <small class="opacity-75 d-block">آخر تحديث</small>
                                    <small class="fw-bold" id="currentTime"></small>
                                </div>
                                <div>
                                    <small class="opacity-75 d-block">حالة النظام</small>
                                    <small class="fw-bold text-success">
                                        <i class="bi bi-check-circle me-1"></i>
                                        يعمل بشكل طبيعي
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="position-relative">
                                <i class="bi bi-speedometer2 display-1 opacity-75"></i>
                                <div class="position-absolute top-50 start-50 translate-middle">
                                    <div class="bg-white bg-opacity-25 rounded-circle p-3">
                                        <i class="bi bi-graph-up text-white fs-3"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-5">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card stats-card h-100 border-0">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon-container primary">
                                <i class="bi bi-box text-white fs-3"></i>
                            </div>
                        </div>
                        <div class="ms-4">
                            <h3 class="stats-number stats-products mb-1"><?php echo number_format($stats['products']); ?></h3>
                            <p class="stats-label mb-0">المنتجات النشطة</p>
                            <small class="text-muted">
                                <i class="bi bi-arrow-up text-success me-1"></i>
                                +5% من الشهر الماضي
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card stats-card h-100 border-0">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon-container success">
                                <i class="bi bi-cart-check text-white fs-3"></i>
                            </div>
                        </div>
                        <div class="ms-4">
                            <h3 class="stats-number stats-orders mb-1"><?php echo number_format($stats['orders']); ?></h3>
                            <p class="stats-label mb-0">إجمالي الطلبات</p>
                            <small class="text-muted">
                                <i class="bi bi-arrow-up text-success me-1"></i>
                                +12% من الشهر الماضي
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card stats-card h-100 border-0">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon-container warning">
                                <i class="bi bi-clock text-white fs-3"></i>
                            </div>
                        </div>
                        <div class="ms-4">
                            <h3 class="stats-number stats-pending mb-1"><?php echo number_format($stats['pending_orders']); ?></h3>
                            <p class="stats-label mb-0">طلبات قيد المعالجة</p>
                            <small class="text-muted">
                                <i class="bi bi-exclamation-triangle text-warning me-1"></i>
                                يتطلب انتباه
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card stats-card h-100 border-0">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="stats-icon-container info">
                                <i class="bi bi-star text-white fs-3"></i>
                            </div>
                        </div>
                        <div class="ms-4">
                            <h3 class="stats-number stats-reviews mb-1"><?php echo number_format($stats['reviews']); ?></h3>
                            <p class="stats-label mb-0">التقييمات</p>
                            <small class="text-muted">
                                <i class="bi bi-star-fill text-warning me-1"></i>
                                متوسط 4.8/5
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sales Statistics -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-up"></i> إحصائيات المبيعات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4 mb-3">
                            <div class="border-end">
                                <h4 class="text-success mb-1">
                                    <?php echo formatPrice($salesStats['total_sales'] ?? 0); ?>
                                </h4>
                                <p class="text-muted mb-0">إجمالي المبيعات</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="border-end">
                                <h4 class="text-primary mb-1">
                                    <?php echo formatPrice($salesStats['avg_order_value'] ?? 0); ?>
                                </h4>
                                <p class="text-muted mb-0">متوسط قيمة الطلب</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <h4 class="text-info mb-1">
                                <?php echo number_format($salesStats['total_orders'] ?? 0); ?>
                            </h4>
                            <p class="text-muted mb-0">عدد الطلبات</p>
                        </div>
                    </div>
                    
                    <!-- Monthly Chart Placeholder -->
                    <div class="mt-4">
                        <canvas id="salesChart" height="100"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-exclamation-triangle text-warning"></i> تنبيهات المخزون
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($lowStockProducts): ?>
                        <?php foreach ($lowStockProducts as $product): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <h6 class="mb-0"><?php echo htmlspecialchars($product['name']); ?></h6>
                                    <small class="text-muted">المتبقي: <?php echo $product['stock']; ?> قطعة</small>
                                </div>
                                <span class="badge bg-warning">منخفض</span>
                            </div>
                            <hr class="my-2">
                        <?php endforeach; ?>
                        <div class="text-center mt-3">
                            <a href="products.php" class="btn btn-outline-warning btn-sm">
                                عرض جميع المنتجات
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted">
                            <i class="bi bi-check-circle display-4 text-success"></i>
                            <p class="mt-2">جميع المنتجات متوفرة بكميات كافية</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Orders -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history"></i> الطلبات الحديثة
                    </h5>
                    <a href="orders.php" class="btn btn-outline-primary btn-sm">
                        عرض جميع الطلبات
                    </a>
                </div>
                <div class="card-body p-0">
                    <?php if ($recentOrders): ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>اسم العميل</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentOrders as $order): ?>
                                        <tr>
                                            <td><strong>#<?php echo $order['id']; ?></strong></td>
                                            <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                            <td><?php echo formatPrice($order['total_price']); ?></td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                $statusText = '';
                                                switch ($order['status']) {
                                                    case 'pending':
                                                        $statusClass = 'bg-warning';
                                                        $statusText = 'قيد المعالجة';
                                                        break;
                                                    case 'confirmed':
                                                        $statusClass = 'bg-info';
                                                        $statusText = 'مؤكد';
                                                        break;
                                                    case 'processing':
                                                        $statusClass = 'bg-primary';
                                                        $statusText = 'قيد التحضير';
                                                        break;
                                                    case 'shipped':
                                                        $statusClass = 'bg-secondary';
                                                        $statusText = 'تم الشحن';
                                                        break;
                                                    case 'delivered':
                                                        $statusClass = 'bg-success';
                                                        $statusText = 'تم التسليم';
                                                        break;
                                                    case 'cancelled':
                                                        $statusClass = 'bg-danger';
                                                        $statusText = 'ملغي';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                            </td>
                                            <td><?php echo formatDate($order['created_at']); ?></td>
                                            <td>
                                                <a href="order-details.php?id=<?php echo $order['id']; ?>" 
                                                   class="btn btn-outline-primary btn-sm">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4 text-muted">
                            <i class="bi bi-inbox display-4"></i>
                            <p class="mt-2">لا توجد طلبات حتى الآن</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-transparent border-0 pb-0">
                    <div class="d-flex align-items-center justify-content-between">
                        <h4 class="mb-0 fw-bold">
                            <i class="bi bi-lightning-charge text-warning me-2"></i>
                            إجراءات سريعة
                        </h4>
                        <small class="text-muted">اختصارات للمهام الأساسية</small>
                    </div>
                </div>
                <div class="card-body pt-4">
                    <div class="row g-4">
                        <div class="col-lg-3 col-md-6">
                            <div class="quick-action-card h-100">
                                <a href="products.php?action=add" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-4 text-decoration-none">
                                    <div class="mb-3">
                                        <i class="bi bi-plus-circle display-4 text-primary"></i>
                                    </div>
                                    <h6 class="fw-bold mb-1">إضافة منتج جديد</h6>
                                    <small class="text-muted">أضف منتجات جديدة للمتجر</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="quick-action-card h-100">
                                <a href="orders.php" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-4 text-decoration-none">
                                    <div class="mb-3">
                                        <i class="bi bi-list-check display-4 text-success"></i>
                                    </div>
                                    <h6 class="fw-bold mb-1">إدارة الطلبات</h6>
                                    <small class="text-muted">تتبع ومعالجة الطلبات</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="quick-action-card h-100">
                                <a href="reviews.php" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-4 text-decoration-none">
                                    <div class="mb-3">
                                        <i class="bi bi-star display-4 text-warning"></i>
                                    </div>
                                    <h6 class="fw-bold mb-1">مراجعة التقييمات</h6>
                                    <small class="text-muted">إدارة تقييمات العملاء</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="quick-action-card h-100">
                                <a href="settings.php" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-4 text-decoration-none">
                                    <div class="mb-3">
                                        <i class="bi bi-gear display-4 text-info"></i>
                                    </div>
                                    <h6 class="fw-bold mb-1">إعدادات الموقع</h6>
                                    <small class="text-muted">تخصيص إعدادات المتجر</small>
                                </a>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="quick-action-card h-100">
                                <a href="homepage_settings.php" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-4 text-decoration-none">
                                    <div class="mb-3">
                                        <i class="bi bi-house-gear display-4 text-success"></i>
                                    </div>
                                    <h6 class="fw-bold mb-1">إعدادات الصفحة الرئيسية</h6>
                                    <small class="text-muted">إدارة محتوى الصفحة الرئيسية</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// رسم بياني للمبيعات الشهرية
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('salesChart');
    if (ctx) {
        const monthlyData = <?php echo json_encode($monthlyStats); ?>;

        const labels = monthlyData.map(item => {
            const date = new Date(item.month + '-01');
            return date.toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
        }).reverse();

        const salesData = monthlyData.map(item => parseFloat(item.total_sales || 0)).reverse();
        const ordersData = monthlyData.map(item => parseInt(item.orders_count || 0)).reverse();

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'المبيعات (<?php echo getSetting('currency'); ?>)',
                    data: salesData,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y'
                }, {
                    label: 'عدد الطلبات',
                    data: ordersData,
                    borderColor: '#764ba2',
                    backgroundColor: 'rgba(118, 75, 162, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'الشهر'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'المبيعات'
                        },
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'عدد الطلبات'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'إحصائيات المبيعات الشهرية'
                    }
                }
            }
        });
    }
});

// تحديث الإحصائيات تلقائياً
function refreshStats() {
    fetch('ajax/dashboard_stats.php')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.error) {
                    console.error('Server error:', data.message);
                    return;
                }

                // تحديث الإحصائيات باستخدام الكلاسات الجديدة
                const productsElement = document.querySelector('.stats-products');
                const ordersElement = document.querySelector('.stats-orders');
                const pendingElement = document.querySelector('.stats-pending');
                const reviewsElement = document.querySelector('.stats-reviews');

                if (productsElement) {
                    productsElement.textContent = formatNumber(data.products);
                }
                if (ordersElement) {
                    ordersElement.textContent = formatNumber(data.orders);
                }
                if (pendingElement) {
                    pendingElement.textContent = formatNumber(data.pending_orders);
                }
                if (reviewsElement) {
                    reviewsElement.textContent = formatNumber(data.reviews);
                }
            } catch (parseError) {
                console.error('JSON Parse Error:', parseError);
                console.error('Response text:', text);
            }
        })
        .catch(error => console.error('Error refreshing stats:', error));
}

// تحديث الإحصائيات كل دقيقة
setInterval(refreshStats, 60000);

// تأثيرات بصرية للبطاقات
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1
    });

    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
        observer.observe(card);
    });
});

// عداد متحرك للأرقام
function animateNumbers() {
    const numbers = document.querySelectorAll('.stats-number');

    numbers.forEach(number => {
        const target = parseInt(number.textContent.replace(/,/g, ''));
        if (isNaN(target)) return;

        let current = 0;
        const increment = target / 50;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            number.textContent = formatNumber(Math.floor(current));
        }, 30);
    });
}

// تشغيل العداد عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(animateNumbers, 500);
});

// إضافة تأثيرات hover للأزرار السريعة
document.addEventListener('DOMContentLoaded', function() {
    const quickActions = document.querySelectorAll('.card-body .btn');

    quickActions.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});

// تحديث الوقت الحالي
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });

    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

// تحديث الوقت كل ثانية
setInterval(updateCurrentTime, 1000);

// إضافة عنصر الوقت إلى الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const welcomeCard = document.querySelector('.bg-gradient-primary .card-body');
    if (welcomeCard) {
        const timeElement = document.createElement('small');
        timeElement.id = 'currentTime';
        timeElement.className = 'opacity-75 d-block mt-2';
        welcomeCard.appendChild(timeElement);
        updateCurrentTime();
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
