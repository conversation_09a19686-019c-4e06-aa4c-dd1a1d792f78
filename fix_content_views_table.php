<?php
require_once 'config/config.php';

echo "=== إصلاح جدول content_views ===\n";

try {
    echo "\n=== 1. فحص وجود جدول content_views ===\n";
    
    $tables = $pdo->query("SHOW TABLES LIKE 'content_views'")->fetchAll();
    
    if (empty($tables)) {
        echo "جدول content_views غير موجود - سيتم إنشاؤه\n";
        
        $createViewsTable = "
        CREATE TABLE content_views (
            id INT AUTO_INCREMENT PRIMARY KEY,
            content_id INT NOT NULL,
            content_type ENUM('guideline', 'influencer') NOT NULL,
            user_ip VARCHAR(45),
            user_agent TEXT,
            viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            INDEX idx_content (content_id, content_type),
            INDEX idx_viewed_at (viewed_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createViewsTable);
        echo "✅ تم إنشاء جدول content_views بنجاح\n";
        
    } else {
        echo "✓ جدول content_views موجود بالفعل\n";
    }
    
    echo "\n=== 2. إضافة بيانات تجريبية للمشاهدات ===\n";
    
    // الحصول على معرفات المؤثرين الموجودين
    $influencers = fetchAll("SELECT id FROM influencers_content");
    
    if (!empty($influencers)) {
        echo "إضافة مشاهدات تجريبية للمؤثرين...\n";
        
        $insertView = $pdo->prepare("INSERT INTO content_views (content_id, content_type, user_ip, viewed_at) VALUES (?, 'influencer', ?, ?)");
        
        foreach ($influencers as $influencer) {
            // إضافة عدد عشوائي من المشاهدات (1-10)
            $viewCount = rand(1, 10);
            
            for ($i = 0; $i < $viewCount; $i++) {
                $randomIP = rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255);
                $randomDate = date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'));
                
                $insertView->execute([$influencer['id'], $randomIP, $randomDate]);
            }
            
            echo "- تم إضافة {$viewCount} مشاهدة للمؤثر ID: " . $influencer['id'] . "\n";
        }
        
        echo "✅ تم إضافة البيانات التجريبية\n";
    }
    
    echo "\n=== 3. اختبار الاستعلام المحدث ===\n";
    
    // اختبار الاستعلام الأصلي
    $sql = "
        SELECT ic.*, cc.name_ar as category_name, p.name as product_name,
               COALESCE(cv.view_count, 0) as total_views
        FROM influencers_content ic
        LEFT JOIN content_categories cc ON ic.category_id = cc.id
        LEFT JOIN products p ON ic.product_id = p.id
        LEFT JOIN (
            SELECT content_id, COUNT(*) as view_count 
            FROM content_views 
            WHERE content_type = 'influencer' 
            GROUP BY content_id
        ) cv ON ic.id = cv.content_id
        WHERE 1=1
        ORDER BY ic.sort_order ASC, ic.created_at DESC
        LIMIT 20 OFFSET 0
    ";
    
    $contents = fetchAll($sql);
    
    echo "عدد النتائج: " . count($contents) . "\n";
    
    if (!empty($contents)) {
        echo "✅ الاستعلام يعمل بنجاح!\n";
        echo "النتائج:\n";
        
        foreach ($contents as $content) {
            echo "- " . $content['influencer_name'] . " (" . $content['content_type'] . ") - مشاهدات: " . $content['total_views'] . "\n";
        }
    } else {
        echo "⚠ لا توجد نتائج - قد تحتاج إلى إضافة بيانات\n";
    }
    
    echo "\n=== 4. إحصائيات المشاهدات ===\n";
    
    $viewStats = fetchOne("SELECT COUNT(*) as total_views, COUNT(DISTINCT content_id) as viewed_content FROM content_views WHERE content_type = 'influencer'");
    
    echo "إجمالي المشاهدات: " . $viewStats['total_views'] . "\n";
    echo "المحتوى المشاهد: " . $viewStats['viewed_content'] . "\n";

} catch (Exception $e) {
    echo "✗ خطأ: " . $e->getMessage() . "\n";
}

echo "\n=== انتهاء الإصلاح ===\n";
?>
