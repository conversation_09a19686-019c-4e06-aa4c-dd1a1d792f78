<?php
require_once '../config/config.php';
requireAdminLogin();

echo "<h2>اختبار رابط الحذف</h2>";

// Check if delete parameter is being passed
if (isset($_GET['delete'])) {
    echo "<p style='color: green;'>✅ تم استلام معامل الحذف: " . htmlspecialchars($_GET['delete']) . "</p>";
    
    if (is_numeric($_GET['delete'])) {
        $productId = (int)$_GET['delete'];
        echo "<p style='color: green;'>✅ معرف المنتج صحيح: $productId</p>";
        
        // Test the exact same logic as products.php
        $product = fetchOne("SELECT name FROM products WHERE id = ?", [$productId]);
        
        if ($product) {
            echo "<p style='color: green;'>✅ تم العثور على المنتج: " . htmlspecialchars($product['name']) . "</p>";
            
            if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
                echo "<h3>تنفيذ الحذف...</h3>";
                
                try {
                    // Same logic as products.php
                    $images = fetchAll("SELECT image_path FROM product_images WHERE product_id = ?", [$productId]);
                    echo "<p>عدد الصور المرتبطة: " . count($images) . "</p>";
                    
                    $deleteImages = deleteData('product_images', 'product_id = ?', [$productId]);
                    $deleteProduct = deleteData('products', 'id = ?', [$productId]);
                    
                    echo "<p><strong>نتيجة حذف الصور:</strong> " . var_export($deleteImages, true) . "</p>";
                    echo "<p><strong>نتيجة حذف المنتج:</strong> " . var_export($deleteProduct, true) . "</p>";
                    
                    if ($deleteProduct !== false && $deleteProduct > 0) {
                        echo "<p style='color: green; font-size: 18px;'>✅ تم حذف المنتج بنجاح!</p>";
                        echo "<p><a href='products.php'>العودة إلى قائمة المنتجات</a></p>";
                    } else {
                        echo "<p style='color: red;'>❌ فشل في حذف المنتج</p>";
                        
                        // Additional debugging
                        $stillExists = fetchOne("SELECT id, name FROM products WHERE id = ?", [$productId]);
                        if ($stillExists) {
                            echo "<p style='color: red;'>المنتج ما زال موجود: " . htmlspecialchars($stillExists['name']) . "</p>";
                        } else {
                            echo "<p style='color: green;'>المنتج لم يعد موجود - قد يكون الحذف نجح فعلاً</p>";
                        }
                    }
                    
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<p><strong>هل تريد حذف هذا المنتج؟</strong></p>";
                echo "<p><a href='?delete=$productId&confirm=yes' class='btn btn-danger'>نعم، احذف المنتج</a></p>";
                echo "<p><a href='products.php' class='btn btn-secondary'>إلغاء</a></p>";
            }
        } else {
            echo "<p style='color: red;'>❌ المنتج غير موجود</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ معرف المنتج غير صحيح</p>";
    }
} else {
    echo "<p style='color: orange;'>لم يتم تمرير معامل الحذف</p>";
    
    // Show available products for testing
    $products = fetchAll("SELECT id, name FROM products ORDER BY id DESC LIMIT 5");
    if (!empty($products)) {
        echo "<h3>منتجات متاحة للاختبار:</h3>";
        echo "<ul>";
        foreach ($products as $prod) {
            echo "<li>";
            echo "<strong>" . htmlspecialchars($prod['name']) . "</strong> ";
            echo "<a href='?delete=" . $prod['id'] . "'>اختبار الحذف</a>";
            echo "</li>";
        }
        echo "</ul>";
    }
}

echo "<hr>";
echo "<p><a href='products.php'>العودة إلى إدارة المنتجات</a></p>";
echo "<p><a href='debug_delete_issue.php'>تشغيل التشخيص الشامل</a></p>";
?>
