<?php
require_once 'config/config.php';

echo "تحديث جدول content_views...\n";

try {
    $pdo = getConnection();
    
    // التحقق من وجود الجدول
    $tableExists = $pdo->query("SHOW TABLES LIKE 'content_views'")->rowCount() > 0;
    
    if (!$tableExists) {
        echo "إنشاء جدول content_views...\n";
        $pdo->exec("
            CREATE TABLE content_views (
                id INT AUTO_INCREMENT PRIMARY KEY,
                content_type ENUM('influencer', 'guideline', 'page') NOT NULL,
                content_id INT NOT NULL,
                page_name VARCHAR(100) NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                referrer VARCHAR(1000),
                session_id VARCHAR(255),
                viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                INDEX idx_content (content_type, content_id),
                INDEX idx_viewed_at (viewed_at),
                INDEX idx_page (content_type, page_name)
            )
        ");
        echo "✅ تم إنشاء جدول content_views\n";
    } else {
        echo "تحديث جدول content_views الموجود...\n";
        
        // التحقق من وجود العمود page_name
        $columns = $pdo->query("SHOW COLUMNS FROM content_views LIKE 'page_name'")->rowCount();
        if ($columns == 0) {
            $pdo->exec("ALTER TABLE content_views ADD COLUMN page_name VARCHAR(100) NULL AFTER content_id");
            echo "✅ تم إضافة عمود page_name\n";
        }
        
        // التحقق من وجود العمود created_at
        $columns = $pdo->query("SHOW COLUMNS FROM content_views LIKE 'created_at'")->rowCount();
        if ($columns == 0) {
            $pdo->exec("ALTER TABLE content_views ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
            echo "✅ تم إضافة عمود created_at\n";
        }
        
        // تحديث ENUM لـ content_type
        $pdo->exec("ALTER TABLE content_views MODIFY content_type ENUM('influencer', 'guideline', 'page') NOT NULL");
        echo "✅ تم تحديث content_type enum\n";
        
        // إضافة فهرس للصفحات
        try {
            $pdo->exec("ALTER TABLE content_views ADD INDEX idx_page (content_type, page_name)");
            echo "✅ تم إضافة فهرس idx_page\n";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                throw $e;
            }
            echo "⚠️ فهرس idx_page موجود مسبقاً\n";
        }
    }
    
    echo "✅ تم تحديث جدول content_views بنجاح\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في تحديث الجدول: " . $e->getMessage() . "\n";
}
?>
